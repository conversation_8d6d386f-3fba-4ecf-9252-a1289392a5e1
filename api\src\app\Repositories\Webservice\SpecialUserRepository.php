<?php

namespace App\Repositories\Webservice;

use App\Models\Webservice\SpecialUser;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use Illuminate\Support\Str;

/**
 * Repository for handling SpecialUser model interactions.
 *
 * This repository implements the SpecialUserRepositoryInterface and provides
 * methods to manage SpecialUser instances, including creation, retrieval,
 * and updates to webhook endpoints and valid IP addresses.
 */
class SpecialUserRepository implements SpecialUserRepoInterface
{
    /**
     * Creates a new SpecialUser record.
     *
     * We first create an instance of SpecialUser, then add the access_token,
     * for we must get help from setAccessTokenAttribute of Model SpecialUser.
     *
     * @param array $data The data required to create a SpecialUser
     * @return SpecialUser The created SpecialUser instance
     */
    public function create(array $data): SpecialUser
    {
        return SpecialUser::create($data);
    }

    /**
     * Finds a SpecialUser by its associated user ID.
     *
     * @param int $userId The ID of the user associated with the SpecialUser.
     * @return SpecialUser|null The SpecialUser instance if found, otherwise null.
     */
    public function findByUserId(int $userId): ?SpecialUser
    {
        return SpecialUser::where('user_id', $userId)->first();
    }

    /**
     * Updates the webhook endpoints for a given SpecialUser.
     *
     * @param int $id The ID of the SpecialUser.
     * @param array $webhookEndpoints The webhook endpoints to update.
     * @return SpecialUser|null The updated SpecialUser instance if successful, otherwise null.
     */
    public function updateWebhookEndpoints(int $id, array $webhookEndpoints): ?SpecialUser
    {
        $specialUser = SpecialUser::find($id);
        if ($specialUser) {
            $specialUser->webhook_endpoints = $webhookEndpoints;
            $specialUser->save();
            return $specialUser;
        }
        return null;
    }

    /**
     * Updates the valid IP addresses for a given SpecialUser.
     *
     * @param int $id The ID of the SpecialUser.
     * @param array $validIps The list of valid IP addresses to update
     * @return SpecialUser|null The updated SpecialUser instance if successful, otherwise null
     */
    public function updateValidIps(int $id, array $validIps): ?SpecialUser
    {
        $specialUser = SpecialUser::find($id);
        if ($specialUser) {
            $specialUser->valid_endpoints = json_encode($validIps);
            $specialUser->save();
            return $specialUser;
        }
        return null;
    }

    /**
     * Generates and assigns a unique API token to a SpecialUser.
     *
     * @param int $id The ID of the special user
     * @return string The generated API token
     */
    public function generateApiToken(int $id): string
    {
        do {
            $apiToken = Str::random(16);
        } while (SpecialUser::getUserByApiToken($apiToken));

        $specialUser = SpecialUser::find($id);
        if ($specialUser) {
            $specialUser->api_token = $apiToken;
            $specialUser->save();
        }

        return $apiToken;
    }

    /**
     * Sets or updates the access token for a given SpecialUser.
     *
     * @param int $id The ID of the SpecialUser.
     * @param string $accessToken The new access token to set.
     * @return SpecialUser|null The updated SpecialUser instance if successful, otherwise null.
     */
    public function updateAccessToken(int $id, string $accessToken): ?SpecialUser
    {
        $specialUser = SpecialUser::find($id);
        if ($specialUser) {
            $specialUser->access_token = $accessToken;
            $specialUser->save();
            return $specialUser;
        }
        return null;
    }
}
