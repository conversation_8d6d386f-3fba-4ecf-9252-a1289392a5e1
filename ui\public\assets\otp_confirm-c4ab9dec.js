import { E as Ve } from "./endpoints-454f23f6.js";
import "./index-00c7d20d.js";
import { c as $, aI as _e, x as ae, e as b, a9 as Ce, v as Ee, b as f, o as j, aJ as J, aK as je, d as k, j as ke, F as le, aj as Le, ad as N, k as Pe, af as Q, aM as S, ai as se, a2 as U, r as W, i as we, al as X, z as xe, n as y, aL as Y, aH as ye } from "./index-169996dc.js";
import { _ as Ae, s as H, l as Oe } from "./logo-a80e11f6.js";
import { b as Ie, u as re, a as Ue } from "./misc-mask-light-c60b2a6b.js";
import { u as Te } from "./useAbility-e6e49333.js";
import "./VField-150a934a.js";
import { V as Fe } from "./VForm-a73b6b87.js";
import "./VInput-c4d3942a.js"; /*! Capacitor: https://capacitorjs.com/ - MIT License */
import { a as D, V as ne } from "./VRow-6c1d54f3.js";
import { V as $e } from "./VTextField-a8984053.js";
const Se=s=>{const e=new Map;e.set("web",{name:"web"});const t=s.CapacitorPlatforms||{currentPlatform:{name:"web"},platforms:e},a=(n,l)=>{t.platforms.set(n,l)},i=n=>{t.platforms.has(n)&&(t.currentPlatform=t.platforms.get(n))};return t.addPlatform=a,t.setPlatform=i,t},De=s=>s.CapacitorPlatforms=Se(s),ce=De(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});ce.addPlatform;ce.setPlatform;var I;(function(s){s.Unimplemented="UNIMPLEMENTED",s.Unavailable="UNAVAILABLE"})(I||(I={}));class Z extends Error{constructor(e,t,a){super(e),this.message=e,this.code=t,this.data=a}}const Me=s=>{var e,t;return s!=null&&s.androidBridge?"android":!((t=(e=s==null?void 0:s.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||t===void 0)&&t.bridge?"ios":"web"},Re=s=>{var e,t,a,i,n;const l=s.CapacitorCustomPlatform||null,r=s.Capacitor||{},c=r.Plugins=r.Plugins||{},o=s.CapacitorPlatforms,d=()=>l!==null?l.name:Me(s),C=((e=o==null?void 0:o.currentPlatform)===null||e===void 0?void 0:e.getPlatform)||d,T=()=>C()!=="web",h=((t=o==null?void 0:o.currentPlatform)===null||t===void 0?void 0:t.isNativePlatform)||T,g=u=>{const m=q.get(u);return!!(m!=null&&m.platforms.has(C())||M(u))},_=((a=o==null?void 0:o.currentPlatform)===null||a===void 0?void 0:a.isPluginAvailable)||g,F=u=>{var m;return(m=r.PluginHeaders)===null||m===void 0?void 0:m.find(A=>A.name===u)},M=((i=o==null?void 0:o.currentPlatform)===null||i===void 0?void 0:i.getPluginHeader)||F,fe=u=>s.console.error(u),me=(u,m,A)=>Promise.reject(`${A} does not have an implementation of "${m}".`),q=new Map,ge=(u,m={})=>{const A=q.get(u);if(A)return console.warn(`Capacitor plugin "${u}" already registered. Cannot register plugins twice.`),A.proxy;const O=C(),V=M(u);let x;const he=async()=>(!x&&O in m?x=typeof m[O]=="function"?x=await m[O]():x=m[O]:l!==null&&!x&&"web"in m&&(x=typeof m.web=="function"?x=await m.web():x=m.web),x),ve=(p,v)=>{var P,L;if(V){const E=V==null?void 0:V.methods.find(w=>v===w.name);if(E)return E.rtype==="promise"?w=>r.nativePromise(u,v.toString(),w):(w,R)=>r.nativeCallback(u,v.toString(),w,R);if(p)return(P=p[v])===null||P===void 0?void 0:P.bind(p)}else{if(p)return(L=p[v])===null||L===void 0?void 0:L.bind(p);throw new Z(`"${u}" plugin is not implemented on ${O}`,I.Unimplemented)}},G=p=>{let v;const P=(...L)=>{const E=he().then(w=>{const R=ve(w,p);if(R){const B=R(...L);return v=B==null?void 0:B.remove,B}else throw new Z(`"${u}.${p}()" is not implemented on ${O}`,I.Unimplemented)});return p==="addListener"&&(E.remove=async()=>v()),E};return P.toString=()=>`${p.toString()}() { [capacitor code] }`,Object.defineProperty(P,"name",{value:p,writable:!1,configurable:!1}),P},ee=G("addListener"),te=G("removeListener"),be=(p,v)=>{const P=ee({eventName:p},v),L=async()=>{const w=await P;te({eventName:p,callbackId:w},v)},E=new Promise(w=>P.then(()=>w({remove:L})));return E.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await L()},E},z=new Proxy({},{get(p,v){switch(v){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return V?be:ee;case"removeListener":return te;default:return G(v)}}});return c[u]=z,q.set(u,{name:u,proxy:z,platforms:new Set([...Object.keys(m),...V?[O]:[]])}),z},pe=((n=o==null?void 0:o.currentPlatform)===null||n===void 0?void 0:n.registerPlugin)||ge;return r.convertFileSrc||(r.convertFileSrc=u=>u),r.getPlatform=C,r.handleError=fe,r.isNativePlatform=h,r.isPluginAvailable=_,r.pluginMethodNoop=me,r.registerPlugin=pe,r.Exception=Z,r.DEBUG=!!r.DEBUG,r.isLoggingEnabled=!!r.isLoggingEnabled,r.platform=r.getPlatform(),r.isNative=r.isNativePlatform(),r},Be=s=>s.Capacitor=Re(s),K=Be(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),de=K.registerPlugin;K.Plugins;class ue{constructor(e){this.listeners={},this.retainedEventArguments={},this.windowListeners={},e&&(console.warn(`Capacitor WebPlugin "${e.name}" config object was deprecated in v3 and will be removed in v4.`),this.config=e)}addListener(e,t){let a=!1;this.listeners[e]||(this.listeners[e]=[],a=!0),this.listeners[e].push(t);const n=this.windowListeners[e];n&&!n.registered&&this.addWindowListener(n),a&&this.sendRetainedArgumentsForEvent(e);const l=async()=>this.removeListener(e,t);return Promise.resolve({remove:l})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,a){const i=this.listeners[e];if(!i){if(a){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}return}i.forEach(n=>n(t))}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:a=>{this.notifyListeners(t,a)}}}unimplemented(e="not implemented"){return new K.Exception(e,I.Unimplemented)}unavailable(e="not available"){return new K.Exception(e,I.Unavailable)}async removeListener(e,t){const a=this.listeners[e];if(!a)return;const i=a.indexOf(t);this.listeners[e].splice(i,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(a=>{this.notifyListeners(e,a)}))}}const oe=s=>encodeURIComponent(s).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),ie=s=>s.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class He extends ue{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(a=>{if(a.length<=0)return;let[i,n]=a.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");i=ie(i).trim(),n=ie(n).trim(),t[i]=n}),t}async setCookie(e){try{const t=oe(e.key),a=oe(e.value),i=`; expires=${(e.expires||"").replace("expires=","")}`,n=(e.path||"/").replace("path=",""),l=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${a||""}${i}; path=${n}; ${l};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}de("CapacitorCookies",{web:()=>new He});const We=async s=>new Promise((e,t)=>{const a=new FileReader;a.onload=()=>{const i=a.result;e(i.indexOf(",")>=0?i.split(",")[1]:i)},a.onerror=i=>t(i),a.readAsDataURL(s)}),Ke=(s={})=>{const e=Object.keys(s);return Object.keys(s).map(i=>i.toLocaleLowerCase()).reduce((i,n,l)=>(i[n]=s[e[l]],i),{})},qe=(s,e=!0)=>s?Object.entries(s).reduce((a,i)=>{const[n,l]=i;let r,c;return Array.isArray(l)?(c="",l.forEach(o=>{r=e?encodeURIComponent(o):o,c+=`${n}=${r}&`}),c.slice(0,-1)):(r=e?encodeURIComponent(l):l,c=`${n}=${r}`),`${a}&${c}`},"").substr(1):null,Ge=(s,e={})=>{const t=Object.assign({method:s.method||"GET",headers:s.headers},e),i=Ke(s.headers)["content-type"]||"";if(typeof s.data=="string")t.body=s.data;else if(i.includes("application/x-www-form-urlencoded")){const n=new URLSearchParams;for(const[l,r]of Object.entries(s.data||{}))n.set(l,r);t.body=n.toString()}else if(i.includes("multipart/form-data")||s.data instanceof FormData){const n=new FormData;if(s.data instanceof FormData)s.data.forEach((r,c)=>{n.append(c,r)});else for(const r of Object.keys(s.data))n.append(r,s.data[r]);t.body=n;const l=new Headers(t.headers);l.delete("content-type"),t.headers=l}else(i.includes("application/json")||typeof s.data=="object")&&(t.body=JSON.stringify(s.data));return t};class ze extends ue{async request(e){const t=Ge(e,e.webFetchExtra),a=qe(e.params,e.shouldEncodeUrlParams),i=a?`${e.url}?${a}`:e.url,n=await fetch(i,t),l=n.headers.get("content-type")||"";let{responseType:r="text"}=n.ok?e:{};l.includes("application/json")&&(r="json");let c,o;switch(r){case"arraybuffer":case"blob":o=await n.blob(),c=await We(o);break;case"json":c=await n.json();break;case"document":case"text":default:c=await n.text()}const d={};return n.headers.forEach((C,T)=>{d[T]=C}),{data:c,headers:d,status:n.status,url:n.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}de("CapacitorHttp",{web:()=>new ze});const Je={class:"d-flex flex-row-reverse"},Qe={__name:"MyOtpField",props:{loading:{type:Boolean}},emits:["finished","changed"],setup(s,{emit:e}){const t=e,a=W(Array(6).fill(""));ye({name:"vueuse-demo-channel"});const i=r=>{let c=r.clipboardData.getData("text");if(!(c.length>6||!J(c))){for(let o=0;o<6;o++)a.value[o]="";for(let o=0;o<c.length;o++)a.value[o]=c.charAt(o);t("changed",a.value.join("")),a.value.join("").length===6&&l()}},n=(r,c)=>{let o=r.key;if(o.toLowerCase()=="unidentified"&&(o=a.value[c-1]),o=="ArrowLeft"){const d=r.target.form.elements[c-2];d&&d.focus();return}if(o=="ArrowRight"){const d=r.target.form.elements[c];d&&d.focus();return}if(o=="Backspace"){const d=r.target.form.elements[c-2];a.value[c-1]="",d&&d.focus();return}if(!J(o)){J(a.value[c-1])||(a.value[c-1]="");return}if(a.value[c-1]=o,o.length===1&&c<6){const d=r.target.form.elements[c];d&&d.focus()}t("changed",a.value.join("")),a.value.join("").length===6&&l()},l=()=>{t("finished",a.value.join(""))};return(r,c)=>(j(),$("div",Je,[(j(),$(le,null,we(6,o=>f(_e,{key:o,class:"field-wrapper","aspect-ratio":"1"},{default:b(()=>[f($e,{modelValue:y(a)[o-1],"onUpdate:modelValue":d=>y(a)[o-1]=d,"hide-spin-buttons":"",type:"tel",density:"comfortable",maxlength:"1",disabled:s.loading,class:"otp-input",inputmode:"numeric",pattern:"[0-9]*",onPaste:N(i,["prevent"]),onKeyup:N(d=>n(d,o),["prevent"])},null,8,["modelValue","onUpdate:modelValue","disabled","onKeyup"])]),_:2},1024)),64))]))}};const Xe={class:"position-relative bg-background rounded-lg w-100 ma-8 me-0"},Ye={class:"d-flex align-center justify-center w-100 h-100"},Ze={class:"d-flex align-end mb-4"},Ne=["src"],et=k("div",{class:"d-flex align-start flex-column ms-2 pb-6"},[k("h6",{class:"text-primary text-h6 font-weight-bold"}," DMPlus "),k("span",{class:"text-body-2"},"اولین فروشگاه ساز هوشمند اینستاگرام در ایران ")],-1),tt={key:0,class:"text-h5 text-center font-weight-bold mb-1"},st={key:1,class:"text-h4 font-weight-bold mb-1"},at={class:"mb-2"},rt={key:0,class:"ms-6 mt-2",style:{color:"rgb(var(--v-theme-error))"}},nt=k("span",{class:"me-1"},"کدی دریافت نکردید؟",-1),ot=k("a",{href:"#"},"ارسال دوباره",-1),it=[nt,ot],lt={key:1,class:"d-flex justify-center align-center flex-wrap"},wt={__name:"otp_confirm",setup(s){const e=re(H,H,H,H,!0),t=re(Ie,Ue),a=Pe();ke();const i=Te(),n=W(""),l=W(!1),r=U("mobile").value.toString(),c=W(""),o=async h=>{n.value=h},d=async()=>{try{const h=U("pageId"),g=h.value?h.value:"";if(h.value)resd.data.profile&&(hasInstaPic.value=!0,userData.value.insta_profile=resd.data.profile),userData.value.name=resd.data.title;else{const _=await X.get(`/pages/${g}`);U("pageId").value=_.data.data[0].id,_.data.data[0].profile&&(hasInstaPic.value=!0,userData.value.insta_profile=_.data.data[0].profile),userData.value.name=_.data.data[0].title}}catch(h){console.log("connectToFaceBook => ",h)}},C=async h=>{try{l.value=!0;const{data:g}=await X.post(Ve.verifyOtp,{phone:Y(r),code:Y(n.value)}),{token:_,user:F,verified:M}=g;M?(U("userAbilityRules").value=[{action:"manage",subject:"all"}],U("userData").value=F,U("accessToken").value=_,await d(),i.update([{action:"manage",subject:"all"}]),a.push("/")):S({text:g==null?void 0:g.message,icon:"error"})}catch{S({text:"کد اشتباه است",icon:"error"})}finally{l.value=!1}},T=async()=>{var h;try{l.value=!0;const{data:g}=await X.post("/reSendOtp",{mobile:Y(r)});g.failed?S({text:g.message,icon:"error"}):S({text:g.message,icon:"success"}),l.value=!1}catch(g){l.value=!1,S({text:(h=g==null?void 0:g.data)==null?void 0:h.message,icon:"error"})}finally{l.value=!1}};return(h,g)=>{const _=Ae,F=Qe;return j(),$(le,null,[f(_),f(ne,{class:"auth-wrapper bg-surface","no-gutters":""},{default:b(()=>[f(D,{md:"8",class:"d-none d-md-flex"},{default:b(()=>[k("div",Xe,[k("div",Ye,[f(se,{"max-width":"600",src:y(e),class:"auth-illustration mt-16 mb-2"},null,8,["src"])]),f(se,{class:"auth-footer-mask",src:y(t)},null,8,["src"])])]),_:1}),f(D,{cols:"12",md:"4",class:"auth-card-v2 d-flex align-center justify-center"},{default:b(()=>[f(Ce,{flat:"","max-width":500,class:"mt-12 mt-sm-0 pa-4"},{default:b(()=>[f(Q,null,{default:b(()=>[f(Q,null,{default:b(()=>[k("div",Ze,[k("img",{src:y(Oe),height:"100"},null,8,Ne),et]),h.$vuetify.display.width<435?(j(),$("h5",tt," 💬 ورود با کد پیامکی ")):(j(),$("h4",st," 💬 ورود با کد پیامکی ")),k("p",at," کد پیامکی که به شماره "+ae(y(r))+" ارسال کردیم را اینجا وارد کن ",1)]),_:1}),f(Q,null,{default:b(()=>[f(Fe,{onSubmit:N(C,["prevent"])},{default:b(()=>[f(ne,null,{default:b(()=>[f(D,{cols:"12"},{default:b(()=>[f(F,{loading:y(l),onChanged:o,onFinished:C},null,8,["loading"])]),_:1}),y(c)?(j(),$("span",rt,ae(y(c)),1)):xe("",!0),f(D,{cols:"12"},{default:b(()=>[f(Le,{block:"",loading:y(l),disabled:y(l),type:"submit"},{default:b(()=>[Ee(" ورود ")]),_:1},8,["loading","disabled"])]),_:1}),f(D,{cols:"12"},{default:b(()=>[y(l)?(j(),$("div",lt,[f(je,{indeterminate:"",color:"secondary"})])):(j(),$("div",{key:0,class:"d-flex justify-center align-center flex-wrap",onClick:T},it))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})],64)}}};export { wt as default };

