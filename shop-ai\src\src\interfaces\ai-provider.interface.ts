/**
 * Interface AiProvider
 *
 * This interface defines the contract for AI-based natural language generation.
 * It provides a method to send a prompt and user comment to an AI engine
 * (e.g., OpenAI) and return a suitable response for publishing.
 */
export interface IAiProvider {
  /**
   * Generates a reply message based on the system prompt and user comment.
   *
   * This method is responsible for sending the system prompt and user comment
   * to the AI engine, then returning the AI-generated message to the service layer.
   *
   * It is typically used for:
   * - **Auto-reply generation** under Instagram posts
   * - **Dynamic content suggestions** in e-commerce, forums, or blogs
   *
   * The AI model (e.g., GPT-4o) must:
   * - Understand and reflect the personality/context injected via prompt
   * - Return a concise and natural-sounding message
   * - Maintain a language consistent with the user comment
   *
   * @param prompt The full prompt text that defines system behavior and context (admin tone, friendliness, etc.)
   * @param userComment The actual user comment for which we are generating a response
   * @param apiToken The Provider token in order to call its APIs
   * @returns A `Promise` resolving to a clean, natural language response string generated by the AI
   *
   * @example **Generating a reply for a comment**
   * ```ts
   * const prompt = `
   *   You are a joyful Persian bookstore owner. A user commented under your post about new arrivals.
   *   Always answer in the same language. Keep it warm and helpful.
   * `;
   *
   * const comment = "سلام، این کتاب ترجمه‌ش خوبه؟";
   *
   * const reply = await aiProvider.generateReply(prompt, comment);
   * console.log(reply); // e.g., "بله دوست عزیز، ترجمه‌اش بسیار روان و حرفه‌ایه 🌟"
   * ```
   */
  generateReply(
    prompt: string,
    userComment: string,
    apiToken: string,
  ): Promise<string>;
}
