<?php

namespace App\Services\PersianParserService;

/**
 * PersianParserService
 *
 * This service is designed to handle Persian number and text parsing.
 */
class PersianParserService
{
    /**
     * Dictionary of Persian words and their corresponding numeric values.
     *
     * @var array
     */
    protected array $dictionary = [
        'صفر' => 0,
        'یک' => 1,
        'دو' => 2,
        'سه' => 3,
        'چهار' => 4,
        'پنج' => 5,
        'شش' => 6,
        'هفت' => 7,
        'هشت' => 8,
        'نه' => 9,
        'ده' => 10,
        'یازده' => 11,
        'دوازده' => 12,
        'سیزده' => 13,
        'چهارده' => 14,
        'پانزده' => 15,
        'شانزده' => 16,
        'هفده' => 17,
        'هجده' => 18,
        'نوزده' => 19,
        'بیست' => 20,
        'سی' => 30,
        'چهل' => 40,
        'پنجاه' => 50,
        'شصت' => 60,
        'هفت<PERSON>' => 70,
        'هشتاد' => 80,
        'نود' => 90,
        'صد' => 100,
        'دویست' => 200,
        'سیصد' => 300,
        'چهارصد' => 400,
        'پانصد' => 500,
        'ششصد' => 600,
        'هفتصد' => 700,
        'هشتصد' => 800,
        'نهصد' => 900,
        'هزار' => 1000,
        'میلیون' => 1000000,
        'میلیارد' => 1000000000,
    ];

    /**
     * Parses the given string and returns its integer equivalent.
     *
     * It can:
     *  - Detect and convert Persian numbers to English integers.
     *  - Convert Persian words representing numbers (e.g., "پانصد هزار") into integer values.
     *  - Recognize and return standard English numbers as integers.
     *
     * @param string $text Input string, which may contain Persian words, Persian numbers, or English numbers.
     * @return int|null The parsed integer if conversion is successful; null if the input is invalid.
     */
    public function parseInput(string $text): ?int
    {
        if ($this->isPersianNumber($text)) {
            return $this->convertPersianToEnglishNumbers($text);
        }elseif ($this->isPersianWordsAndNumeric($text)) {
            $text = $this->hotfix($text);
            return $this->wordsToNumber($text);
        } elseif ($this->isPersianWordsAndNumbers($text)) {
            $text = $this->hotfix($text);
            return $this->wordsToNumber($text);
        } elseif ($this->isPersianWords($text)) {
            return $this->wordsToNumber($text);
        } elseif (is_numeric($text)) {
            return (int) $text;
        } else {
            return null;
        }
    }

    protected function isPersianWords($text): bool
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text);
    }

    protected function isPersianNumber($text): bool
    {
        return preg_match('/^[۰-۹]+$/u', $text);
    }

    protected function convertPersianToEnglishNumbers(string $text): string
    {
        $persian_numbers = ['0', '۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        $english_numbers = ['0', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        return str_replace($persian_numbers, $english_numbers, $text);
    }

    protected function wordsToNumber($text): int
    {
        $parts = explode(' ', $text);
        $number = 0;
        $temp = 0;

        foreach ($parts as $part) {
            if (isset($this->dictionary[$part])) {
                $value = $this->dictionary[$part];
                if ($value >= 1000) {
                    $temp = ($temp ?: 1) * $value;
                    $number += $temp;
                    $temp = 0;
                } elseif ($value >= 100) {
                    $temp = ($temp ?: 1) * $value;
                } else {
                    $temp += $value;
                }
            }
        }

        return $number + $temp;
    }

    protected function isPersianWordsAndNumbers($text): bool
    {
        // Check if the text contains Persian words and Persian numbers
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text) && preg_match('/[۰-۹]+/u', $text);
    }

    protected function isPersianWordsAndNumeric($text): bool
    {
        // Check if the text contains Persian words (range: 0x0600-0x06FF) and numeric digits (Arabic/English numbers)
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text) && preg_match('/\d+/', $text);
    }



    public function hotfix(string $text): string
    {
        // First, match and extract the numeric part from the string
        preg_match('/\d+|[۰-۹]+/', $text, $matches);

        // If a number is found
        if (isset($matches[0])) {
            // Convert the numeric string to an integer
            $numberStr = $matches[0];

            // Check if the number is in Persian numerals (۰-۹) and convert to standard Arabic numerals
            $persianToArabic = [
                '۰' => '0', '۱' => '1', '۲' => '2', '۳' => '3', '۴' => '4',
                '۵' => '5', '۶' => '6', '۷' => '7', '۸' => '8', '۹' => '9'
            ];

            // Replace Persian numbers with Arabic numbers
            $numberStr = strtr($numberStr, $persianToArabic);

            // Convert the numeric value to integer
            $number = (int)$numberStr;

            // Now convert that number to Persian words
            $inverseDictionary = array_flip($this->dictionary); // Flip the dictionary to make numbers the keys
            $words = [];

            // Handle the conversion based on the value of the number
            if ($number < 20) {
                // Numbers less than 20 are directly mapped
                $words[] = $inverseDictionary[$number];
            } elseif ($number < 100) {
                // Handle tens (20, 30, 40, ...)
                $tens = intdiv($number, 10) * 10;
                $ones = $number % 10;
                $words[] = $inverseDictionary[$tens];
                if ($ones > 0) {
                    $words[] = $inverseDictionary[$ones];
                }
            } elseif ($number < 1000) {
                // Handle hundreds (100, 200, 300, ...)
                $hundreds = intdiv($number, 100);
                $remainder = $number % 100;
                $words[] = $inverseDictionary[$hundreds * 100];
                if ($remainder > 0) {
                    $words[] = $this->numberToPersianWord($remainder);  // Recursively handle the remainder
                }
            } else {
                // Handle large numbers (e.g., هزار, میلیون)
                $powers = [1000, 1000000, 1000000000];  // Powers for هزار, میلیون, میلیارد, ...
                foreach ($powers as $power) {
                    if ($number >= $power) {
                        $quotient = intdiv($number, $power);
                        $words[] = $this->numberToPersianWord($quotient);  // Convert the quotient
                        $words[] = $inverseDictionary[$power]; // Add the corresponding Persian word (e.g., هزار)
                        $number = $number % $power; // Update the number to the remainder
                    }
                }
                if ($number > 0) {
                    $words[] = $this->numberToPersianWord($number);
                }
            }

            // Join the words together
            $result = implode(' ', $words);

            // Append any scale (like "میلیون", "هزار") found in the original text
            // Extract scale (like "میلیون", "هزار") from the text
            preg_match('/\s*(\D+)/u', $text, $scaleMatch);
            if (isset($scaleMatch[1])) {
                // Add scale to the result (trim to avoid extra spaces)
                $result .= ' ' . trim($scaleMatch[1]);
            }

            return $result;
        }

        return $text; // If no number found, return the original text
    }
}
