<?php

namespace App\Repositories\Interfaces;

use App\Models\Webservice\SpecialUser;

/**
 * Interface SpecialUserRepositoryInterface
 *
 * This interface defines the methods for interacting with the SpecialUser model.
 * It provides functions to create, retrieve, and update special users, including
 * webhook endpoints and valid IP addresses.
 */
interface SpecialUserRepositoryInterface
{
    /**
     * Create a new SpecialUser.
     *
     * This method creates a new special user entry and stores it in the database.
     *
     * @param array $data The data required to create a SpecialUser.
     * @return SpecialUser The created SpecialUser instance.
     *
     * @example
     * ```php
     * $specialUser = $specialUserRepository->create([
     *     'user_id' => 1,
     *     'access_token' => 'TmtZambEWgsSAJRX5nPR8pXoBe4w5f8DbnAD1yRAPwpICI',
     *     'instagram_id' => '123456789',
     * ]);
     * echo $specialUser->id;
     * ```
     */
    public function create(array $data): SpecialUser;

    /**
     * Retrieve a SpecialUser by user ID.
     *
     * This method fetches a special user associated with the given user ID.
     *
     * @param int $userId The ID of the user.
     * @return SpecialUser|null The SpecialUser instance if found, otherwise null.
     *
     * @example
     * ```php
     * $specialUser = $specialUserRepository->findByUserId(1);
     * if ($specialUser) {
     *     echo $specialUser->instagram_id;
     * }
     * ```
     */
    public function findByUserId(int $userId): ?SpecialUser;

    /**
     * Update webhook endpoints for a SpecialUser.
     *
     * This method updates the webhook endpoints associated with a given SpecialUser.
     *
     * @param int $id The ID of the SpecialUser.
     * @param array $webhookEndpoints The webhook endpoints to update.
     * @return SpecialUser|null The updated SpecialUser instance if successful, otherwise null.
     *
     * @example
     * ```php
     * $updatedUser = $specialUserRepository->updateWebhookEndpoints(1, ['https://example.com/webhook']);
     * echo $updatedUser->webhook_endpoints;
     * ```
     */
    public function updateWebhookEndpoints(int $id, array $webhookEndpoints): ?SpecialUser;

    /**
     * Update valid IPs for a SpecialUser.
     *
     * This method updates the valid IP addresses associated with a given SpecialUser.
     *
     * @param int $id The ID of the SpecialUser.
     * @param array $validIps The list of valid IP addresses to update.
     * @return SpecialUser|null The updated SpecialUser instance if successful, otherwise null.
     *
     * @example
     * ```php
     * $updatedUser = $specialUserRepository->updateValidIps(1, ['***********', '********']);
     * echo json_decode($updatedUser->valid_endpoints, true);
     * ```
     */
    public function updateValidIps(int $id, array $validIps): ?SpecialUser;

    /**
     * Generates and assigns a unique API token to a SpecialUser.
     *
     * This method ensures the generated token is unique by checking against
     * existing tokens before assigning it to the user.
     *
     * @param int $id The ID of the special user
     * @return string The generated API token.
     *
     * @example
     * ```php
     * $apiToken = $specialUserRepository->generateApiToken(1);
     * echo $apiToken;
     * ```
     */
    public function generateApiToken(int $id): string;

    /**
     * Update the access token for a SpecialUser.
     *
     * This method updates the `access_token` field for a given SpecialUser.
     * Note that the access token provided as input (a plain token string, e.g.
     *
     * ```
     * IGQWRNRmY4aEVTZAnFZARTVxU1JhRTZAqZAUlSekxHckVVUkhnLXRTOUtsMmNoT1oxeGFBQnJWc2xuWlM5MzJRaTlmVVlhZAEFZAZAXF4amJ5d2FfT2dQQVJSRUZALaXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
     * ```
     *
     * ) will typically be processed (e.g. encrypted or hashed) by the model before saving.
     * So in the database, the stored value will look like an encrypted string or JSON array of strings, for example:
     *
     * ```
     * [
     * "Vm9FhTVwVacLkpvVeR8xo\/gxfEnwAauxf98wBAYzOt3DNEBRgXFa4dme\/jdAW58oPEG06d6zTO4VUdz1DtZBKP2nagKK7h3VLDtw7DNZDGXBfG6wEeCcCii\/2wDnnrvNrLAA3hzXmMdPnvwlCOhl5QUA18qcUm0vuzthZbCnwIPX003t0dwiFJD612DT+S0uprv+Sa\/elFH0jfN4v2Ydig==",
     * "j4zy5R85uZt60pdTUqHqBJT8QUP8687RNl5GFaP9FUI+No4Qk8C7nlshLfSmYPy3PhrkjwzBF6bsekT2PqL5c7Z0kkEYZuTykiQsynLyTZrP1b+IJuaNIdGe66JOaQ1TNXLsJbnelhLUcBNxO31xGY9yz\/sAPJVpye1+SsGjRF+XtzRGLWFfW1rZErxCjisxlmd1fkASS2b8lsCRYEDLLMO4nOaN\/s3OBulGzwvgB4qvAY0AdmLsIuYAFVv1ycmEXEMvqjWSkqmCWMPdUPGh\/gqgrWgDrvXI2+V+yrw9l9kGWtpWxIqEZvlesWIKXQX6dAqfywPU0wEu15O5ogTTpw==",
     * "PzjuZyV1V6TfVnmJDuMsVA=="
     * ]
     * ```
     *
     * @param int $id The ID of the SpecialUser.
     * @param string $accessToken The new access token string to set.
     * @return SpecialUser|null The updated SpecialUser instance if successful, otherwise null.
     *
     * @example
     * ```php
     * $plainToken = 'IGQWRNRmY4aEVTZAnFZARTVxU1JhRTZAqZAUlSekxHckVVUkhnLXRTOUtsMmNoT1oxeGFBQnJWc2xuWlM5MzJRaTlmVVlhZAEFZAZAXF4amJ5d2FfT2dQQVJSRUZALaXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX';
     * $updatedUser = $specialUserRepository->updateAccessToken(1, $plainToken);
     * if ($updatedUser) {
     *     echo "Access token updated successfully.";
     * } else {
     *     echo "User not found.";
     * }
     * ```
     */
    public function updateAccessToken(int $id, string $accessToken): ?SpecialUser;
}
