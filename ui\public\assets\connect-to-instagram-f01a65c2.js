import { _ as Y } from "./DialogCloseBtn-b32209d9.js";
import { H as at, af as b, F as ct, _ as D, f as d, e, aM as E, ak as et, R as F, a4 as h, U as I, aK as it, v as l, a as lt, aj as m, n, a8 as N, am as nt, o, aO as ot, r as p, ai as R, b as s, x as S, aN as st, d as t, al as T, a6 as tt, c as v, y as V, a9 as w, z as y, s as Z } from "./index-169996dc.js";
import { V as A } from "./VAlert-fc722507.js";
import { a as B, V as M } from "./VRow-6c1d54f3.js";
const P=u=>(F("data-v-a849fbd8"),u=u(),I(),u),ut=P(()=>t("p",null," کاربر گرامی؛ ضمن سپاس از انتخاب سرویس دایرکت هوشمند اینستاگرام لازم است پیش از خرید توافقنامه و قوانین ذیل را مطالعه فرمایید. DMPlus سرویس خود را تحت شرایط و مقررات این توافقنامه در اختیار شما می‌گذارد و شما به‌عنوان کاربران این سرویس ملزم به قبول و رعایت مفاد مذکور در این توافقنامه که ممکن است در آینده تغییر یابد، هستید. ",-1)),rt=P(()=>t("ul",null,[t("li",null," سیستم DMPlus یک سیستم مطلقا دیجیتال و خودکار است که شما به محض اتصال میتوانید از آن استفاده کنید و تمدید کنید. "),t("li",null," با توجه به اینکه این شرکت پارتنر تجاری فیسبوک می‌باشد در صورت بروز هر مشکل فنی از سمت فیسبوک ، سیستم هوشمند DMPlus میتواند با اختلال در سرویس مواجه شود و این اختلال میتواند تا چند روز و یا چند هفته به طول انجامد. "),t("li",null," در صورتی که بروز مشکل از سمت فیس بوک که به قطع خدمات DMPlus بیش از ۳ روز بیانجامد مدت زمان قطعی کامل محاسبه شده و به اکانت شما اضافه خواهد شد. "),t("li",null," در صورت بروز هر مشکل تیم پشتیبانی در ساعات اداری از طریق (تلگرام) در خدمت شما میباشد. "),t("li",null," مسؤلیت ارسال هر گونه محتوا به هر صورت متنی، صدا، فیلم و عکس و یا لینک به عهده خود کاربر بوده و شرکت DMPlus (چت بوستر) هیچ گونه مسولیتی در قبال این محتوا نخواهد داشت. "),t("li",null," از رفتارها و کلمات ممنوعه اینستاگرام استفاده نکنید. از اینجا می توانید لیست کلمات ممنوعه را مشاهده کنید. "),t("li",null,[l(" از DMPlus به طور صحیح و درست استفاده کنید. به طور مثال: در صورتی که درخواست بیش از اندازه یک کلمه، شماره یا عبارت را از کاربر داشته باشید احتمال اختلال در دایرکت شما وجود خواهد داشت. "),t("br"),l(" مثلا برای کاربران چنین دستوری تعریف نکنید: عدد ۵۰ را برای دستور وارد کنید و قرعه کشی بسازید و هر کس بیش تر عدد ۵۰ را وارد کرده بود جایزه بگیرد که چنین موردی سبب اختلال در دایرکت شما می شود. ")]),t("li",null," در صورتی که پیج شما بالای 25کا فالوور هست و پکیج های اقتصادی زیر 25 کا را خریداری کنید، مبلغ پرداختی شما استرداد نخواهد شد "),t("li",null," مجموعه DMPlus به هیچ وجه عودت وجه نخواهد داشت، ولی در صورتی که پس از تلاش های بسیار برای اتصال، پیج شما به DMPlus متصل نشد، وجه شما عودت داده می شود. ")],-1)),dt=P(()=>t("span",null," باشه ",-1)),_t={__name:"CigPrivacyPolicy",setup(u){const f=p(!1);return(g,a)=>{const _=Y;return o(),d(et,{modelValue:n(f),"onUpdate:modelValue":a[1]||(a[1]=r=>tt(f)?f.value=r:null),width:"500"},{activator:e(({props:r})=>[t("span",Z(r,{class:"terms"}),"شرایط و قوانین",16)]),default:e(()=>[s(_,{onClick:a[0]||(a[0]=r=>f.value=!1)}),s(w,{title:"شرایط استفاده از DMPlus"},{default:e(()=>[s(b,null,{default:e(()=>[ut,rt]),_:1}),s(b,{class:"d-flex justify-end"},{default:e(()=>[s(m,{onClick:g.send},{default:e(()=>[s(h,{icon:"tabler-check"}),dt]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["modelValue"])}}},pt=D(_t,[["__scopeId","data-v-a849fbd8"]]);const q=u=>(F("data-v-42fb5fcf"),u=u(),I(),u),ft=q(()=>t("span",null," پشتیبانی خرید ",-1)),mt=q(()=>t("span",null," فعالسازی و آموزش ",-1)),gt={key:0},ht={__name:"SupportFLoatingButton",setup(u){const f=p(null),g=p(!1);return st(()=>{const a=f.value.$el.offsetWidth,_=document.querySelector(".support-floating-button__active"),r=document.querySelector(".support-floating-button__buy");_&&(_.style.width=`${a}px`),_&&(r.style.width=`${a}px`)}),(a,_)=>(o(),v("div",null,[s(m,{to:{path:"/support/sell"},class:V(["support-floating-button__buy",[{"not-active":!n(g)}]])},{default:e(()=>[ft]),_:1},8,["class"]),s(m,{class:V(["support-floating-button__active",[{"not-active":!n(g)}]]),to:{path:"/support/tech"}},{default:e(()=>[mt]),_:1},8,["class"]),s(m,{ref_key:"mButton",ref:f,class:"support-floating-button",onClick:_[0]||(_[0]=r=>g.value=!n(g))},{default:e(()=>[s(h,{icon:"tabler-headphones",start:a.$vuetify.display.smAndUp},null,8,["start"]),a.$vuetify.display.smAndUp?(o(),v("span",gt," ارتباط با پشتیبانی ")):y("",!0)]),_:1},512)]))}},yt=D(ht,[["__scopeId","data-v-42fb5fcf"]]),O="/assets/man-ced6a036.jpg";const c=u=>(F("data-v-6aeb13ad"),u=u(),I(),u),bt=c(()=>t("span",null," برای اتصال روی ",-1)),vt=c(()=>t("span",null," بزنید",-1)),kt=c(()=>t("h6",{class:"text-h6"}," نکات مهم ",-1)),wt=c(()=>t("ul",null,[t("li",null,[l(" پیج اینستاگرام شما حتما باید "),t("strong",null,"business account"),l(" یا "),t("strong",null,"creator account"),l(" باشد ، پیج های personal امکان اتصال به DMPlus را نخواهند داشت ")]),t("li",null,[t("strong",null," حتما پیج اینستاگرام شما میبایست به پیج فیسبوک متصل باشد "),l("، در صورتی که نیاز به اموزش بیش تر در این بخش دارید میتوانید اموزش اتصال پیج اینستاگرام به فیسبوک را مشاهده کنید ")]),t("li",null,[l(" .پس از اتصال پیج اینستا به پیج فیسبوک میبایست گزینه "),t("strong",null,"allow access message"),l(" در اینستاگرام شما روشن باشد ،‌برای روشن کردن این گزینه از طریق اینستاگرام میتوانید اموزش روشن بودن قابلیت "),t("strong",null,"allow access message"),l(" را از بخش .حتما پیج اینستاگرام شما میبایست به پیج فیسبوک متصل باشد ، در صورتی که نیاز به اموزش بیش تر در این بخش دارید میتوانید آموزش ها مشاهده کنید. ")]),t("li",null," برای اتصال DMPlus به اینستاگرام خود ، روی دکمه ی DMPlus کلیک کنید ، در فیسبوک لاگین کرده و سپس پیج اینستاگرام خود و سپس پیج فیسبوک متصل شده را انتخاب کنید و پروسه را ادامه داده تا اتصال پیج با موفقیت انجام شود. ")],-1)),xt={key:0,class:"text-center"},Ct={key:1},Bt=c(()=>t("span",null,"اکانت اینستاگرام متصل : ",-1)),Vt=c(()=>t("span",null," ادامه دادن با فیسبوک ",-1)),$t=c(()=>t("span",null," اتصال مجدد ",-1)),Tt=c(()=>t("span",null," قطع اتصال ",-1)),St={class:"mt-4"},Dt=c(()=>t("span",null," با ثبت نام کردن در DMPlus شما تمام ",-1)),Ft=c(()=>t("span",null," را میپذیرید",-1)),It={key:0,class:"text-center"},Pt={key:1},jt=c(()=>t("span",null,"اکانت اینستاگرام متصل : ",-1)),Lt=c(()=>t("span",null," ادامه دادن با فیسبوک ",-1)),Ut=c(()=>t("span",null," اتصال مجدد ",-1)),Wt=c(()=>t("span",null," قطع اتصال ",-1)),zt=c(()=>t("p",{class:"mt-4"},[t("span",null," با ثبت نام کردن در DMPlus شما تمام "),t("span",{class:"terms"},"شرایط و قوانین"),t("span",null," را میپذیرید")],-1)),Nt=c(()=>t("h6",{class:"text-h6"}," نکات مهم ",-1)),Et=c(()=>t("ul",null,[t("li",null,[l(" پیج اینستاگرام شما حتما باید "),t("strong",null,"business account"),l(" یا "),t("strong",null,"creator account"),l(" باشد ، پیج های personal امکان اتصال به DMPlus را نخواهند داشت ")]),t("li",null,[t("strong",null," حتما پیج اینستاگرام شما میبایست به پیج فیسبوک متصل باشد "),l("، در صورتی که نیاز به اموزش بیش تر در این بخش دارید میتوانید اموزش اتصال پیج اینستاگرام به فیسبوک را مشاهده کنید ")]),t("li",null,[l(" .پس از اتصال پیج اینستا به پیج فیسبوک میبایست گزینه "),t("strong",null,"allow access message"),l(" در اینستاگرام شما روشن باشد ،‌برای روشن کردن این گزینه از طریق اینستاگرام میتوانید اموزش روشن بودن قابلیت "),t("strong",null,"allow access message"),l(" را از بخش .حتما پیج اینستاگرام شما میبایست به پیج فیسبوک متصل باشد ، در صورتی که نیاز به اموزش بیش تر در این بخش دارید میتوانید آموزش ها مشاهده کنید. ")]),t("li",null," برای اتصال DMPlus به اینستاگرام خود ، روی دکمه ی DMPlus کلیک کنید ، در فیسبوک لاگین کرده و سپس پیج اینستاگرام خود و سپس پیج فیسبوک متصل شده را انتخاب کنید و پروسه را ادامه داده تا اتصال پیج با موفقیت انجام شود. ")],-1)),Rt={__name:"connect-to-instagram",setup(u){const f=p(null),g=p(null),a=p(!1),_=p(!0),r=p(!1),x=p(!1),$=p(""),j=p(""),L=p(window.innerWidth),U=nt(),G=N(()=>U.freeComments),H=N(()=>U.credit),W=()=>{L.value=window.innerWidth};at(async()=>{H<=0&&G>=500&&(await E({title:"برای استفاده از DMPlus میتوانید با خریدن هرکدام از پکیج های زیر از خدمات دایرکت هوشمند اینستاگرام استفاده نمایید",confirmButtonText:"باشه"}),router.push("/pricing")),window.addEventListener("resize",W),K()}),ot(()=>{window.removeEventListener("resize",W)});const K=async()=>{_.value=!0;try{const i=await T("/faceBookStatus",{method:"GET"});i.message.status=="ok"?(a.value=i.message.status=="ok",$.value=i.data.insta_username):j.value=i.message}catch(i){console.log("connectToFaceBook => ",i)}_.value=!1},C=async()=>{if(!x.value){x.value=!0;try{const i=await T("/connectToFaceBook",{method:"POST"});if(i.success){const k=i.data;window.open(k,"_self")}}catch(i){x.value=!1,console.log("connectToFaceBook => ",i)}}},z=async()=>{if((await E({title:"آیا از قطع اتصال اینستاگرام مطمئن هستید؟",showDenyButton:!0,showCancelButton:!1,confirmButtonText:"بله قطع کن",denyButtonText:"خیر"})).isConfirmed){r.value=!0;try{const k=await T("/disconnectFromFaceBook",{method:"POST"});a.value=!k.success}catch(k){console.log("disconnectFromInsta => ",k)}r.value=!1}};return(i,k)=>{const J=yt,Q=pt,X=lt("Tepmlate");return o(),v(ct,null,[s(J),n(_)?(o(),d(w,{key:1},{default:e(()=>[s(b,{class:"d-flex align-center justify-center"},{default:e(()=>[s(it,{indeterminate:""})]),_:1})]),_:1})):(o(),d(X,{key:0},{default:e(()=>[n(a)?(o(),d(A,{key:0,color:"primary",icon:"tabler-check",class:"mb-4"},{default:e(()=>[l(" تبریک! شما به فیسبوک متصل هستید ")]),_:1})):(o(),d(A,{key:1,icon:"tabler-x",class:"mb-4"},{default:e(()=>[t("span",null,S(n(j)),1),bt,t("span",{class:"cursor-pointer text-primary",onClick:C}," ادامه دادن با فیسبوک "),vt]),_:1})),n(L)>959?(o(),d(M,{key:2,class:"match-height"},{default:e(()=>[s(B,{ref_key:"pointsDiv",ref:f,cols:"12",md:"6"},{default:e(()=>[s(w,null,{default:e(()=>[s(b,null,{default:e(()=>[kt,wt]),_:1})]),_:1})]),_:1},512),s(B,{ref_key:"imageDiv",ref:g,cols:"12",md:"6"},{default:e(()=>[s(w,{class:"h-100"},{default:e(()=>[s(b,{class:"d-flex flex-column justify-center h-100"},{default:e(()=>[t("div",{class:V(["d-flex justify-center bg-blue align-start pb-0 mb-4 rounded",i.classs])},[s(R,{cover:"",src:n(O),height:"300"},null,8,["src"])],2),n(a)?(o(),v("p",Ct,[Bt,t("strong",null,S(n($)),1)])):(o(),v("p",xt," DMPlus برای پاسخ هوشمند نیاز به دسترسی های خاصی از طرف اینستاگرام دارد برروی دکمه زیر بزنید تا اجازه ها داده شود ")),n(a)?y("",!0):(o(),d(m,{key:2,loading:n(x),class:"connect_btn",color:"primary",onClick:C},{default:e(()=>[s(h,{icon:"tabler-brand-facebook",class:"me-4"}),Vt]),_:1},8,["loading"])),n(a)?(o(),d(m,{key:3,disabled:n(r),class:"connect_btn",color:"primary",onClick:C},{default:e(()=>[s(h,{icon:"tabler-brand-facebook",start:""}),$t]),_:1},8,["disabled"])):y("",!0),n(a)?(o(),d(m,{key:4,class:"connect_btn mt-4",loading:n(r),color:"secondary",onClick:z},{default:e(()=>[s(h,{icon:"tabler-x",start:""}),Tt]),_:1},8,["loading"])):y("",!0),t("p",St,[Dt,s(Q),Ft])]),_:1})]),_:1})]),_:1},512)]),_:1})):(o(),d(M,{key:3,class:"match-height"},{default:e(()=>[s(B,{ref_key:"imageDiv",ref:g,cols:"12",md:"6"},{default:e(()=>[s(w,{class:"h-100"},{default:e(()=>[s(b,{class:"d-flex flex-column justify-center h-100"},{default:e(()=>[t("div",{class:V(["d-flex justify-center bg-blue align-start pb-0 mb-4 rounded",i.classs])},[s(R,{cover:"",src:n(O),height:"300"},null,8,["src"])],2),n(a)?(o(),v("p",Pt,[jt,t("strong",null,S(n($)),1)])):(o(),v("p",It," DMPlus برای پاسخ هوشمند نیاز به دسترسی های خاصی از طرف اینستاگرام دارد برروی دکمه زیر بزنید تا اجازه ها داده شود ")),n(a)?y("",!0):(o(),d(m,{key:2,loading:n(x),class:"connect_btn",color:"primary",onClick:C},{default:e(()=>[s(h,{icon:"tabler-brand-facebook",class:"me-4"}),Lt]),_:1},8,["loading"])),n(a)?(o(),d(m,{key:3,disabled:n(r),class:"connect_btn",color:"primary",onClick:C},{default:e(()=>[s(h,{icon:"tabler-brand-facebook",start:""}),Ut]),_:1},8,["disabled"])):y("",!0),n(a)?(o(),d(m,{key:4,class:"connect_btn mt-4",loading:n(r),color:"secondary",onClick:z},{default:e(()=>[s(h,{icon:"tabler-x",start:""}),Wt]),_:1},8,["loading"])):y("",!0),zt]),_:1})]),_:1})]),_:1},512),s(B,{ref_key:"pointsDiv",ref:f,md:"6",cols:"12"},{default:e(()=>[s(w,null,{default:e(()=>[s(b,null,{default:e(()=>[Nt,Et]),_:1})]),_:1})]),_:1},512)]),_:1}))]),_:1}))],64)}}},Gt=D(Rt,[["__scopeId","data-v-6aeb13ad"]]);export { Gt as default };

