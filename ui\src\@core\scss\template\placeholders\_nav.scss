// ℹ️ This is common style that needs to be applied to both navs
%nav {
  color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
}

/*
    Active nav link styles for horizontal & vertical nav

    For horizontal nav it will be only applied to top level nav items
    For vertical nav it will be only applied to nav links (not nav groups)
*/
%nav-link-active {
  background: linear-gradient(72.47deg, rgb(var(--v-global-theme-primary)) 22.16%, rgba(var(--v-global-theme-primary), 0.7) 76.47%) !important;
  box-shadow: 0 2px 6px rgba(var(--v-global-theme-primary), 0.48);
  font-weight: 500;
}
