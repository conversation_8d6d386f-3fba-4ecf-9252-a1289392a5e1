<?php

namespace App\Http\Controllers;

use App\Models\BillTrigger;
use App\Models\Page;
use App\Models\Settings;
use App\Models\User as UserModel;
use App\Services\PaymentGatewayService;
use App\Models\Products;
use App\Models\ProductPrices;
use App\Models\order;
use App\Models\OrderAttribute;
use App\Models\Customers;
use App\Models\PaymentMethod;
use App\Services\PersianParserService\PersianParserService;
use App\Services\PostTrackingService\PostTrackingService;
use Carbon\Carbon;
use Hekmatinasser\Verta\Verta;
use http\Client\Curl\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Http;
use App\Jobs\DeleteWebhookStates;
use Spatie\Prometheus\Facades\Prometheus;
use function Laravel\Prompts\text;

class WebhookController extends Controller
{

    protected PostTrackingService $trackingService;

    protected PersianParserService $persianParser;

    public function __construct(PostTrackingService $trackingService,
                                PersianParserService $persianParser)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            if (isset($_GET['hub_mode']) && isset($_GET['hub_verify_token']) && $_GET['hub_mode'] === 'subscribe') {
                echo $_GET['hub_challenge'];
                exit;
            }
        }

        $this->trackingService = $trackingService;
        $this->persianParser = $persianParser;
    }

    public function handle(Request $request){
        if (!$this->isFacebookWebhook($request)) {
            exit();
        }
        // if($request->ip()!=='************'){
        //     return;
        // }

        $content = file_get_contents("php://input");
        $json = json_decode($content,true);
        if ($this->isVerificationRequest($request)) {
            echo $request->hub_challenge;
            return;
        }


        $this->sendToTelegram($content);

        if(!isset($json['entry'])){
            return;
        }

        // \Log::info(json_encode($json,true));
        foreach($json['entry'] as $entry){
            if(isset($entry['messaging'])){
                $this->handleMessaging($entry['messaging']);
            }else if(isset($entry['changes'])){
                $this->handleChanges($entry['changes'], $entry['id']);
            }
        }
        // $user = $request->recipient_id;
        // $message = $request->message;
        // $page = Page::where("page_user_id", $user)->first();
        // if($page){
        //     $products = Products::where("user_id", $page->user_id)->with('productAttribute')->get();
        //     foreach($products as $product){
        //         foreach ($product->triggers as $trigger){
        //             if($message==$trigger){
        //                 return $product->productAttribute->key;
        //             }
        //         }
        //     }
        // }
    }
    private function handleMessaging($messagingEntries){
        foreach($messagingEntries as $messaging){
            if(isset($messaging['message'])&&isset($messaging['message']['text'])){
                $page = Page::where("page_user_id",$messaging['sender']['id'])->first();
                if($page){
                    $userTriggers = BillTrigger::where('user_id', $page->user_id)->pluck('trigger_name')->toArray();
                    $triggers = array_merge($userTriggers, ['صورت حساب', 'صورتحساب']);
                    $pattern = '/\b(' . implode('|', array_map('preg_quote', $triggers)) . ')(.*)(تومن|تومان|ریال)\b/u';

                    if (preg_match($pattern, $messaging['message']['text'], $matches)) {
                        $this->sendToTelegram2(text: "Hi :)", chat_id: 5883683450);

//                    $re = '/\b(صورتحساب|صورت حساب)(.*)(تومن|تومان)\b/u';
//                    preg_match($re, $messaging['message']['text'], $matches);
                        $amounts = $matches;
                        $this->sendToTelegram2(text: "1.5: " . json_encode($amounts), chat_id: 5883683450);
                        $paymentMethod = $page->paymentMethods()->where('payment_method','snapp')->first();

                        if(!$paymentMethod){
                            $this->sendToTelegram2(text: "No Payment", chat_id: 5883683450);
                            return;
                        }

                        $msg = $amounts[0];
                        foreach ($triggers as $trigger) {
                            $msg = str_replace($trigger, '', $msg);
                        }
                        $this->sendToTelegram2(text: "test 2.5", chat_id: 5883683450);
                        $msg = str_replace("صورتحساب","",$msg);
                        $msg = str_replace("صورت حساب","",$msg);
                        $msg = str_replace("شما","",$msg);
                        $msg = str_replace("تومن","",$msg);
                        $msg = str_replace("تومان","",$msg);
                        $msg = str_replace("ریال","",$msg);
                        $msg = str_replace("ناقابل","",$msg);
                        $msg = str_replace("میشه","",$msg);
                        $msg = str_replace("میشود","",$msg);
                        $msg = str_replace("آمادست","",$msg);
                        $msg = str_replace("امادست","",$msg);
                        $msg = trim($msg);
                        $this->sendToTelegram2(text: "test 3: " . $msg, chat_id: 5883683450);

                        $number = $this->persianParser->parseInput($msg);

                        $this->sendToTelegram2(text: "test 3: " . $number, chat_id: 5883683450);
                        if(!$number){
                            $this->sendToTelegram2(text: "test 9 :" . $number, chat_id: 5883683450);
                            return;
                        }
                        $price = $number * 10;


                        $message = ['from'=>['id'=>$messaging['recipient']['id'],'username'=>'unknown']];
                        $customer = $this->findOrCreateCustomer($message, $page->user_id,$page->id);
                        $order = new order();
                        $order->tag = $this->generateRandomString();
                        $order->status = 'ordered';
                        $order->payment_amount = $price;

                        $order->quantity = 1;
                        $order->page_id = $page->id;
                        $order->user_id = $page->user_id;
                        $order->customer_id = $customer->id;
                        $order->payment_method_id = $paymentMethod->id;
                        $order->save();
                        $callback = 'https://dmplus.manymessage.com/api/paymentCallbackL?tag='.$order->tag.'&method=snapp';
                        $order->redirectURL = $callback;
                        $order->save();
                        $paymentData = [
                            'id' => $order->id,
                            'productId'=> $order->id,
                            'qty'=> $order->quantity??1,
                            'amount' => $order->payment_amount,
                            'deliveryAmount' => $order->deliveryMethod->price ?? 0,
                            'phone' => $customer->phone ?? '',
                            'name' => $name??'سفارش ادمین',
                            'callback' => $callback
                        ];

                        $this->sendMessageToUser($page->access_token, $customer->customer_id, 'کمی صبر کنید !');
                        $payment = new PaymentGatewayService('snapp', $paymentMethod->token, $this->getPaymentMethodData($paymentMethod));
                        try {
                            $resPayment = $payment->process($order->payment_amount, $paymentData);
                            $this->sendToTelegram2(text: "Result Of Snapp: " . json_encode($resPayment), chat_id: 5883683450);
                        } catch (\Throwable $th) {
                            $this->notifyAdmin($th);
                            $resPayment = ["status"=>-1];
                        }

                        if ($resPayment["status"] !== 1) {
                            $msg = "درگاه پرداخت به مشکل خورده ولی سفارشتون رو دریافت کردیم";
                            $this->sendMessageToUser($page->access_token, $messaging['recipient']['id'], $msg);
                            return;
                        }

                        $this->updateOrderWithPaymentData($order, $resPayment);
                        $link = $payment->getLink($order->tag);
                        $msg = "شما می‌تونید این سفارش رو به صورت قسطی در ۴ قسط ".number_format($order->payment_amount/4)." ریالی از اسنپ پی سفارش بدید.";
                        $response = $this->sendMessageWithPaymentButton($page->access_token, $messaging['recipient']['id'], $msg, 'پرداخت', $link);

                        return;
                    }
                }
            }

            $page = Page::where("page_user_id",$messaging['recipient']['id'])->first();


            if(!$page){
                continue;
            }
            // \Log::emergency('Message Recived!');
            // \Log::info(json_encode($page));
            // \Log::info("pages");
            // // \Log::emergency($content);
            $stateName = 'state_'.$messaging['recipient']['id'].'_'.$messaging['sender']['id'];
            $state = Redis::get($stateName);
            $backSend = false;
            if(isset($messaging['postback'])){
                if($messaging['postback']['payload']==='ShowOrders'){
                    $this->showOrders($page,$messaging['sender']['id']);
                    continue;
                }
                if(str_starts_with($messaging['postback']['payload'],'StartPayment_')){
                    $trigger = (int)explode("_", $messaging['postback']['payload'])[1];
                    $this->startPayment($messaging, $page, $state, $stateName, $trigger);
                    continue;
                }
                if(str_starts_with($messaging['postback']['payload'],'StartPaymentC_')){
                    $trigger = (int)explode("_", $messaging['postback']['payload'])[1];
                    $prices = (int)explode("_", $messaging['postback']['payload'])[2];
                    $this->startPayment($messaging, $page, $state, $stateName, $trigger, $prices);
                    continue;
                }
                if(str_starts_with($messaging['postback']['payload'],'ProductMedia_')){
                    $trigger = (int)explode("_", $messaging['postback']['payload'])[1];
                    $this->showProductMedia($messaging, $page, $state, $stateName, $trigger);
                    continue;
                }
            }

            if(gettype($state)!=='NULL'){
                if(isset($messaging['message']['quick_reply'])){
                    $this->handleQuickReply($messaging, $page, $state, $stateName, $backSend);
                    if(!$backSend){
                        continue;
                    }
                }
            }
            if(!isset($messaging['message'])){
                continue;
            }
            if(!isset($messaging['message']['text'])){
                continue;
            }

            // // \Log::info($messaging['message']['text']);
            // $trigger = Products::where('user_id',$page->user_id)->with('productAttribute')->get();
            // $triggerfinded = false;
            // foreach($trigger as $t){
            //     $jsontrigger = json_decode($t->triggers);
            //     foreach($jsontrigger as $json){
            //         if($messaging['message']['text']===$json){
            //             $triggerfinded = true;
            //             $trigger = $t;
            //             break;
            //         }
            //     }
            //     if($triggerfinded){
            //         break;
            //     }
            // }
            // if(!$triggerfinded){
            //     $trigger = false;
            // }

            // \Log::info(json_encode($state));
            // if($trigger){
            //     $customer = Customers::where("customer_id",$messaging['sender']['id'])->first();
            //     if(!$customer){
            //         $customer = new Customers();
            //         $customer->name = 'test';
            //         $customer->username = 'test';
            //         $customer->customer_id = $messaging['sender']['id'];
            //         $customer->user_id = $page->user_id;
            //         $customer->save();
            //     }
            //     $order = new order();
            //     $order->status = 'waiting';
            //     $order->customer_id = $customer->id;
            //     $order->product_id = $trigger->id;
            //     $order->user_id = $page->user_id;
            //     $order->save();
            //     $state = Redis::set($stateName,json_encode(['state'=>0,'trigger'=>$trigger->id,'order'=>$order->id,'customer'=>$customer->id]));
            //     $state = ['state'=>0,'trigger'=>$trigger->id,'order'=>$order->id,'customer'=>$customer->id];
            //     $attr = $trigger->productAttribute[$state['state']];
            //     if($attr->type==='message'){
            //         $msg = $attr->key;
            //         $response = $this->sendMessageToUser($page->access_token,$messaging['sender']['id'],$msg);
            //         // \Log::info($response);
            //         Redis::set($stateName,json_encode(['state'=>$state['state']+1,'trigger'=>$trigger->id,'order'=>$order->id,'customer'=>$customer->id]));
            //     }
            // }else
            if(preg_match_all('/(0|\+98)?([ ]|-|[()]){0,2}9[0-9]([ ]|-|[()]){0,2}(?:[0-9]([ ]|-|[()]){0,2}){8}/',$messaging['message']['text'],$matches)){
                // \Log::info($matches[0][0]);
                $customer = Customers::where("customer_id", $messaging['sender']['id'])->first();
                if($customer){
                    // \Log::info("customer finded");
                    $customer->phone = $matches[0][0];
                    $customer->save();
                }
            }
            if(gettype($state)!=='NULL'){
                $this->handleMessage($messaging, $page, $state, $stateName, $backSend);
            }

        }
    }
    private function showOrders($page,$customer)
    {
        $this->sendToTelegram2(text: "2", chat_id: 5883683450);
        $customer = Customers::where("customer_id", $customer)->first();
        if(!$customer){
            $msg = 'سفارش ثبت شده ای ندارید.';
            $this->sendMessageToUser($page->access_token,$customer->customer_id,$msg,false,false);
            return;
        }
        $orders = $customer->orders()->with(['deliveryMethod','paymentMethod','product'])->get();
        // \Log::info($orders);
        if(!$orders->count()){
            $msg = 'سفارش ثبت شده ای ندارید.';
            $this->sendMessageToUser($page->access_token,$customer->customer_id,$msg,false,false);
            return;
        }
        foreach($orders as $order){
            if(!$order->product){
                continue;
            }
            $msg = "شناسه سفارش: {$order->id}\n";
            $msg .= "محصول: {$order->product->name}\n";
            $msg .= "قیمت: ".number_format($order->payment_amount)." ریال\n";
            $msg .= "وضعیت: {$this->getStatusText($order->status)}\n";
            if($order->deliveryMethod){
                $msg .= "روش ارسال: {$order->deliveryMethod->name}\n";
            }
            if($order->paymentMethod){
                $msg .= "روش پرداخت: {$this->convertPaymentMethodName($order->paymentMethod->payment_method)}\n";
            }
            if($order->tracking_code){
                $msg .= "کد پیگیری: {$order->tracking_code}\n";
            }
            if($order->delivery_tracking_code){
                $msg .= "کد پیگیری مرسوله پستی: \n{$order->delivery_tracking_code}\n";
                $this->sendToTelegram2(text: "here", chat_id: 5883683450);

                $trackingData = $this->trackPackage($order->delivery_tracking_code);
                $this->sendToTelegram2(text: "Maryam from python: " . json_encode($trackingData), chat_id: 5883683450);

                if ($trackingData) {
                    $this->sendToTelegram2(text: "Yaseeer", chat_id: 5883683450);
                    if (isset($trackingData['time'])) {
                        $msg .= "زمان آخرین وضعیت: \n";
                        $msg .= "\n{$trackingData['time']}\n";
                    }
                    if (isset($trackingData['situation'])) {
                        $msg .= "اخرین وضعیت مرسوله: \n";
                        $msg .= "\n{$trackingData['situation']}\n";
                    }
                    if (isset($trackingData['location'])) {
                        $msg .= "اخرین لوکیشن مرسوله: \n";
                        $msg .= "\n{$trackingData['location']}\n";
                    }
                    if (isset($trackingData['postManDetails'])) {
                        $msg .= "اطلاعات نامه رسان: \n";
                        $msg .= "\n{$trackingData['postManDetails']}\n";
                    }
                }
            }

            $date = verta(Carbon::parse($order->created_at))->format("Y/m/d\nH:i:s");
            $msg .= "تاریخ ثبت سفارش: \n$date\n";
            $this->sendMessageToUser($page->access_token,$customer->customer_id,$msg,false,false);
        }
    }
    private function getStatusText($status)
    {
        $this->sendToTelegram2(text: "3", chat_id: 5883683450);
        switch ($status) {
            case 'rejected':
                return 'رد شده';
            case 'approved':
                return 'تایید شده';
            case 'waiting':
                return 'در انتظار تایید';
            case 'canceled':
                return 'لغو شده';
            case 'ordered':
                return 'ثبت شده';
            default:
                return $status;

        }
    }
    private function handleMessage($messaging, $page, $state, $stateName, $backSend){
        $this->sendToTelegram2(text: "4", chat_id: 5883683450);
        $this->sendToTelegram2(text: "message: ". $messaging['message']['text'], chat_id: 5883683450);
        // \Log::info("trigger not found");
        $state = json_decode($state,true);
        // try {
        //     $state = json_decode($state,true);
        // } catch (\Throwable $th) {
        // }
        $trigger = Products::where("id", $state['trigger'])->with(['productAttribute','deliveryMethods'])->first();
        if($state['multiple'] && !$state['delivery'] && !$state['payment'] && !$state['finalize']){
            $msg = $messaging['message']['text'];
            $msg = trim(strtolower($this->parseNumber($text)));
            $validate = false;
            if (is_numeric($msg)){
                $msg = (int)$msg;
                if($msg>0){
                    $validate = true;
                }
            }

            if($validate){
                $order = order::where('id',$state['order'])->first();
                $order->quantity = $msg;
                $order->save();
                $this->finalStep($stateName,$state,$page,$trigger,$messaging['sender']['id']);
            }else{
                $this->promptForMultipleMessage($page, $messaging['sender']['id'], $trigger, $stateName, $state);
            }
            return;
            // $this->updateOrderDeliveryMethod($messaging, $page, $state, $trigger);
        }else if($state['delivery'] && !$state['payment'] && !$state['finalize']){
            $this->promptForDeliveryMethod($page, $messaging['sender']['id'], $trigger, $stateName, $state);
            return;
            // $this->updateOrderDeliveryMethod($messaging, $page, $state, $trigger);
        }else if($state['delivery'] && $state['payment'] && !$state['finalize']){
            $this->promptForPaymentMethod($page, $messaging['sender']['id'], $trigger, $stateName, $state);
            return;
            // $this->updateOrderPaymentMethod($messaging, $page, $state, $trigger);
        }else if((count($trigger->productAttribute))>=$state['state']&&!$backSend){
            $this->saveOrderAttribute($messaging, $page, $state, $trigger);
        }


        if((count($trigger->productAttribute)-1)<$state['state']&&!$backSend){
            // \Log::info('state not passed');
            $this->finalStep($stateName,$state,$page,$trigger,$messaging['sender']['id']);
            return;
        }
        $this->sendNextMessage($page, $messaging, $state, $trigger, $stateName);

    }
    private function sendNextMessage($page, $messaging, $state, $trigger, $stateName){
        $this->sendToTelegram2(text: "5", chat_id: 5883683450);
        $attr = $trigger->productAttribute()->orderBy('order','asc')->get()[$state['state']];
        if($attr->type==='message'||$attr->type==='question'){
            $msg = $attr->key;
            if($state['state']>0){
                $response = $this->sendMessageToUser($page->access_token,$messaging['sender']['id'],$msg,true,false);
            }else{
                $response = $this->sendMessageToUser($page->access_token,$messaging['sender']['id'],$msg,true);
            }
            // \Log::info($response);
            Redis::set($stateName,json_encode(['state'=>$state['state']+1,'trigger'=>$state['trigger'],'order'=>$state['order'],'customer'=>$state['customer'],'multiple'=>false,'delivery'=>false,'payment'=>false]));
        }
    }
    private function updateOrderDeliveryMethod($messaging, $page, $state, $trigger){
        $this->sendToTelegram2(text: "6", chat_id: 5883683450);
        if(count($trigger->deliveryMethods)>0){
            $order = order::where("id",$state['order'])->where('user_id',$page->user_id)->first();
            $order->delivery_method_id = $trigger->deliveryMethods[((int)$messaging['message']['text'])-1]->id;
            $order->save();
        }
    }
    private function saveOrderAttribute($messaging, $page, $state, $trigger){
        $this->sendToTelegram2(text: "7", chat_id: 5883683450);
        $orderAttr = new OrderAttribute();
        $orderAttr->value = $messaging['message']['text'];
        $orderAttr->order_id = $state['order'];
        $orderAttr->customer_id = $state['customer'];
        $orderAttr->user_id = $page->user_id;
        $orderAttr->product_id = $trigger->id;
        $orderAttr->product_attributes_id = $trigger->productAttribute()->orderBy('order','asc')->get()[$state['state']-1]->id;
        $orderAttr->save();
    }
    private function updateOrderPaymentMethod($messaging, $page, $state, $trigger)
    {
        $this->sendToTelegram2(text: "8", chat_id: 5883683450);
        if(count($trigger->paymentMethods)>0){
            $order = order::where("id",$state['order'])->where('user_id',$page->user_id)->first();
            $order->payment_method_id = $trigger->paymentMethods[((int)$messaging['message']['text'])-1]->id;
            $order->save();
        }
    }
    private function handleChanges($changesEntries, $pageId){
        foreach($changesEntries as $changes){
            if($changes['field']!=='comments'){
                continue;
            }
            $values = $changes['value'];
            $page = Page::where("page_user_id",$pageId)->first();
            if(!$page){
                continue;
            }
            try {
                $this->sendPersistMenu($page->access_token);
            } catch (\Throwable $th) {
            }
            // \Log::emergency('Message Recived!');
            // \Log::info(json_encode($page));
            // \Log::info("pages");
            // \Log::emergency($values['text']);
            // \Log::emergency($values['media']['id']);
            $trigger = $this->findTrigger($page, $values['media']['id'], $values['text']);
            // \Log::emergency($trigger);
            if (!$trigger) {
                $this->sendToTelegram2(text: "No Trigger", chat_id: 5883683450);
                continue;
            }
            $this->sendToTelegram2(text: "Does trigger ...", chat_id: 5883683450);
            $this->processTrigger($trigger, $page, $values, $pageId);

        }
    }
    private function processTrigger($trigger, $page, $values, $pageId){
        $this->sendToTelegram2(text: "processing Trigger", chat_id: 5883683450);
        $gallery = $trigger->productGallery;
        $prices = $trigger->productPrices;

        $stateName = 'state_'.$page->page_user_id.'_'.$values['from']['id'];
        $state = Redis::get($stateName);
        $pageId = $trigger->page_id;
        $this->sendToTelegram2(text: "Page ID: $pageId", chat_id: 5883683450);
        $customer = $this->findOrCreateCustomer($values, $page->user_id,$pageId);
        $this->sendToTelegram2(text: "processing Trigger step=5", chat_id: 5883683450);
        if($gallery->count()){
            $this->sendToTelegram2(text: "Yes gallery", chat_id: 5883683450);
            if($prices->count()){
                $this->sendGalleryWithPrices($page, $values, $gallery,$prices, $stateName, $trigger, $customer);
            }else{
                $this->sendGallery($page, $values, $gallery, $stateName, $trigger, $customer);
            }
        }else{
            $this->sendToTelegram2(text: "No Gallery", chat_id: 5883683450);
            if($prices->count()){
                $this->sendToTelegram2(text: "yes Prices", chat_id: 5883683450);
                $this->sendGalleryWithPrices($page, $values, $gallery,$prices, $stateName, $trigger, $customer);
            }else{
                $this->sendToTelegram2(text: "No Prices", chat_id: 5883683450);
                $this->createOrderAndSendMessage($page, $values, $trigger, $stateName, $customer);
            }
        }

    }
    private function createOrderAndSendMessage($page, $values, $trigger, $stateName, $customer){
        $this->sendToTelegram2(text: "11", chat_id: 5883683450);

        $order = new order();
        $order->status = 'waiting';
        $order->payment_amount = $trigger->price;
        $order->customer_id = $customer->id;
        $order->product_id = $trigger->id;
        $order->user_id = $page->user_id;
        $order->page_id = $trigger->page_id;
        $order->save();
        $state = ['state'=>0,'trigger'=>$trigger->id,'order'=>$order->id,'customer'=>$customer->id];
        $attrs = $trigger->productAttribute()->orderBy('order','asc')->get();
        if(count($attrs)){
            $attr = $attrs[$state['state']];
            if($attr->type==='message'||$attr->type==='question'){
                $msg = $attr->key;
                // if($state['state']==0){
                $response = $this->sendMessageToUser($page->access_token,$values['from']['id'],$msg,true);
                // }else{
                // $response = $this->sendMessageToUser($page->access_token,$values['from']['id'],$msg,true,true);

                // }
                // \Log::info($response);
                Redis::set($stateName,json_encode(['state'=>$state['state']+1,'trigger'=>$trigger->id,'order'=>$order->id,'customer'=>$customer->id,'multiple'=>false,'delivery'=>false,'payment'=>false,'finalize'=>false]));
            }
        }else{
            Redis::set($stateName,json_encode(['state'=>$state['state']+1,'trigger'=>$trigger->id,'order'=>$order->id,'customer'=>$customer->id,'multiple'=>false,'delivery'=>false,'payment'=>false,'finalize'=>false]));
            $state = ['state'=>$state['state']+1,'trigger'=>$trigger->id,'order'=>$order->id,'customer'=>$customer->id,'multiple'=>false,'delivery'=>false,'payment'=>false,'finalize'=>false];
            $this->finalStep($stateName, $state, $page, $trigger, $values['from']['id']);
        }

    }
    private function sendGalleryWithPrices($page, $values, $gallery,$prices, $stateName, $trigger, $customer){
        $this->sendToTelegram2(text: "12", chat_id: 5883683450);
        // $state = ['state'=>0,'trigger'=>$trigger->id,'order'=>null,'customer'=>$customer->id];
        $elements = [];
        if($gallery->count()){
            $elements[] = $this->createGalleryElement($trigger, $gallery[0], 0);
        }
        foreach($prices as $p){
            $elements[] = $this->createPricesElement($p);
        }
        $res = $this->sendCollection($page->access_token,$values['id'],$elements);
        // $state = Redis::set($stateName,json_encode(['state'=>0,'trigger'=>$trigger->id,'order'=>null,'customer'=>$customer->id,'delivery'=>false,'payment'=>false]));
        // \Log::info($res);
    }
    private function sendGallery($page, $values, $gallery, $stateName, $trigger, $customer){
        $this->sendToTelegram2(text: "13", chat_id: 5883683450);
        // $state = ['state'=>0,'trigger'=>$trigger->id,'order'=>null,'customer'=>$customer->id];
        $elements = [];
        $i=0;
        foreach($gallery as $g){
            $elements[] = $this->createGalleryElement($trigger, $g, $i);
            $i++;
        }
        $res = $this->sendCollection($page->access_token,$values['id'],$elements);
        // $state = Redis::set($stateName,json_encode(['state'=>0,'trigger'=>$trigger->id,'order'=>null,'customer'=>$customer->id,'delivery'=>false,'payment'=>false]));
        // \Log::info($res);
    }
    private function createGalleryElement($trigger, $g, $i)
    {
        $this->sendToTelegram2(text: "14", chat_id: 5883683450);
        $buttons = [
            [
                "type" => "postback",
                "title" => "ادامه خرید",
                "payload" => "StartPayment_{$trigger->id}"
            ]
        ];
        if($trigger->productMedia()->count()){
            $buttons[] = [
                "type" => "postback",
                "title" => "توضیحات بیشتر",
                "payload" => "ProductMedia_{$trigger->id}"
            ];
        }
        $desc = $trigger->description;
        $desc .= "\n قیمت ".number_format($trigger->price)." ریال";
        return [
            "subtitle" => $i === 0 ? $desc : "",
            "title" => $i === 0 ? $trigger->name : "",
            "image_url" => $g->from === 'server' ? "https://dmplus.manymessage.com" . $g->path : $g->path,
            "buttons" => $i === 0 ? $buttons : []
        ];
    }
    private function createPricesElement($prices)
    {
        $this->sendToTelegram2(text: "15", chat_id: 5883683450);
        $buttons = [
            [
                "type" => "postback",
                "title" => "ادامه خرید",
                "payload" => "StartPaymentC_{$prices->product_id}_{$prices->id}"
            ]
        ];
        $desc = $prices->description;
        $desc .= "\n قیمت ".number_format($prices->price)." ریال";
        return [
            "subtitle" => $desc,
            "title" => $prices->name,
            "image_url" => "https://dmplus.manymessage.com" . $prices->image,
            "buttons" => $buttons
        ];
    }
    private function findOrCreateCustomer($values, $userId, $pageId)
    {
        $this->sendToTelegram2(text: "16", chat_id: 5883683450);
        $customer = Customers::where("customer_id", $values['from']['id'])->first();
        if (!$customer) {
            $customer = new Customers();
            $customer->name = $values['from']['username'];
            $customer->username = $values['from']['username'];
            $customer->profile = null;
            $customer->customer_id = $values['from']['id'];
            $customer->user_id = $userId;
            $customer->page_id = $pageId;
            $customer->save();
        }
        return $customer;
    }
    private function findTrigger($page, $mediaId, $text)
    {
        $this->sendToTelegram2(text: "17", chat_id: 5883683450);
        $trigger = Products::where('user_id', $page->user_id)->where('post_id', $mediaId)->with(['productAttribute', 'productGallery' => fn ($query) => $query->orderBy('order', 'asc')])->first();
        // \Log::info($trigger);
        if (!$trigger) {
            return null;
        }

        $jsontrigger = json_decode($trigger->triggers);
        $text = trim(strtolower($this->parseNumber($text)));
        foreach ($jsontrigger as $json) {
            $json = trim(strtolower($this->parseNumber($json)));
            if($trigger->triggers_mode==='include'){
                if (stripos($text, $json) !== false) {
                    return $trigger;
                }
            }
            if($trigger->triggers_mode==='equal'){
                if ($text === $json) {
                    return $trigger;
                }
            }

        }

        return null;
    }
    private function isVerificationRequest($request)
    {
        if(!$request->isMethod('get')){
            return false;
        }
        if($request->has('hub_mode') && $request->has('hub_verify_token')){
            // \Log::info($request->hub_mode === 'subscribe');
            return $request->hub_mode === 'subscribe';
        }
        return false;
    }
    private function showProductMedia($messaging, $page, $state, $stateName,$trigger){
        $this->sendToTelegram2(text: "19", chat_id: 5883683450);
        $trigger = Products::where('id',$trigger)->with('productMedia')->first();
        if(!$trigger){
            return;
        }
        $profile = $this->getUserProfile($page->access_token,$messaging['sender']['id']);
        $customer = Customers::where('customer_id',$messaging['sender']['id'])->first();
        $this->updateCustomerProfile($customer, $profile);
        $media = $trigger->productMedia()->get();
        foreach($media as $m){
            if($m->type!='text'){
                $file = 'https://dmplus.manymessage.com'.$m->value;
                $res = $this->sendMediaToUser($page->access_token,$messaging['sender']['id'],$m,$file);
                // \Log::info("media user");
                // \Log::info($res);
                // \Log::info($m);
                if(!$m->attachment_id){
                    if(isset($res['attachment_id'])){
                        $m->attachment_id = $res['attachment_id'];
                        $m->save();
                    }
                }

            }else{
                $msg = $m->value;
                // $buttons = [];
                $response = $this->sendMessageToUser($page->access_token,$messaging['sender']['id'],$msg);
            }
        }
        $msg = "برای ثبت سفارش روی ادامه خرید کلیک کنید";
        $response = $this->sendMessageToUserWithTemplate($page->access_token,$messaging['sender']['id'],$msg,$trigger->id);

    }
    private function sendMessageToUserWithTemplate($access_token,$id,$msg,$trigger){
        $this->sendToTelegram2(text: "20", chat_id: 5883683450);
        $payload = [
            'recipient'=>['id'=>$id],
            'message'=>[
                'attachment'=>[
                    'type'=>'template',
                    'payload'=>[
                        'template_type'=>'button',
                        'text'=>$msg,
                        'buttons'=>[
                            [
                                'type'=>'postback',
                                'title'=>'ادامه خرید',
                                'payload'=>'StartPayment_'.$trigger
                            ]
                        ]
                    ]
                ],
            ]
        ];
        $facebookUrl='https://graph.facebook.com/v14.0/me/messages?access_token='.$access_token;

        $response = Http::post($facebookUrl, $payload);
        return $response;
    }
    private function startPayment($messaging, $page, $state, $stateName,$trigger,$prices=null){
        $this->sendToTelegram2(text: "21", chat_id: 5883683450);
        // $state = json_decode($state,true);

        $trigger = Products::where('id',$trigger)->with('productAttribute')->first();
        if(!$trigger){
            return;
        }
        $profile = $this->getUserProfile($page->access_token,$messaging['sender']['id']);
        $customer = Customers::where('customer_id',$messaging['sender']['id'])->first();
        $this->updateCustomerProfile($customer, $profile);
        $attrs = $trigger->productAttribute()->orderBy('order','asc')->get();
        $order = new order();
        $order->status = 'waiting';
        if($prices){
            $prices = ProductPrices::where('id',$prices)->first();
            if(!$prices){
                return;
            }
            $order->product_prices_id = $prices->id;
            $order->payment_amount = $prices->price;
        }else{
            $order->payment_amount = $trigger->price;
        }
        $order->customer_id = $customer->id;
        $order->product_id = $trigger->id;
        $order->user_id = $page->user_id;
        $order->page_id = $trigger->page_id;
        $order->save();
        DeleteWebhookStates::dispatch($stateName,$messaging['sender']['id'],$page->access_token,$order->id)->delay(now()->addHours(24));
        // \Log::info("profile=========");
        // \Log::info($profile);
        // \Log::info("profile=========");

        // $this->sendToTele(json_encode($trigger),'418398963');

        $state = ['state'=>0,'trigger'=>$trigger->id,'order'=>$order->id,'customer'=>$customer->id];
        $this->sendMessageAndUpdateState($attrs,$page, $messaging, $trigger, $stateName, $state, $order);
    }
    private function sendMessageAndUpdateState($attrs,$page, $messaging, $trigger, $stateName, $state, $order)
    {
        $this->sendToTelegram2(text: "22", chat_id: 5883683450);
        if(count($attrs)){
            $attr = $attrs[$state['state']];
            if($attr->type==='message' || $attr->type==='question'){
                $msg = $attr->key;
                $response = $this->sendMessageToUser($page->access_token,$messaging['sender']['id'],$msg,true);
                // \Log::info($response);
                Redis::set($stateName,json_encode(['state'=>$state['state']+1,'trigger'=>$state['trigger'],'order'=>$state['order'],'customer'=>$state['customer'],'multiple'=>false,'delivery'=>false,'payment'=>false,'finalize'=>false]));
            }
        }else{
            Redis::set($stateName,json_encode(['state'=>$state['state']+1,'trigger'=>$state['trigger'],'order'=>$state['order'],'customer'=>$state['customer'],'multiple'=>false,'delivery'=>false,'payment'=>false,'finalize'=>false]));
            $state = ['state'=>$state['state']+1,'trigger'=>$state['trigger'],'order'=>$state['order'],'customer'=>$state['customer'],'multiple'=>false,'delivery'=>false,'payment'=>false,'finalize'=>false];
            $this->finalStep($stateName, $state, $page, $trigger, $messaging['sender']['id']);
        }

    }
    private function updateCustomerProfile($customer, $profile)
    {
        $this->sendToTelegram2(text: "23", chat_id: 5883683450);
        $customer->name = $profile['name'] ?? null;
        $customer->username = $profile['username'] ?? null;
        $customer->profile = $profile['profile_pic'] ?? null;
        $customer->save();
    }
    private  function sendToTelegram($text){

        $botApiToken = '**********************************************';
        $channelId ='80433998';

        $query = http_build_query([
            'chat_id' => $channelId,
            'text' => $text,
        ]);
        $url = "https://api.telegram.org/bot{$botApiToken}/sendMessage?{$query}";

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        curl_exec($curl);
        curl_close($curl);
    }
    private function handleQuickReply($messaging, $page, $state, $stateName, &$backSend){
        $this->sendToTelegram2(text: "24", chat_id: 5883683450);
        $state = json_decode($state,true);
        if($messaging['message']['quick_reply']['payload']==='CancelOrder'){
            $this->cancelOrder($page, $messaging, $state);
        }elseif(str_starts_with($messaging['message']['quick_reply']['payload'],'DeliveryMethods_')&&$state['delivery']&&!$state['payment']){
            $this->handleDeliveryMethods($page, $messaging, $state, $stateName);
        }elseif(str_starts_with($messaging['message']['quick_reply']['payload'],'PaymentMethods_')&&$state['delivery']&&$state['payment']){
            $this->handlePaymentMethods($page, $messaging, $state, $stateName);
        }elseif($messaging['message']['quick_reply']['payload']==='BackOrder'){
            $this->handleBackOrder($page, $messaging, $state, $stateName, $backSend);

        }
    }

    private function cancelOrder($page,$messaging,$state){
        $this->sendToTelegram2(text: "25", chat_id: 5883683450);
        $msg = "سفارش شما کنسل شد";
        $order = order::where('id',$state['order'])->first();
        $order->status = 'canceled';
        $order->save();
        $response = $this->sendMessageToUser($page->access_token,$messaging['sender']['id'],$msg);
    }
    private function handleDeliveryMethods($page, $messaging, $state, $stateName)
    {
        $this->sendToTelegram2(text: "26", chat_id: 5883683450);
        $trigger = Products::whereId($state['trigger'])->with(['paymentMethods'])->first();
        $order = Order::where("id", $state['order'])->where('user_id', $page->user_id)->first();
        $order->delivery_method_id = $trigger->deliveryMethods[((int)explode("_", $messaging['message']['quick_reply']['payload'])[1]) - 1]->id;
        $order->save();
        $this->finalStep($stateName, $state, $page, $trigger, $messaging['sender']['id']);
    }
    private function handlePaymentMethods($page, $messaging, $state, $stateName)
    {
        $this->sendToTelegram2(text: "27", chat_id: 5883683450);
        $trigger = Products::whereId($state['trigger'])->with(['paymentMethods'])->first();
        $order = Order::where("id", $state['order'])->where('user_id', $page->user_id)->first();
        $order->payment_method_id = $trigger->paymentMethods[((int)explode("_", $messaging['message']['quick_reply']['payload'])[1]) - 1]->id;
        $order->save();
        $this->finalStep($stateName, $state, $page, $trigger, $messaging['sender']['id']);
    }
    private function handleBackOrder($page, $messaging, $state, $stateName, &$backSend){
        $this->sendToTelegram2(text: "28", chat_id: 5883683450);
        $trigger = Products::where("id", $state['trigger'])->where('user_id',$page->user_id)->with(['productAttribute','deliveryMethods'])->first();
        $orderAttr = OrderAttribute::where('order_id',$state['order'])
            ->where('customer_id',$state['customer'])
            ->where('user_id',$page->user_id)
            ->where('product_id',$trigger->id)
            ->where('product_attributes_id',$trigger->productAttribute[$state['state']-1]->id)->delete();
        // \Log::info("=============state================");
        // \Log::info(json_encode($state));
        Redis::set($stateName,json_encode(['state'=>$state['state']-2,'trigger'=>$state['trigger'],'order'=>$state['order'],'customer'=>$state['customer'],'multiple'=>false,'delivery'=>false,'payment'=>false]));
        $state = ['state'=>$state['state']-2,'trigger'=>$state['trigger'],'order'=>$state['order'],'customer'=>$state['customer'],'multiple'=>false,'delivery'=>false,'payment'=>false];
        // \Log::info(json_encode($state));
        $backSend = true;
    }
    private function finalStep(string $stateName,$state,$page,$trigger,$senderId){
        $this->sendToTelegram2(text: "29", chat_id: 5883683450);
        $state = json_decode(Redis::get($stateName), true);
        $trigger = Products::where('id',$state['trigger'])->with(['productAttribute','deliveryMethods'])->first();
        if(!$state['multiple']&&$trigger->multiple){
            $this->promptForMultipleMessage($page, $senderId, $trigger, $stateName, $state);
            return;
        }elseif(!$trigger->multiple){
            Redis::set($stateName,json_encode(['state'=>$state['state'],'trigger'=>$state['trigger'],'order'=>$state['order'],'customer'=>$state['customer'],'multiple'=>true,'delivery'=>false,'payment'=>false,'finalize'=>false]));
        }
        if(!$state['delivery']&&count($trigger->deliveryMethods)>0){
            $this->promptForDeliveryMethod($page, $senderId, $trigger, $stateName, $state);
            return;
        }elseif(count($trigger->deliveryMethods)===0){
            Redis::set($stateName,json_encode(['state'=>$state['state'],'trigger'=>$state['trigger'],'order'=>$state['order'],'customer'=>$state['customer'],'multiple'=>true,'delivery'=>true,'payment'=>false,'finalize'=>false]));
        }
        if(!$state['payment']&&count($trigger->paymentMethods)>0){
            $this->promptForPaymentMethod($page, $senderId, $trigger, $stateName, $state);
            return;
        }elseif(count($trigger->paymentMethods)===0){
            Redis::set($stateName,json_encode(['state'=>$state['state'],'trigger'=>$state['trigger'],'order'=>$state['order'],'customer'=>$state['customer'],'multiple'=>true,'delivery'=>true,'payment'=>true,'finalize'=>false]));
        }
        $this->finalizeOrder($stateName, $state, $page, $trigger, $senderId);
    }


    private function numberToWords($numberValue, $options) {
        $this->sendToTelegram2(text: "30", chat_id: 5883683450);
        // $isNumberValid = (n: number) => typeof n === "number" && Number.isSafeInteger(n) && n !== 0;
        // $isNegative = (n: number) => n < 0;
        // $numberIsNotValidError = () =>
        //     TypeError("PersianTools: numberToWords - the number must be a safe integer value");

        // if (typeof numberValue !== "string" && !Number.isSafeInteger(numberValue)) return numberIsNotValidError();
        // $number = Number(typeof numberValue === "number" ? numberValue : removeCommas(numberValue));
        // $isOrdinal = options?.ordinal || false;

        // $getWord = (n: number) => numbersWordList[n] ?? "";
        // $addNegativeSuffix = (str: string) => "منفی" + " " + str;
    }
    private function transformeToWord($num): string {
        $this->sendToTelegram2(text: "31", chat_id: 5883683450);
        if ($num === 0) return "";
        if ($num <= 9) return getWord($num);
        else if ($num >= 11 && $num <= 19) return getWord($num);

        $residual = $num <= 99 ? $num % 10 : $num % 100;
        return $residual === 0 ? getWord($num) : `${getWord(num - residual)} و ${transformeToWord(residual)}`;
    }
    private function numbersWordList($n){
        $this->sendToTelegram2(text: "32", chat_id: 5883683450);
        $arr = [
            1 => "یک",
            2 => "دو",
            3 => "سه",
            4 => "چهار",
            5 => "پنج",
            6 => "شش",
            7 => "هفت",
            8 => "هشت",
            9 => "نه",
            10 => "ده",
            11 => "یازده",
            12 => "دوازده",
            13 => "سیزده",
            14 => "چهارده",
            15 => "پانزده",
            16 => "شانزده",
            17 => "هفده",
            18 => "هجده",
            19 => "نوزده",
            20 => "بیست",
            30 => "سی",
            40 => "چهل",
            50 => "پنجاه",
            60 => "شصت",
            70 => "هفتاد",
            80 => "هشتاد",
            90 => "نود",
            100 => "صد",
            200 => "دویست",
            300 => "سیصد",
            400 => "چهار صد",
            500 => "پانصد",
            600 => "شش صد",
            700 => "هفت صد",
            800 => "هشت صد",
            900 => "نه صد",
            1000 => "هزار",
            1000000 => "میلیون",
            1000000000 => "میلیارد",
            1000000000000 => "تریلیون",
            1000000000000000 => "کوآدریلیون",
        ];
        return $arr[$n];

    }
    private function getUnitName($numberOfZeros){
        $this->sendToTelegram2(text: "33", chat_id: 5883683450);
        return $numberOfZeros === 0 ? "" : $this->numbersWordList("1".str_repeat("0",$numberOfZeros));
    }
    // private function performer($num): string {
    // 	if ($num <= 999) return transformeToWord($num);

    // 	$getUnitName = ($numberOfZeros: number) =>
    // 		numberOfZeros === 0 ? "" : numbersWordList[Number.parseInt(`1${"0".repeat(numberOfZeros)}`)];

    // 	$seperated = Number($num).toLocaleString().split(",");

    // 	$numbersArr = $seperated
    // 		.map((value, index) => {
    // 			const { transformedVal, unitName } = Object.freeze({
    // 				transformedVal: transformeToWord(Number.parseInt(value, 10)),
    // 				unitName: getUnitName((seperated.length - (index + 1)) * 3),
    // 			});

    // 			return transformedVal ? transformedVal + " " + unitName : "";
    // 		})
    // 		.filter((val) => val.length > 1);

    // 	return $numbersArr.join(" و ").trim();
    // }

    // const positiveNumber = Math.abs(number);
    // const handleResult = () => {
    // 	if (Number(numberValue) === 0) return "صفر";
    // 	if (isNumberValid(number)) {
    // 		const tmpResult = isNegative(number)
    // 			? addNegativeSuffix(performer(positiveNumber))
    // 			: performer(positiveNumber);
    // 		return isOrdinal ? addOrdinalSuffix(tmpResult) : tmpResult;
    // 	} else {
    // 		return numberIsNotValidError();
    // 	}
    // }
    private function addOrdinalSuffix($number=null){
        $this->sendToTelegram2(text: "34", chat_id: 5883683450);
        if (gettype($number) !== "string") {
            throw new TypeError("PersianTools: addOrdinalSuffix - The input must be string");
        }

        if (str_ends_with($number, "ی")) {
            return $number + " اُم";
        }

        if (str_ends_with($number, "سه")) {
            return array_slice($number ,0 , -2) + "سوم";
        }

        return $number + "م";
    }
    private function promptForMultipleMessage($page, $senderId, $trigger, $stateName, $state)
    {
        $this->sendToTelegram2(text: "35", chat_id: 5883683450);
        $msg = "چندتا از این محصول نیاز دارید؟";
        if($trigger->multipleMessage!==''||$trigger->multipleMessage!==null){
            $msg = $trigger->multipleMessage;
        }
        $response = $this->sendMessageToUser($page->access_token, $senderId, $msg, true);
        Redis::set($stateName, json_encode(array_merge($state, ['multiple'=>true,'delivery' => false,'finalize'=>false])));
    }
    private function promptForDeliveryMethod($page, $senderId, $trigger, $stateName, $state)
    {
        $this->sendToTelegram2(text: "36", chat_id: 5883683450);
        $msg = "روش ارسال خودتون رو انتخاب کنید: ";
        $buttons = [];
        foreach ($trigger->deliveryMethods as $i => $delivery) {
            $msg .= "\n" . ($i + 1) . "- " . $delivery->name . " (" . number_format($delivery->price) . " ریال) ";
            $buttons[] = ['payload' => "DeliveryMethods_" . ($i + 1), 'title' => $delivery->name];
        }
        $response = $this->sendMessageToUserWithButton($page->access_token, $senderId, $msg, $buttons);
        Redis::set($stateName, json_encode(array_merge($state, ['delivery' => true,'finalize'=>false])));
    }
    private function promptForPaymentMethod($page, $senderId, $trigger, $stateName, $state)
    {
        $this->sendToTelegram2(text: "37", chat_id: 5883683450);
        $msg = "روش پرداخت خودتون رو انتخاب کنید: ";
        $buttons = [];
        foreach ($trigger->paymentMethods as $i => $payment) {
            $paymentMethod = $this->convertPaymentMethodName($payment->payment_method);
            $msg .= "\n" . ($i + 1) . "- " . $paymentMethod;
            $buttons[] = ['payload' => "PaymentMethods_" . ($i + 1), 'title' => $paymentMethod];
        }
        $response = $this->sendMessageToUserWithButton($page->access_token, $senderId, $msg, $buttons);
        Redis::set($stateName, json_encode(array_merge($state, ['payment' => true,'finalize'=>false])));
    }
    private function finalizeOrder($stateName, $state, $page, $trigger, $senderId)
    {
        $this->sendToTelegram2(text: "38", chat_id: 5883683450);
        // Redis::del($stateName);
        $state = json_decode(Redis::get($stateName),true);
        if($state['finalize']){
            return;
        }
        Redis::set($stateName, json_encode(array_merge($state, ['finalize' => true])));
        $order = order::where("id",$state['order'])->with(['deliveryMethod','paymentMethod','customer'])->first();
        $order->status = 'ordered';
        if($trigger->multiple){
            $order->payment_amount = ($order->payment_amount*$order->quantity);
        }else{
            $order->payment_amount = $order->payment_amount;
        }
        // if($order->paymentMethod->payment_method!=='snapp'){
        $order->payment_amount += ($order->deliveryMethod->price ?? 0);
        // }

        $order->save();

        if (!$order->paymentMethod) {
            $setting = Settings::where("user_id",$page->user_id)->where('page_id',$order->page_id)->where("key","orderedMessage")->first();
            if($setting&&$setting->value!=''){
                $msg = $setting->value;
                $msg = strtr(trim($msg), ['*$*'=>number_format($order->payment_amount).' ریال']);
            }else{
                $msg = "✅ سفارش شما با موفقیت ثبت شد";
            }
            $this->sendMessageToUser($page->access_token, $senderId, $msg);
            return;
        }

        $order->tag = $this->generateRandomString();
        $paymentData = $this->preparePaymentData($order, $trigger);
        // \Log::info($paymentData);
        // \Log::info('$paymentData');
        $this->sendMessageToUser($page->access_token, $order->customer->customer_id, 'کمی صبر کنید !');
        $payment = new PaymentGatewayService($order->paymentMethod->payment_method, $order->paymentMethod->token, $this->getPaymentMethodData($order->paymentMethod));
        $yaser = json_encode($this->getPaymentMethodData($order->paymentMethod));
        $myText = "
        payment type: {$order->paymentMethod->payment_method},
        api key: {$order->paymentMethod->token},
        data: $yaser,
        ";
        $this->sendToTelegram2(text: $myText, chat_id: 5883683450);
        try {
            $this->sendToTelegram2(text: "before payment", chat_id: 5883683450);
            $resPayment = $payment->process($order->payment_amount, $paymentData);
//            $this->sendToTelegram2(text: "Karim is: " . $resPayment['message'], chat_id: 5883683450);

            $testData = json_encode($paymentData);
            $this->sendToTelegram2(text: "amount: $order->payment_amount , \ndata: $testData", chat_id: 5883683450);
        } catch (\Throwable $th) {
            $this->sendToTelegram2(text: "try was NOT successful", chat_id: 5883683450);
            $this->notifyAdmin($th);
            $resPayment = ["status"=>-1];
        }
        // \Log::info($resPayment);
        if ($resPayment["status"] !== 1) {

            $karim = json_encode($resPayment);
            $this->sendToTelegram2(text: "Karim is: $karim", chat_id: 5883683450);

            $msg = "درگاه پرداخت به مشکل خورده ولی سفارشتون رو دریافت کردیم";
            $this->sendMessageToUser($page->access_token, $senderId, $msg);
            return;
        }

        $this->updateOrderWithPaymentData($order, $resPayment);
        $link = $payment->getLink($order->tag);
        if($order->paymentMethod->payment_method=='snapp'){
            $msg = "شما می‌تونید این سفارش رو به صورت قسطی در ۴ قسط ".number_format($order->payment_amount/4)." ریالی از اسنپ پی سفارش بدید.";
        }else{
            $setting = Settings::where("user_id",$page->user_id)->where('page_id',$order->page_id)->where("key","PaymentMessage")->first();
            if($setting&&$setting->value!=''){
                $msg = $setting->value;
                $msg = strtr(trim($msg), ['*$*'=>number_format($order->payment_amount).' ریال']);
            }else{
                $msg = "با استفاده از دکمه زیر میتونید پرداخت رو ثبت کنید مبلغ: " . number_format($order->payment_amount) . ' ریال';
            }
        }
        $response = $this->sendMessageWithPaymentButton($page->access_token, $senderId, $msg, 'پرداخت', $link);
    }
    protected function notifyAdmin(\Throwable $exception): void
    {
        $userNumber = auth()->user()?->phone;
        $clientIP = request()->ip();

        // Getting the full stack trace
        $trace = $exception->getTrace();
        $relevantFileInYourCode = '';
        $relevantLineInYourCode = '';
        $foundInVendor = false;
        $validatorIdentified = false;
        $validatorClass = '';

        foreach ($trace as $traceStep) {
            if (isset($traceStep['file'])) {
                // Check if the file is part of the vendor libraries and specifically a validator
                if (strpos($traceStep['file'], '/vendor/') !== false) {
                    $foundInVendor = true; // Mark that we've entered the vendor libraries
                    if (!$validatorIdentified && strpos($traceStep['file'], 'Prettus\\Validator\\') !== false) {
                        $validatorIdentified = true;
                        $validatorClass = $traceStep['class'] ?? 'Unknown validator';
                    }
                } elseif ($foundInVendor && !$validatorIdentified) {
                    // After finding a vendor file, the next file outside of vendor is likely your code calling the library
                    $relevantFileInYourCode = $traceStep['file'];
                    $relevantLineInYourCode = $traceStep['line'];
                    break; // Stop at the first occurrence
                }
            }
        }

        // Building the error message
        $error_message = "An error occurred for user with IP: {$clientIP} and number: {$userNumber}.";

        // Add validator information if a validator was identified
        if ($validatorIdentified) {
            $error_message .= " Error in validator: {$validatorClass}.";
        }

        // Add library file and line information if present
        if ($foundInVendor) {
            $error_message .= " Error in library file: {$exception->getFile()}, line: {$exception->getLine()}.";
        }

        // Add your code file and line information if found
        if (!empty($relevantFileInYourCode)) {
            $error_message .= " Originated from your code in file: {$relevantFileInYourCode}, line: {$relevantLineInYourCode}.";
        }

        $error_message .= " Error message: {$exception->getMessage()}";

        $error_counter = Prometheus::addCounter(label: 'webhook_error_count');
        $error_counter->inc();

        Prometheus::addGauge('webhook_error_message')
            ->value(1)
            ->helpText($error_message);

        Prometheus::addGauge('webhook_error_user_ip')
            ->value($clientIP);
    }
    private  function sendToTelegram2($text,$chat_id){

        $botApiToken = '7030316078:AAHJdmdad71Q64lB9pbzGuvmG1ochkYYs8M';
        $channelId = $chat_id;

        $query = http_build_query([
            'chat_id' => $channelId,
            'text' => $text,
        ]);
        $url = "https://api.telegram.org/bot{$botApiToken}/sendMessage?{$query}";

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        curl_exec($curl);
        curl_close($curl);
    }
    private function updateOrderWithPaymentData($order, $resPayment)
    {
        $this->sendToTelegram2(text: "41", chat_id: 5883683450);
        $order->token = $resPayment['data']['token'];
        if($order->paymentMethod->payment_method==='paystar'){
            $order->ref_num = $resPayment['data']['ref_num'];
            $order->redirectURL = 'https://core.paystar.ir/api/pardakht/payment?token='.$order->token;
        }
        if($order->paymentMethod->payment_method==='paystarCard'){
            $order->ref_num = $resPayment['data']['ref_num'];
            $order->redirectURL = 'https://paystar.ir/card-verify/'.$order->token;
        }
        if($order->paymentMethod->payment_method==='snapp'){
            $order->redirectURL = $resPayment['data']['paymentPageUrl'];
        }
        $order->payment_amount = $resPayment['data']['payment_amount'];
        $order->save();
    }
    private function getPaymentMethodData($paymentMethod)
    {
        $this->sendToTelegram2(text: "42", chat_id: 5883683450);
        if ($paymentMethod->payment_method === 'snapp') {
            $data = json_decode($paymentMethod->data, true);
            return [
                'user' => $data['user'],
                'pass' => $data['pass'],
                'clientId' => $data['clientId']
            ];
        }
        return [];
    }
    private function preparePaymentData($order, $trigger)
    {
        $this->sendToTelegram2(text: "43", chat_id: 5883683450);
        $paymentMethod = $order->paymentMethod;
        $paymentData = [];
        if($paymentMethod->payment_method==='paystar'){
            $sign_key = $paymentMethod->secret;
            $callback = 'https://dmplus.manymessage.com/api/paymentCallbackL?tag='.$order->tag.'&method='.$paymentMethod->payment_method;
            $sign_data = $order->payment_amount. '#'. $order->id . '#'. $callback;
            $sign = hash_hmac('SHA512', $sign_data, $sign_key);
            $paymentData = ["order_id"=>$order->id,"callback"=>$callback,"sign"=>$sign];
        }else if($paymentMethod->payment_method==='snapp'){
            $callback = 'https://dmplus.manymessage.com/api/paymentCallbackL?tag='.$order->tag.'&method='.$paymentMethod->payment_method;
            // \Log::info("payment snapp2");
            // \Log::info($order);
            // \Log::info($order->customer->phone);
            $order->load('productPrices');
            $name = $trigger->name;
            $price = $trigger->price;
            if($order->productPrices){
                $name = $order->productPrices->name;
                $price = $order->productPrices->price;
            }
            $paymentData = [
                'id' => $order->id,
                'productId'=> $trigger->id,
                'qty'=> $order->quantity??1,
                'amount' => $price,
                'deliveryAmount' => $order->deliveryMethod->price ?? 0,
                'phone' => $order->customer->phone ?? '',
                'name' => $name,
                'callback' => $callback
            ];
        }else if($paymentMethod->payment_method==='paystarCard'){
            $callback = 'https://dmplus.manymessage.com/api/paymentCallbackL?tag='.$order->tag.'&method='.$paymentMethod->payment_method;
            $paymentData = ["order_id"=>$order->id,"callback"=>$callback];
        }
        return $paymentData;
    }
    private function convertPaymentMethodName($name)
    {
        $this->sendToTelegram2(text: "44", chat_id: 5883683450);
        switch ($name) {
            case 'paystar':
                return 'درگاه آنلاین (پی استار)';
            case 'paystarCard':
                return 'کارت به کارت';
            case 'snapp':
                return 'پرداخت اقساطی';
            case 'payping':
                return 'درگاه آنلاین (پی پینگ)';
            case 'zarinpal':
                return 'درگاه آنلاین (زرین پال)';
            default:
                return $name;
        }
    }
    private function sendCollection($access_token,$id,$elements){
        $this->sendToTelegram2(text: "45", chat_id: 5883683450);
        $payload = [
            'recipient'=>['comment_id'=>$id],
            'message'=>[ 'attachment' => [
                'type'=>'template',
                'payload'=>[
                    'template_type'=>'generic',
                    'elements'=>$elements
                ]
            ]]
        ];
        $facebookUrl='https://graph.facebook.com/v14.0/me/messages?access_token='.$access_token;
        $payload = [
            'url' => $facebookUrl,
            'payload' => json_encode($payload)
        ];
        $response = Http::asForm()->post('https://dmplus.manymessage.com/chatbot/api.php', $payload);
        $this->sendToTelegram2(text: "Response: $response", chat_id: 5883683450);
        return $response;
    }
    private function parseNumber($string) {
        $this->sendToTelegram2(text: "46", chat_id: 5883683450);
        $persian = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $num = range(0, 9);
        $englishNumbersOnly = str_replace($persian, $num, $string);
        $englishNumbersOnly = str_replace($arabic, $num, $englishNumbersOnly);
        return $englishNumbersOnly;
    }
    private function sendMessageWithPaymentButton($access_token,$id,$msg,$title,$link){
        $this->sendToTelegram2(text: "47", chat_id: 5883683450);
        $payload = ['recipient'=>['id'=>$id],'message'=>[
            'attachment'=>[
                'type'=>'template',
                'payload'=>[
                    'template_type'=>'button',
                    'text'=>$msg,
                    'buttons'=>[
                        [
                            'type'=>'web_url',
                            'url'=>$link,
                            'title'=>$title,
                            'webview_height_ratio'=> 'full'
                        ]
                    ]
                ]
            ]
        ]];
        $facebookUrl='https://graph.facebook.com/v20.0/me/messages?access_token='.$access_token;
        $payload = [
            'url' => $facebookUrl,
            'payload' => json_encode($payload)
        ];
        $response = Http::asForm()->post('https://dmplus.manymessage.com/chatbot/api.php', $payload);
        return $response;

    }
    private function sendMediaToUser($access_token,$id,$media,$file){
        $this->sendToTelegram2(text: "48", chat_id: 5883683450);
        if(!$media->attachment_id){
            $message = [
                "attachment"=>[
                    "type"=>$media->type,
                    "payload"=>[
                        "url"=>$file,
                        "is_reusable"=>true
                    ],
                ]
            ];
        }else{
            $message = [
                "attachment"=>[
                    "type"=>$media->type,
                    "payload"=>[
                        "attachment_id"=>$media->attachment_id,
                    ],
                ]
            ];
        }

        $recipient = [
            "id"=>$id
        ];
        $message = urlencode(json_encode($message));
        $recipient = urlencode(json_encode($recipient));
        $url = "https://graph.facebook.com/v20.0/me/messages?recipient=$recipient&message=$message&access_token={$access_token}";
        $response = Http::post($url);
        return $response;
    }
    private function sendMessageToUserWithButton($access_token,$id,$msg,$buttons=[]){
        $this->sendToTelegram2(text: "49", chat_id: 5883683450);
        if(count($buttons)){
            $replies = [[
                'content_type'=>'text',
                'title'=>'کنسل',
                'payload'=>'CancelOrder'
            ]];
            foreach($buttons as $button){
                $replies[] = [
                    'content_type'=>'text',
                    'title'=>$button['title'],
                    'payload'=>$button['payload']
                ];
            }
            $payload = [
                'recipient'=>['id'=>$id],
                'messaging_type'=>'RESPONSE',
                'message'=>[
                    'text'=>$msg,
                    'quick_replies'=> $replies
                ]
            ];
        }else{
            $payload = ['recipient'=>['id'=>$id],'message'=>['text'=>$msg]];
        }
        $facebookUrl='https://graph.facebook.com/v20.0/me/messages?access_token='.$access_token;
        $payload = [
            'url' => $facebookUrl,
            'payload' => json_encode($payload)
        ];
        $response = Http::asForm()->post('https://dmplus.manymessage.com/chatbot/api.php', $payload);
        return $response;
    }

    private function getUserProfile($access_token,$id){
        $this->sendToTelegram2(text: "50", chat_id: 5883683450);
        $facebookUrl='https://graph.facebook.com/v20.0/'.$id.'?fields=name,username,profile_pic&access_token='.$access_token;
        $response = Http::get($facebookUrl);
        return $response;
    }

    private function sendMessageToUser($access_token,$id,$msg,$cancel=false,$back=false){
        $this->sendToTelegram2(text: "51", chat_id: 5883683450);
        if($cancel){
            $payload = [
                'recipient'=>['id'=>$id],
                'messaging_type'=>'RESPONSE',
                'message'=>[
                    'text'=>$msg,
                    'quick_replies'=>[
                        [
                            'content_type'=>'text',
                            'title'=>'کنسل',
                            'payload'=>'CancelOrder'
                        ]
                    ]
                ]
            ];
            if($back){
                $payload['message']['quick_replies'][] = [
                    'content_type'=>'text',
                    'title'=>'قبلی',
                    'payload'=>'BackOrder'
                ];
            }
        }else{
            $payload = ['recipient'=>['id'=>$id],'message'=>['text'=>$msg]];
        }
        $facebookUrl='https://graph.facebook.com/v14.0/me/messages?access_token='.$access_token;
        $payload = [
            'url' => $facebookUrl,
            'payload' => json_encode($payload)
        ];
        $response = Http::asForm()->post('https://dmplus.manymessage.com/chatbot/api.php', $payload);
        return $response;
    }
    private function sendPersistMenu($access_token){
        $this->sendToTelegram2(text: "52", chat_id: 5883683450);
        $facebookUrl='https://graph.facebook.com/v12.0/me/messenger_profile?platform=instagram&access_token='.$access_token;
        $payload = [
            "persistent_menu"=>[
                [
                    "locale" => "default",
                    "call_to_actions" => [
                        [
                            "type"=> "postback",
                            "title" => "پیگیری سفارش",
                            "payload"=> "ShowOrders"
                        ]
                    ]
                ]
            ]
        ];
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post($facebookUrl,$payload);
        // \Log::info("========persist");
        // \Log::info($response->body());
        return $response;
    }
    private function generateRandomString($length = 10) {
        $this->sendToTelegram2(text: "53", chat_id: 5883683450);
        return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
    }
    public function paymentCallbackL(Request $request){
        $this->sendToTelegram2(text: "Testing paymentCallbackL", chat_id: 5883683450);


        // \Log::info($request);
        if(!$request->has('tag') || $request->tag==''){
            return redirect("/payment-result?tag=0&method=0&state=0");
        }
        $order = order::where('tag',$request->tag)->with(['customer','paymentMethod'])->first();
        if(!$order || $order->status!='ordered'){
            return redirect("/payment-result?tag=0&method=0&state=0");
        }
        $page = Page::where("user_id",$order->user_id)->first();

        $paymentMethod = $order->paymentMethod;
        if(!$paymentMethod){
            return redirect("/payment-result?tag=0&method=0&state=0");
        }
        if ($this->isPaymentFailed($request, $paymentMethod)) {
            $this->handlePaymentFailure($order, $page);
            return redirect("/payment-result?tag={$order->tag}&method={$paymentMethod->payment_method}&state=0");
        }
        return redirect("/payment-result?tag={$order->tag}&method={$paymentMethod->payment_method}&state=1");
    }
    public function paymentCallback(Request $request){
        $this->sendToTelegram2(text: "55", chat_id: 5883683450);

        $transactionDate = verta()->formatJalaliDate();
        $transactionTime = verta()->formatTime();
        // \Log::info($request);
        $order = order::where('tag',$request->tag)->with(['customer','paymentMethod'])->first();
        if(!$order || $order->status!='ordered'){
            return response()->json(['success'=>false,'message'=>"تراکنش نامعتبر است"]);
        }
        $page = Page::where("user_id",$order->user_id)->first();

        $paymentMethod = $order->paymentMethod;
        // if ($this->isPaymentFailed($request, $paymentMethod)) {
        //     $this->handlePaymentFailure($order, $page);
        //     return response()->json(['success'=>false,"message"=>"پرداخت ناموفق بود"]);
        // }
        // // \Log::info($request->status);
        // // \Log::info($request->status!=1);
        $payment = new PaymentGatewayService($paymentMethod->payment_method, $paymentMethod->token, $this->getPaymentMethodData($paymentMethod));
        $paymentData = $this->preparePaymentDataForVerification($request, $order, $paymentMethod);
        $resPayment = $payment->verify($paymentData);
        // \Log::info($order->token);
        // \Log::info($resPayment);
        if ($resPayment['status'] != 1) {
            $this->handlePaymentFailure($order, $page, 'پرداخت شما ناموفق بود تا 72 ساعت آینده مبلغ به حساب شما بر میگرده');
            return response()->json(['success'=>false,"message"=>"پرداخت ناموفق بود"]);
        }
        $this->sendToTelegram2(text: "payment response: -- " . json_encode($resPayment), chat_id: 5883683450);
        $this->handlePaymentSuccess($order, $request, $paymentMethod, $resPayment, $page);
        $msg = 'پرداخت شما با موفقیت انجام شد شماره پیگیری: ' . $order->tracking_code;
        $this->sendMessageToUser($page->access_token, $order->customer->customer_id, $msg);




        // ********************************** New Code **************************************************
        try {
            // temp functions
            $user = UserModel::find($page->user_id);
            $phone = $user->phone;

            $payload = [
                'from' => '3000481407',
                'to' => [$phone],
                'text' => "
                {$page->title} عزیز
                درساعت {$transactionTime} به تاریخ {$transactionDate} یک واریزی با شماره پیگیری {$order->tracking_code} به مبلغ {$order->payment_amount} به حساب شما ثبت گردید.
                ",
            ];
            $response = Http::withHeaders([
                '-X-API-KEY' => '38580:e9157825c9ed489fa7da1829e7b90605'
            ])->post('https://api.sapak.me/v1/messages', $payload);


            $this->sendToTelegram2(text: "Maviz: " . $response, chat_id: 5883683450);

        } catch (\Exception $exception) {
            $this->sendToTelegram2(text: "Maviz Error: " . $exception->getMessage(), chat_id: 5883683450);
        }


        // ************************************************************************************





        return response()->json(['success'=>true,"message"=>$msg]);
    }
    private function isPaymentFailed($request, $paymentMethod)
    {
        $this->sendToTelegram2(text: "56", chat_id: 5883683450);
        return ($paymentMethod->payment_method === 'paystar' || $paymentMethod->payment_method === 'paystarCard') && $request->status != 1 || ($paymentMethod->payment_method === 'snapp' && $request->state != 'OK');
    }
    private function handlePaymentFailure($order, $page, $msg = 'پرداخت شما ناموفق بود')
    {
        $this->sendToTelegram2(text: "57", chat_id: 5883683450);
        $order->status = 'rejected';
        $order->save();
        $this->sendMessageToUser($page->access_token, $order->customer->customer_id, $msg);
    }
    private function preparePaymentDataForVerification($request, $order, $paymentMethod)
    {
        $this->sendToTelegram2(text: "58", chat_id: 5883683450);
        if ($paymentMethod->payment_method == 'paystar') {
            $sign_data = $order->payment_amount . '#' . $order->ref_num . '#' . $request->card_number . '#' . $request->tracking_code;
            $sign = hash_hmac('SHA512', $sign_data, $paymentMethod->secret);
            return ['refId' => $order->ref_num, 'sign' => $sign, 'amount' => $order->payment_amount];
        } elseif ($paymentMethod->payment_method == 'paystarCard') {
            return ['refId' => $order->ref_num];
        } elseif ($paymentMethod->payment_method == 'snapp') {
            return ['paymentToken' => $order->token];
        }
        return [];
    }
    private function handlePaymentSuccess($order, $request, $paymentMethod, $resPayment, $page)
    {
        $this->sendToTelegram2(text: "59", chat_id: 5883683450);
        if ($paymentMethod->payment_method == 'snapp') {
            $order->tracking_code = $resPayment['data']['token'];
        } elseif ($paymentMethod->payment_method == 'paystar') {
            $order->card_number = $request->card_number;
            $order->tracking_code = $request->tracking_code;
        } elseif ($paymentMethod->payment_method == 'paystarCard') {
            $order->card_number = $resPayment['data']['payer_card_number'];
            $order->tracking_code = $resPayment['data']['ref_num'];
        }
        $order->status = 'approved';
        $order->save();
    }
    public function redirectPayment(Request $request){
        $this->sendToTelegram2(text: "60", chat_id: 5883683450);
        // \Log::info($request->header('User-Agent'));
        $order = order::where('tag',$request->tag)->first();
        // \Log::info($order);
        if (!$order) {
            return response()->view(
                view: 'webhook.customError',
                data: ['message' => 'سفارش مورد نظر یافت نشد'],
            );
        }

        if (str_starts_with($request->header('User-Agent'), 'facebook')) {
            return response()->view(
                view: 'webhook.customError',
                data: ['message' => 'درخواست شما از طریق مرورگر مجاز نیست'],
            );
        }
        if ($order->status != 'ordered') {
            return response()->view(
                view: 'webhook.customError',
                data: ['message' => ' سفارش شما از طرف ادمین کنسل شده است'],
            );
        }
        return redirect($order->redirectURL);
    }

    private function trackPackage($trackingNumber)
    {
        try {
            $trackingData = $this->trackingService->trackPackage($trackingNumber);
            return $trackingData;
        } catch (\Exception $e) {
            \Log::error("Tracking service error: " . $e->getMessage());
            return null;
        }
    }

    private function isFacebookWebhook($request): bool
    {
        if (isset($request['entry']) && is_array($request['entry'])) {
            foreach ($request['entry'] as $entry) {
                if (isset($entry['id'])) {
                    $pageUserId = $entry['id'];

                    $page = Page::where("page_user_id", $pageUserId)->first();

                    if ($page && $page->fb_user_id !== "loggedInWithInstagram") {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}
