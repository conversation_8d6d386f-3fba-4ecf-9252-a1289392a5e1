<?php

namespace App\Http\Controllers\Monitoring;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Prometheus\Facades\Prometheus;

class PrometheusController extends Controller
{
    public function metrics()
    {
        $metrics = Prometheus::renderCollectors();

        return response($metrics)
            ->header('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
    }
}
