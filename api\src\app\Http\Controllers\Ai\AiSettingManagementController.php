<?php

namespace App\Http\Controllers\Ai;

use App\Http\Requests\Ai\AiSettingRequest;
use App\Services\Ai\AiSettingService;
use Illuminate\Http\JsonResponse;

class AiSettingManagementController extends AbstractAiSettingController
{
    public function __construct(AiSettingService $aiSettingService)
    {
        parent::__construct($aiSettingService);
    }

    public function store(AiSettingRequest $request): JsonResponse
    {
        $this->authUser = auth()->user();
        $data = $request->validated();
        $data['user_id'] = $this->authUser->id;

        $validationResult = $this->aiSettingService->checkActionLimits($data, 'create');
        if (!$validationResult['success']) {
            return response()->json([
                'success' => false,
                'message' => $validationResult['message'],
            ], 400);
        }

        $aiSettings = $this->aiSettingService->createAiSetting($data);

        return response()->json([
            'success' => true,
            'data'    => $aiSettings,
        ], 201);
    }

    public function update(int $id, AiSettingRequest $request): JsonResponse
    {
        $this->authUser = auth()->user();
        $data = $request->validated();
        $data['user_id'] = $this->authUser->id;

        $aiSetting = $this->aiSettingService->getAiSetting($id);
        if (!$aiSetting) {
            return response()->json([
                'success' => false,
                'message' => 'AI settings do not exist for this ID.',
            ], 404);
        }

        if ($aiSetting->post_id != $data['post_id']) {
            $data['previous_post_id'] = $aiSetting->post_id;
            $validationResult = $this->aiSettingService->checkActionLimits($data, 'update');
            unset($data['previous_post_id']);
            if (!$validationResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $validationResult['message'],
                ], 400);
            }
        }

        $aiSettings = $this->aiSettingService->updateAiSetting($id, $data);

        return response()->json([
            'success' => true,
            'data'    => $aiSettings,
        ], 200);
    }

    public function delete(int $id): JsonResponse
    {
        $this->authUser = auth()->user();

        $aiSetting = $this->aiSettingService->getAiSetting($id);
        if (!$aiSetting) {
            return response()->json([
                'success' => false,
                'message' => 'AI settings do not exist for this ID.',
            ], 404);
        }

        if ($aiSetting->user_id != $this->authUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'You are not allowed to delete this setting.',
            ]);
        }

        $this->aiSettingService->removeAiSetting($id);
        return response()->json([
            'success' => true,
            'message' => 'AI Setting successfully deleted.',
        ]);
    }
}
