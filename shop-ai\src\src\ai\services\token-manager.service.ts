import { Injectable } from '@nestjs/common';
import { DirectamApiService } from '../../directam-api/directam-api.service';

@Injectable()
export class TokenManagerService {
  /**
   * Cache to store the tokens associated with each adminId.
   * The Map stores adminId as the key and the associated token as the value.
   */
  private tokenCache: Map<string, string> = new Map();

  constructor(private readonly directamApiService: DirectamApiService) {}

  /**
   * Retrieves the API token for the specified adminId.
   * If the token is already cached, it is returned from the cache.
   * If the token is not cached, it will be fetched using the DirectamApiService.
   * If no token is found, the default token from environment variables is returned.
   *
   * @param {string} adminId - The adminId for which the token is requested
   * @returns {Promise<string>} - The token associated with the given adminId
   * @throws {Error} - Throws an error if the token could not be fetched or if
   * both the cache and API call return null
   */
  public async getToken(adminId: string): Promise<string> {
    if (this.tokenCache.has(adminId)) {
      return this.tokenCache.get(adminId)!;
    }
    let token: string | null =
      await this.directamApiService.fetchOpenAiToken(adminId);
    if (token == null) token = process.env.OPENAI_API_KEY!;
    this.tokenCache.set(adminId, token);

    return token;
  }
}
