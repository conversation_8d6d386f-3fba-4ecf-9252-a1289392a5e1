#!/bin/bash

REPO_DIR="/root/shop_backend"
BRANCH="hosein"
SERVICE_NAME="frankenphp"
CONTAINER_NAME="laravel"

handle_error() {
    echo "Error during deployment. Exiting."
    exit 1
}

echo "Going to Located Repository Directory ..."
cd "$REPO_DIR" || handle_error

echo "Checking out branch $BRANCH and pulling the latest changes..."
git checkout "$BRANCH" || handle_error
git pull origin "$BRANCH" || handle_error

echo "Restarting the Back-End Docker service..."
docker compose stop "$SERVICE_NAME" || handle_error
docker compose up -d "$SERVICE_NAME" || handle_error

echo "Clearing Laravel caches..."
docker exec "$CONTAINER_NAME" php artisan optimize:clear || handle_error

echo "Production deployment complete!"
