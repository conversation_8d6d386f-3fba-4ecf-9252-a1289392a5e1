import{_ as me,a as ce,b as fe,c as _e,d as ve,e as pe}from"./MediaComponent-21dbcad4.js";import{_ as ge}from"./CurrencyInput-8fbc55d3.js";import{V as Ve,_ as ye}from"./VSkeletonLoader-4c44fbcf.js";import{_ as be}from"./DialogCloseBtn-b32209d9.js";import{r as i,o as m,c as _,b as e,e as a,d,aj as D,n as o,a9 as B,af as he,F as x,i as E,f as z,ai as R,aK as xe,aa as O,a4 as q,v as g,x as w,a6 as V,ak as K,ad as ke,al as Q,a2 as Ce,aM as we}from"./index-169996dc.js";import{E as G}from"./endpoints-454f23f6.js";import{r as H,v as Pe}from"./validations-4c0aab88.js";import{r as $e}from"./MessageBox-e9a5b0b1.js";import{V as S,a as P}from"./VRow-6c1d54f3.js";import{V as Ue}from"./VContainer-63fa55f4.js";import{V as Me}from"./VStepper-57999623.js";import{V as De}from"./VForm-a73b6b87.js";import{V as Be}from"./VTextField-a8984053.js";import{V as J}from"./filter-09e553ed.js";import{V as Ee}from"./VTextarea-1c13d440.js";import{V as Re,a as Se,b as Te,c as Ae}from"./VExpansionPanel-909a4c05.js";import{V as Fe}from"./VDivider-12bfa926.js";const Ie={class:"d-flex"},Le={class:"d-flex align-center justify-center fill-height"},Ne={class:"d-flex justify-space-between mt-4"},je={class:"mt-4"},ze={class:"d-flex flex-column flex-md-row gap-4"},Oe={key:0,class:""},qe={"space-between":"30",pagination:"true",style:{width:"200px"},navigation:"true",effect:"fade",class:"h-100 mx-auto","events-prefix":"swiper-"},Ke={key:1,class:"mx-auto"},Qe={class:"d-flex flex-column w-100 gap-y-4"},Ge={class:"d-flex flex-column flex-md-row gap-2"},He={class:"mt-5"},Je={class:"rounded border-trigger pa-4"},We=d("div",{class:"font-weight-bold"}," اضافه کردن عکس، ویس، فیلم و توضیحات بیشتر برای محصول ",-1),Xe={class:"d-flex flex-wrap gap-3 pb-5 overflow-visible"},vl={__name:"AddProductDialog",props:{refresh:Function,templates:{type:Array,default:[]}},setup(W){const T=W,X=i(),v=i(!1),y=i(!0),c=i(!1),f=i(!1),$=i([]),r=i(null),k=i(),A=i(),F=i(),I=i(),L=i(),b=i(1),U=i(),N=i(null),p=i(!1),C=i(!1),Y=async()=>{try{f.value=!0;const{data:n}=await Q.get(G.pages+"/"+k.value+"/contents");$.value=n.data}catch{we({text:"خطا در دریافت اطلاعات",icon:"error"})}finally{f.value=!1}},Z=()=>{v.value=!0,Y()},ee=n=>{v.value=!1,c.value=!0,r.value=n},le=()=>{var n;(n=A.value)==null||n.validate().then(({valid:l})=>{l&&ae()})},ae=async()=>{var n,l,h;try{f.value=!0;const u=await Q.post(G.products,{name:r.value.name,description:r.value.body,post_id:r.value.id,media_url:r.value.media_url,media_type:r.value.media_type,triggers:L.value.items,price:r.value.price,triggers_mode:y.value?"include":"equal",delivery_methods:F.value.selectedDeliveriesCheckBox,payment_methods:I.value.selectedPaymentCheckBox,page_id:k.value,multiple:p.value,multipleMessage:(n=U.value)==null?void 0:n.message});N.value=(l=u==null?void 0:u.data)==null?void 0:l.id,b.value++,await T.refresh()}catch(u){console.log(u,"errror"),await Pe((h=u==null?void 0:u.data)==null?void 0:h.message)}finally{f.value=!1}},te=async()=>{k.value=Ce("pageId").value},se=()=>{var n,l;(l=(n=U.value)==null?void 0:n.message)!=null&&l.length?p.value=!0:p.value=!1},oe=n=>{p.value=n,n&&(C.value=!0)};return te(),$e(),(n,l)=>{const h=be,u=ye,ne=ge,ie=fe,re=_e,de=ve,ue=pe;return m(),_(x,null,[e(K,{modelValue:o(v),"onUpdate:modelValue":l[1]||(l[1]=t=>V(v)?v.value=t:null),width:"800"},{activator:a(({props:t})=>[d("div",Ie,[e(D,{icon:"tabler-plus",size:"x-large",class:"add-product",disabled:!o(k),style:{"font-size":"2rem"},onClick:Z},null,8,["disabled"])])]),default:a(()=>[e(h,{onClick:l[0]||(l[0]=t=>v.value=!o(v))}),e(B,{title:"پست اینستاگرامی محصول خود را انتخاب کنید"},{default:a(()=>[e(he,null,{default:a(()=>[e(S,null,{default:a(()=>{var t;return[o(f)?(m(),_(x,{key:0},E(9,s=>e(P,{key:s,cols:"12",md:"4"},{default:a(()=>[e(Ve,{type:"card-avatar"})]),_:2},1024)),64)):(m(),_(x,{key:1},[(t=o($))!=null&&t.length?(m(!0),_(x,{key:0},E(o($),s=>(m(),z(P,{key:s.id,cols:"12",md:"4"},{default:a(()=>[e(B,{border:"",class:"w-100 cursor-pointer",onClick:M=>ee(s)},{default:a(()=>[e(R,{height:"180",cover:"","lazy-src":"https://picsum.photos/id/11/100/60",src:(s==null?void 0:s.media_type)==="CAROUSEL_ALBUM"?s==null?void 0:s.media_url[0]:s==null?void 0:s.media_url},{placeholder:a(()=>[d("div",Le,[e(xe,{color:"grey-lighten-4",indeterminate:""})])]),_:2},1032,["src"]),e(O,null,{default:a(()=>{var M,j;return[d("div",Ne,[d("div",null,[e(q,{icon:"tabler-heart",color:"red"}),g(" "+w(s==null?void 0:s.like_count),1)]),d("div",null,[e(q,{icon:"tabler-message-circle",color:"yellow"}),g(" "+w(s==null?void 0:s.comments_count),1)])]),d("p",je,w(((M=s==null?void 0:s.caption)==null?void 0:M.length)>30?((j=s==null?void 0:s.caption)==null?void 0:j.substr(0,30))+"...":s==null?void 0:s.caption),1)]}),_:2},1024)]),_:2},1032,["onClick"])]),_:2},1024))),128)):(m(),z(Ue,{key:1},{default:a(()=>[e(S,null,{default:a(()=>[e(u)]),_:1})]),_:1}))],64))]}),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(K,{modelValue:o(c),"onUpdate:modelValue":l[11]||(l[11]=t=>V(c)?c.value=t:null),width:"900"},{default:a(()=>[e(h,{onClick:l[2]||(l[2]=t=>c.value=!o(c))}),e(Me,{modelValue:o(b),"onUpdate:modelValue":l[10]||(l[10]=t=>V(b)?b.value=t:null),items:["اطلاعات محصول","سوالات"],"hide-actions":"","alt-labels":"","non-linear":"",dir:"rtl"},{"item.1":a(()=>[e(De,{ref_key:"formRef",ref:A,onSubmit:ke(le,["prevent"])},{default:a(()=>[e(B,{elevation:"0"},{default:a(()=>[e(O,null,{default:a(()=>[d("div",ze,[o(r).media_type==="CAROUSEL_ALBUM"?(m(),_("div",Oe,[d("swiper-container",qe,[(m(!0),_(x,null,E(o(r).media_url,t=>(m(),_("swiper-slide",{key:t},[e(R,{width:"200",height:"240",cover:"",class:"h-100",rounded:"",src:t},null,8,["src"])]))),128))])])):(m(),_("div",Ke,[e(R,{width:"200",height:"240",cover:"",rounded:"",src:o(r).media_url,class:"h-100"},null,8,["src"])])),d("div",Qe,[d("div",Ge,[e(S,null,{default:a(()=>[e(P,{cols:"12",md:"6"},{default:a(()=>[e(Be,{modelValue:o(r).name,"onUpdate:modelValue":l[3]||(l[3]=t=>o(r).name=t),rules:o(H)("نام"),variant:"outlined",label:"نام محصول",placeholder:"نام محصول شما (مثال: دوچرخه برقی...)",dir:"rtl",class:"mt-5"},null,8,["modelValue","rules"])]),_:1}),e(P,{cols:"12",md:"6"},{default:a(()=>[d("div",He,[e(ne,{modelValue:o(r).price,"onUpdate:modelValue":l[4]||(l[4]=t=>o(r).price=t),options:{currency:"IRR",currencyDisplay:"hidden"}},null,8,["modelValue"])])]),_:1})]),_:1})]),d("div",Je,[e(J,{modelValue:o(y),"onUpdate:modelValue":l[5]||(l[5]=t=>V(y)?y.value=t:null),class:"w-auto mb-1"},{label:a(()=>[g(w(o(y)?"شامل کلمه های فعال کننده":"فقط کلمه های فعال کننده"),1)]),_:1},8,["modelValue"]),e(ie,{ref_key:"refTriggers",ref:L,label:"فعال کنندها"},null,512)]),e(Ee,{modelValue:o(r).body,"onUpdate:modelValue":l[6]||(l[6]=t=>o(r).body=t),rules:o(H)("توضیحات"),label:"توضیحات",placeholder:"توضیحات را وارد کنید...",class:"mt-2"},null,8,["modelValue","rules"]),e(me,{ref_key:"deliveries",ref:F},null,512),e(re,{ref_key:"payments",ref:I},null,512),e(J,{modelValue:o(p),"onUpdate:modelValue":[l[7]||(l[7]=t=>V(p)?p.value=t:null),oe],class:"w-auto mt-5"},{label:a(()=>[g(" گرفتن تعداد سفارش کاربر ")]),_:1},8,["modelValue"])])]),e(Re,{class:"mt-5"},{default:a(()=>[e(Se,null,{default:a(()=>[e(Te,null,{default:a(()=>[We]),_:1}),e(Ae,null,{default:a(()=>[e(ce,{ref_key:"childComponentRef",ref:X,templates:T.templates},null,8,["templates"])]),_:1})]),_:1})]),_:1}),e(Fe,{class:"my-5"}),d("div",Xe,[e(D,{loading:o(f),disabled:o(f),type:"submit"},{default:a(()=>[g(" ذخیره ")]),_:1},8,["loading","disabled"]),e(D,{color:"secondary",variant:"tonal",disabled:o(f),onClick:l[8]||(l[8]=t=>c.value=!1)},{default:a(()=>[g(" بستن ")]),_:1},8,["disabled"])])]),_:1})]),_:1})]),_:1},512)]),"item.2":a(()=>[e(de,{"product-id":o(N),onClose:l[9]||(l[9]=t=>(c.value=!1,b.value=1))},null,8,["product-id"])]),_:1},8,["modelValue"])]),_:1},8,["modelValue"]),e(ue,{ref_key:"refMultiProduct",ref:U,visible:o(C),"onUpdate:visible":l[12]||(l[12]=t=>V(C)?C.value=t:null),onClose:se},null,8,["visible"])],64)}}};export{vl as _};
