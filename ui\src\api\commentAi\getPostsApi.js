// src/services/AIService.ts
import { $api } from "@/utils/api";

class AIPostService {
  /**
   * Fetches Posts data from the server
   * @returns The array of user's instagram posts
   * @throws Error if the request fails
   */
  async fetchData(page_cursor) {
    const endpoint = page_cursor
      ? `/instagram-app/posts?page_cursor=${page_cursor}`
      : "/instagram-app/posts"; 
    const res = await $api(endpoint);
    if (res.data.success) return res.data;
    if (res.status === 404){
        throw new Error("Failed to fetch Instagram Posts");
    };
   
  }
}

// Export a singleton instance
export const aiPostService = new AIPostService();
