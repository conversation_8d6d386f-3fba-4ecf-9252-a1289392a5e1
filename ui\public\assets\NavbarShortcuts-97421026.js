import{P as f}from"./vue3-perfect-scrollbar.esm-e1f82a0e.js";import{k as h,a as g,o,f as r,e,b as t,a4 as n,a9 as V,aa as x,ab as y,v,n as d,c as C,F as S,i as k,y as A,ac as I,d as p,x as m}from"./index-169996dc.js";import{V as B}from"./VMenu-2cfb0f14.js";import{V as w}from"./VDivider-12bfa926.js";import{V as D,a as M}from"./VRow-6c1d54f3.js";const N={class:"text-base font-weight-medium mt-2 mb-0"},P={class:"text-sm"},z={__name:"Shortcuts",props:{togglerIcon:{type:String,required:!1,default:"tabler-layout-grid-add"},shortcuts:{type:Array,required:!0}},setup(l){const s=l,c=h();return(_,i)=>{const u=g("IconBtn");return o(),r(u,null,{default:e(()=>[t(n,{size:"26",icon:s.togglerIcon},null,8,["icon"]),t(B,{activator:"parent",offset:"14px",location:"bottom end"},{default:e(()=>[t(V,{width:"340","max-height":"560",class:"d-flex flex-column"},{default:e(()=>[t(x,{class:"py-4"},{append:e(()=>[t(u,null,{default:e(()=>[t(n,{icon:"tabler-layout-grid-add"})]),_:1})]),default:e(()=>[t(y,null,{default:e(()=>[v("Shortcuts")]),_:1})]),_:1}),t(w),t(d(f),{options:{wheelPropagation:!1}},{default:e(()=>[t(D,{class:"ma-0 mt-n1"},{default:e(()=>[(o(!0),C(S,null,k(s.shortcuts,(a,b)=>(o(),r(M,{key:a.title,cols:"6",class:A(["text-center border-t cursor-pointer pa-4 shortcut-icon",(b+1)%2?"border-e":""]),onClick:R=>d(c).push(a.to)},{default:e(()=>[t(I,{variant:"tonal",size:"48"},{default:e(()=>[t(n,{icon:a.icon},null,8,["icon"])]),_:2},1024),p("h6",N,m(a.title),1),p("span",P,m(a.subtitle),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})}}},E={__name:"NavbarShortcuts",setup(l){const s=[{icon:"tabler-calendar",title:"Calendar",subtitle:"Appointments",to:{name:"apps-calendar"}},{icon:"tabler-file",title:"Invoice App",subtitle:"Manage Accounts",to:{name:"apps-invoice-list"}},{icon:"tabler-user",title:"Users",subtitle:"Manage Users",to:{name:"apps-user-list"}},{icon:"tabler-lock",title:"Role Management",subtitle:"Permission",to:{name:"apps-roles"}},{icon:"tabler-layout-dashboard",title:"Dashboard",subtitle:"Dashboard Analytics",to:{name:"dashboards-analytics"}},{icon:"tabler-settings",title:"Settings",subtitle:"Account Settings",to:{name:"pages-account-settings-tab",params:{tab:"account"}}}];return(c,_)=>{const i=z;return o(),r(i,{shortcuts:s})}}};export{E as default};
