<?php

namespace App\Http\Controllers\Ai;

use App\Models\User;
use App\Services\Ai\AiSettingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AiSettingAdjustmentController extends AbstractAiSettingController
{
    public function __construct(AiSettingService $aiSettingService)
    {
        parent::__construct($aiSettingService);
    }

    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'number'         => 'required|string|exists:users,phone',
            'comments'       => 'nullable|integer|min:0',
            'editable_posts' => 'nullable|integer|min:0',
        ]);

        $user = User::where('phone', $request->number)->firstOrFail();

        $aiSettings = $this->aiSettingService->getActiveUserAiSettings($user->id);

        if ($aiSettings->isEmpty()) {
            return response()->json(['message' => 'No active AI settings found for this user.'], 404);
        }

        $updated = [];

        foreach ($aiSettings as $setting) {
            $data = [];

            if ($request->has('comments')) {
                $data['remaining_comments'] = $request->comments;
            }

            if ($request->has('editable_posts')) {
                $data['remaining_edit_posts'] = $request->editable_posts;
            }

            if (!empty($data)) {
                $this->aiSettingService->updateAiSetting($setting->id, $data);
                $updated[] = $setting->id;
            }
        }

        return response()->json([
            'message'     => 'AI settings updated successfully.',
            'updated_ids' => $updated,
        ]);
    }
}
