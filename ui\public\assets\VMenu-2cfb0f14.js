import{ap as T,bg as M,bx as A,bV as O,as as S,b6 as I,b7 as K,by as U,a8 as b,r as N,V as R,bW as y,aV as F,P as L,bX as $,w as j,s as V,at as z,bz as g,b as w,bc as W,bi as X,aN as Y,bY as E,bZ as Z,bT as P}from"./index-169996dc.js";const q=T({id:String,...M(A({closeDelay:250,closeOnContentClick:!0,locationStrategy:"connected",openDelay:300,scrim:!1,scrollStrategy:"reposition",transition:{component:O}}),["absolute"])},"VMenu"),H=S()({name:"VMenu",props:q(),emits:{"update:modelValue":o=>!0},setup(o,p){let{slots:i}=p;const l=I(o,"modelValue"),{scopeId:C}=K(),k=U(),v=b(()=>o.id||`v-menu-${k}`),s=N(),t=R(y,null),c=F(0);L(y,{register(){++c.value},unregister(){--c.value},closeParents(e){setTimeout(()=>{!c.value&&(e==null||e&&!$(e,s.value.contentEl))&&(l.value=!1,t==null||t.closeParents())},40)}});async function f(e){var u,r,d;const n=e.relatedTarget,a=e.target;await Y(),l.value&&n!==a&&((u=s.value)!=null&&u.contentEl)&&((r=s.value)!=null&&r.globalTop)&&![document,s.value.contentEl].includes(a)&&!s.value.contentEl.contains(a)&&((d=E(s.value.contentEl)[0])==null||d.focus())}j(l,e=>{e?(t==null||t.register(),document.addEventListener("focusin",f,{once:!0})):(t==null||t.unregister(),document.removeEventListener("focusin",f))});function x(e){t==null||t.closeParents(e)}function D(e){var n,a,u;o.disabled||(e.key==="Tab"||e.key==="Enter"&&!o.closeOnContentClick?(e.key==="Enter"&&e.preventDefault(),Z(E((n=s.value)==null?void 0:n.contentEl,!1),e.shiftKey?"prev":"next",d=>d.tabIndex>=0)||(l.value=!1,(u=(a=s.value)==null?void 0:a.activatorEl)==null||u.focus())):["Enter"," "].includes(e.key)&&o.closeOnContentClick&&(l.value=!1,t==null||t.closeParents()))}function m(e){var a;if(o.disabled)return;const n=(a=s.value)==null?void 0:a.contentEl;n&&l.value?e.key==="ArrowDown"?(e.preventDefault(),P(n,"next")):e.key==="ArrowUp"&&(e.preventDefault(),P(n,"prev")):["ArrowDown","ArrowUp"].includes(e.key)&&(l.value=!0,e.preventDefault(),setTimeout(()=>setTimeout(()=>m(e))))}const h=b(()=>V({"aria-haspopup":"menu","aria-expanded":String(l.value),"aria-owns":v.value,onKeydown:m},o.activatorProps));return z(()=>{const e=g.filterProps(o);return w(g,V({ref:s,id:v.value,class:["v-menu",o.class],style:o.style},e,{modelValue:l.value,"onUpdate:modelValue":n=>l.value=n,absolute:!0,activatorProps:h.value,"onClick:outside":x,onKeydown:D},C),{activator:i.activator,default:function(){for(var n=arguments.length,a=new Array(n),u=0;u<n;u++)a[u]=arguments[u];return w(W,{root:"VMenu"},{default:()=>{var r;return[(r=i.default)==null?void 0:r.call(i,...a)]}})}})}),X({id:v,ΨopenChildren:c},s)}});export{H as V};
