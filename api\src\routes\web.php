<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Monitoring\PrometheusController;
use App\Http\Controllers\Instagram\InstagramOAuthController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/instalogin/instagramBack.php', [InstagramOAuthController::class, 'callback'])
    ->name('instagram.callback');

/*
* Prometheus Service route for Monitoring
*/
Route::get('/metrics', [PrometheusController::class, 'metrics']);

//Route::get('/', function () {
//    return view('app');
//})->name('welcome');
//Route::get('/login', function () {
//    return view('welcome');
//})->name('login');
Route::get('/{vue_capture?}', function () {
    return view('app');
})->where('vue_capture', '[\/\w\.-]*');
