import{M as dn,a8 as le,aT as fn,n as X,r as hn,cS as pn,N as vn,aR as gt,w as gn,aO as mn,H as bn,aN as yn,a6 as et,ap as qe,aq as Je,cn as wn,ar as Vt,ax as En,as as Qe,co as Sn,b1 as xn,bb as Yt,a1 as me,at as Ze,b as U,cv as Xt,V as jt,cw as Dn,K as Lt,L as _n,au as Et,bK as Tn,az as Ht,bP as Cn,a4 as Pn,a_ as On,cr as In,aw as An,ct as kn,b3 as Mn,aA as Nn,P as Rn}from"./index-169996dc.js";import{V as Bn}from"./index-00c7d20d.js";const Ro="/assets/drag-svg-6fb97bd5.svg";var Vn=Object.defineProperty,Fe=Object.getOwnPropertySymbols,Ft=Object.prototype.hasOwnProperty,Wt=Object.prototype.propertyIsEnumerable,St=(e,n,t)=>n in e?Vn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,be=(e,n)=>{for(var t in n||(n={}))Ft.call(n,t)&&St(e,t,n[t]);if(Fe)for(var t of Fe(n))Wt.call(n,t)&&St(e,t,n[t]);return e},zt=(e,n)=>{var t={};for(var o in e)Ft.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&Fe)for(var o of Fe(e))n.indexOf(o)<0&&Wt.call(e,o)&&(t[o]=e[o]);return t};const Ut="[vue-draggable-plus]: ";function Yn(e){console.warn(Ut+e)}function Xn(e){console.error(Ut+e)}function xt(e,n,t){return t>=0&&t<e.length&&e.splice(t,0,e.splice(n,1)[0]),e}function jn(e){return e.replace(/-(\w)/g,(n,t)=>t?t.toUpperCase():"")}function Ln(e){return Object.keys(e).reduce((n,t)=>(typeof e[t]<"u"&&(n[jn(t)]=e[t]),n),{})}function Dt(e,n){return Array.isArray(e)&&e.splice(n,1),e}function _t(e,n,t){return Array.isArray(e)&&e.splice(n,0,t),e}function Hn(e){return typeof e>"u"}function Fn(e){return typeof e=="string"}function Tt(e,n,t){const o=e.children[t];e.insertBefore(n,o)}function tt(e){e.parentNode&&e.parentNode.removeChild(e)}function Wn(e,n=document){var t;let o=null;return typeof(n==null?void 0:n.querySelector)=="function"?o=(t=n==null?void 0:n.querySelector)==null?void 0:t.call(n,e):o=document.querySelector(e),o||Yn(`Element not found: ${e}`),o}function zn(e,n,t=null){return function(...o){return e.apply(t,o),n.apply(t,o)}}function Un(e,n){const t=be({},e);return Object.keys(n).forEach(o=>{t[o]?t[o]=zn(e[o],n[o]):t[o]=n[o]}),t}function Gn(e){return e instanceof HTMLElement}function $n(e,n){Object.keys(e).forEach(t=>{n(t,e[t])})}/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function Ct(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,o)}return t}function Q(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Ct(Object(t),!0).forEach(function(o){qn(e,o,t[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ct(Object(t)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(t,o))})}return e}function Ye(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ye=function(n){return typeof n}:Ye=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Ye(e)}function qn(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function ee(){return ee=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},ee.apply(this,arguments)}function Jn(e,n){if(e==null)return{};var t={},o=Object.keys(e),a,i;for(i=0;i<o.length;i++)a=o[i],!(n.indexOf(a)>=0)&&(t[a]=e[a]);return t}function Qn(e,n){if(e==null)return{};var t=Jn(e,n),o,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)o=i[a],!(n.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(e,o)&&(t[o]=e[o])}return t}var Zn="1.15.2";function K(e){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(e)}var te=K(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),ke=K(/Edge/i),Pt=K(/firefox/i),Te=K(/safari/i)&&!K(/chrome/i)&&!K(/android/i),Gt=K(/iP(ad|od|hone)/i),$t=K(/chrome/i)&&K(/android/i),qt={capture:!1,passive:!1};function E(e,n,t){e.addEventListener(n,t,!te&&qt)}function y(e,n,t){e.removeEventListener(n,t,!te&&qt)}function We(e,n){if(n){if(n[0]===">"&&(n=n.substring(1)),e)try{if(e.matches)return e.matches(n);if(e.msMatchesSelector)return e.msMatchesSelector(n);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(n)}catch{return!1}return!1}}function Kn(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function q(e,n,t,o){if(e){t=t||document;do{if(n!=null&&(n[0]===">"?e.parentNode===t&&We(e,n):We(e,n))||o&&e===t)return e;if(e===t)break}while(e=Kn(e))}return null}var Ot=/\s+/g;function j(e,n,t){if(e&&n)if(e.classList)e.classList[t?"add":"remove"](n);else{var o=(" "+e.className+" ").replace(Ot," ").replace(" "+n+" "," ");e.className=(o+(t?" "+n:"")).replace(Ot," ")}}function p(e,n,t){var o=e&&e.style;if(o){if(t===void 0)return document.defaultView&&document.defaultView.getComputedStyle?t=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(t=e.currentStyle),n===void 0?t:t[n];!(n in o)&&n.indexOf("webkit")===-1&&(n="-webkit-"+n),o[n]=t+(typeof t=="string"?"":"px")}}function we(e,n){var t="";if(typeof e=="string")t=e;else do{var o=p(e,"transform");o&&o!=="none"&&(t=o+" "+t)}while(!n&&(e=e.parentNode));var a=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return a&&new a(t)}function Jt(e,n,t){if(e){var o=e.getElementsByTagName(n),a=0,i=o.length;if(t)for(;a<i;a++)t(o[a],a);return o}return[]}function J(){var e=document.scrollingElement;return e||document.documentElement}function O(e,n,t,o,a){if(!(!e.getBoundingClientRect&&e!==window)){var i,r,s,l,u,d,h;if(e!==window&&e.parentNode&&e!==J()?(i=e.getBoundingClientRect(),r=i.top,s=i.left,l=i.bottom,u=i.right,d=i.height,h=i.width):(r=0,s=0,l=window.innerHeight,u=window.innerWidth,d=window.innerHeight,h=window.innerWidth),(n||t)&&e!==window&&(a=a||e.parentNode,!te))do if(a&&a.getBoundingClientRect&&(p(a,"transform")!=="none"||t&&p(a,"position")!=="static")){var b=a.getBoundingClientRect();r-=b.top+parseInt(p(a,"border-top-width")),s-=b.left+parseInt(p(a,"border-left-width")),l=r+i.height,u=s+i.width;break}while(a=a.parentNode);if(o&&e!==window){var f=we(a||e),S=f&&f.a,w=f&&f.d;f&&(r/=w,s/=S,h/=S,d/=w,l=r+d,u=s+h)}return{top:r,left:s,bottom:l,right:u,width:h,height:d}}}function It(e,n,t){for(var o=se(e,!0),a=O(e)[n];o;){var i=O(o)[t],r=void 0;if(r=a>=i,!r)return o;if(o===J())break;o=se(o,!1)}return!1}function Ee(e,n,t,o){for(var a=0,i=0,r=e.children;i<r.length;){if(r[i].style.display!=="none"&&r[i]!==v.ghost&&(o||r[i]!==v.dragged)&&q(r[i],t.draggable,e,!1)){if(a===n)return r[i];a++}i++}return null}function mt(e,n){for(var t=e.lastElementChild;t&&(t===v.ghost||p(t,"display")==="none"||n&&!We(t,n));)t=t.previousElementSibling;return t||null}function z(e,n){var t=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)e.nodeName.toUpperCase()!=="TEMPLATE"&&e!==v.clone&&(!n||We(e,n))&&t++;return t}function At(e){var n=0,t=0,o=J();if(e)do{var a=we(e),i=a.a,r=a.d;n+=e.scrollLeft*i,t+=e.scrollTop*r}while(e!==o&&(e=e.parentNode));return[n,t]}function eo(e,n){for(var t in e)if(e.hasOwnProperty(t)){for(var o in n)if(n.hasOwnProperty(o)&&n[o]===e[t][o])return Number(t)}return-1}function se(e,n){if(!e||!e.getBoundingClientRect)return J();var t=e,o=!1;do if(t.clientWidth<t.scrollWidth||t.clientHeight<t.scrollHeight){var a=p(t);if(t.clientWidth<t.scrollWidth&&(a.overflowX=="auto"||a.overflowX=="scroll")||t.clientHeight<t.scrollHeight&&(a.overflowY=="auto"||a.overflowY=="scroll")){if(!t.getBoundingClientRect||t===document.body)return J();if(o||n)return t;o=!0}}while(t=t.parentNode);return J()}function to(e,n){if(e&&n)for(var t in n)n.hasOwnProperty(t)&&(e[t]=n[t]);return e}function nt(e,n){return Math.round(e.top)===Math.round(n.top)&&Math.round(e.left)===Math.round(n.left)&&Math.round(e.height)===Math.round(n.height)&&Math.round(e.width)===Math.round(n.width)}var Ce;function Qt(e,n){return function(){if(!Ce){var t=arguments,o=this;t.length===1?e.call(o,t[0]):e.apply(o,t),Ce=setTimeout(function(){Ce=void 0},n)}}}function no(){clearTimeout(Ce),Ce=void 0}function Zt(e,n,t){e.scrollLeft+=n,e.scrollTop+=t}function Kt(e){var n=window.Polymer,t=window.jQuery||window.Zepto;return n&&n.dom?n.dom(e).cloneNode(!0):t?t(e).clone(!0)[0]:e.cloneNode(!0)}function en(e,n,t){var o={};return Array.from(e.children).forEach(function(a){var i,r,s,l;if(!(!q(a,n.draggable,e,!1)||a.animated||a===t)){var u=O(a);o.left=Math.min((i=o.left)!==null&&i!==void 0?i:1/0,u.left),o.top=Math.min((r=o.top)!==null&&r!==void 0?r:1/0,u.top),o.right=Math.max((s=o.right)!==null&&s!==void 0?s:-1/0,u.right),o.bottom=Math.max((l=o.bottom)!==null&&l!==void 0?l:-1/0,u.bottom)}}),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var H="Sortable"+new Date().getTime();function oo(){var e=[],n;return{captureAnimationState:function(){if(e=[],!!this.options.animation){var t=[].slice.call(this.el.children);t.forEach(function(o){if(!(p(o,"display")==="none"||o===v.ghost)){e.push({target:o,rect:O(o)});var a=Q({},e[e.length-1].rect);if(o.thisAnimationDuration){var i=we(o,!0);i&&(a.top-=i.f,a.left-=i.e)}o.fromRect=a}})}},addAnimationState:function(t){e.push(t)},removeAnimationState:function(t){e.splice(eo(e,{target:t}),1)},animateAll:function(t){var o=this;if(!this.options.animation){clearTimeout(n),typeof t=="function"&&t();return}var a=!1,i=0;e.forEach(function(r){var s=0,l=r.target,u=l.fromRect,d=O(l),h=l.prevFromRect,b=l.prevToRect,f=r.rect,S=we(l,!0);S&&(d.top-=S.f,d.left-=S.e),l.toRect=d,l.thisAnimationDuration&&nt(h,d)&&!nt(u,d)&&(f.top-d.top)/(f.left-d.left)===(u.top-d.top)/(u.left-d.left)&&(s=io(f,h,b,o.options)),nt(d,u)||(l.prevFromRect=u,l.prevToRect=d,s||(s=o.options.animation),o.animate(l,f,d,s)),s&&(a=!0,i=Math.max(i,s),clearTimeout(l.animationResetTimer),l.animationResetTimer=setTimeout(function(){l.animationTime=0,l.prevFromRect=null,l.fromRect=null,l.prevToRect=null,l.thisAnimationDuration=null},s),l.thisAnimationDuration=s)}),clearTimeout(n),a?n=setTimeout(function(){typeof t=="function"&&t()},i):typeof t=="function"&&t(),e=[]},animate:function(t,o,a,i){if(i){p(t,"transition",""),p(t,"transform","");var r=we(this.el),s=r&&r.a,l=r&&r.d,u=(o.left-a.left)/(s||1),d=(o.top-a.top)/(l||1);t.animatingX=!!u,t.animatingY=!!d,p(t,"transform","translate3d("+u+"px,"+d+"px,0)"),this.forRepaintDummy=ao(t),p(t,"transition","transform "+i+"ms"+(this.options.easing?" "+this.options.easing:"")),p(t,"transform","translate3d(0,0,0)"),typeof t.animated=="number"&&clearTimeout(t.animated),t.animated=setTimeout(function(){p(t,"transition",""),p(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1},i)}}}}function ao(e){return e.offsetWidth}function io(e,n,t,o){return Math.sqrt(Math.pow(n.top-e.top,2)+Math.pow(n.left-e.left,2))/Math.sqrt(Math.pow(n.top-t.top,2)+Math.pow(n.left-t.left,2))*o.animation}var pe=[],ot={initializeByDefault:!0},Me={mount:function(e){for(var n in ot)ot.hasOwnProperty(n)&&!(n in e)&&(e[n]=ot[n]);pe.forEach(function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),pe.push(e)},pluginEvent:function(e,n,t){var o=this;this.eventCanceled=!1,t.cancel=function(){o.eventCanceled=!0};var a=e+"Global";pe.forEach(function(i){n[i.pluginName]&&(n[i.pluginName][a]&&n[i.pluginName][a](Q({sortable:n},t)),n.options[i.pluginName]&&n[i.pluginName][e]&&n[i.pluginName][e](Q({sortable:n},t)))})},initializePlugins:function(e,n,t,o){pe.forEach(function(r){var s=r.pluginName;if(!(!e.options[s]&&!r.initializeByDefault)){var l=new r(e,n,e.options);l.sortable=e,l.options=e.options,e[s]=l,ee(t,l.defaults)}});for(var a in e.options)if(e.options.hasOwnProperty(a)){var i=this.modifyOption(e,a,e.options[a]);typeof i<"u"&&(e.options[a]=i)}},getEventProperties:function(e,n){var t={};return pe.forEach(function(o){typeof o.eventProperties=="function"&&ee(t,o.eventProperties.call(n[o.pluginName],e))}),t},modifyOption:function(e,n,t){var o;return pe.forEach(function(a){e[a.pluginName]&&a.optionListeners&&typeof a.optionListeners[n]=="function"&&(o=a.optionListeners[n].call(e[a.pluginName],t))}),o}};function ro(e){var n=e.sortable,t=e.rootEl,o=e.name,a=e.targetEl,i=e.cloneEl,r=e.toEl,s=e.fromEl,l=e.oldIndex,u=e.newIndex,d=e.oldDraggableIndex,h=e.newDraggableIndex,b=e.originalEvent,f=e.putSortable,S=e.extraEventProperties;if(n=n||t&&t[H],!!n){var w,B=n.options,A="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!te&&!ke?w=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(w=document.createEvent("Event"),w.initEvent(o,!0,!0)),w.to=r||t,w.from=s||t,w.item=a||t,w.clone=i,w.oldIndex=l,w.newIndex=u,w.oldDraggableIndex=d,w.newDraggableIndex=h,w.originalEvent=b,w.pullMode=f?f.lastPutMode:void 0;var F=Q(Q({},S),Me.getEventProperties(o,n));for(var D in F)w[D]=F[D];t&&t.dispatchEvent(w),B[A]&&B[A].call(n,w)}}var lo=["evt"],Y=function(e,n){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=t.evt,a=Qn(t,lo);Me.pluginEvent.bind(v)(e,n,Q({dragEl:c,parentEl:C,ghostEl:g,rootEl:_,nextEl:he,lastDownEl:Xe,cloneEl:T,cloneHidden:re,dragStarted:xe,putSortable:k,activeSortable:v.active,originalEvent:o,oldIndex:ye,oldDraggableIndex:Pe,newIndex:L,newDraggableIndex:ie,hideGhostForTarget:an,unhideGhostForTarget:rn,cloneNowHidden:function(){re=!0},cloneNowShown:function(){re=!1},dispatchSortableEvent:function(i){R({sortable:n,name:i,originalEvent:o})}},a))};function R(e){ro(Q({putSortable:k,cloneEl:T,targetEl:c,rootEl:_,oldIndex:ye,oldDraggableIndex:Pe,newIndex:L,newDraggableIndex:ie},e))}var c,C,g,_,he,Xe,T,re,ye,L,Pe,ie,Ne,k,ge=!1,ze=!1,Ue=[],de,$,at,it,kt,Mt,xe,ve,Oe,Ie=!1,Re=!1,je,M,rt=[],dt=!1,Ge=[],Ke=typeof document<"u",Be=Gt,Nt=ke||te?"cssFloat":"float",so=Ke&&!$t&&!Gt&&"draggable"in document.createElement("div"),tn=function(){if(Ke){if(te)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto",e.style.pointerEvents==="auto"}}(),nn=function(e,n){var t=p(e),o=parseInt(t.width)-parseInt(t.paddingLeft)-parseInt(t.paddingRight)-parseInt(t.borderLeftWidth)-parseInt(t.borderRightWidth),a=Ee(e,0,n),i=Ee(e,1,n),r=a&&p(a),s=i&&p(i),l=r&&parseInt(r.marginLeft)+parseInt(r.marginRight)+O(a).width,u=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+O(i).width;if(t.display==="flex")return t.flexDirection==="column"||t.flexDirection==="column-reverse"?"vertical":"horizontal";if(t.display==="grid")return t.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(a&&r.float&&r.float!=="none"){var d=r.float==="left"?"left":"right";return i&&(s.clear==="both"||s.clear===d)?"vertical":"horizontal"}return a&&(r.display==="block"||r.display==="flex"||r.display==="table"||r.display==="grid"||l>=o&&t[Nt]==="none"||i&&t[Nt]==="none"&&l+u>o)?"vertical":"horizontal"},co=function(e,n,t){var o=t?e.left:e.top,a=t?e.right:e.bottom,i=t?e.width:e.height,r=t?n.left:n.top,s=t?n.right:n.bottom,l=t?n.width:n.height;return o===r||a===s||o+i/2===r+l/2},uo=function(e,n){var t;return Ue.some(function(o){var a=o[H].options.emptyInsertThreshold;if(!(!a||mt(o))){var i=O(o),r=e>=i.left-a&&e<=i.right+a,s=n>=i.top-a&&n<=i.bottom+a;if(r&&s)return t=o}}),t},on=function(e){function n(a,i){return function(r,s,l,u){var d=r.options.group.name&&s.options.group.name&&r.options.group.name===s.options.group.name;if(a==null&&(i||d))return!0;if(a==null||a===!1)return!1;if(i&&a==="clone")return a;if(typeof a=="function")return n(a(r,s,l,u),i)(r,s,l,u);var h=(i?r:s).options.group.name;return a===!0||typeof a=="string"&&a===h||a.join&&a.indexOf(h)>-1}}var t={},o=e.group;(!o||Ye(o)!="object")&&(o={name:o}),t.name=o.name,t.checkPull=n(o.pull,!0),t.checkPut=n(o.put),t.revertClone=o.revertClone,e.group=t},an=function(){!tn&&g&&p(g,"display","none")},rn=function(){!tn&&g&&p(g,"display","")};Ke&&!$t&&document.addEventListener("click",function(e){if(ze)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),ze=!1,!1},!0);var fe=function(e){if(c){e=e.touches?e.touches[0]:e;var n=uo(e.clientX,e.clientY);if(n){var t={};for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o]);t.target=t.rootEl=n,t.preventDefault=void 0,t.stopPropagation=void 0,n[H]._onDragOver(t)}}},fo=function(e){c&&c.parentNode[H]._isOutsideThisEl(e.target)};function v(e,n){if(!(e&&e.nodeType&&e.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=n=ee({},n),e[H]=this;var t={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return nn(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(i,r){i.setData("Text",r.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:v.supportPointer!==!1&&"PointerEvent"in window&&!Te,emptyInsertThreshold:5};Me.initializePlugins(this,e,t);for(var o in t)!(o in n)&&(n[o]=t[o]);on(n);for(var a in this)a.charAt(0)==="_"&&typeof this[a]=="function"&&(this[a]=this[a].bind(this));this.nativeDraggable=n.forceFallback?!1:so,this.nativeDraggable&&(this.options.touchStartThreshold=1),n.supportPointer?E(e,"pointerdown",this._onTapStart):(E(e,"mousedown",this._onTapStart),E(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(E(e,"dragover",this),E(e,"dragenter",this)),Ue.push(this.el),n.store&&n.store.get&&this.sort(n.store.get(this)||[]),ee(this,oo())}v.prototype={constructor:v,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(ve=null)},_getDirection:function(e,n){return typeof this.options.direction=="function"?this.options.direction.call(this,e,n,c):this.options.direction},_onTapStart:function(e){if(e.cancelable){var n=this,t=this.el,o=this.options,a=o.preventOnFilter,i=e.type,r=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,s=(r||e).target,l=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,u=o.filter;if(wo(t),!c&&!(/mousedown|pointerdown/.test(i)&&e.button!==0||o.disabled)&&!l.isContentEditable&&!(!this.nativeDraggable&&Te&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=q(s,o.draggable,t,!1),!(s&&s.animated)&&Xe!==s)){if(ye=z(s),Pe=z(s,o.draggable),typeof u=="function"){if(u.call(this,e,s,this)){R({sortable:n,rootEl:l,name:"filter",targetEl:s,toEl:t,fromEl:t}),Y("filter",n,{evt:e}),a&&e.cancelable&&e.preventDefault();return}}else if(u&&(u=u.split(",").some(function(d){if(d=q(l,d.trim(),t,!1),d)return R({sortable:n,rootEl:d,name:"filter",targetEl:s,fromEl:t,toEl:t}),Y("filter",n,{evt:e}),!0}),u)){a&&e.cancelable&&e.preventDefault();return}o.handle&&!q(l,o.handle,t,!1)||this._prepareDragStart(e,r,s)}}},_prepareDragStart:function(e,n,t){var o=this,a=o.el,i=o.options,r=a.ownerDocument,s;if(t&&!c&&t.parentNode===a){var l=O(t);if(_=a,c=t,C=c.parentNode,he=c.nextSibling,Xe=t,Ne=i.group,v.dragged=c,de={target:c,clientX:(n||e).clientX,clientY:(n||e).clientY},kt=de.clientX-l.left,Mt=de.clientY-l.top,this._lastX=(n||e).clientX,this._lastY=(n||e).clientY,c.style["will-change"]="all",s=function(){if(Y("delayEnded",o,{evt:e}),v.eventCanceled){o._onDrop();return}o._disableDelayedDragEvents(),!Pt&&o.nativeDraggable&&(c.draggable=!0),o._triggerDragStart(e,n),R({sortable:o,name:"choose",originalEvent:e}),j(c,i.chosenClass,!0)},i.ignore.split(",").forEach(function(u){Jt(c,u.trim(),lt)}),E(r,"dragover",fe),E(r,"mousemove",fe),E(r,"touchmove",fe),E(r,"mouseup",o._onDrop),E(r,"touchend",o._onDrop),E(r,"touchcancel",o._onDrop),Pt&&this.nativeDraggable&&(this.options.touchStartThreshold=4,c.draggable=!0),Y("delayStart",this,{evt:e}),i.delay&&(!i.delayOnTouchOnly||n)&&(!this.nativeDraggable||!(ke||te))){if(v.eventCanceled){this._onDrop();return}E(r,"mouseup",o._disableDelayedDrag),E(r,"touchend",o._disableDelayedDrag),E(r,"touchcancel",o._disableDelayedDrag),E(r,"mousemove",o._delayedDragTouchMoveHandler),E(r,"touchmove",o._delayedDragTouchMoveHandler),i.supportPointer&&E(r,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(s,i.delay)}else s()}},_delayedDragTouchMoveHandler:function(e){var n=e.touches?e.touches[0]:e;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){c&&lt(c),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._disableDelayedDrag),y(e,"touchend",this._disableDelayedDrag),y(e,"touchcancel",this._disableDelayedDrag),y(e,"mousemove",this._delayedDragTouchMoveHandler),y(e,"touchmove",this._delayedDragTouchMoveHandler),y(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,n){n=n||e.pointerType=="touch"&&e,!this.nativeDraggable||n?this.options.supportPointer?E(document,"pointermove",this._onTouchMove):n?E(document,"touchmove",this._onTouchMove):E(document,"mousemove",this._onTouchMove):(E(c,"dragend",this),E(_,"dragstart",this._onDragStart));try{document.selection?Le(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,n){if(ge=!1,_&&c){Y("dragStarted",this,{evt:n}),this.nativeDraggable&&E(document,"dragover",fo);var t=this.options;!e&&j(c,t.dragClass,!1),j(c,t.ghostClass,!0),v.active=this,e&&this._appendGhost(),R({sortable:this,name:"start",originalEvent:n})}else this._nulling()},_emulateDragOver:function(){if($){this._lastX=$.clientX,this._lastY=$.clientY,an();for(var e=document.elementFromPoint($.clientX,$.clientY),n=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint($.clientX,$.clientY),e!==n);)n=e;if(c.parentNode[H]._isOutsideThisEl(e),n)do{if(n[H]){var t=void 0;if(t=n[H]._onDragOver({clientX:$.clientX,clientY:$.clientY,target:e,rootEl:n}),t&&!this.options.dragoverBubble)break}e=n}while(n=n.parentNode);rn()}},_onTouchMove:function(e){if(de){var n=this.options,t=n.fallbackTolerance,o=n.fallbackOffset,a=e.touches?e.touches[0]:e,i=g&&we(g,!0),r=g&&i&&i.a,s=g&&i&&i.d,l=Be&&M&&At(M),u=(a.clientX-de.clientX+o.x)/(r||1)+(l?l[0]-rt[0]:0)/(r||1),d=(a.clientY-de.clientY+o.y)/(s||1)+(l?l[1]-rt[1]:0)/(s||1);if(!v.active&&!ge){if(t&&Math.max(Math.abs(a.clientX-this._lastX),Math.abs(a.clientY-this._lastY))<t)return;this._onDragStart(e,!0)}if(g){i?(i.e+=u-(at||0),i.f+=d-(it||0)):i={a:1,b:0,c:0,d:1,e:u,f:d};var h="matrix(".concat(i.a,",").concat(i.b,",").concat(i.c,",").concat(i.d,",").concat(i.e,",").concat(i.f,")");p(g,"webkitTransform",h),p(g,"mozTransform",h),p(g,"msTransform",h),p(g,"transform",h),at=u,it=d,$=a}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!g){var e=this.options.fallbackOnBody?document.body:_,n=O(c,!0,Be,!0,e),t=this.options;if(Be){for(M=e;p(M,"position")==="static"&&p(M,"transform")==="none"&&M!==document;)M=M.parentNode;M!==document.body&&M!==document.documentElement?(M===document&&(M=J()),n.top+=M.scrollTop,n.left+=M.scrollLeft):M=J(),rt=At(M)}g=c.cloneNode(!0),j(g,t.ghostClass,!1),j(g,t.fallbackClass,!0),j(g,t.dragClass,!0),p(g,"transition",""),p(g,"transform",""),p(g,"box-sizing","border-box"),p(g,"margin",0),p(g,"top",n.top),p(g,"left",n.left),p(g,"width",n.width),p(g,"height",n.height),p(g,"opacity","0.8"),p(g,"position",Be?"absolute":"fixed"),p(g,"zIndex","100000"),p(g,"pointerEvents","none"),v.ghost=g,e.appendChild(g),p(g,"transform-origin",kt/parseInt(g.style.width)*100+"% "+Mt/parseInt(g.style.height)*100+"%")}},_onDragStart:function(e,n){var t=this,o=e.dataTransfer,a=t.options;if(Y("dragStart",this,{evt:e}),v.eventCanceled){this._onDrop();return}Y("setupClone",this),v.eventCanceled||(T=Kt(c),T.removeAttribute("id"),T.draggable=!1,T.style["will-change"]="",this._hideClone(),j(T,this.options.chosenClass,!1),v.clone=T),t.cloneId=Le(function(){Y("clone",t),!v.eventCanceled&&(t.options.removeCloneOnHide||_.insertBefore(T,c),t._hideClone(),R({sortable:t,name:"clone"}))}),!n&&j(c,a.dragClass,!0),n?(ze=!0,t._loopId=setInterval(t._emulateDragOver,50)):(y(document,"mouseup",t._onDrop),y(document,"touchend",t._onDrop),y(document,"touchcancel",t._onDrop),o&&(o.effectAllowed="move",a.setData&&a.setData.call(t,o,c)),E(document,"drop",t),p(c,"transform","translateZ(0)")),ge=!0,t._dragStartId=Le(t._dragStarted.bind(t,n,e)),E(document,"selectstart",t),xe=!0,Te&&p(document.body,"user-select","none")},_onDragOver:function(e){var n=this.el,t=e.target,o,a,i,r=this.options,s=r.group,l=v.active,u=Ne===s,d=r.sort,h=k||l,b,f=this,S=!1;if(dt)return;function w(Se,cn){Y(Se,f,Q({evt:e,isOwner:u,axis:b?"vertical":"horizontal",revert:i,dragRect:o,targetRect:a,canSort:d,fromSortable:h,target:t,completed:A,onMove:function(wt,un){return Ve(_,n,c,o,wt,O(wt),e,un)},changed:F},cn))}function B(){w("dragOverAnimationCapture"),f.captureAnimationState(),f!==h&&h.captureAnimationState()}function A(Se){return w("dragOverCompleted",{insertion:Se}),Se&&(u?l._hideClone():l._showClone(f),f!==h&&(j(c,k?k.options.ghostClass:l.options.ghostClass,!1),j(c,r.ghostClass,!0)),k!==f&&f!==v.active?k=f:f===v.active&&k&&(k=null),h===f&&(f._ignoreWhileAnimating=t),f.animateAll(function(){w("dragOverAnimationComplete"),f._ignoreWhileAnimating=null}),f!==h&&(h.animateAll(),h._ignoreWhileAnimating=null)),(t===c&&!c.animated||t===n&&!t.animated)&&(ve=null),!r.dragoverBubble&&!e.rootEl&&t!==document&&(c.parentNode[H]._isOutsideThisEl(e.target),!Se&&fe(e)),!r.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),S=!0}function F(){L=z(c),ie=z(c,r.draggable),R({sortable:f,name:"change",toEl:n,newIndex:L,newDraggableIndex:ie,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),t=q(t,r.draggable,n,!0),w("dragOver"),v.eventCanceled)return S;if(c.contains(e.target)||t.animated&&t.animatingX&&t.animatingY||f._ignoreWhileAnimating===t)return A(!1);if(ze=!1,l&&!r.disabled&&(u?d||(i=C!==_):k===this||(this.lastPutMode=Ne.checkPull(this,l,c,e))&&s.checkPut(this,l,c,e))){if(b=this._getDirection(e,t)==="vertical",o=O(c),w("dragOverValid"),v.eventCanceled)return S;if(i)return C=_,B(),this._hideClone(),w("revert"),v.eventCanceled||(he?_.insertBefore(c,he):_.appendChild(c)),A(!0);var D=mt(n,r.draggable);if(!D||go(e,b,this)&&!D.animated){if(D===c)return A(!1);if(D&&n===e.target&&(t=D),t&&(a=O(t)),Ve(_,n,c,o,t,a,e,!!t)!==!1)return B(),D&&D.nextSibling?n.insertBefore(c,D.nextSibling):n.appendChild(c),C=n,F(),A(!0)}else if(D&&vo(e,b,this)){var Z=Ee(n,0,r,!0);if(Z===c)return A(!1);if(t=Z,a=O(t),Ve(_,n,c,o,t,a,e,!1)!==!1)return B(),n.insertBefore(c,Z),C=n,F(),A(!0)}else if(t.parentNode===n){a=O(t);var W=0,m,x=c.parentNode!==n,N=!co(c.animated&&c.toRect||o,t.animated&&t.toRect||a,b),I=b?"top":"left",V=It(t,"top","top")||It(c,"top","top"),ne=V?V.scrollTop:void 0;ve!==t&&(m=a[I],Ie=!1,Re=!N&&r.invertSwap||x),W=mo(e,t,a,b,N?1:r.swapThreshold,r.invertedSwapThreshold==null?r.swapThreshold:r.invertedSwapThreshold,Re,ve===t);var G;if(W!==0){var oe=z(c);do oe-=W,G=C.children[oe];while(G&&(p(G,"display")==="none"||G===g))}if(W===0||G===t)return A(!1);ve=t,Oe=W;var ce=t.nextElementSibling,ae=!1;ae=W===1;var ue=Ve(_,n,c,o,t,a,e,ae);if(ue!==!1)return(ue===1||ue===-1)&&(ae=ue===1),dt=!0,setTimeout(po,30),B(),ae&&!ce?n.appendChild(c):t.parentNode.insertBefore(c,ae?ce:t),V&&Zt(V,0,ne-V.scrollTop),C=c.parentNode,m!==void 0&&!Re&&(je=Math.abs(m-O(t)[I])),F(),A(!0)}if(n.contains(c))return A(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){y(document,"mousemove",this._onTouchMove),y(document,"touchmove",this._onTouchMove),y(document,"pointermove",this._onTouchMove),y(document,"dragover",fe),y(document,"mousemove",fe),y(document,"touchmove",fe)},_offUpEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._onDrop),y(e,"touchend",this._onDrop),y(e,"pointerup",this._onDrop),y(e,"touchcancel",this._onDrop),y(document,"selectstart",this)},_onDrop:function(e){var n=this.el,t=this.options;if(L=z(c),ie=z(c,t.draggable),Y("drop",this,{evt:e}),C=c&&c.parentNode,L=z(c),ie=z(c,t.draggable),v.eventCanceled){this._nulling();return}ge=!1,Re=!1,Ie=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ft(this.cloneId),ft(this._dragStartId),this.nativeDraggable&&(y(document,"drop",this),y(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Te&&p(document.body,"user-select",""),p(c,"transform",""),e&&(xe&&(e.cancelable&&e.preventDefault(),!t.dropBubble&&e.stopPropagation()),g&&g.parentNode&&g.parentNode.removeChild(g),(_===C||k&&k.lastPutMode!=="clone")&&T&&T.parentNode&&T.parentNode.removeChild(T),c&&(this.nativeDraggable&&y(c,"dragend",this),lt(c),c.style["will-change"]="",xe&&!ge&&j(c,k?k.options.ghostClass:this.options.ghostClass,!1),j(c,this.options.chosenClass,!1),R({sortable:this,name:"unchoose",toEl:C,newIndex:null,newDraggableIndex:null,originalEvent:e}),_!==C?(L>=0&&(R({rootEl:C,name:"add",toEl:C,fromEl:_,originalEvent:e}),R({sortable:this,name:"remove",toEl:C,originalEvent:e}),R({rootEl:C,name:"sort",toEl:C,fromEl:_,originalEvent:e}),R({sortable:this,name:"sort",toEl:C,originalEvent:e})),k&&k.save()):L!==ye&&L>=0&&(R({sortable:this,name:"update",toEl:C,originalEvent:e}),R({sortable:this,name:"sort",toEl:C,originalEvent:e})),v.active&&((L==null||L===-1)&&(L=ye,ie=Pe),R({sortable:this,name:"end",toEl:C,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){Y("nulling",this),_=c=C=g=he=T=Xe=re=de=$=xe=L=ie=ye=Pe=ve=Oe=k=Ne=v.dragged=v.ghost=v.clone=v.active=null,Ge.forEach(function(e){e.checked=!0}),Ge.length=at=it=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":c&&(this._onDragOver(e),ho(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],n,t=this.el.children,o=0,a=t.length,i=this.options;o<a;o++)n=t[o],q(n,i.draggable,this.el,!1)&&e.push(n.getAttribute(i.dataIdAttr)||yo(n));return e},sort:function(e,n){var t={},o=this.el;this.toArray().forEach(function(a,i){var r=o.children[i];q(r,this.options.draggable,o,!1)&&(t[a]=r)},this),n&&this.captureAnimationState(),e.forEach(function(a){t[a]&&(o.removeChild(t[a]),o.appendChild(t[a]))}),n&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,n){return q(e,n||this.options.draggable,this.el,!1)},option:function(e,n){var t=this.options;if(n===void 0)return t[e];var o=Me.modifyOption(this,e,n);typeof o<"u"?t[e]=o:t[e]=n,e==="group"&&on(t)},destroy:function(){Y("destroy",this);var e=this.el;e[H]=null,y(e,"mousedown",this._onTapStart),y(e,"touchstart",this._onTapStart),y(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(y(e,"dragover",this),y(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(n){n.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Ue.splice(Ue.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!re){if(Y("hideClone",this),v.eventCanceled)return;p(T,"display","none"),this.options.removeCloneOnHide&&T.parentNode&&T.parentNode.removeChild(T),re=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(re){if(Y("showClone",this),v.eventCanceled)return;c.parentNode==_&&!this.options.group.revertClone?_.insertBefore(T,c):he?_.insertBefore(T,he):_.appendChild(T),this.options.group.revertClone&&this.animate(c,T),p(T,"display",""),re=!1}}};function ho(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.cancelable&&e.preventDefault()}function Ve(e,n,t,o,a,i,r,s){var l,u=e[H],d=u.options.onMove,h;return window.CustomEvent&&!te&&!ke?l=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(l=document.createEvent("Event"),l.initEvent("move",!0,!0)),l.to=n,l.from=e,l.dragged=t,l.draggedRect=o,l.related=a||n,l.relatedRect=i||O(n),l.willInsertAfter=s,l.originalEvent=r,e.dispatchEvent(l),d&&(h=d.call(u,l,r)),h}function lt(e){e.draggable=!1}function po(){dt=!1}function vo(e,n,t){var o=O(Ee(t.el,0,t.options,!0)),a=en(t.el,t.options,g),i=10;return n?e.clientX<a.left-i||e.clientY<o.top&&e.clientX<o.right:e.clientY<a.top-i||e.clientY<o.bottom&&e.clientX<o.left}function go(e,n,t){var o=O(mt(t.el,t.options.draggable)),a=en(t.el,t.options,g),i=10;return n?e.clientX>a.right+i||e.clientY>o.bottom&&e.clientX>o.left:e.clientY>a.bottom+i||e.clientX>o.right&&e.clientY>o.top}function mo(e,n,t,o,a,i,r,s){var l=o?e.clientY:e.clientX,u=o?t.height:t.width,d=o?t.top:t.left,h=o?t.bottom:t.right,b=!1;if(!r){if(s&&je<u*a){if(!Ie&&(Oe===1?l>d+u*i/2:l<h-u*i/2)&&(Ie=!0),Ie)b=!0;else if(Oe===1?l<d+je:l>h-je)return-Oe}else if(l>d+u*(1-a)/2&&l<h-u*(1-a)/2)return bo(n)}return b=b||r,b&&(l<d+u*i/2||l>h-u*i/2)?l>d+u/2?1:-1:0}function bo(e){return z(c)<z(e)?1:-1}function yo(e){for(var n=e.tagName+e.className+e.src+e.href+e.textContent,t=n.length,o=0;t--;)o+=n.charCodeAt(t);return o.toString(36)}function wo(e){Ge.length=0;for(var n=e.getElementsByTagName("input"),t=n.length;t--;){var o=n[t];o.checked&&Ge.push(o)}}function Le(e){return setTimeout(e,0)}function ft(e){return clearTimeout(e)}Ke&&E(document,"touchmove",function(e){(v.active||ge)&&e.cancelable&&e.preventDefault()});v.utils={on:E,off:y,css:p,find:Jt,is:function(e,n){return!!q(e,n,e,!1)},extend:to,throttle:Qt,closest:q,toggleClass:j,clone:Kt,index:z,nextTick:Le,cancelNextTick:ft,detectDirection:nn,getChild:Ee};v.get=function(e){return e[H]};v.mount=function(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];n[0].constructor===Array&&(n=n[0]),n.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(v.utils=Q(Q({},v.utils),o.utils)),Me.mount(o)})};v.create=function(e,n){return new v(e,n)};v.version=Zn;var P=[],De,ht,pt=!1,st,ct,$e,_e;function Eo(){function e(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var n in this)n.charAt(0)==="_"&&typeof this[n]=="function"&&(this[n]=this[n].bind(this))}return e.prototype={dragStarted:function(n){var t=n.originalEvent;this.sortable.nativeDraggable?E(document,"dragover",this._handleAutoScroll):this.options.supportPointer?E(document,"pointermove",this._handleFallbackAutoScroll):t.touches?E(document,"touchmove",this._handleFallbackAutoScroll):E(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(n){var t=n.originalEvent;!this.options.dragOverBubble&&!t.rootEl&&this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?y(document,"dragover",this._handleAutoScroll):(y(document,"pointermove",this._handleFallbackAutoScroll),y(document,"touchmove",this._handleFallbackAutoScroll),y(document,"mousemove",this._handleFallbackAutoScroll)),Rt(),He(),no()},nulling:function(){$e=ht=De=pt=_e=st=ct=null,P.length=0},_handleFallbackAutoScroll:function(n){this._handleAutoScroll(n,!0)},_handleAutoScroll:function(n,t){var o=this,a=(n.touches?n.touches[0]:n).clientX,i=(n.touches?n.touches[0]:n).clientY,r=document.elementFromPoint(a,i);if($e=n,t||this.options.forceAutoScrollFallback||ke||te||Te){ut(n,this.options,r,t);var s=se(r,!0);pt&&(!_e||a!==st||i!==ct)&&(_e&&Rt(),_e=setInterval(function(){var l=se(document.elementFromPoint(a,i),!0);l!==s&&(s=l,He()),ut(n,o.options,l,t)},10),st=a,ct=i)}else{if(!this.options.bubbleScroll||se(r,!0)===J()){He();return}ut(n,this.options,se(r,!1),!1)}}},ee(e,{pluginName:"scroll",initializeByDefault:!0})}function He(){P.forEach(function(e){clearInterval(e.pid)}),P=[]}function Rt(){clearInterval(_e)}var ut=Qt(function(e,n,t,o){if(n.scroll){var a=(e.touches?e.touches[0]:e).clientX,i=(e.touches?e.touches[0]:e).clientY,r=n.scrollSensitivity,s=n.scrollSpeed,l=J(),u=!1,d;ht!==t&&(ht=t,He(),De=n.scroll,d=n.scrollFn,De===!0&&(De=se(t,!0)));var h=0,b=De;do{var f=b,S=O(f),w=S.top,B=S.bottom,A=S.left,F=S.right,D=S.width,Z=S.height,W=void 0,m=void 0,x=f.scrollWidth,N=f.scrollHeight,I=p(f),V=f.scrollLeft,ne=f.scrollTop;f===l?(W=D<x&&(I.overflowX==="auto"||I.overflowX==="scroll"||I.overflowX==="visible"),m=Z<N&&(I.overflowY==="auto"||I.overflowY==="scroll"||I.overflowY==="visible")):(W=D<x&&(I.overflowX==="auto"||I.overflowX==="scroll"),m=Z<N&&(I.overflowY==="auto"||I.overflowY==="scroll"));var G=W&&(Math.abs(F-a)<=r&&V+D<x)-(Math.abs(A-a)<=r&&!!V),oe=m&&(Math.abs(B-i)<=r&&ne+Z<N)-(Math.abs(w-i)<=r&&!!ne);if(!P[h])for(var ce=0;ce<=h;ce++)P[ce]||(P[ce]={});(P[h].vx!=G||P[h].vy!=oe||P[h].el!==f)&&(P[h].el=f,P[h].vx=G,P[h].vy=oe,clearInterval(P[h].pid),(G!=0||oe!=0)&&(u=!0,P[h].pid=setInterval((function(){o&&this.layer===0&&v.active._onTouchMove($e);var ae=P[this.layer].vy?P[this.layer].vy*s:0,ue=P[this.layer].vx?P[this.layer].vx*s:0;typeof d=="function"&&d.call(v.dragged.parentNode[H],ue,ae,e,$e,P[this.layer].el)!=="continue"||Zt(P[this.layer].el,ue,ae)}).bind({layer:h}),24))),h++}while(n.bubbleScroll&&b!==l&&(b=se(b,!1)));pt=u}},30),ln=function(e){var n=e.originalEvent,t=e.putSortable,o=e.dragEl,a=e.activeSortable,i=e.dispatchSortableEvent,r=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(n){var l=t||a;r();var u=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,d=document.elementFromPoint(u.clientX,u.clientY);s(),l&&!l.el.contains(d)&&(i("spill"),this.onSpill({dragEl:o,putSortable:t}))}};function bt(){}bt.prototype={startIndex:null,dragStart:function(e){var n=e.oldDraggableIndex;this.startIndex=n},onSpill:function(e){var n=e.dragEl,t=e.putSortable;this.sortable.captureAnimationState(),t&&t.captureAnimationState();var o=Ee(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(n,o):this.sortable.el.appendChild(n),this.sortable.animateAll(),t&&t.animateAll()},drop:ln};ee(bt,{pluginName:"revertOnSpill"});function yt(){}yt.prototype={onSpill:function(e){var n=e.dragEl,t=e.putSortable,o=t||this.sortable;o.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),o.animateAll()},drop:ln};ee(yt,{pluginName:"removeOnSpill"});v.mount(new Eo);v.mount(yt,bt);function So(e){return e==null?e:JSON.parse(JSON.stringify(e))}function xo(e){gt()&&mn(e)}function Do(e){gt()?bn(e):yn(e)}const Bt=Symbol("cloneElement");function _o(...e){var n,t;const o=(n=gt())==null?void 0:n.proxy,a=e[0];let[,i,r]=e;Array.isArray(X(i))||(r=i,i=null);let s=null;const{immediate:l=!0,clone:u=So,customUpdate:d}=(t=X(r))!=null?t:{};function h(m){var x;m.item[Bt]=u(X((x=X(i))==null?void 0:x[m.oldIndex]))}function b(m){const x=m.item[Bt];if(!Hn(x)){if(tt(m.item),et(i)){const N=[...X(i)];i.value=_t(N,m.newDraggableIndex,x);return}_t(X(i),m.newDraggableIndex,x)}}function f(m){const{from:x,item:N,oldIndex:I,oldDraggableIndex:V,pullMode:ne,clone:G}=m;if(Tt(x,N,I),ne==="clone"){tt(G);return}if(et(i)){const oe=[...X(i)];i.value=Dt(oe,V);return}Dt(X(i),V)}function S(m){if(d){d(m);return}const{from:x,item:N,oldIndex:I,newIndex:V}=m;if(tt(N),Tt(x,N,I),et(i)){const ne=[...X(i)];i.value=xt(ne,I,V);return}xt(X(i),I,V)}const w={onUpdate:S,onStart:h,onAdd:b,onRemove:f};function B(m){const x=X(a);return m||(m=Fn(x)?Wn(x,o==null?void 0:o.$el):x),m&&!Gn(m)&&(m=m.$el),m||Xn("Root element not found"),m}function A(){var m;const x=(m=X(r))!=null?m:{},N=zt(x,["immediate","clone"]);return Un(i===null?{}:w,N)}const F=m=>{m=B(m),s&&D.destroy(),s=new v(m,A())};gn(()=>r,()=>{s&&$n(A(),(m,x)=>{s==null||s.option(m,x)})},{deep:!0});const D={option:(m,x)=>s==null?void 0:s.option(m,x),destroy:()=>{s==null||s.destroy(),s=null},save:()=>s==null?void 0:s.save(),toArray:()=>s==null?void 0:s.toArray(),closest:(...m)=>s==null?void 0:s.closest(...m)},Z=()=>D==null?void 0:D.option("disabled",!0),W=()=>D==null?void 0:D.option("disabled",!1);return Do(()=>{l&&F()}),xo(D.destroy),be({start:F,pause:Z,resume:W},D)}const vt=["update","start","add","remove","choose","unchoose","end","sort","filter","clone","move","change"],To=["clone","animation","ghostClass","group","sort","disabled","store","handle","draggable","swapThreshold","invertSwap","invertedSwapThreshold","removeCloneOnHide","direction","chosenClass","dragClass","ignore","filter","preventOnFilter","easing","setData","dropBubble","dragoverBubble","dataIdAttr","delay","delayOnTouchOnly","touchStartThreshold","forceFallback","fallbackClass","fallbackOnBody","fallbackTolerance","fallbackOffset","supportPointer","emptyInsertThreshold","scroll","forceAutoScrollFallback","scrollSensitivity","scrollSpeed","bubbleScroll","modelValue","tag","target","customUpdate",...vt.map(e=>`on${e.replace(/^\S/,n=>n.toUpperCase())}`)],Bo=dn({name:"VueDraggable",model:{prop:"modelValue",event:"update:modelValue"},props:To,emits:["update:modelValue",...vt],setup(e,{slots:n,emit:t,expose:o,attrs:a}){const i=vt.reduce((d,h)=>{const b=`on${h.replace(/^\S/,f=>f.toUpperCase())}`;return d[b]=(...f)=>t(h,...f),d},{}),r=le(()=>{const d=fn(e),h=zt(d,["modelValue"]),b=Object.entries(h).reduce((f,[S,w])=>{const B=X(w);return B!==void 0&&(f[S]=B),f},{});return be(be({},i),Ln(be(be({},a),b)))}),s=le({get:()=>e.modelValue,set:d=>t("update:modelValue",d)}),l=hn(),u=pn(_o(e.target||l,s,r));return o(u),()=>{var d;return vn(e.tag||"div",{ref:l},(d=n==null?void 0:n.default)==null?void 0:d.call(n,u))}}});const Ae=Symbol.for("vuetify:v-expansion-panel"),Co=["default","accordion","inset","popout"],Po=qe({color:String,flat:Boolean,focusable:Boolean,static:Boolean,tile:Boolean,variant:{type:String,default:"default",validator:e=>Co.includes(e)},readonly:Boolean,...Je(),...wn(),...Vt(),...En()},"VExpansionPanels"),Vo=Qe()({name:"VExpansionPanels",props:Po(),emits:{"update:modelValue":e=>!0},setup(e,n){let{slots:t}=n;Sn(e,Ae);const{themeClasses:o}=xn(e),a=le(()=>e.variant&&`v-expansion-panels--variant-${e.variant}`);return Yt({VExpansionPanel:{color:me(e,"color"),readonly:me(e,"readonly")},VExpansionPanelTitle:{focusable:me(e,"focusable"),static:me(e,"static")}}),Ze(()=>U(e.tag,{class:["v-expansion-panels",{"v-expansion-panels--flat":e.flat,"v-expansion-panels--tile":e.tile},o.value,a.value,e.class],style:e.style},t)),{}}}),Oo=qe({...Je(),...Xt()},"VExpansionPanelText"),Io=Qe()({name:"VExpansionPanelText",props:Oo(),setup(e,n){let{slots:t}=n;const o=jt(Ae);if(!o)throw new Error("[Vuetify] v-expansion-panel-text needs to be placed inside v-expansion-panel");const{hasContent:a,onAfterLeave:i}=Dn(e,o.isSelected);return Ze(()=>U(Bn,{onAfterLeave:i},{default:()=>{var r;return[Lt(U("div",{class:["v-expansion-panel-text",e.class],style:e.style},[t.default&&a.value&&U("div",{class:"v-expansion-panel-text__wrapper"},[(r=t.default)==null?void 0:r.call(t)])]),[[_n,o.isSelected.value]])]}})),{}}}),sn=qe({color:String,expandIcon:{type:Et,default:"$expand"},collapseIcon:{type:Et,default:"$collapse"},hideActions:Boolean,focusable:Boolean,static:Boolean,ripple:{type:[Boolean,Object],default:!1},readonly:Boolean,...Je()},"VExpansionPanelTitle"),Ao=Qe()({name:"VExpansionPanelTitle",directives:{Ripple:Tn},props:sn(),setup(e,n){let{slots:t}=n;const o=jt(Ae);if(!o)throw new Error("[Vuetify] v-expansion-panel-title needs to be placed inside v-expansion-panel");const{backgroundColorClasses:a,backgroundColorStyles:i}=Ht(e,"color"),r=le(()=>({collapseIcon:e.collapseIcon,disabled:o.disabled.value,expanded:o.isSelected.value,expandIcon:e.expandIcon,readonly:e.readonly}));return Ze(()=>{var s;return Lt(U("button",{class:["v-expansion-panel-title",{"v-expansion-panel-title--active":o.isSelected.value,"v-expansion-panel-title--focusable":e.focusable,"v-expansion-panel-title--static":e.static},a.value,e.class],style:[i.value,e.style],type:"button",tabindex:o.disabled.value?-1:void 0,disabled:o.disabled.value,"aria-expanded":o.isSelected.value,onClick:e.readonly?void 0:o.toggle},[U("span",{class:"v-expansion-panel-title__overlay"},null),(s=t.default)==null?void 0:s.call(t,r.value),!e.hideActions&&U("span",{class:"v-expansion-panel-title__icon"},[t.actions?t.actions(r.value):U(Pn,{icon:o.isSelected.value?e.collapseIcon:e.expandIcon},null)])]),[[Cn("ripple"),e.ripple]])}),{}}}),ko=qe({title:String,text:String,bgColor:String,...Je(),...On(),...In(),...Xt(),...An(),...Vt(),...sn()},"VExpansionPanel"),Yo=Qe()({name:"VExpansionPanel",props:ko(),emits:{"group:selected":e=>!0},setup(e,n){let{slots:t}=n;const o=kn(e,Ae),{backgroundColorClasses:a,backgroundColorStyles:i}=Ht(e,"bgColor"),{elevationClasses:r}=Mn(e),{roundedClasses:s}=Nn(e),l=le(()=>(o==null?void 0:o.disabled.value)||e.disabled),u=le(()=>o.group.items.value.reduce((b,f,S)=>(o.group.selected.value.includes(f.id)&&b.push(S),b),[])),d=le(()=>{const b=o.group.items.value.findIndex(f=>f.id===o.id);return!o.isSelected.value&&u.value.some(f=>f-b===1)}),h=le(()=>{const b=o.group.items.value.findIndex(f=>f.id===o.id);return!o.isSelected.value&&u.value.some(f=>f-b===-1)});return Rn(Ae,o),Yt({VExpansionPanelText:{eager:me(e,"eager")},VExpansionPanelTitle:{readonly:me(e,"readonly")}}),Ze(()=>{const b=!!(t.text||e.text),f=!!(t.title||e.title);return U(e.tag,{class:["v-expansion-panel",{"v-expansion-panel--active":o.isSelected.value,"v-expansion-panel--before-active":d.value,"v-expansion-panel--after-active":h.value,"v-expansion-panel--disabled":l.value},s.value,a.value,e.class],style:[i.value,e.style]},{default:()=>{var S;return[U("div",{class:["v-expansion-panel__shadow",...r.value]},null),f&&U(Ao,{key:"title",collapseIcon:e.collapseIcon,color:e.color,expandIcon:e.expandIcon,hideActions:e.hideActions,ripple:e.ripple},{default:()=>[t.title?t.title():e.title]}),b&&U(Io,{key:"text"},{default:()=>[t.text?t.text():e.text]}),(S=t.default)==null?void 0:S.call(t)]}})}),{}}});export{Ro as D,Vo as V,Yo as a,Ao as b,Io as c,Bo as t};
