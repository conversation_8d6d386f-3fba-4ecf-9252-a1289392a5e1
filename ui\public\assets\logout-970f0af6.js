import{k as l,a2 as e,H as s,al as r,o as n,c}from"./index-169996dc.js";import{u as i}from"./useAbility-e6e49333.js";const k={__name:"logout",setup(p){const t=l(),o=i(),u=e("userData");return s(async()=>{try{r("/logout",{method:"GET"})}catch(a){console.log(a)}e("accessToken").value=null,u.value=null,e("userData").value=null,await t.replace("/auth/login"),e("userAbilityRules").value=null,o.update([])}),(a,m)=>(n(),c("div"))}};export{k as default};
