import{_ as D}from"./TableComponent.vue_vue_type_style_index_0_lang-c0109d42.js";import{E as _}from"./endpoints-454f23f6.js";import{m as V}from"./jalali-moment-c79ac113.js";import{_ as P}from"./AddProductDialog-eb403242.js";import{k as E,j as R,r as e,w as T,H as I,o as Y,f as z,e as a,n as i,al as w,b as o,a4 as c,v as H,af as M,ai as N,d as S,x as j,aM as m}from"./index-169996dc.js";import{V as q}from"./VRow-6c1d54f3.js";import"./VDataTable-a3a359fa.js";import"./VPagination-8a53e08f.js";import"./VSelect-d6fde9f4.js";import"./VTextField-a8984053.js";import"./VField-150a934a.js";import"./index-00c7d20d.js";import"./VInput-c4d3942a.js";import"./VList-349a1ccf.js";import"./ssrBoot-c101cd97.js";import"./VDivider-12bfa926.js";import"./VMenu-2cfb0f14.js";import"./VCheckboxBtn-be9663e7.js";import"./VSelectionControl-8ecbcf09.js";import"./VChip-ccd89083.js";import"./filter-09e553ed.js";import"./VSkeletonLoader-4c44fbcf.js";import"./MediaComponent-21dbcad4.js";import"./DialogCloseBtn-b32209d9.js";import"./validations-4c0aab88.js";import"./iconify-64e5a48d.js";import"./VExpansionPanel-909a4c05.js";import"./VAlert-fc722507.js";import"./VTextarea-1c13d440.js";import"./VCheckbox-b54d510b.js";import"./MessageBox-e9a5b0b1.js";import"./profile_ph-c1153e1f.js";import"./CurrencyInput-8fbc55d3.js";import"./VContainer-63fa55f4.js";import"./VStepper-57999623.js";import"./VWindowItem-f84b956d.js";import"./VForm-a73b6b87.js";const F="/assets/profile-4785df67.png",Dt={__name:"test",setup(A){const u=E();R(),e(),e(),e(),e();const s=e(!1),n=e(1),d=e(),y=e(10),f=e(0);e();const h=[{title:"ID",sortable:!1,key:"id"},{title:"عکس",key:"image"},{title:"نام",key:"name"},{title:"قیمت",key:"price"},{title:"تاریخ",key:"created_at"},{title:"عملیات",key:"actions"}],k=t=>{n.value=t},l=async()=>{try{s.value=!0;const{data:t}=await w.get(_.products,{params:{page:n.value}});d.value=t.data,f.value=t.total}catch(t){throw new Error(t)}finally{s.value=!1}},v=async t=>{try{s.value=!0,await w.delete(_.products+"/"+t),await m({title:"با موفقیت حذف شد!"}),await l()}catch(p){throw m({icon:"error",title:"خطا در حذف محصول"}),new Error(p)}finally{s.value=!1}},x=t=>{u.push(`/products/${t.id}`)},C=t=>{u.push(`/products/${t.id}/setting`)},b=async t=>{(await m({title:`آیا از حذف ${t.name} مطمئن هستید؟`,showDenyButton:!0,confirmButtonText:"بله",denyButtonText:"خیر"})).isConfirmed&&v(t.id)},$=t=>V(t).locale("fa").format("YYYY/M/D HH:mm:ss");return T(n,async()=>{await l()}),I(async()=>{await l()}),(t,p)=>{const B=D;return Y(),z(B,{page:i(n),"per-page":i(y),"total-item":i(f),columns:h,data:i(d),loading:i(s),onPagination:k},{title:a(()=>[o(c,{icon:"tabler-shopping-cart",color:"rgb(123, 30, 193)"}),H(" لیست محصولات ")]),extraTop:a(()=>[o(q,null,{default:a(()=>[o(M,null,{default:a(()=>[o(P,{refresh:l})]),_:1})]),_:1})]),image:a(({item:r})=>[o(N,{src:i(F),width:"40",height:"40",cover:"",style:{"border-radius":"100%"}},null,8,["src"])]),created_at:a(({item:r})=>[S("span",null,j($(r.created_at)),1)]),actions:a(({item:r})=>[o(c,{icon:"tabler-settings",color:"#607D8B",size:"30px",onClick:g=>C(r)},null,8,["onClick"]),o(c,{icon:"tabler-edit",color:"#00B26E",size:"30px",onClick:g=>x(r)},null,8,["onClick"]),o(c,{icon:"tabler-trash",color:"red",size:"30px",onClick:g=>b(r)},null,8,["onClick"])]),_:1},8,["page","per-page","total-item","data","loading"])}}};export{Dt as default};
