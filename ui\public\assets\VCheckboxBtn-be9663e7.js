import{m as V,V as o}from"./VSelectionControl-8ecbcf09.js";import{ap as f,au as b,as as v,b6 as c,a8 as l,at as I,bg as k,b as x,s as h}from"./index-169996dc.js";const C=f({indeterminate:Boolean,indeterminateIcon:{type:b,default:"$checkboxIndeterminate"},...V({falseIcon:"$checkboxOff",trueIcon:"$checkboxOn"})},"VCheckboxBtn"),y=v()({name:"VCheckboxBtn",props:C(),emits:{"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,s){let{slots:r}=s;const t=c(e,"indeterminate"),a=c(e,"modelValue");function u(n){t.value&&(t.value=!1)}const i=l(()=>t.value?e.indeterminateIcon:e.falseIcon),m=l(()=>t.value?e.indeterminateIcon:e.trueIcon);return I(()=>{const n=k(o.filterProps(e),["modelValue"]);return x(o,h(n,{modelValue:a.value,"onUpdate:modelValue":[d=>a.value=d,u],class:["v-checkbox-btn",e.class],style:e.style,type:"checkbox",falseIcon:i.value,trueIcon:m.value,"aria-checked":t.value?"mixed":void 0}),r)}),{}}});export{y as V,C as m};
