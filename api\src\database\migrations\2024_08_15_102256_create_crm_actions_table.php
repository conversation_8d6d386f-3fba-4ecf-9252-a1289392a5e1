<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 */
	public function up(): void
	{
		Schema::create('crm_actions', function (Blueprint $table) {
			$table->id();
			$table->string('name')->unique()->comment('English name');
			$table->string('title')->comment('Persian title');
		});
	}

	/**
	 * Reverse the migrations.
	 */
	public function down(): void
	{
		Schema::dropIfExists('crm_actions');
	}
};
