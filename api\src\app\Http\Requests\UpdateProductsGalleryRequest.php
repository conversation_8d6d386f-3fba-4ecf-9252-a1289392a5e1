<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProductsGalleryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if(auth()->user() == null) {
            return false;
        }
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'description'=>'max:2000',
            'image'=>'image|mimes:jpeg,png,gif',
            'order'=>'integer'
        ];
    }
    public function messages(): array
    {
        return [
            'description.required'=>'توضیحات اجباری است',
            'description.max'=>'توضیحات نمیتواند بیشتر از 2000 کاراکتر باشد',
            'product_id.required'=>'محصول اجباری است',
            'product_id.exists'=>'محصول پیدا نشد',
            'path.required'=>'عکس اجباری است',
            'path.image'=>'عکس معتبر نیست',
            'path.mimes'=>'فرمت ارسال شده معتبر نیست فرمت های معتبر: jpeg,png,gif',
        ];
    }
}
