<?php

namespace App\Http\Controllers\Webservice;

use App\Http\Controllers\Controller;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SpecialUserWebhookController extends Controller
{
    protected SpecialUserRepoInterface $specialUserRepository;

    public function __construct(SpecialUserRepoInterface $specialUserRepository)
    {
        $this->specialUserRepository = $specialUserRepository;
    }

    /**
     * @OA\Put(
     *     path="/api/webservice/webhooks",
     *     summary="Update webhook endpoints for a special user",
     *     description="Allows an authenticated user to update webhook endpoints. The maximum number of webhook endpoints allowed is 5.",
     *     operationId="updateWebhookEndpoints",
     *     tags={"Special User Webhooks"},
     *     security={{"sanctum":{}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"webhook_endpoints"},
     *             @OA\Property(property="webhook_endpoints", type="array", @OA\Items(type="string"), example={"https://webhook1.example.com", "https://webhook2.example.com"})
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Webhook endpoints updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="webhook_endpoints", type="array", @OA\Items(type="string"), example={"https://webhook1.example.com", "https://webhook2.example.com"}),
     *                 @OA\Property(property="valid_ips", type="array", @OA\Items(type="string"), example={"***********", "***********"})
     *             )
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=422,
     *         description="Validation failed due to incorrect request format",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="errors", type="object",
     *                 @OA\Property(property="webhook_endpoints", type="array", @OA\Items(type="string"), example={"The webhook_endpoints field is required."})
     *             )
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized - User does not have access",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="User is not eligible for web service")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized - Authentication required",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated")
     *         )
     *     )
     * )
     */
    public function update(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'webhook_endpoints' => 'present|array|max:5|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors'  => $validator->errors()
            ], 422);
        }

        $userId = auth()->user()->id;
        $specialUser = $this->specialUserRepository->findByUserId($userId);

        $specialUser = $this->specialUserRepository->updateWebhookEndpoints(
            $specialUser->id, $request->input('webhook_endpoints')
        );

        return response()->json([
            'success' => true,
            'data'    => [
                'webhook_endpoints' => $specialUser->webhook_endpoints,
                'valid_ips'         => json_decode($specialUser->valid_endpoints, true),
            ]
        ], 200);
    }
}
