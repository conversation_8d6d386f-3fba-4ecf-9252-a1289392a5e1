<?php

namespace App\Services\Directam;

use Illuminate\Support\Facades\Http;

class PackageCheckerService
{
    protected string $baseUrl;
    protected string $authorizationToken;

    public function __construct()
    {
        $this->baseUrl = config('services.directam.base_url');
        $this->authorizationToken = config('services.directam.authorization_token');
    }

    /**
     * Check if a user is subscribed to an annual package.
     *
     * @param string|null $phone User's phone number
     * @param string|null $instagramUsername User's Instagram username
     * @return bool Returns true if the user has an annual package, false otherwise
     */
    public function isAnnuallySubscribed(?string $phone = null, ?string $instagramUsername = null): bool
    {
        $response = $this->checkUserWithDirectam($phone, $instagramUsername);

        if ($response) {
            $decodedResponse = json_decode($response, true);

            return isset($decodedResponse['success']) && $decodedResponse['success'] === true &&
                isset($decodedResponse['data']['userSubscription']) &&
                isset($decodedResponse['data']['userSubscription']['package']) &&
                $decodedResponse['data']['userSubscription']['package'] === 'annually';
        }

        return false;
    }

    /**
     * Call Directam API to check if user is signed up.
     *
     * @param string|null $phone User's phone number
     * @param string|null $instagramUsername User's Instagram username
     * @return string|null JSON response from Directam API or null if the request fails
     */
    private function checkUserWithDirectam(?string $phone = null, ?string $instagramUsername = null): ?string
    {
        if ($phone !== null) {
            $queryParams = ['phone' => $phone];
        } elseif ($instagramUsername !== null) {
            $queryParams = ['instaUserName' => $instagramUsername];
        } else {
            return null;
        }

        $response = Http::withHeaders([
            'Authorization' => $this->authorizationToken,
        ])->get($this->baseUrl . '/ideh/is-signed-up', $queryParams);

        if ($response->successful()) {
            return $response->body();
        }

        return null;
    }
}
