<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class TestController extends Controller
{
    private function addCommas($number) {
        if (gettype($number) !== "number" && gettype($number) !== "string") return "";
    
        $convertedToString = (string)$number;
        $tokenizedToEnglish = isPersian($convertedToString)
            ? ((string)digitsFaToEn($convertedToString))
            : $convertedToString;
    
        $tokenizedNumber = explode(".",$tokenizedToEnglish);
        $integer = preg_replace("/(\d)(?=(\d{3})+(?!\d))/g","$1,",$tokenizedNumber[0]);
        $decimal = $tokenizedNumber[1] ? ".".tokenizedNumber[1] : "";
    
        return $integer + $decimal;
    }
    // private function replaceArray ($string, $find) {
    //     $typolist = [
    //         "شیش صد"=> "ششصد",
    //         "شش صد"=> "ششصد",
    //         "هفت صد"=> "هفتصد",
    //         "هشت صد"=> "هشتصد",
    //         "نه صد"=> "نهصد",
    //     ];
    //     $pattern = new RegExp(implode("|",array_keys($find)), "gi");
    
    //     return string.replace(pattern, (matched) => find[matched] as string);
    // };
    private function performer($num) {
		if ($num <= 999) return transformeToWord($num);

		
		$seperated = (int)($num).toLocaleString().split(",");
        $numbersArr = [];
        foreach($seperated as $index=>$value){
            $transformedVal = $this->transformeToWord((int)$value);
            $unitName = $this->getUnitName((count($seperated) - ($index + 1))*3);
            
            $val = $transformedVal ? $transformedVal + " " + $unitName : "";
            if(count($val) > 1){
                $numbersArr[] = $val;
            }
        }
		// $numbersArr = $seperated
		// 	.map((value, index) => {
		// 		const { transformedVal, unitName } = Object.freeze({
		// 			transformedVal: transformeToWord(Number.parseInt(value, 10)),
		// 			unitName: getUnitName((seperated.length - (index + 1)) * 3),
		// 		});

		// 		return transformedVal ? transformedVal + " " + unitName : "";
		// 	})
		// 	.filter((val) => val.length > 1);
        $numbersArr = trim(implode(" و ",$numbersArr));
        return $numbersArr;
	}
    private function transformeToWord($num): string {
        if ($num === 0) return "";
        if ($num <= 9) return $this->getWord($num);
        else if ($num >= 11 && $num <= 19) return $this->getWord($num);

        $residual = $num <= 99 ? $num % 10 : $num % 100;
        return $residual === 0 ? $this->getWord($num) : $this->getWord($num - $residual)." و ".$this->transformeToWord($residual);
    }
    private function numbersWordList($n){
        $arr = [
            "1" => "یک",
            "2" => "دو",
            "3" => "سه",
            "4" => "چهار",
            "5" => "پنج",
            "6" => "شش",
            "7" => "هفت",
            "8" => "هشت",
            "9" => "نه",
            "10" => "ده",
            "11" => "یازده",
            "12" => "دوازده",
            "13" => "سیزده",
            "14" => "چهارده",
            "15" => "پانزده",
            "16" => "شانزده",
            "17" => "هفده",
            "18" => "هجده",
            "19" => "نوزده",
            "20" => "بیست",
            "30" => "سی",
            "40" => "چهل",
            "50" => "پنجاه",
            "60" => "شصت",
            "70" => "هفتاد",
            "80" => "هشتاد",
            "90" => "نود",
            "100" => "صد",
            "200" => "دویست",
            "300" => "سیصد",
            "400" => "چهار صد",
            "500" => "پانصد",
            "600" => "شش صد",
            "700" => "هفت صد",
            "800" => "هشت صد",
            "900" => "نه صد",
            "1000" => "هزار",
            "1000000" => "میلیون",
            "1000000000" => "میلیارد",
            "1000000000000" => "تریلیون",
            "1000000000000000" => "کوآدریلیون",
        ];
        try {
            return $arr[$n];
        } catch (\Throwable $th) {
            return "";
        }
        
    }
    private function getWord($n){
        $this->numbersWordList($n);
    }
    private function getUnitName($numberOfZeros){
        return $numberOfZeros === 0 ? "" : $this->numbersWordList("1".str_repeat("0",$numberOfZeros));
    }
    private function addNegativeSuffix ($str) {
        return "منفی" + " " + $str;
    }
    private function isSafeInteger($n){
        return $n > -(2**53 - 1) && $n < (2**53 - 1);
    }
    private function isNumberValid($n){
        return  gettype($n) === "number" && $this->isSafeInteger($n) && $n !== 0;
    }
    private function isNegative($n){
        return n < 0;
    }
    private function numberIsNotValidError() {
        return "numberToWords - the number must be a safe integer value";
    }
    private function addOrdinalSuffix($number=null){
        if (gettype($number) !== "string") {
            throw new TypeError("PersianTools: addOrdinalSuffix - The input must be string");
        }
    
        if (str_ends_with($number, "ی")) {
            return $number + " اُم";
        }
    
        if (str_ends_with($number, "سه")) {
            return array_slice($number ,0 , -2) + "سوم";
        }
    
        return $number + "م";
    }
    private function indexOf($array, $word) {
        foreach($array as $key => $value) if($value === $word) return $key;
        return -1;
    }
    private function removeCommas($str) {
        if (gettype($str) !== "string") {
            return "removeCommas - The input must be string";
        }
    
        $result = "" + $str;
        if ($this->indexOf($result,",") !== -1) {
            $result = preg_replace("/,\s?/g","",$result);
        }
    
        return (int)$result;
    }
    private function numberToWords($numberValue, $options) {
        // $isNumberValid = ($n) => typeof n === "number" && Number.isSafeInteger(n) && n !== 0;
        // $isNegative = (n: number) => n < 0;
        // $numberIsNotValidError = () =>
        //     TypeError("PersianTools: numberToWords - the number must be a safe integer value");
    
        if (gettype($numberValue) !== "string" && !$this->isSafeInteger($numberValue))
        {
            return $this->numberIsNotValidError();
        }
        $number = (int)(gettype($numberValue) === "number" ? $numberValue : $this->removeCommas($numberValue));
        if(isset($options["ordinal"])){
            $isOrdinal = $options["ordinal"] || false;
        }else{
            $isOrdinal = false;
        }
    
        $getWord = $this->getWord($n);
        $addNegativeSuffix = $this->addNegativeSuffix($str);
        $positiveNumber = abs($number);
        return $this->handleResult($numberValue,$number,$positiveNumber,$isOrdinal);
    }
    private function handleResult($numberValue,$number,$positiveNumber,$isOrdinal){
        if (Number($numberValue) === 0) return "صفر";
        if ($this->isNumberValid($number)) {
			$tmpResult = $this->isNegative($number)
				? $this->addNegativeSuffix($this->performer($positiveNumber))
				: $this->performer($positiveNumber);
			return $isOrdinal ? $this->addOrdinalSuffix($tmpResult) : $tmpResult;
		} else {
			return $this->numberIsNotValidError();
		}
    }
}
