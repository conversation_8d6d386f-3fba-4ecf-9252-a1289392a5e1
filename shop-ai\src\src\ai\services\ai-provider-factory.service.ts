import { Injectable } from '@nestjs/common';
import { OpenAiProvider } from '../providers/openai.provider';
import { IAiProvider } from '../../interfaces/ai-provider.interface';

@Injectable()
export class AiProviderFactory {
  getProvider(aiDriver: string): IAiProvider {
    switch (aiDriver) {
      case 'openai':
        return new OpenAiProvider();
      // Later we add more cases for other AI providers
      default:
        throw new Error(`AI provider for "${aiDriver}" is not supported`);
    }
  }
}
