<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class productAttributes extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'order',
        'type',
        'key',
        'product_id',
        'user_id',
    ];
    public function orderAttributes(): HasMany
    {
        return $this->hasMany(OrderAttribute::class);
    }
    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class,'product_id','id');
    }
}
