<?php

namespace App\Services\Instagram\Sync\Post;

use App\Contracts\Instagram\MetaPostServiceInterface;
use App\DTOs\Instagram\MetaConfigDTO;
use App\Services\Instagram\Abstract\AbstractPostService;

class InstagramPostService extends AbstractPostService implements MetaPostServiceInterface
{
    protected string $baseUrl = 'https://graph.instagram.com';

    public function __construct(MetaConfigDTO $config) {
        parent::__construct($config);
    }

    protected function buildInitialEndpoint(): string
    {
        return "{$this->baseUrl}/me/media?fields={$this->getMediaFields()}
        &access_token={$this->config->accessToken}";
    }
}
