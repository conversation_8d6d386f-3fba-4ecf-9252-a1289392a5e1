<?php

namespace App\Http\Controllers\Ai;

use App\Http\Controllers\Controller;
use App\Services\Ai\AiSettingService;
use Illuminate\Contracts\Auth\Authenticatable;

abstract class AbstractAiSettingController extends Controller
{
    protected AiSettingService $aiSettingService;
    protected ?Authenticatable $authUser;

    public function __construct(AiSettingService $aiSettingService)
    {
        $this->aiSettingService = $aiSettingService;
    }
}
