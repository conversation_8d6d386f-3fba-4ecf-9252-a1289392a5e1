<?php
/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/
#print($_GET['hub_challenge']);
#exit();


function connectd()
{
    include("asl.php");
    $conn = new PDO("mysql:host=$servername;dbname=$mydb", $username, $password, array(PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"));
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $conn;
}

$conn = connectd();



function sendInstaImage($sender_id,$image,$keyHolder,$access_token){

    print('a1');
    if($keyHolder==''){
        $all=array('recipient'=>array("id" => $sender_id),"message"=>array("attachment" => array('type'=>'image','payload'=>array('url'=>$image))));
    }
    else{
        $all=array('recipient'=>array("id" => $sender_id),"message"=>array("attachment" => array('type'=>'image','payload'=>array('url'=>$image)),'quick_replies'=>$keyHolder));
    }

    print('a2');
    print_r($all);
    $payload = json_encode($all,true);
    print("<br /> payload : <br />");
    print($payload);
    $url = "https://graph.facebook.com/v12.0/me/messages?access_token=$access_token";
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />hey   RESULT->>> : <hr /><hr />");

    print($result);
    return$result;


}












#print($_GET['hub_challenge']);
#exit();

function sendmessage($text,$chatid,$keys){
    $send = "https://api.telegram.org/bot5076049244:AAFLjQ745jOHsOFVYTugGwZHkk-BKUrXU34/sendmessage?chat_id=$chatid&text=$text&parse_mode=Html&reply_markup=$keys";
    $c = curl_init($send);
    curl_setopt($c, CURLOPT_RETURNTRANSFER, true);
    //curl_setopt(... other options you want...)
    curl_setopt($c, CURLOPT_FOLLOWLOCATION, true);
    //curl_setopt($c, CURLOPT_RETURNTRANSFER, true);
    $sended = curl_exec($c);
    if (curl_error($c)) {
        die(curl_error($c));
    }
    // Get the status code
    $status = curl_getinfo($c, CURLINFO_HTTP_CODE);
    curl_close($c);
    $decodeData = json_decode($sended, true);
    $id_message = $decodeData['result']['message_id'];
    print$sended;
    return $id_message;
}
//sendmessage(urlencode($datamytext_postback.'content is top : '.$content),80433998,'');



function sendInstaTemplateMessage($user_id,$templateHolder,$access_token){

    $all=array('recipient'=>array("id" => $user_id),"message"=>array("attachment" => array("type"=>"template","payload"=>array("template_type"=>"generic","elements"=>$templateHolder))));
    $json_encode_data = json_encode($all,true);
    $payload = json_encode($all,true);
    $url = "https://graph.facebook.com/v12.0/me/messages?access_token=$access_token";
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />RESULT->>> : <hr /><hr />");
    print($result);
    return($result);

}


function sendInstaMessage($sender_id,$text,$keyHolder,$access_token){


    if($keyHolder==''){
        $all=array('recipient'=>array("id" => $sender_id),"message"=>array("text" => $text));
    }
    else{
        $test= json_encode($keyHolder);
        $all=array('recipient'=>array("id" => $sender_id),"message"=>array("text" => $text,'quick_replies'=>$keyHolder));
    }

    print('a2');
    print_r($all);
    $payload = json_encode($all);
    print("<br /> payload : <br />");
    print($payload);
    $url = "https://graph.facebook.com/v14.0/me/messages?access_token=$access_token";
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />hey   RESULT->>> : <hr /><hr />");



    print($result);
    return$result;


}

function getPageId($access_token){

    $url = "https://graph.facebook.com/v12.0/me?fields=id&access_token=$access_token";
    $ch = curl_init($url);


    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />RESULT : <hr /><hr />");
    print($result);
    $data = json_decode($result, true);
    return $data['id'];

}




function getUsername($message_mid,$access_token){

    $url = "https://graph.facebook.com/v12.0/$message_mid?fields=from&access_token=$access_token";
    $ch = curl_init($url);


    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />RESULT : <hr /><hr />");
    print($result);
    $data = json_decode($result, true);
    return $data['from']['username'];

}

function getInstaPageId($access_token){
    $url="https://graph.facebook.com/v14.0/me/instagram_accounts?access_token=$access_token";
    #$url = "https://graph.facebook.com/v12.0/me?fields=instagram_business_account&access_token=$access_token";
    $ch = curl_init($url);


    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />RESULT : <hr /><hr />");
    print($result);
    $data = json_decode($result, true);
    $insta_page_id=$data['data'][0]['id'];


    return $insta_page_id;
}
function getInstaId($access_token,$insta_page_id){





    $url="https://graph.facebook.com/v14.0/$insta_page_id?fields=username&access_token=$access_token";

    $ch = curl_init($url);


    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />RESULT : <hr /><hr />");
    print($result);
    $data = json_decode($result, true);






    return $data['username'];

}

function getMessageInfo($access_token,$mid){





    $url="https://graph.facebook.com/v14.0/$mid?fields=from,to&access_token=$access_token";

    $ch = curl_init($url);


    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />RESULT : <hr /><hr />");
    print($result);
    $data = json_decode($result, true);
    print_r($data);


    if($data['to']['data'][0]['username']){
        $mainData[]=$data['to']['data'][0]['username'];
        $mainData[]=$data['to']['data'][0]['id'];

        return $mainData;
    }
    else{
        return 'empty';
    }






    return $data['username'];

}

function getUsernameV2($access_token,$user_id){

    $url = "https://graph.facebook.com/v12.0/$user_id?fields=username&access_token=$access_token";
    $ch = curl_init($url);


    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />RESULT : <hr /><hr />");
    print($result);
    $data = json_decode($result, true);
    return $data['username'];

}


#$username=getUsername('17841451817326582');

function getName($user_id,$access_token){

    $url = "https://graph.facebook.com/v12.0/$user_id?fields=name&access_token=$access_token";
    $ch = curl_init($url);


    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    print("<hr /><hr />RESULT : <hr /><hr />");
    print($result);
    $data = json_decode($result, true);
    return $data['name'];

}


function ifnewuserinsert($user_id, $username, $name, $conn,$mainUserId)
{
    if($user_id==''){return;}
    //$sql = "insert into instagram_users (username,name,userId,mainUserId) values (\"$username\",\"$name\",\"$user_id\",\"$mainUserId\")";
    //$conn->exec($sql);
}

function getMainUserData($insta_id,$conn){
    /*
    $sql="select * from users where insta_id=\"$insta_id\"  ";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stmt->setFetchMode(PDO::FETCH_ASSOC);
    $getter = $stmt->fetchAll();
    $numrowfusers = $stmt->rowCount();

    if($numrowfusers==0){
        return'no_user';
    }

    return$getter[0];
    */

}

function getTemplateInfo($id_template,$conn){
    /*
    $sql="select * from services where id=\"$id_template\"  ";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stmt->setFetchMode(PDO::FETCH_ASSOC);
    $getter = $stmt->fetchAll();
    $numrowfusers = $stmt->rowCount();

    return$getter[0];
    */
}


function getinfo($user_id, $conn, $username, $name,$mainUserId)
{
    /*
    global $req1,$req2,$req3,$is_new,$id,$name,$phone,$address,$page_id,$insta_id,$chatbot_status;

    $sql="select * from instagram_users where userId=\"$user_id\"  and mainUserId=\"$mainUserId\" ";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stmt->setFetchMode(PDO::FETCH_ASSOC);
    $getter = $stmt->fetchAll();
    $numrowfusers = $stmt->rowCount();

    if ($numrowfusers == 0) {
        ifnewuserinsert($user_id, $username, $name, $conn,$mainUserId);
        $is_new='yes';
    }
    else{
        $is_new='no';
    }


    $req1 =  $getter[0]["lastreq1"];
    $req2 =  $getter[0]["lastreq2"];
    $req3 =  $getter[0]["lastreq3"];
    $chatbot_status= $getter[0]["chatbot_status"];

    $name =  $getter[0]["name"];
    $phone =  $getter[0]["phone"];
    $address =  $getter[0]["address"];

    $id =  $getter[0]["id"];
*/

}
