<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 */
	public function up(): void
	{
		Schema::create('crm_tracks', function (Blueprint $table) {
			$table->id();
			$table->foreignId('crm_action_id')->constrained()->restrictOnDelete();
			$table->foreignId('user_id')->constrained()->cascadeOnDelete();
			$table->foreignId('page_id')->nullable()->constrained()->cascadeOnDelete();
			$table->foreignId('transaction_id')->nullable()->constrained()->cascadeOnDelete();
			$table->boolean('is_processed')->default(false);
			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 */
	public function down(): void
	{
		Schema::dropIfExists('crm_tracks');
	}
};
