import{ap as h,au as w,bS as z,aq as U,bl as H,ax as O,as as _,b6 as R,by as $,a8 as n,P as J,c3 as Q,bb as W,a1 as u,at as N,b as d,bK as X,aV as D,r as Y,bA as Z,s as T,K as p,bP as ee,F as le,a4 as ae,V as te,bm as oe,b_ as I,aC as ne,az as ue,c4 as ie,aN as re}from"./index-169996dc.js";import{a as ce}from"./VInput-c4d3942a.js";const j=Symbol.for("vuetify:selection-control-group"),q=h({color:String,disabled:{type:Boolean,default:null},defaultsTarget:String,error:Boolean,id:String,inline:Boolean,falseIcon:w,trueIcon:w,ripple:{type:[Boolean,Object],default:!0},multiple:{type:Boolean,default:null},name:String,readonly:{type:Boolean,default:null},modelValue:null,type:String,valueComparator:{type:Function,default:z},...U(),...H(),...O()},"SelectionControlGroup"),se=h({...q({defaultsTarget:"VSelectionControl"})},"VSelectionControlGroup"),be=_()({name:"VSelectionControlGroup",props:se(),emits:{"update:modelValue":e=>!0},setup(e,i){let{slots:v}=i;const l=R(e,"modelValue"),t=$(),b=n(()=>e.id||`v-selection-control-group-${t}`),c=n(()=>e.name||b.value),a=new Set;return J(j,{modelValue:l,forceUpdate:()=>{a.forEach(o=>o())},onForceUpdate:o=>{a.add(o),Q(()=>{a.delete(o)})}}),W({[e.defaultsTarget]:{color:u(e,"color"),disabled:u(e,"disabled"),density:u(e,"density"),error:u(e,"error"),inline:u(e,"inline"),modelValue:l,multiple:n(()=>!!e.multiple||e.multiple==null&&Array.isArray(l.value)),name:c,falseIcon:u(e,"falseIcon"),trueIcon:u(e,"trueIcon"),readonly:u(e,"readonly"),ripple:u(e,"ripple"),type:u(e,"type"),valueComparator:u(e,"valueComparator")}}),N(()=>{var o;return d("div",{class:["v-selection-control-group",{"v-selection-control-group--inline":e.inline},e.class],style:e.style,role:e.type==="radio"?"radiogroup":void 0},[(o=v.default)==null?void 0:o.call(v)])}),{}}}),de=h({label:String,baseColor:String,trueValue:null,falseValue:null,value:null,...U(),...q()},"VSelectionControl");function ve(e){const i=te(j,void 0),{densityClasses:v}=oe(e),l=R(e,"modelValue"),t=n(()=>e.trueValue!==void 0?e.trueValue:e.value!==void 0?e.value:!0),b=n(()=>e.falseValue!==void 0?e.falseValue:!1),c=n(()=>!!e.multiple||e.multiple==null&&Array.isArray(l.value)),a=n({get(){const f=i?i.modelValue.value:l.value;return c.value?I(f).some(r=>e.valueComparator(r,t.value)):e.valueComparator(f,t.value)},set(f){if(e.readonly)return;const r=f?t.value:b.value;let m=r;c.value&&(m=f?[...I(l.value),r]:I(l.value).filter(y=>!e.valueComparator(y,t.value))),i?i.modelValue.value=m:l.value=m}}),{textColorClasses:o,textColorStyles:C}=ne(n(()=>{if(!(e.error||e.disabled))return a.value?e.color:e.baseColor})),{backgroundColorClasses:V,backgroundColorStyles:S}=ue(n(()=>a.value&&!e.error&&!e.disabled?e.color:e.baseColor)),k=n(()=>a.value?e.trueIcon:e.falseIcon);return{group:i,densityClasses:v,trueValue:t,falseValue:b,model:a,textColorClasses:o,textColorStyles:C,backgroundColorClasses:V,backgroundColorStyles:S,icon:k}}const ye=_()({name:"VSelectionControl",directives:{Ripple:X},inheritAttrs:!1,props:de(),emits:{"update:modelValue":e=>!0},setup(e,i){let{attrs:v,slots:l}=i;const{group:t,densityClasses:b,icon:c,model:a,textColorClasses:o,textColorStyles:C,backgroundColorClasses:V,backgroundColorStyles:S,trueValue:k}=ve(e),f=$(),r=D(!1),m=D(!1),y=Y(),g=n(()=>e.id||`input-${f}`),P=n(()=>!e.disabled&&!e.readonly);t==null||t.onForceUpdate(()=>{y.value&&(y.value.checked=a.value)});function x(s){P.value&&(r.value=!0,ie(s.target,":focus-visible")!==!1&&(m.value=!0))}function A(){r.value=!1,m.value=!1}function E(s){s.stopPropagation()}function K(s){P.value&&(e.readonly&&t&&re(()=>t.forceUpdate()),a.value=s.target.checked)}return N(()=>{var B,G;const s=l.label?l.label({label:e.label,props:{for:g.value}}):e.label,[L,M]=Z(v),F=d("input",T({ref:y,checked:a.value,disabled:!!e.disabled,id:g.value,onBlur:A,onFocus:x,onInput:K,"aria-disabled":!!e.disabled,"aria-label":e.label,type:e.type,value:k.value,name:e.name,"aria-checked":e.type==="checkbox"?a.value:void 0},M),null);return d("div",T({class:["v-selection-control",{"v-selection-control--dirty":a.value,"v-selection-control--disabled":e.disabled,"v-selection-control--error":e.error,"v-selection-control--focused":r.value,"v-selection-control--focus-visible":m.value,"v-selection-control--inline":e.inline},b.value,e.class]},L,{style:e.style}),[d("div",{class:["v-selection-control__wrapper",o.value],style:C.value},[(B=l.default)==null?void 0:B.call(l,{backgroundColorClasses:V,backgroundColorStyles:S}),p(d("div",{class:["v-selection-control__input"]},[((G=l.input)==null?void 0:G.call(l,{model:a,textColorClasses:o,textColorStyles:C,backgroundColorClasses:V,backgroundColorStyles:S,inputNode:F,icon:c.value,props:{onFocus:x,onBlur:A,id:g.value}}))??d(le,null,[c.value&&d(ae,{key:"icon",icon:c.value},null),F])]),[[ee("ripple"),e.ripple&&[!e.disabled&&!e.readonly,null,["center","circle"]]]])]),s&&d(ce,{for:g.value,onClick:E},{default:()=>[s]})])}),{isFocused:r,input:y}}});export{ye as V,q as a,be as b,de as m};
