import{m as G,f as H,V as J,a as L}from"./VField-150a934a.js";import{m as Q,u as W,V as I}from"./VInput-c4d3942a.js";import{ap as X,as as Y,b$ as Z,b6 as ee,a8 as v,r as x,at as te,bA as ne,b as n,s as V,K as le,bP as ae,F as y,c0 as ue,bi as ie,aN as S,c1 as oe}from"./index-169996dc.js";const se=["color","file","time","date","datetime-local","week","month"],re=X({autofocus:Boolean,counter:[Boolean,Number,String],counterValue:[Number,Function],prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,suffix:String,role:String,type:{type:String,default:"text"},modelModifiers:Object,...Q(),...G()},"VTextField"),me=Y()({name:"VTextField",directives:{Intersect:Z},inheritAttrs:!1,props:re(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,w){let{attrs:m,emit:g,slots:l}=w;const i=ee(e,"modelValue"),{isFocused:s,focus:A,blur:p}=W(e),D=v(()=>typeof e.counterValue=="function"?e.counterValue(i.value):typeof e.counterValue=="number"?e.counterValue:(i.value??"").toString().length),N=v(()=>{if(m.maxlength)return m.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter}),C=v(()=>["plain","underlined"].includes(e.variant));function R(t,a){var u,o;!e.autofocus||!t||(o=(u=a[0].target)==null?void 0:u.focus)==null||o.call(u)}const F=x(),P=x(),r=x(),T=v(()=>se.includes(e.type)||e.persistentPlaceholder||s.value||e.active);function d(){var t;r.value!==document.activeElement&&((t=r.value)==null||t.focus()),s.value||A()}function B(t){g("mousedown:control",t),t.target!==r.value&&(d(),t.preventDefault())}function M(t){d(),g("click:control",t)}function E(t){t.stopPropagation(),d(),S(()=>{i.value=null,oe(e["onClick:clear"],t)})}function O(t){var u;const a=t.target;if(i.value=a.value,(u=e.modelModifiers)!=null&&u.trim&&["text","search","password","tel","url"].includes(e.type)){const o=[a.selectionStart,a.selectionEnd];S(()=>{a.selectionStart=o[0],a.selectionEnd=o[1]})}}return te(()=>{const t=!!(l.counter||e.counter!==!1&&e.counter!=null),a=!!(t||l.details),[u,o]=ne(m),{modelValue:ce,...U}=I.filterProps(e),j=H(e);return n(I,V({ref:F,modelValue:i.value,"onUpdate:modelValue":c=>i.value=c,class:["v-text-field",{"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-input--plain-underlined":C.value},e.class],style:e.style},u,U,{centerAffix:!C.value,focused:s.value}),{...l,default:c=>{let{id:f,isDisabled:b,isDirty:h,isReadonly:z,isValid:K}=c;return n(J,V({ref:P,onMousedown:B,onClick:M,"onClick:clear":E,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"],role:e.role},j,{id:f.value,active:T.value||h.value,dirty:h.value||e.dirty,disabled:b.value,focused:s.value,error:K.value===!1}),{...l,default:$=>{let{props:{class:k,...q}}=$;const _=le(n("input",V({ref:r,value:i.value,onInput:O,autofocus:e.autofocus,readonly:z.value,disabled:b.value,name:e.name,placeholder:e.placeholder,size:1,type:e.type,onFocus:d,onBlur:p},q,o),null),[[ae("intersect"),{handler:R},null,{once:!0}]]);return n(y,null,[e.prefix&&n("span",{class:"v-text-field__prefix"},[n("span",{class:"v-text-field__prefix__text"},[e.prefix])]),l.default?n("div",{class:k,"data-no-activator":""},[l.default(),_]):ue(_,{class:k}),e.suffix&&n("span",{class:"v-text-field__suffix"},[n("span",{class:"v-text-field__suffix__text"},[e.suffix])])])}})},details:a?c=>{var f;return n(y,null,[(f=l.details)==null?void 0:f.call(l,c),t&&n(y,null,[n("span",null,null),n(L,{active:e.persistentCounter||s.value,value:D.value,max:N.value},l.counter)])])}:void 0})}),ie({},F,P,r)}});export{me as V,re as m};
