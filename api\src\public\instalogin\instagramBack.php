<?php
/**
 * instagramBack.php - Handles the Instagram OAuth flow to connect the Prest to a user's Instagram account.
 *
 * It begins by capturing an authorization code from Instagram, exchanging it for an access token, and fetching user
 * information.
 * The script then saves this data to MongoDB and finalizes the connection to the Prest application.
 *
 * Steps:
 *
 * 1. **Capture Authorization Code**:
 *    Retrieve the `code` and `state` parameters from the Instagram redirect. The `state` parameter should contain
 *    user and page IDs (`uid` and `pid`) to identify the session.
 *
 * 2. **Exchange Authorization Code for Access Token**:
 *    Use the authorization code to request an access token from Instagram, allowing API access on behalf of the user.
 *
 * 3. **Exchange Access Token**:
 *     Use the short live access token to get the long live one.
 *
 * 4. **Fetch User Data**:
 *    Retrieve the user’s Instagram ID, username, and account type using the access token, ensuring proper permissions.
 *
 * 5. **Save Account Data to MongoDB**:
 *    Prepare the user’s Instagram data and access token and send them to the Prest application's `save-insta-to-mongo`
 *    endpoint to store them in MongoDB for future access.
 *
 * 6. **Finalize Instagram Connection**:
 *    Complete the Instagram connection by sending `user_id`, `page_id`, and `instagram_id` to the Prest backend’s
 *    `connectToInstagramAccount` endpoint, which registers the Instagram account with the Prest system.
 *
 * 7. **Redirect to Prest Application**:
 *    Redirect the user back to the Prest application's Instagram connection page to complete the connection flow.
 *
 */

$redirectUrl = "https://dmplus.manymessage.com/pages";

try {
    // Step 1: Capture Authorization Code from Facebook Response
    $code = $_GET['code'] ?? null;
    $state = $_GET['state'] ?? null;

    if (!$code || !$state) {
        throw new Exception('Authorization code or state parameter missing');
    }

    $stateData = json_decode(urldecode($state), true);

    if (!isset($stateData['uid'], $stateData['pid'])) {
        throw new Exception('Invalid state parameter');
    }

    // Step 2: Exchange the authorization code for an access token
    $clientId = "****************";
    $clientSecret = "cc187cdc00f53e81debc2ccd77470bce";
    $redirectUri = "https://dmplus.manymessage.com/instalogin/instagramBack.php";
    $tokenEndpoint = "https://api.instagram.com/oauth/access_token";

    $postFields = [
        'client_id' => $clientId,
        'client_secret' => $clientSecret,
        'grant_type' => 'authorization_code',
        'redirect_uri' => $redirectUri,
        'code' => $code
    ];

    $ch = curl_init($tokenEndpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        throw new Exception('cURL Error: ' . curl_error($ch));
    }
    curl_close($ch);

    $responseData = json_decode($response, true);

    if (!isset($responseData['access_token'])) {
        throw new Exception('Failed to retrieve access token');
    }

    $shortLiveAccessToken = $responseData['access_token'];
    $userId = $responseData['user_id'];

    // Step 3: Exchange short live access token with a long live one
    $longLiveTokenApi = "https://graph.instagram.com/access_token?grant_type=ig_exchange_token";

    $apiData = [
        'grant_type' => 'ig_exchange_token',
        'client_secret' => $clientSecret,
        'access_token' => $shortLiveAccessToken
    ];

    $queryString = http_build_query($apiData);
    $url = $longLiveTokenApi . '&' . $queryString;

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPGET, true);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        throw new Exception('cURL Error: ' . curl_error($ch));
    }
    curl_close($ch);

    $responseData = json_decode($response, true);

    if (!isset($responseData['access_token'])) {
        throw new Exception('Failed to receive long live access token');
    }

    $accessToken = $responseData['access_token'];

    // Step 4: Fetch user data using the access token
    $userEndpoint = "https://graph.instagram.com/me?fields=user_id,username,account_type&access_token={$accessToken}";

    $ch = curl_init($userEndpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $userResponse = curl_exec($ch);

    if (curl_errno($ch)) {
        throw new Exception('cURL Error: ' . curl_error($ch));
    }
    curl_close($ch);

    $userData = json_decode($userResponse, true);

    if (!isset($userData['user_id'], $userData['username'])) {
        throw new Exception('Failed to retrieve user data');
    }

    // Step 5: Send parameters to PageController@saveInstagramAccountToMongo

    $apiEndpoint = "https://dmplus.manymessage.com/api/save-insta-to-mongo/{$stateData['uid']}";

    $accountsPayload = [[
        'access_token' => $accessToken,
        'fbpage_id' => null,
        'fbpage_name' => null,
        'insta_id' => $userData['user_id'],
        'insta_username' => $userData['username'],
        'insta_follower_count' => $userData['followers_count'] ?? '0'
    ]];

    $apiData = [
        'at' => $accessToken,
        'accounts' => $accountsPayload
    ];

    $ch = curl_init($apiEndpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($apiData));

    $apiResponse = curl_exec($ch);

    if (curl_errno($ch)) {
        throw new Exception('PageController@saveInstagramAccountToMongo Error: ' . curl_error($ch));
    }
    curl_close($ch);

    // Step 6: Finalize instagram connection via PageController@connectToInstagramAccount

    $apiEndpoint = "https://dmplus.manymessage.com/api/instagram-account";

    $apiData = [
        'user_id' => $stateData['uid'],
        'page_id' => $stateData['pid'],
        'instagram_id' => $userData['user_id']
    ];

    $ch = curl_init($apiEndpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($apiData));

    $apiResponse = curl_exec($ch);

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        throw new Exception('cURL Error: ' . curl_error($ch));
    }
    curl_close($ch);

    // Step 7: Returning to Prest instagram connection page

    header("Location: $redirectUrl");
    exit;

} catch (Exception $e) {
//    echo "Login Error: " . $e->getMessage();
    header("Location: $redirectUrl");
    exit;
}
