stages:
  - build
  - deploy
variables:
  IMAGE_NAME: "${OCI_REGISTRY_IMAGE}:${CI_COMMIT_SHORT_SHA}"
  IMAGE_NAME_LATEST: ${OCI_REGISTRY_IMAGE}:latest
build-docker:
  stage: build
  image: docker:cli
  services:
    - docker:dind
  before_script:
    - docker login --username "${CI_REGISTRY_USER}" --password "${CI_REGISTRY_PASSWORD}" "${CI_REGISTRY}"
  script:
    - docker buildx build .
      --file Dockerfile
      --build-arg APP_COMMIT_SHA=${CI_COMMIT_SHA}
      --platform linux/amd64
      --cache-from "type=registry,ref=${IMAGE_NAME_LATEST}"
      --cache-to "type=inline"
      --output "type=image,oci-mediatypes=true,store=false,compression=zstd,compression-level=3,\"name=${IMAGE_NAME},${IMAGE_NAME_LATEST}\",push=true"
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - when: always

deploy-prod:
  stage: deploy
  tags:
    - directam-ovh3-deployer
  before_script:
    - mkdir -p ~/.ssh
    - |-
      echo -e "
      StrictHostKeyChecking='no'
      PreferredAuthentications='publickey'
      IdentityFile='~/.ssh/deploy_server_key'
      Compression='yes'
      " > ~/.ssh/config
    - echo -e "-----BEGIN OPENSSH PRIVATE KEY-----\n${DEPLOY_KEY_PROD}\n-----END OPENSSH PRIVATE KEY-----" > ~/.ssh/deploy_server_key
    - chmod 400 ~/.ssh/deploy_server_key

  script:
    - ssh -i deploy_server_key -p "${DEPLOY_PORT_PROD}" "${DEPLOY_USER_PROD}"@"${DEPLOY_HOST_PROD_DMPLUS}" "
        docker login --username '${OCI_REGISTRY_USER}' --password '${OCI_REGISTRY_PASSWORD}' '${OCI_REGISTRY}' &&
        docker image pull --quiet '${IMAGE_NAME}' &&
        dotenv --file '${DEPLOY_PATH_PROD}/environments/docker-compose.env' set DMPLUS_SHOP_AI_IMAGE_NAME='${IMAGE_NAME}' &&
        docker compose --project-directory '${DEPLOY_PATH_PROD}' up --pull never --remove-orphans --detach shop-ai"
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v.*$/'
      when: manual
    - when: never