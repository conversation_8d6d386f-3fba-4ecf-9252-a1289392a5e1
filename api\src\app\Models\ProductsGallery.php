<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductsGallery extends Model
{
    use HasFactory,SoftDeletes;
    protected $fillable = [
        'description',
        'path',
    ];
    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class, 'product_id');
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
//    public function page_content(): BelongsTo
//    {
//        return $this->belongsTo(PageContent::class, 'page_content_id');
//    }
}
