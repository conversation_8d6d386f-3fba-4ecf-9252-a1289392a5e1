// 👉 IsEmpty
import { useConfigStore } from '@core/stores/config'
import { useUserDataStore } from '@layouts/stores/useUserDataStore'

import * as jalaali from 'jalaali-js'
import Swal from 'sweetalert2'

export const isEmpty = value => {
  if (value === null || value === undefined || value === '')
    return true
  
  return !!(Array.isArray(value) && value.length === 0)
}


// Convert Persian numbers to Western
export const convertPersianNumbersToWestern = s => {
  const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹']
  
  return s.replace(/[۰-۹]/g, m => persianNumbers.indexOf(m))
}

export const isNumeric = str=> {
  if (typeof str != "string") return false // we only process strings!  
  
  // Convert and then check if numeric
  const convertedStr = convertPersianNumbersToWestern(str)
  
  return !isNaN(convertedStr) && !isNaN(parseFloat(convertedStr))
}

// 👉 IsNullOrUndefined
export const isNullOrUndefined = value => {
  return value === null || value === undefined
}

// 👉 IsEmptyArray
export const isEmptyArray = arr => {
  return Array.isArray(arr) && arr.length === 0
}

// 👉 IsObject
export const isObject = obj => obj !== null && !!obj && typeof obj === 'object' && !Array.isArray(obj)

// 👉 IsToday
export const isToday = date => {
  const today = new Date()
  
  return (date.getDate() === today.getDate()
        && date.getMonth() === today.getMonth()
        && date.getFullYear() === today.getFullYear())
}

export const generateToken = () => {
  const min = 100000 // Minimum 6-digit number
  const max = 999999 // Maximum 6-digit number

  return Math.floor(Math.random() * (max - min + 1)) + min
}

export const getUploadedPath=(file, directory)=>{
  
  
  let path = ''
  if(file != null&& file !=''){
    path = `${ import.meta.env.VITE_UPLOAD_URL}${directory}/${file}`
  }
  
  return path
} 
export const getJalaiTextDateOfGregorianDate=(gregorianDate, format)=>{

  const jalaliMonths = [
    "فروردین",  // Farvardin
    "اردیبهشت", // Ordibehesht
    "خرداد",    // Khordad
    "تیر",      // Tir
    "مرداد",    // Mordad
    "شهریور",   // Shahrivar
    "مهر",      // Mehr
    "آبان",     // Aban
    "آذر",      // Azar
    "دی",       // Dey
    "بهمن",     // Bahman
    "اسفند",     // Esfand
  ]

  console.log(gregorianDate)


  gregorianDate = gregorianDate.split(" ")[0]

  const date =  gregorianDate.split('/')
  
  return `${date[2]} ${jalaliMonths[parseInt(date[1])-1]} ماه ${date[0]} ` 



} 
export const  blobToFile =(theBlob, fileName)=>{       
  return new File([theBlob], fileName, { lastModified: new Date().getTime(), type: theBlob.type })
}
export const  swalFire =swalData=>{       
  let color = '#F8F7FA'
  let text='#2F2B3D'
  const configStore = useConfigStore()

  if((usePreferredDark().value && configStore.theme=='system') || configStore.theme == 'dark'){
    color = '#25293C'
    text='#D0D4F1'
  }
  swalData.background=color
  swalData.color=text
  
  return Swal.fire(swalData)
}

export const ensureCompleteUrl = url=> {
  // Check if the URL starts with http:// or https://
  if (!/^https?:\/\//i.test(url)) {
    // If it doesn't, prepend http:// to the URL
    return 'http://' + url
  }

  // If it does, return the URL as is
  return url
}
export const  validateLink =link=> {
  // Regular expression to match a valid URL
  var urlPattern = /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i

  // Test if the provided link matches the pattern
  return urlPattern.test(link)
}

export const calcOffPercent = (price, offPrice) => {
  // First, check if the price and offPrice are valid numbers
  price = parseInt(price)
  offPrice =  parseInt(offPrice)
  if (typeof price !== 'number' || typeof offPrice !== 'number' || price <= 0 || offPrice < 0 || offPrice > price) {
    throw new Error("Invalid input: Price and offPrice must be numbers, with price > 0 and 0 <= offPrice <= price")
  }

  // Calculate the discount percentage
  const discountPercent = ((price - offPrice) / price) * 100

  // Return the discount percentage, rounded to 2 decimal places
  return `${Math.round(discountPercent * 100) / 100} %`
}


export const  checkImageUrl = url=> {
  return new Promise((resolve, reject) => {


    // Create a new Image object
    const img = new Image()

    // Set up event listener for successful image load
    img.onload = () => resolve(true)

    // Set up event listener for error in loading image
    img.onerror = () => resolve(false)

    // Attempt to load the image
    img.src = url
  })
}


export const  differenceInDaysBetweenJalaliDates = targetDateString  =>{
  try {
    const now = new Date()


    // Parse target Jalali date string
    const datePart = targetDateString.split(' ')[0]

    console.log('datePart => ', datePart)

    const [year, month, day] = datePart.split('/').map(Number)


    // Convert target Jalali date to Gregorian
    const targetGregorian = jalaali.toGregorian(year, month, day)


    const targetDate = new Date(targetGregorian.gy, targetGregorian.gm - 1, targetGregorian.gd)

 
    // Calculate difference in milliseconds
    const diffMilliseconds = targetDate - now

    console.log('diffMilliseconds => ', diffMilliseconds)
    

    // Convert milliseconds to days
    const diffDays = diffMilliseconds / (1000 * 60 * 60 * 24)

    console.log('diffDays => ', diffDays)
 
    return Math.floor(diffDays)
  } catch (error) {
    return 0 
  }
}

export const getUserData = async()=>{
  try {
    const res = await $api('get-user', {
      method: 'GET',
    })

    useCookie('userData').value = res.data

    const userDataStore = useUserDataStore()

    userDataStore.setCredit(differenceInDaysBetweenJalaliDates(res.data['expiration_date']))
    userDataStore.setFreeComments(res.data['comments_count'])
  } catch (err) {
    console.log(err)
    
  }

}

