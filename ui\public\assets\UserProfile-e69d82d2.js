import{r as m,j as Y,a2 as f,a8 as Q,w as Z,o as s,f as n,e as a,b as t,n as e,a9 as E,ab as H,a4 as F,v as _,d as P,c as h,F as k,i as z,a6 as M,ai as w,x as y,aj as R,z as S,aa as ee,ak as ae,al as B,_ as te,k as oe,am as le,H as se,an as ne,a as ie,ac as q,ao as re,s as ce,R as ue,U as de}from"./index-169996dc.js";import{V as pe,_ as fe}from"./VSkeletonLoader-4c44fbcf.js";import{_ as me}from"./DialogCloseBtn-b32209d9.js";import{v as G}from"./validations-4c0aab88.js";import{a as j}from"./profile_ph-c1153e1f.js";import{V as _e,a as ge}from"./VRadioGroup-52375e28.js";import{V as ve,a as O}from"./VRow-6c1d54f3.js";import{V as ye}from"./VTextField-a8984053.js";import{P as he}from"./vue3-perfect-scrollbar.esm-e1f82a0e.js";import{u as ke}from"./useAbility-e6e49333.js";import{V as Ve}from"./VMenu-2cfb0f14.js";import{V as be,a as J,b as K,c as N}from"./VList-349a1ccf.js";import{a as we,V as T}from"./VBadge-751ce1d1.js";import{V as Ce}from"./VDivider-12bfa926.js";import"./VSelectionControl-8ecbcf09.js";import"./VInput-c4d3942a.js";import"./index-00c7d20d.js";import"./VField-150a934a.js";import"./ssrBoot-c101cd97.js";const Ie={class:"pa-2"},xe={class:"d-flex flex-row-reverse align-center ga-2"},De={class:"d-flex flex-column align-end"},Pe={class:"font-weight-bold text-sm"},Se={__name:"ShowAccountsDialog",props:{isDialogVisible:{type:Boolean,required:!1}},emits:["update:isDialogVisible"],setup(V,{emit:U}){const $=V,b=U,l=m("");Y();const C=m(!1),I=m(!1),g=m([]),A=f("pageId").value,p=m(null);A&&(p.value=A);const i=m(!1),x=()=>{i.value=!0},L=async()=>{try{const o=await B.post("/pages",{title:l.value}),r=f("pageId");r.value=o.data.id,window.open("https://prest.manymessage.com/pages","_self")}catch{}},D=Q({get(){return $.isDialogVisible},set(o){b("update:isDialogVisible",o)}}),u=async()=>{try{C.value=!0;const o=await B.get("/pages");g.value=o.data.data}catch{G("خطا در دریافت اطلاعات")}finally{C.value=!1}},v=o=>{p.value=o},d=async()=>{try{I.value=!0;const o=f("pageId");o.value=p.value,window.open("https://prest.manymessage.com/pages","_self")}catch{G("خطا در انجام عملیات")}finally{I.value=!1}};return Z(D,o=>{const r=f("pageId").value;r&&(p.value=r),console.log(o),o===!0&&u()}),(o,r)=>{const W=me,X=fe;return s(),n(ae,{"max-width":"600","model-value":e(D),"onUpdate:modelValue":r[3]||(r[3]=c=>b("update:isDialogVisible",c))},{default:a(()=>[t(W,{onClick:r[0]||(r[0]=c=>D.value=!1)}),e(i)?S("",!0):(s(),n(E,{key:0,"max-width":"600",class:"pa-4"},{default:a(()=>[t(H,null,{default:a(()=>[t(F,{icon:"tabler-users",color:"rgb(193, 53, 132)"}),_(" حساب های کاربری ")]),_:1}),P("div",Ie,[e(C)?(s(),h(k,{key:0},z(5,c=>t(pe,{key:c,type:"avatar, sentences, divider"})),64)):(s(),h(k,{key:1},[e(g).length?(s(),h(k,{key:0},[t(_e,{modelValue:e(p),"onUpdate:modelValue":[r[1]||(r[1]=c=>M(p)?p.value=c:null),v]},{default:a(()=>[(s(!0),h(k,null,z(e(g),c=>(s(),n(ge,{value:c.id,onClick:$e=>v(c.id),class:"cursor-pointer"},{default:a(()=>[P("div",xe,[c.profile?(s(),n(w,{key:0,src:c.profile,width:"40",height:"40",rounded:""},null,8,["src"])):(s(),n(w,{key:1,src:e(j),width:"40",height:"40",rounded:""},null,8,["src"])),P("div",De,[P("span",Pe,y(c.title),1)])])]),_:2},1032,["value","onClick"]))),256))]),_:1},8,["modelValue"]),t(ve,null,{default:a(()=>[t(O,{cols:"10",class:"align-self-end"},{default:a(()=>[t(R,{variant:"flat",block:"",loading:e(I),disabled:!e(p)||e(A)===e(p),onClick:d,class:"align-self-end"},{default:a(()=>[_(" ورود به حساب ")]),_:1},8,["loading","disabled"])]),_:1}),t(O,{cols:"2"},{default:a(()=>[t(R,{variant:"tonal",block:"",size:"small",icon:"tabler-plus",class:"mt-5",rounded:"",onClick:x})]),_:1})]),_:1})],64)):(s(),n(X,{key:1}))],64))])]),_:1})),e(i)?(s(),n(E,{key:1,"max-width":"600",class:"pa-4"},{default:a(()=>[t(H,null,{default:a(()=>[t(F,{icon:"tabler-users",color:"rgb(193, 53, 132)"}),_(" ساخت حساب کاربری جدید ")]),_:1}),t(ee,null,{default:a(()=>[t(ye,{modelValue:e(l),"onUpdate:modelValue":r[2]||(r[2]=c=>M(l)?l.value=c:null),class:"mt-2",label:"نام",variant:"outlined"},null,8,["modelValue"]),t(R,{variant:"flat",block:"",class:"mt-5",onClick:L},{default:a(()=>[_(" ساخت حساب ")]),_:1})]),_:1})]),_:1})):S("",!0)]),_:1},8,["model-value"])}}};const Ae=V=>(ue("data-v-8363585c"),V=V(),de(),V),Be=Ae(()=>P("span",null," کد معرف : ",-1)),Ue={__name:"UserProfile",setup(V){const U=oe(),$=ke(),b=m(!1),l=f("userData"),C=()=>{b.value=!b.value},I=async()=>{try{B("/logout",{method:"POST"})}catch(u){console.log(u)}f("accessToken").value=null,l.value=null,f("userData").value=null,f("pageId").value=null,await U.push("/auth/login"),f("userAbilityRules").value=null,$.update([])},g=m(!1),p=[{type:"navItem",icon:"tabler-user",title:"تغییر اکانت",to:null,onClick:()=>{g.value=!0}},{type:"navItem",icon:"tabler-currency-dollar",title:"خرید اکانت",to:{name:"pricing"}},{type:"divider"},{type:"navItem",icon:"tabler-logout",title:"خروج",onClick:I}],i=m(!1),x=le(),L=Q(()=>x.credit),D=async()=>{try{const u=f("pageId"),v=u.value?u.value:"",d=await B.get(`/pages/${v}`);u.value?(d.data.profile&&(i.value=!0,l.value.insta_profile=d.data.profile),x.setCredit(d.data.expired_at),l.value.name=d.data.title):(f("pageId").value=d.data.data[0].id,d.data.data[0].profile&&(i.value=!0,l.value.insta_profile=d.data.data[0].profile),x.setCredit(d.data.data[0].expired_at),l.value.name=d.data.data[0].title)}catch(u){console.log("connectToFaceBook => ",u)}};return se(async()=>{D();try{const u=await ne(l.value.insta_profile);i.value=!!(l&&l.value.insta_profile&&u)}catch{console.log("not"),i.value=!1}}),(u,v)=>{const d=ie("ChangePasswordDialog");return s(),h(k,null,[t(Se,{"is-dialog-visible":e(g),"onUpdate:isDialogVisible":v[0]||(v[0]=o=>M(g)?g.value=o:null)},null,8,["is-dialog-visible"]),t(d,{"show-dialog":e(b),onClose:C},null,8,["show-dialog"]),e(l)?(s(),n(T,{key:0,dot:"",bordered:"",location:"bottom right","offset-x":"3","offset-y":"3",color:"success"},{default:a(()=>[t(q,{class:"cursor-pointer",color:e(i)?void 0:"primary",variant:e(i)?void 0:"tonal"},{default:a(()=>[e(i)?(s(),n(w,{key:0,src:e(l).insta_profile},null,8,["src"])):(s(),n(w,{key:1,src:e(j)},null,8,["src"])),t(Ve,{activator:"parent",width:"230",location:"bottom end",offset:"14px"},{default:a(()=>[t(be,null,{default:a(()=>[t(J,null,{prepend:a(()=>[t(we,{start:""},{default:a(()=>[t(T,{dot:"",location:"bottom right","offset-x":"3","offset-y":"3",color:"success",bordered:""},{default:a(()=>[t(q,{color:e(i)?void 0:"primary",variant:e(i)?void 0:"tonal"},{default:a(()=>[e(i)?(s(),n(w,{key:0,src:e(l).insta_profile},null,8,["src"])):(s(),n(w,{key:1,src:e(j)},null,8,["src"]))]),_:1},8,["color","variant"])]),_:1})]),_:1})]),default:a(()=>[t(K,{class:"font-weight-medium"},{default:a(()=>[_(y(e(l).name||"")+" "+y(e(l).last_name||""),1)]),_:1}),e(l).mobile||e(l).phone?(s(),n(N,{key:0},{default:a(()=>[_(y(e(l).mobile||e(l).phone),1)]),_:1})):S("",!0),t(N,null,{default:a(()=>[_("اعتبار اکانت "+y(e(L))+" روز",1)]),_:1}),e(l).identifier_code?(s(),n(N,{key:1},{default:a(()=>[Be,_(" "+y(e(l).identifier_code),1)]),_:1})):S("",!0)]),_:1}),t(e(he),{options:{wheelPropagation:!1}},{default:a(()=>[(s(),h(k,null,z(p,o=>(s(),h(k,{key:o.title},[o.type==="navItem"?(s(),n(J,{key:0,to:o.to,onClick:r=>o.onClick&&o.onClick()},re({prepend:a(()=>[t(F,{class:"me-2",icon:o.icon,size:"22"},null,8,["icon"])]),default:a(()=>[t(K,null,{default:a(()=>[_(y(o.title),1)]),_:2},1024)]),_:2},[o.badgeProps?{name:"append",fn:a(()=>[t(T,ce({ref_for:!0},o.badgeProps),null,16)]),key:"0"}:void 0]),1032,["to","onClick"])):(s(),n(Ce,{key:1,class:"my-2"}))],64))),64))]),_:1})]),_:1})]),_:1})]),_:1},8,["color","variant"])]),_:1})):S("",!0)],64)}}},Ze=te(Ue,[["__scopeId","data-v-8363585c"]]);export{Ze as default};
