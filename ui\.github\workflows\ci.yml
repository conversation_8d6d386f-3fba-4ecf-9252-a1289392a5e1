name: Vue.js CI and Deployment

on:
  push:
    branches: [hosein,master]
  pull_request:
    branches: [hosein,master]
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "18.16.0"
      - name: Cache pnpm modules
        uses: actions/cache@v2
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install Dependencies
        run: pnpm install

      - name: Build
        run: pnpm build

      - name: Copy .htaccess to dist directory
        run: cp .htaccess dist/

      - name: Remove specific files and directories
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SSH_PASSWORD }}
          port: ${{ secrets.SSH_PORT }}
          script: |
            rm -rf ${{secrets.SERVER_ADDRESS}}/assets
            rm -rf ${{secrets.SERVER_ADDRESS}}/images
            rm -f ${{secrets.SERVER_ADDRESS}}/index.html
            rm -f ${{secrets.SERVER_ADDRESS}}/favicon.ico
            rm -f ${{secrets.SERVER_ADDRESS}}/loader.css
            rm -f ${{secrets.SERVER_ADDRESS}}/mockServiceWorker.js
            rm -f ${{secrets.SERVER_ADDRESS}}/logo.svg
            rm -f ${{secrets.SERVER_ADDRESS}}/manifest.json
      - name: Copy repository contents to remote server via scp
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          port: ${{ secrets.SSH_PORT }}
          password: ${{ secrets.SSH_PASSWORD }}
          source: "dist/"
          target: "${{secrets.SERVER_ADDRESS}}"

      - name: move dist files to public html
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SSH_PASSWORD }}
          # password: ${{ secrets.SERVER_PASSWORD }} # Use the SSH password secret
          port: ${{secrets.SSH_PORT}}
          script: |
            cd ${{secrets.SERVER_ADDRESS}}
            mv dist/* .
