import { Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { WebhookModule } from './webhook/webhook.module';
import { AiModule } from './ai/ai.module';
import { DirectamApiModule } from './directam-api/directam-api.module';
import { DatabaseModule } from './database/database.module';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiSetting } from './database/entities/ai-settings.entity';
import { MetaModule } from './meta/meta.module';
import { Page } from './database/entities/pages.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST,
      port: +process.env.DB_PORT! || 5432,
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      entities: [AiSetting, Page],
      synchronize: false,
      migrationsRun: false,
    }),
    WebhookModule,
    AiModule,
    DirectamApiModule,
    DatabaseModule,
    MetaModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
