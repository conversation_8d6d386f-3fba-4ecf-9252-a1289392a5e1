<?php

namespace App\Http\Controllers\Instagram;

use App\Factories\Instagram\MetaPostServiceFactory;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="Instagram Posts",
 *     description="API for interacting with Instagram posts"
 * )
 */
class PostController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/instagram-app/posts",
     *     summary="Get paginated Instagram posts",
     *     description="Fetches a paginated list of Instagram posts for the logged-in user. Use the token for navigating pages.",
     *     tags={"Instagram Posts"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="token",
     *         in="query",
     *         required=false,
     *         description="Pagination token from the previous response (e.g., next:42:timestamp:randomStr)",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Posts fetched successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="posts", type="array", @OA\Items(type="object")),
     *                 @OA\Property(property="next_token", type="string", example="next:42:1717730220:abcXYZ789"),
     *                 @OA\Property(property="prev_token", type="string", example="prev:42:1717730220:defLMN456")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Failed to fetch posts",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="error", type="string", example="Failed to fetch posts: Error message")
     *         )
     *     )
     * )
     */
    public function index(): JsonResponse
    {
        try {
            $userId = auth()->user()->id;
            $metaPostService = MetaPostServiceFactory::create($userId);

            $cursor = request()->get('page_cursor');

            $result = $cursor
                ? $metaPostService->getPageByToken($cursor)
                : $metaPostService->getFirstPage();

            return response()->json([
                'success' => true,
                'data'    => [
                    'posts'      => $result['posts'],
                    'next_cursor' => $result['next_token'],
                    'prev_cursor' => $result['prev_token'],
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error'   => 'Failed to fetch posts: ' . $e->getMessage()
            ], 400);
        }
    }
}
