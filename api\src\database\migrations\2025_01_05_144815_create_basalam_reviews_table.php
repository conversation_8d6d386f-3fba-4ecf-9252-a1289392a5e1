<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('basalam_reviews', function (Blueprint $table) {
            // Columns
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('message_id');
            $table->tinyInteger('score')->comment('1 = perfect, 2 = not bad, 3 = not good');
            $table->timestamps();

            // Foreign Keys
            $table->foreign('user_id')
                ->references('id')
                ->on('basalam_users')
                ->onDelete('cascade');

            $table->foreign('message_id')
                ->references('id')
                ->on('basalam_messages')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('basalam_reviews');
    }
};
