import { E as Y } from "./endpoints-454f23f6.js";
import { t as k } from "./formatters-c282fd5f.js";
import "./index-00c7d20d.js";
import { k as $, af as C, s as D, H as E, ai as F, cK as G, ak as H, j as I, a6 as J, aj as K, cJ as L, R as M, F as N, K as O, al as P, a7 as Q, aM as S, U as T, y as V, i as W, aK as X, c as b, o as c, r as f, f as g, x as h, d as i, _ as j, b as o, a9 as q, e as r, n as s, a2 as v, v as x, z as y, L as z } from "./index-169996dc.js";
import { V as B } from "./VChip-ccd89083.js";
import { a as R, V as U } from "./VRow-6c1d54f3.js";
const Z=L("pricing",{state:()=>({showMobileDialog:!1,packageId:-1}),getters:{getShowMobileDialog:e=>e.showMobileDialog},actions:{setMobileDialog(e){this.showMobileDialog=e},setPackageId(e){this.packageId=e}}});const w=e=>(M("data-v-908b1875"),e=e(),T(),e),ee={class:"position-relative d-flex flex-column justify-center align-center"},te={key:0,class:"d-flex justify-center align-center py-8"},ae={class:"text-2xl font-weight-medium"},se=w(()=>i("sub",{class:"text-sm ms-1"},"ریال",-1)),re={key:1,class:"d-flex justify-center align-center pt-8"},ie={class:"text-sm text-linethrough font-weight-medium"},oe=w(()=>i("sub",{class:"text-sm ms-1"},"ریال",-1)),ce=w(()=>i("span",null," تخفیف ",-1)),ne={key:3,class:"d-flex justify-center align-center"},le={class:"text-xl2 font-weight-medium"},de=w(()=>i("sub",{class:"text-sm ms-1"},"ریال",-1)),ue={__name:"PricingCard",props:{id:{type:Number,required:!0},image:{type:String,required:!0},title:{type:String,required:!0},description:{type:String,required:!1},price:{type:Number,required:!1},discounted_price:{type:Number,required:!1}},setup(e){const _=e;Z(),I();const a=f(!1),l=async()=>{const t=v("pageId").value;a.value=!0;try{const n=await P.post("/plans/buy",{plan:_.id,page:t});if(n.data.success){const u=n.data.url;window.open(u,"_self")}}catch(n){console.error(n)}a.value=!1};return(t,n)=>(c(),g(q,{flat:"",border:"",class:V(["h-100",e.id==6?"border-primary border-opacity-100":""])},{default:r(()=>[o(C,{class:"d-flex h-100 flex-column align-center"},{default:r(()=>[O(o(B,{label:"",color:"primary",size:"small"},{default:r(()=>[x(" محبوب ترین ")]),_:1},512),[[z,e.id==2]]),e.image?(c(),g(F,{key:0,height:"140",width:"100%",src:e.image,class:"mx-auto mb-5"},null,8,["src"])):y("",!0),i("h3",{class:V([{"mt-8":e.id!==2,"mt-2":e.id===2},"text-h4 mb-2 text-center"])},h(e.title),3),i("div",ee,[e.discounted_price?y("",!0):(c(),b("div",te,[i("h1",ae,h(("thousandSeperator"in t?t.thousandSeperator:s(k))(e.price)),1),se])),e.discounted_price?(c(),b("div",re,[i("sub",ie,h(("thousandSeperator"in t?t.thousandSeperator:s(k))(e.price)),1),oe])):y("",!0),e.discounted_price?(c(),g(B,{key:2,label:"",class:"my-3",color:"primary"},{default:r(()=>[i("span",null,h(("calcOffPercent"in t?t.calcOffPercent:s(G))(e.price,e.discounted_price)),1),ce]),_:1})):y("",!0),e.discounted_price?(c(),b("div",ne,[i("h1",le,h(("thousandSeperator"in t?t.thousandSeperator:s(k))(e.discounted_price)),1),de])):y("",!0),o(K,{disabled:s(a),onClick:l},{default:r(()=>[x(" خرید ")]),_:1},8,["disabled"])]),o(H,{modelValue:s(a),"onUpdate:modelValue":n[0]||(n[0]=u=>J(a)?a.value=u:null),width:"300",persistent:""},{default:r(()=>[o(q,{color:"primary",width:"300"},{default:r(()=>[o(C,{class:"pt-3"},{default:r(()=>[o(Q,{indeterminate:"",color:"white",height:8,class:"mb-0 mt-4"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["class"]))}},pe=j(ue,[["__scopeId","data-v-908b1875"]]);const me=e=>(M("data-v-9a12965b"),e=e(),T(),e),fe=me(()=>i("div",{class:"text-center"},[i("h2",{class:"text-h4 pricing-title mb-2"}," تعرفه های DMPlus "),i("sub",{class:"text-sm text-disabled ms-1"},[x(" پکیج های "),i("span",{class:"text-primary"},"اقتصادی"),x(" به منظور حمایت از پیج های اینستاگرامی با حجم فالور پایین در نظر گرفته شده است ، درصورتی که کم تر از ۲۵ کا فالور دارید میتوانید از این پکیج ها استفاده نمایید . ")])],-1)),ge={__name:"AppPricing",props:{xs:{type:[Number,String],required:!1},sm:{type:[Number,String],required:!1},md:{type:[String,Number],required:!1},lg:{type:[String,Number],required:!1},xl:{type:[String,Number],required:!1},packages:{type:Array,required:!0}},emits:["getCredit"],setup(e,{emit:_}){const a=e,l=_,t=I(),n=$();return E(async()=>{if(t.query.hasOwnProperty("paystatus")){const u=t.query.paystatus;let p={};if(u=="success"){p={title:" پرداخت با موفقیت انجام شد",icon:"success",confirmButtonText:"باشه"},v("referral_code").value="",await S(p),l("getCredit");return}else{p={title:" پرداخت با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"},await S(p);const d=t.query.discount_code_id??"",m=v("referral_code").value??"";await S(p),n.replace({path:"/pricing",query:{discount_code_id:d,referral_code:m}})}}}),(u,p)=>{const d=pe;return c(),b(N,null,[fe,o(U,{class:"mt-4 align-stretch"},{default:r(()=>[(c(!0),b(N,null,W(e.packages,m=>(c(),g(R,D({key:m.id,ref_for:!0},a,{cols:"12",md:"4"}),{default:r(()=>[o(d,D({ref_for:!0},m),null,16)]),_:2},1040))),128))]),_:1})],64)}}},_e=j(ge,[["__scopeId","data-v-9a12965b"]]);const ke={__name:"pricing",setup(e){E(async()=>{t.query.referral_code&&(v("referral_code").value=t.query.referral_code),await u()});const _=f([]);f([]),f(""),f("");const a=f(!0),l=f(!1),t=I(),n=$(),u=async()=>{a.value=!0;const d=await P(Y.packages,{method:"GET"});try{_.value=d.data}catch(m){console.error(m)}a.value=!1},p=async()=>{l.value=!0;try{const d=await P("get-user",{method:"GET"});await n.replace("/pricing"),location.reload()}catch(d){console.log(d)}l.value=!1};return(d,m)=>{const A=_e;return c(),g(q,{class:"pt-6"},{default:r(()=>[o(C,{class:"pt-12 mb-16 pb-16"},{default:r(()=>[o(U,null,{default:r(()=>[o(R,{cols:"12",sm:"8",md:"12",lg:"10",class:V([[{"d-flex":s(a)||s(l),"align-center":s(a)||s(l),"justify-center":s(a)||s(l)}],"mx-auto"])},{default:r(()=>[s(a)||s(l)?(c(),g(X,{key:0,class:"ma-auto",indeterminate:""})):(c(),g(A,{key:1,packages:s(_),md:"4",onGetCredit:p},null,8,["packages"]))]),_:1},8,["class"])]),_:1})]),_:1})]),_:1})}}};export { ke as default };

