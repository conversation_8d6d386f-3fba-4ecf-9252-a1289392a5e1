import { Controller, Post, Body } from '@nestjs/common';
import { WebhookService } from './webhook.service';
import { DatabaseService } from '../database/database.service';
import { AiService } from '../ai/ai.service';
import { WebhookDto } from './dto/webhook.dto';
import { AiSetting } from '../database/entities/ai-settings.entity';
import { MetaService } from '../meta/meta.service';

/**
 * WebhookResponse
 *
 * This interface defines the structure of the response returned by webhook handling methods.
 * It is used to provide feedback about the status of webhook processing, including whether
 * the processing was successful and an optional message detailing the result.
 */
interface WebhookResponse {
  success: boolean;
  message: string;
}

/**
 * WebhookController
 *
 * This controller handles incoming webhooks for Instagram, processes the webhook data,
 * and responds with a confirmation message. It also processes the data in the background
 * to generate an AI reply and send it via the appropriate Meta platform (Instagram or Facebook).
 */
@Controller('webhook')
export class WebhookController {
  constructor(
    private readonly webhookService: WebhookService,
    private readonly databaseService: DatabaseService,
    private readonly aiService: AiService,
    private readonly metaService: MetaService,
  ) {}

  /**
   * Handles incoming Instagram webhooks and starts background processing.
   * This method is triggered when a new comment is received on an Instagram post.
   *
   * @param webhookData - The data received from the Instagram webhook,
   * containing information about the comment
   * @returns A response object with a success message and status
   */
  @Post('/instagram')
  public async handleInstagramWebhook(
    @Body() webhookData: WebhookDto,
  ): Promise<WebhookResponse> {
    this.processWebhookInBackground(webhookData).catch((error) => {
      console.error('Background processing error:', error);
    });

    return {
      success: true,
      message: 'Webhook received and being processed',
    };
  }

  /**
   * Processes the webhook data in the background to generate an AI reply
   * and send it to the appropriate Meta platform (Instagram or Facebook).
   *
   * This method extracts the necessary information from the webhook data, fetches the
   * AI settings from the database, generates an AI reply using the AiService, and
   * sends the reply to the Meta platform via the MetaService.
   *
   * @param webhookData - The data received from the webhook, containing comment and admin information
   * @returns A promise that resolves once the background processing is complete
   */
  private async processWebhookInBackground(
    webhookData: WebhookDto,
  ): Promise<void> {
    try {
      const { adminId, userId, postId, commentId, userComment } =
        this.webhookService.processWebhook(webhookData);

      if (adminId == userId) return;

      const aiSettings: AiSetting | null =
        await this.databaseService.getAiSettings(postId);

      if (!aiSettings || aiSettings.remaining_comments <= 0) {
        return;
      }

      const aiReply: string = await this.aiService.getAiReply(
        aiSettings,
        userComment,
        adminId,
      );

      const response: MetaResponse = await this.metaService.replyToComment(
        { adminId, userId, postId, commentId, userComment },
        aiReply,
      );

      if (response?.id) {
        await this.databaseService.decrementCommentCredit(aiSettings);
      } else {
        console.error('Failed to send Meta reply:', response?.error);
      }
    } catch (error) {
      console.error('Background processing failed:', error);
    }
  }
}
