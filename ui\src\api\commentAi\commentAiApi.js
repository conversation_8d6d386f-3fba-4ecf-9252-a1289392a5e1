// src/services/AIService.ts
import { $api } from "@/utils/api";

class AIService {
  /**
   * Fetches AI data from the server
   * @returns The first AI data object or "not_created" if 404
   * @throws Error if the request fails
   */
  async fetchData() {
    const res = await $api("/ai-service");
    if (res.data.success) return res.data.data[0];
    if (res.status === 404) return "not_created";
    throw new Error("Failed to fetch AI data");
  }

  /**
   * Creates a new AI service entry
   * @param payload The data to send
   * @returns The API response
   */
  create(payload) {
    return $api("/ai-service", {
      method: "POST",
      data: payload,
    });
  }

  /**
   * Updates an existing AI service entry
   * @param id The ID of the entry to update
   * @param payload The data to update with
   * @returns The API response
   */
  update(id, payload) {
    return $api(`/ai-service/${id}`, {
      method: "PUT",
      data: payload,
    });
  }

  /**
   * Deletes an AI service entry
   * @param id The ID of the entry to delete
   * @returns The API response
   */
  delete(id) {
    return $api(`/ai-service/${id}`, {
      method: "DELETE",
    });
  }
}

// Export a singleton instance
export const aiService = new AIService();
