import { Injectable } from '@nestjs/common';

@Injectable()
export class PromptBuilderService {
  buildPrompt(context: {
    post_description?: string;
    friendly_tone?: number;
    bot_character?: string;
    custom_prompt?: string;
  }): string {
    const { post_description, friendly_tone, bot_character, custom_prompt } =
      context;

    if (custom_prompt) return custom_prompt;

    const friendlyTone: string = friendly_tone
      ? `Your friendliness level should be rated ${friendly_tone} out of 5, where 1 is the least and 5 is the most friendly.`
      : `Maintain a normally friendly tone.`;

    const userInfo: string = bot_character
      ? `The owner of this post is described as: ${bot_character}. Try to reflect this personality in your answer.`
      : '';

    return `
  You are a friendly and engaging assistant. Read the post description and the user's comment.
  Based on the comment, generate a proper, natural-sounding reply.
  This is the post description: ${post_description}.
  ${friendlyTone}
  ${userInfo}
  Always answer in the same language as the user comment.
  Keep it concise and relevant.
  Thank you.
  `.trim();
  }
}
