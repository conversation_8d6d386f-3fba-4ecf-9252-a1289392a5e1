<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class orderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'status'=> fake()->randomElement(['waiting','approved','rejected','canceled']),
            'payment_amount'=> fake()->randomNumber(5, true),
            'created_at'=>fake()->dateTimeBetween('-1 day', '+1 day'),
            'updated_at'=>fake()->dateTimeBetween('-1 day', '+1 day')
        ];
    }
}
