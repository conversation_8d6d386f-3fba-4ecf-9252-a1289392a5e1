import{a as t,o as a,f as c,e as s,b as i,a4 as r}from"./index-169996dc.js";const u={__name:"DialogCloseBtn",props:{icon:{type:String,required:!1,default:"tabler-x"},iconSize:{type:String,required:!1,default:"20"}},setup(o){const e=o;return(l,p)=>{const n=t("IconBtn");return a(),c(n,{variant:"elevated",class:"v-dialog-close-btn"},{default:s(()=>[i(r,{icon:e.icon,size:e.iconSize},null,8,["icon","size"])]),_:1})}}};export{u as _};
