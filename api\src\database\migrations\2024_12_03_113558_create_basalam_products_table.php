<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('basalam_products', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('product_id')->unique();
            $table->string('name');
            $table->text('description');
            $table->decimal('price', 15, 2)->unsigned();
            $table->string('photo_medium');
            $table->decimal('average_rating', 3, 2)->nullable();
            $table->integer('rating_count')->default(0);
            $table->string('status');
            $table->string('owner_name');
            $table->string('owner_city');
            $table->bigInteger('owner_id');
            $table->boolean('has_delivery')->default(false);
            $table->boolean('is_available')->default(true);
            $table->boolean('can_add_to_cart')->default(true);
            $table->integer('preparation_days')->default(0);
            $table->integer('stock')->default(0);
            $table->decimal('weight', 8, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('basalam_products');
    }
};
