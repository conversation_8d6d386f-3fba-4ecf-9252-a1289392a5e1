import{r,a2 as N,o as P,f as $,e as t,a9 as T,al as I,b as l,ad as j,ao as q,ai as w,n as d,d as n,aj as _,v as x,K as C,L as F,a4 as h,aa as D,aM as J}from"./index-169996dc.js";import{E as b}from"./endpoints-454f23f6.js";import{r as y,a as K}from"./validations-4c0aab88.js";import{S as L}from"./step-1-16a14f82.js";import{S as O}from"./step-2-f7065dcf.js";import{S as R}from"./step-3-9b7d6151.js";import{V as z}from"./VForm-a73b6b87.js";import{V as A}from"./VStepper-57999623.js";import{V as S,a as i}from"./VRow-6c1d54f3.js";import{V as g}from"./VTextField-a8984053.js";import"./VInput-c4d3942a.js";import"./index-00c7d20d.js";import"./VWindowItem-f84b956d.js";import"./ssrBoot-c101cd97.js";import"./VDivider-12bfa926.js";import"./VField-150a934a.js";const G=n("div",{class:"d-flex flex-column ga-2 mt-10"},[n("div",{class:"font-weight-bold text-lg text-center"}," انتخاب نام کاربری و رمز عبور "),n("p",{class:"text-md text-center"}," در این مرحله ابتدا نام کاربری و رمزعبور خود را وارد کنید. ")],-1),H=n("div",{class:"d-flex flex-column ga-2 mt-10"},[n("div",{class:"font-weight-bold text-lg text-center"}," توکن "),n("p",{class:"text-md text-center"}," در این قسمت بعد از انتخاب درگاه پرداخت ، توکنی که از درگاه پرداخت مورد نظر دریافت کردید در اینجا وارد کنید. ")],-1),Q={class:"font-weight-bold text-lg text-center mt-5"},W=n("span",null," عملیات با موفقیت انجام شد ",-1),X=n("br",null,null,-1),ve={__name:"index",setup(Y){const u=r(1),m=r(),M=r(),p=r();r();const k=r(),c=r(null),f=r(null),v=r(null),V=N("pageId").value,U=async()=>{try{p.value=!0;let o=b.paymentsMethod;V&&(o=b.paymentsMethod+"/page/"+V);const{data:e}=await I.get(o),a=e.find(s=>s.payment_method==="snapp");if(a!==void 0){const s=JSON.parse(a.data);f.value=s.user,v.value=s.pass,c.value=s.clientId,m.value=a.token,M.value=a}}catch(o){throw new Error(o)}finally{p.value=!1}},B=async()=>{var o;try{p.value=!0;let e=b.paymentsMethod;V&&(e=b.paymentsMethod+"/page/"+V);const a={user:f.value,pass:v.value,token:m.value,payment_method:"snapp",clientId:c.value},{data:s}=await I.post(e,a);(s==null?void 0:s.success)!==void 0&&!(s!=null&&s.success)?await J({text:s==null?void 0:s.message,icon:"error"}):u.value++}catch(e){await K((o=e==null?void 0:e.data)==null?void 0:o.errors,e==null?void 0:e.status)}finally{p.value=!1}},E=()=>{var o;(o=k.value)==null||o.validate().then(({valid:e})=>{e&&B()})};return U(),(o,e)=>(P(),$(T,{class:"h-settings"},{default:t(()=>[l(D,null,{default:t(()=>[l(z,{ref_key:"refVForm",ref:k,onSubmit:j(E,["prevent"])},{default:t(()=>[l(A,{modelValue:u.value,"onUpdate:modelValue":e[6]||(e[6]=a=>u.value=a),"non-linear":"",items:[null,null,null],"hide-actions":"","alt-labels":"",dir:"rtl"},q({"item.1":t(()=>[C(n("div",null,[l(w,{src:d(L),cover:"",alt:"step1",width:"300",class:"mx-auto"},null,8,["src"]),G,l(S,null,{default:t(()=>[l(i,{cols:"12",md:"6"},{default:t(()=>[l(g,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=a=>f.value=a),label:"نام کاربری",rules:d(y)("نام کاربری"),type:"tel",dir:"rtl"},null,8,["modelValue","rules"])]),_:1}),l(i,{cols:"12",md:"6"},{default:t(()=>[l(g,{modelValue:v.value,"onUpdate:modelValue":e[1]||(e[1]=a=>v.value=a),label:"رمز عبور",type:"tel",rules:d(y)("رمز عبور"),dir:"rtl"},null,8,["modelValue","rules"])]),_:1})]),_:1}),l(_,{variant:"flat",block:"",class:"mt-5",disabled:!f.value||!v.value,onClick:e[2]||(e[2]=a=>u.value++)},{append:t(()=>[l(h,{icon:"tabler-arrow-narrow-left"})]),default:t(()=>[x(" بعدی ")]),_:1},8,["disabled"])],512),[[F,u.value===1]])]),"item.2":t(()=>[C(n("div",null,[l(w,{src:d(O),cover:"",alt:"step2",width:"300",class:"mx-auto"},null,8,["src"]),H,l(S,null,{default:t(()=>[l(i,{cols:"12",md:"6"},{default:t(()=>[l(g,{modelValue:m.value,"onUpdate:modelValue":e[3]||(e[3]=a=>m.value=a),rules:d(y)("توکن"),label:"توکن",dir:"rtl"},null,8,["modelValue","rules"])]),_:1}),l(i,{cols:"12",md:"6"},{default:t(()=>[l(g,{modelValue:c.value,"onUpdate:modelValue":e[4]||(e[4]=a=>c.value=a),rules:d(y)("سکرت"),label:"clientId",dir:"rtl"},null,8,["modelValue","rules"])]),_:1})]),_:1}),l(S,null,{default:t(()=>[l(i,null,{default:t(()=>[l(_,{variant:"outlined",block:"",onClick:e[5]||(e[5]=a=>u.value--)},{prepend:t(()=>[l(h,{icon:"tabler-arrow-narrow-right"})]),default:t(()=>[x(" قبلی ")]),_:1})]),_:1}),l(i,null,{default:t(()=>[l(_,{block:"",loading:p.value,disabled:!c.value||!m.value,type:"submit"},{prepend:t(()=>[l(h,{icon:"tabler-printer"})]),default:t(()=>[x(" ذخیره ")]),_:1},8,["loading","disabled"])]),_:1})]),_:1})],512),[[F,u.value===2]])]),_:2},[u.value===3?{name:"item.3",fn:t(()=>[l(w,{src:d(R),cover:"",alt:"step1",width:"300",class:"mx-auto"},null,8,["src"]),n("div",Q,[W,X,l(_,{to:"/",class:"my-5"},{default:t(()=>[x(" باشه ")]),_:1})])]),key:"0"}:void 0]),1032,["modelValue"])]),_:1},512)]),_:1})]),_:1}))}};export{ve as default};
