<?php

namespace App\Services\PythonService;

use Illuminate\Support\Facades\Http;

/**
 * PythonServiceHealth<PERSON><PERSON><PERSON>
 *
 * Checks the health of the Python microservice by pinging a health endpoint.
 */
class PythonServiceHealthChecker
{
    protected string $pythonServiceUrl;

    public function __construct()
    {
        $this->pythonServiceUrl = env('PYTHON_SERVICE_URL', 'http://python_service:8000');
    }

    /**
     * Checks if the Python service is healthy.
     *
     * @return bool
     */
    public function isHealthy(): bool
    {
        try {
            $response = Http::get("{$this->pythonServiceUrl}/health");
            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }
}
