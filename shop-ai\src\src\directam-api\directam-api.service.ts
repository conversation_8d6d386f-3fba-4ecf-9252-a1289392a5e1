import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class DirectamApiService {
  private readonly apiUrl: string = process.env.DIRECTAM_BASE_URL!;
  private readonly apiKey: string = process.env.DIRECTAM_API_KEY!;

  constructor(private readonly httpService: HttpService) {}

  /**
   * Fetches the OpenAI token for the given adminId from the Directam API.
   * @param adminId The ID of the admin for which the token is requested
   * @returns Promise resolving to the OpenAI token or null if not found/error occurs
   * @throws Error when the API request fails (excluding 404-like responses)
   */
  public async fetchOpenAiToken(adminId: string): Promise<string | null> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<{ success: boolean; openai_token?: string }>(
          this.apiUrl + '/instagram-bot/admin-ai-token',
          { admin_id: adminId },
          {
            headers: {
              'Content-Type': 'application/json',
              'X-API-KEY': this.apiKey,
            },
          },
        ),
      );

      if (response.data?.success && response.data.openai_token) {
        return response.data.openai_token;
      }

      return null;
    } catch (error) {
      return null;
    }
  }
}
