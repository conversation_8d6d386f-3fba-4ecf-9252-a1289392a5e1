import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AiSetting } from './entities/ai-settings.entity';
import { Page } from './entities/pages.entity';
import { EncryptionService } from './services/encryption.service';

/**
 * DatabaseService
 * Service responsible for interacting with the database
 */
@Injectable()
export class DatabaseService {
  constructor(
    @InjectRepository(AiSetting)
    private readonly aiSettingRepository: Repository<AiSetting>,
    @InjectRepository(Page)
    private readonly pageRepository: Repository<Page>,

    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Retrieves AI settings for a given post-ID from the database.
   *
   * @param postId - The ID of the post for which AI settings are to be fetched.
   * @returns A promise that resolves to the AI settings if found, otherwise null.
   */
  public async getAiSettings(postId: string): Promise<AiSetting | null> {
    const aiSetting: AiSetting | null = await this.aiSettingRepository.findOne({
      where: { post_id: postId },
    });

    if (!aiSetting) return null;

    return aiSetting;
  }

  /**
   * Decrements the remaining comment credit count for the given AI settings.
   * This operation reduces the remaining_comments value by 1 and persists the
   * change to the database.
   *
   * @param aiSettings - The AI settings entity containing the comment credits
   * @returns A promise that resolves when the decrement operation is complete
   */
  public async decrementCommentCredit(aiSettings: AiSetting): Promise<void> {
    aiSettings.remaining_comments -= 1;
    await this.aiSettingRepository.save(aiSettings);
  }

  /**
   * Retrieves metadata (access token and platform) for a given admin ID.
   *
   * @param adminId - The ID of the admin for which metadata is to be fetched.
   * @returns A promise that resolves to an object containing the access token and platform,
   *          or null if no data is found.
   */
  public async getMetaDataForAdmin(
    adminId: string,
  ): Promise<{ accessToken: string; platform: string } | null> {
    const page: Page | null = await this.pageRepository.findOne({
      where: { page_user_id: adminId },
    });
    if (!page) return null;

    const accessToken: string = this.encryptionService.decryptToken(
      page.access_token,
    );

    const platform: string =
      page.fb_user_id === 'loggedInWithInstagram' ? 'instagram' : 'facebook';

    return {
      accessToken,
      platform,
    };
  }
}
