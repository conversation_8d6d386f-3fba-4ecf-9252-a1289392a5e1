<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_settings', function (Blueprint $table) {
            // Columns
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('post_id')->unique();
            $table->integer('friendly_tone')->nullable();
            $table->text('post_description')->nullable();
            $table->text('bot_character')->nullable();
            $table->text('custom_prompt')->nullable();
            $table->string('ai_driver');
            $table->integer('remaining_comments')->default(0);
            $table->integer('remaining_edit_posts')->default(1);
            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_settings');
    }
};
