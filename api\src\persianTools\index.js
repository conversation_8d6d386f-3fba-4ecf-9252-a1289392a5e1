import { wordsToNumber,numberToWords  } from "@persian-tools/persian-tools";
import { v4 as uuidv4 } from 'uuid';
import express from 'express';
// const word = wordsToNumber("دو میلیون"); 
// const word2 = wordsToNumber("200 هزار")
// const number = numberToWords(word);

const app = express()
const port = 3000

app.post('/', (req, res) => {
    // console.log(req);
    // if(req?.query?.secret==='7471df93-92c0-41f6-b853-c0102f24bba6'){
    //     if(req.query.)
    // }
    // console.log(uuidv4())
    var bodyStr = '';
    req.on("data",function(chunk){
        bodyStr += chunk.toString();
    });
    req.on("end",function(){
        const body = JSON.parse(bodyStr);
        if(body.secret==='7471df93-92c0-41f6-b853-c0102f24bba6'){
            let words = body.words;
            words = words.replaceAll("تومان","")
            words = words.replaceAll("تومن","")
            words = words.replaceAll("ریال","")
            words = words.split(" و ")
            let total = 0;
            for(const word of words){
                const toNumber = wordsToNumber(word)
                total += toNumber
            }
            res.send({total:total})
            return
        }
        res.send('Not found')
    });
    // res.send('Hello World!')
})
app.listen(port,'127.0.0.1', () => {
    console.log(`Example app listening on port ${port}`)
})