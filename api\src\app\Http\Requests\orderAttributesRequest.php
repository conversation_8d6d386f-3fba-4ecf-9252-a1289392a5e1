<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class orderAttributesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if(auth()->user() == null) {
            return false;
        }
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'value'=>'required|max:500',
            'product_attrs_id'=>'required|exists:product_attrs,id',
            'product_id'=>'required|exists:products,id',
            'order_id'=>'required|exists:orders,id',
        ];
    }
    public function messages(): array
    {
        return [
            'value.required'=> 'Please enter value',
            'value.max'=> 'Value cannot be more than 500 characters',
            'product_attrs_id.required'=> 'Please enter product attrs_id',
            'product_attrs_id.exists'=> 'Please enter valid product_attrs_id',
            'product_attrs_id.id'=> 'Please enter valid product_attrs_id',
            'order_id.required'=> 'Please enter order_id',
            'order_id.exists'=> 'Please enter valid order_id',
            'order_id.id'=> 'Please enter valid order_id',
        ];
    }
}
