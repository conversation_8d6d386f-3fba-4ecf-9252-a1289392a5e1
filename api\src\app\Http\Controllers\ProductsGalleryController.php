<?php

namespace App\Http\Controllers;

use App\Http\Requests\productsGalleryRequest;
use App\Http\Requests\UpdateProductsGalleryRequest;
use App\Models\ProductsGallery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\Products;
use Illuminate\Support\Facades\File;
class ProductsGalleryController extends Controller
{
    private $baseUrl = 'https://graph.facebook.com/v17.0';
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $gallery = ProductsGallery::where('user_id',auth()->user()->id)->paginate(15);
        return $gallery;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(productsGalleryRequest $request)
    {
        $product = Products::where('user_id',auth()->user()->id)
            ->where('id',$request->product_id)
            ->with(['productGallery'])
            ->first();
        if(!$product){
            return response()->json([
                'success'=>false,
                'message'=>'محصول نامعتبر است'
            ]);
        }
        if($product->productGallery()->count()>=10){
            return response()->json([
                'success'=>false,
                'message'=>'فقط 10 عکس مجاز است'
            ]);
        }
        $gallery = new ProductsGallery();
        $gallery->product_id = $request->product_id;
        $gallery->from = 'server';
        $imageName = time().$this->generateRandomString().'.'.$request->image->extension();
        $request->image->move(public_path('storage/images'), $imageName);
        $gallery->path = '/storage/images/'. $imageName;
        $gallery->user_id = auth()->user()->id;
        $gallery->order = $request->order??0;
        $gallery->save();

        return $gallery;
    }

    private function generateRandomString($length = 10) {
        return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
    }
    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $gallery = ProductsGallery::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        return $gallery;
    }
    public function test(){


            // $access_token = 'EAADw8J6MnoABO4NzDl5eKaMNqusmNVMXVJUaIkCzgskHQs9MYoTA4SKU3GIgUmyf6gDFqYZBfANuhCIxNhXF8qux8hiYpZAYopnL1gMulKalXjQCo21ZAdVyEKwUIDlqbtXKZCVATE2tpGzVo1eB3R0iPbTbh1McgQzw95JCqYeYEFIPgwCAapJjekIwmpLQNjYpqqcZD';
            // $userId = '17841455374881302';
            // // Build the base URL with query parameters
            // $url = "{$this->baseUrl}/{$userId}/media?fields=id,caption,like_count,comments_count,title,description,text,media_url,thumbnail_url,media_type&access_token={$access_token}&limit=20";

            // $response = Http::get($url);

            // if ($response->successful()) {
            //     return $response->json();
            // }

            // if ($response->failed()) {
            //     $error = $response->json();
            //     return [];
            // }
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProductsGalleryRequest $request, $id)
    {
        $gallery = ProductsGallery::where('id',$id)->where('user_id',auth()->user()->id)->first();
        if(!$gallery){
            return response()->json([
                'success'=>false,
                'message'=>'عکس نامعتبر است'
            ]);
        }
        if($request->hasFile('image')){
            if($gallery->from === 'server'){
                if(file_exists(public_path($gallery->path))){
                    unlink(public_path($gallery->path));
                }
            }
            $gallery->from = 'server';
            $imageName = time().$this->generateRandomString().'.'.$request->image->extension();
            $request->image->move(public_path('storage/images'), $imageName);
            $gallery->path = '/storage/images/'. $imageName;
        }
        if($request->has('order')){
            $gallery->order = $request->order??0;
        }
        $gallery->save();

        return $gallery;
        // $productsGallery = ProductsGallery::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        // $productsGallery->product_id = $request->product_id;
        // $productsGallery->description = $request->description;
        // $productsGallery->path = $request->path;
        // $productsGallery->save();
        // return $productsGallery;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $productsGallery = ProductsGallery::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        // if($productsGallery->from==='server'){
        //     if (File::exists(public_path($productsGallery->path))) {
        //         File::delete(public_path($productsGallery->path));
        //     }
        // }
        if($productsGallery->from === 'server'){
            if(file_exists(public_path($productsGallery->path))){
                unlink(public_path($productsGallery->path));
            }
        }
        $productsGallery->delete();
        return $productsGallery;
    }
}
