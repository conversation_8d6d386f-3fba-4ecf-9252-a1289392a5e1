<?php

namespace App\Repositories\Ai;

use App\Models\Ai\AiSetting;
use App\Repositories\Interfaces\AiSettingRepositoryInterface as AiSettingRepoInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class AiSettingRepository implements AiSettingRepoInterface
{
    protected bool $includeTrashed = false;

    /**
     * Includes soft-deleted records in the next query.
     *
     * @return self
     */
    public function withTrashed(): self
    {
        $this->includeTrashed = true;
        return $this;
    }

    /**
     * Finds an AI setting by its ID.
     *
     * @param int $id
     * @return AiSetting|null
     */
    public function find(int $id): ?AiSetting
    {
        return AiSetting::find($id);
    }

    /**
     * Creates a new AI setting.
     *
     * @param array $data
     * @return AiSetting
     */
    public function create(array $data): AiSetting
    {
        return AiSetting::create($data);
    }

    /**
     * Updates the AI settings.
     *
     * @param int $id
     * @param array $data
     * @return AiSetting
     */
    public function update(int $id, array $data): AiSetting
    {
        $aiSettings = AiSetting::find($id);

        if (!$aiSettings) {
            throw new ModelNotFoundException(
                'AI Setting not found for id ' . $id
            );
        }

        $aiSettings->update($data);
        return $aiSettings;
    }

    /**
     * Returns a AiSetting Instance with the given post_id.
     *
     * @param string $postId
     * @return AiSetting|null
     */
    public function getByPostId(string $postId): ?AiSetting
    {
        return AiSetting::where('post_id', $postId)->first();
    }

    /**
     * Returns all AI settings associated with a given user_id.
     *
     * @param int $userId
     * @return Collection
     */
    public function getByUserId(int $userId): Collection
    {
        $query = AiSetting::query();

        if ($this->includeTrashed) {
            $query->withTrashed();
            $this->includeTrashed = false;
        }

        return $query->where('user_id', $userId)->get();
    }

    /**
     * Decrements the remaining editable posts for the given post_id.
     *
     * @param string $postId
     * @return void
     */
    public function decrementRemainingEditPosts(string $postId): void
    {
        $aiSetting = $this->getByPostId($postId);

        if ($aiSetting) {
            $aiSetting->remaining_edit_posts -= 1;
            $aiSetting->save();
        }
    }

    /**
     * Removes an Instance of AiSetting (soft-delete).
     *
     * @param int $id
     * @return void
     */
    public function remove(int $id): void
    {
        $aiSetting = $this->find($id);
        $aiSetting?->delete();
    }
}
