import{a as D,V}from"./VRow-6c1d54f3.js";import{r as u,a as _,o as m,c as v,b as a,e as s,f as x,aa as C,d as e,n as c,v as g,x as i,a9 as w,z as B,a6 as f,F as U}from"./index-169996dc.js";import{V as p}from"./VChip-ccd89083.js";const y="/assets/avatar-1-129659bb.png";const k={class:"d-flex justify-start align-center ga-2"},P=["src"],E={class:"font-weight-bold text-black"},I=e("br",null,null,-1),N={class:"d-flex ga-2 mt-4"},q={class:"d-flex flex-column align-center ga-2 w-100"},A={class:"font-weight-bold text-black"},F=e("span",{class:"text-body-2 font-weight-medium"},"خرید کل",-1),R={class:"d-flex flex-column align-center ga-2 w-100"},S={class:"font-weight-bold text-black"},j=e("span",{class:"text-body-2 font-weight-medium"},"نام کاربری",-1),H={__name:"CustomerBioPanel",props:{customerData:{type:null,required:!0},totalPurchase:{type:null,required:!0}},setup(d){const o=d,n=u(!1),r=u(!1);return(z,l)=>{const h=_("UserInfoEditDialog"),b=_("UserUpgradePlanDialog");return m(),v(U,null,[a(V,null,{default:s(()=>[a(D,{cols:"12"},{default:s(()=>[o.customerData?(m(),x(w,{key:0},{default:s(()=>[a(C,null,{default:s(()=>[e("div",k,[e("img",{src:d.customerData.profile??c(y),alt:"Avatar",width:"65",height:"65",class:"avatar-customer"},null,8,P),e("div",E,[g(i(o.customerData.name??"-")+" ",1),I,g(" "+i(o.customerData.phone),1)])]),e("div",N,[a(p,{color:"#CBCBCB",class:"w-100 pa-8 chip-purchase-total"},{default:s(()=>{var t;return[e("div",q,[e("div",A,i((t=o.totalPurchase)==null?void 0:t.toLocaleString())+" ریال",1),F])]}),_:1}),a(p,{color:"#CBCBCB",class:"w-100 pa-8 chip-purchase-total"},{default:s(()=>{var t;return[e("div",R,[e("div",S,i((t=o.customerData)==null?void 0:t.username),1),j])]}),_:1})])]),_:1})]),_:1})):B("",!0)]),_:1})]),_:1}),a(h,{isDialogVisible:c(n),"onUpdate:isDialogVisible":l[0]||(l[0]=t=>f(n)?n.value=t:null)},null,8,["isDialogVisible"]),a(b,{isDialogVisible:c(r),"onUpdate:isDialogVisible":l[1]||(l[1]=t=>f(r)?r.value=t:null)},null,8,["isDialogVisible"])],64)}}};export{H as _};
