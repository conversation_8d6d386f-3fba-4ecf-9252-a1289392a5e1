<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="manifest" href="/manifest.json" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />

    <title>DMPlus</title>

    <link rel="stylesheet" type="text/css" href="/loader.css" />
    <script type="module" crossorigin src="/assets/index-169996dc.js"></script>
    <link rel="stylesheet" href="/assets/index-af792bbb.css" />
  </head>

  <body>
    <div id="app">
      <div id="loading-bg">
        <div class="loading-logo">
          <img src="/logo.svg" />
        </div>
        <div class="loading">
          <div class="effect-1 effects"></div>
          <div class="effect-2 effects"></div>
          <div class="effect-3 effects"></div>
        </div>
      </div>
    </div>

    <script>
      const loaderColor =
        localStorage.getItem("vuexy-initial-loader-bg") || "#FFFFFF";
      const primaryColor =
        localStorage.getItem("vuexy-initial-loader-color") || "#7367F0";

      if (loaderColor)
        document.documentElement.style.setProperty(
          "--initial-loader-bg",
          loaderColor,
        );

      if (primaryColor)
        document.documentElement.style.setProperty(
          "--initial-loader-color",
          primaryColor,
        );
    </script>
    <script type="text/javascript">
      (function(c,l,a,r,i,t,y){
          c[a]=c[a]function(){(c[a].q=c[a].q[]).push(arguments)};
          t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
          y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "li4a5x7m0x");
    </script>
  </body>
</html>
