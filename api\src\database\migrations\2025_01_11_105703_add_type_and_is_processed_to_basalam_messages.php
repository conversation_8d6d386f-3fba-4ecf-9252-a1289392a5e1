<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('basalam_messages', function (Blueprint $table) {
            $table->enum('type', [
                'image', 'text',
            ])->after('url')->nullable();
            $table->boolean('is_processed')->default(false)->after('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('basalam_messages', function (Blueprint $table) {
            $table->dropColumn(['type', 'is_processed']);
        });
    }
};
