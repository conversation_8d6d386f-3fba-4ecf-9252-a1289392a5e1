<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class TempProxyController extends Controller
{

    /**
     * Handle GET and POST requests and forward them to another API.
     *
     */
    public function proxy(Request $request)
    {
        $targetApiUrl = 'https://dmplus.manymessage.com/webhook/api/instagram-webhook';

        try {
            $method = $request->method();

            if ($method === 'GET') {
                $response = Http::get($targetApiUrl, $request->query());
            }
            elseif ($method === 'POST') {
                $response = Http::post($targetApiUrl, $request->all());
            } else {
                return response()->json(['error' => 'Unsupported request method'], 405);
            }
            return response()->json([
                'success' => true,
                'data' => $response->json(),
                'status' => $response->status(),
            ], $response->status());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
