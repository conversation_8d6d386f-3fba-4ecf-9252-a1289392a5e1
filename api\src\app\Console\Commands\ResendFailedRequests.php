<?php

namespace App\Console\Commands;

use App\Models\CrmAction;
use App\Models\CrmTrack;
use App\Models\Page;
use App\Models\Transaction;
use App\Models\User;
use App\Services\CrmApiService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class ResendFailedRequests extends Command
{
	private CrmApiService $crmApiService;

	public function __construct(CrmApiService $crmApiService)
	{
		parent::__construct();
		$this->crmApiService = $crmApiService;
	}

	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'app:resend-failed-requests';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Command description';

	/**
	 * Execute the console command.
	 */
	public function handle()
	{
		$this->resendUsersData();
		$this->resendPagesData(0);
		$this->resendPagesData(2);
		$this->resendPagesData(7);
		$this->resendTransactionsData();
	}

	private function resendUsersData()
	{
		$crmAction = CrmAction::where('name', CrmAction::USER_REGISTER)->first();
		$crmTracks = $this->getCrmTracks($crmAction);
		foreach ($crmTracks as $crmTrack) {
			$user = User::findOrFail($crmTrack->user_id);
			$this->crmApiService->sendUserData(
				user: $user,
				crmAction: $crmAction,
				crmTrack: $crmTrack,
			);
		}
	}

	private function resendPagesData(int $days)
	{
		$crmAction = match ($days) {
			0 => CrmAction::where('name', CrmAction::PAGE_EXPIRED)->first(),
			2 => CrmAction::where('name', CrmAction::PAGE_2_DAYS_TO_EXPIRE)->first(),
			7 => CrmAction::where('name', CrmAction::PAGE_7_DAYS_TO_EXPIRE)->first(),
		};
		$crmTracks = $this->getCrmTracks($crmAction);
		foreach ($crmTracks as $crmTrack) {
			$page = Page::findOrFail($crmTrack->page_id);
			$page->load(['user', 'transaction']);
			$this->crmApiService->sendPageData(
				page: $page,
				days: $days,
				crmAction: $crmAction,
				crmTrack: $crmTrack,
			);
		}
	}

	private function resendTransactionsData()
	{
		$crmActions = CrmAction::whereIn('name', [
			CrmAction::TRANSACTION_APPROVED,
			CrmAction::TRANSACTION_WAITING,
		])->get();
		foreach ($crmActions as $crmAction) {
			$crmTracks = $this->getCrmTracks($crmAction);
			foreach ($crmTracks as $crmTrack) {
				$transaction = Transaction::findOrFail($crmTrack->transaction_id);
				$this->crmApiService->sendTransactionData(
					transaction: $transaction,
					crmAction: $crmAction,
					crmTrack: $crmTrack,
				);
			}
		}
	}

	private function getCrmTracks(CrmAction $crmAction): Collection|array
	{
		return CrmTrack::where([
			['crm_action_id', $crmAction->id],
			['is_processed', false],
		])->get();
	}
}
