<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreProductsMediaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if(auth()->user() == null) {
            return false;
        }
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'text'=>'max:2000',
            'product_id'=>'required|exists:products,id',
            // 'file'=>'mimes:jpeg,jpg,png,gif,acc,m4a,wav,mp4,ogg,avi,mov,webm,weba,audio/webm,webm;codecs=pcm,audio/webm;codecs=pcm',
            'type'=>['required',Rule::in(['text','audio', 'image','video'])]
        ];
    }
    public function messages(): array
    {
        return [
            'type.required'=>"تایپ اجباری میباشد",
            'type.in'=>'مقدار تایپ غیر مجاز میباشد',
            'text.max'=>'توضیحات نمیتواند بیشتر از 2000 کاراکتر باشد',
            'product_id.required'=>'محصول اجباری است',
            'product_id.exists'=>'محصول پیدا نشد',
            'file.mimes'=>'فرمت ارسال شده معتبر نیست فرمت های معتبر: jpeg,jpg,png,gif,acc,m4a,wav,mp4,ogg,avi,mov,webm,weba',
        ];
    }
}
