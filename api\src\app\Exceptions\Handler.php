<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Log;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        \Illuminate\Auth\AuthenticationException::class,
        \Illuminate\Validation\ValidationException::class,
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     * @throws Throwable
     */
    public function report(Throwable $e): void
    {
        if ($this->shouldReport($e)) {
            $this->logExceptionWithDetails($e);
        }

        parent::report($e);
    }

    /**
     * Log exception details using the notifyAdmin method.
     */
    protected function logExceptionWithDetails(Throwable $exception): void
    {
        // Generate a detailed error message using notifyAdmin
        $error_message = $this->notifyAdmin($exception);

        // Log the detailed error message to the daily log
        Log::channel('daily')->error($error_message, [
            'exception_type' => get_class($exception),
            'trace' => $exception->getTraceAsString(),
        ]);
    }

    /**
     * Notify admin and construct a detailed error message.
     */
    protected function notifyAdmin(Throwable $exception): string
    {
        $userNumber = auth()->user()?->phone;
        $clientIP = request()->ip();

        // Getting the full stack trace
        $trace = $exception->getTrace();
        $relevantFileInYourCode = '';
        $relevantLineInYourCode = '';
        $foundInVendor = false;
        $validatorIdentified = false;
        $validatorClass = '';

        foreach ($trace as $traceStep) {
            if (isset($traceStep['file'])) {
                // Check if the file is part of the vendor libraries and specifically a validator
                if (strpos($traceStep['file'], '/vendor/') !== false) {
                    $foundInVendor = true; // Mark that we've entered the vendor libraries
                    if (!$validatorIdentified && strpos($traceStep['file'], 'Prettus\\Validator\\') !== false) {
                        $validatorIdentified = true;
                        $validatorClass = $traceStep['class'] ?? 'Unknown validator';
                    }
                } elseif ($foundInVendor && !$validatorIdentified) {
                    // After finding a vendor file, the next file outside of vendor is likely your code calling the library
                    $relevantFileInYourCode = $traceStep['file'];
                    $relevantLineInYourCode = $traceStep['line'];
                    break; // Stop at the first occurrence
                }
            }
        }

        // Building the error message
        $error_message = "An error occurred for user with IP: {$clientIP} and number: {$userNumber}.";

        // Add validator information if a validator was identified
        if ($validatorIdentified) {
            $error_message .= " Error in validator: {$validatorClass}.";
        }

        // Add library file and line information if present
        if ($foundInVendor) {
            $error_message .= " Error in library file: {$exception->getFile()}, line: {$exception->getLine()}.";
        }

        // Add your code file and line information if found
        if (!empty($relevantFileInYourCode)) {
            $error_message .= " Originated from your code in file: {$relevantFileInYourCode}, line: {$relevantLineInYourCode}.";
        }

        $error_message .= " Error message: {$exception->getMessage()}";

        return $error_message;
    }
}
