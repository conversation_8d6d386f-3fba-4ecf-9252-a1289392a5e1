<?php

namespace App\Http\Controllers\Monitoring;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Spatie\Prometheus\Facades\Prometheus;

use Spatie\Prometheus\Prometheus as TestingPrometheus;

class TestingPrometheusController extends Controller
{
    public function testMetrics(Request $request)
    {
        // Increment the request counter
        $counter = Prometheus::addCounter(label: 'test_requests_total', namespace: 'Total number of requests received.');
        $counter->inc();

        // Track the start time to measure response time
        $startTime = microtime(true);


        try {

            $name = $request->get('name');
            if (empty($name)) {
                throw new \Exception('The "name" parameter is required.');
            }

            // Simulate a success response
            $response = "Test success! \nThe name is $name.";

            // Simulate a delay for testing response time (optional for testing purposes)
            usleep(500000); // 0.5 second delay to simulate processing time

            // Track the response time in Prometheus (in seconds)
            $responseTime = microtime(true) - $startTime;
            Prometheus::addGauge('test_response_time_seconds')
                ->value($responseTime);

            // Return the success response
            return response($response);

        } catch (\Exception $e) {
            // Increment the error counter in case of exceptions
            Prometheus::addCounter(label:'test_errors_total', namespace:'Total number of errors occurred during processing.')
                ->inc();

            // Optionally, save error information as a Prometheus label or gauge
            Prometheus::addGauge('test_error_details')
                ->value(1)
                ->helpText($e->getMessage());

	    report($e);

            return response('An error occurred', 500);
        }
    }
}

