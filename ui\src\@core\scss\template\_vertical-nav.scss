@use "@configured-variables" as variables;
@use "@core/scss/base/mixins";

.layout-nav-type-vertical {
  // 👉 Layout Vertical nav
  .layout-vertical-nav {
    @include mixins.elevation(2);

    .nav-header {
      .app-logo {
        .app-title {
          font-size: 22px;
        }
      }

     
    }

    // 👉 Nav group & Link
    .nav-link,
    .nav-group {
      // shadow cut issue fix
      margin-block-end: -0.5rem;
      padding-block-end: 0.5rem;

      a {
        outline: none;
      }
    }

    // 👉 Nav group active
    .nav-group.active {
      > .nav-group-label {
        color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));
        font-weight: 500;
      }
    }

    .nav-section-title .placeholder-icon {
      margin-inline-start: 0.3rem;
    }
  }
}
