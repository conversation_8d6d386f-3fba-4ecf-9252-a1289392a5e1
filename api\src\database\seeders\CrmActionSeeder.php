<?php

namespace Database\Seeders;

use App\Models\CrmAction;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CrmActionSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 */
	public function run(): void
	{
		$data = [
			['name' => CrmAction::USER_REGISTER, 'title' => 'ثبت نام کاربر'],
			['name' => CrmAction::PAGE_EXPIRED, 'title' => 'پیج منقضی شده'],
			['name' => CrmAction::PAGE_2_DAYS_TO_EXPIRE, 'title' => '2 روز مانده به انقضای پیج'],
			['name' => CrmAction::PAGE_7_DAYS_TO_EXPIRE, 'title' => '7 روز مانده به انقضای پیج'],
			['name' => CrmAction::TRANSACTION_APPROVED, 'title' => 'تراکنش موفق'],
			['name' => CrmAction::TRANSACTION_WAITING, 'title' => 'تراکنش معلق'],
		];

		CrmAction::insert($data);
	}
}
