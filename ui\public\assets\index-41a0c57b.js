import{_ as J,V as z}from"./VSkeletonLoader-4c44fbcf.js";import{_ as K}from"./DialogCloseBtn-b32209d9.js";import{v as N,s as O}from"./validations-4c0aab88.js";import{j as G,r as c,a8 as Q,o as i,f as h,e as o,b as a,ab as X,a4 as H,v as k,aa as R,n as l,c as p,i as q,F as y,a6 as E,aj as $,a9 as M,ak as Y,al as x,a2 as S,d as u,ai as U,x as C,k as Z,H as ee,ao as ae,aM as se}from"./index-169996dc.js";import{V as te,a as oe}from"./VRadioGroup-52375e28.js";import{S as le}from"./step-3-9b7d6151.js";import{A as ne}from"./account-svg-********.js";import{V as P}from"./VAlert-fc722507.js";import{V as ie}from"./VStepper-********.js";import{V as ce,a as W}from"./VRow-6c1d54f3.js";import"./VSelectionControl-8ecbcf09.js";import"./VInput-c4d3942a.js";import"./index-00c7d20d.js";import"./VWindowItem-f84b956d.js";import"./ssrBoot-c101cd97.js";import"./VDivider-12bfa926.js";const re=["onClick"],ue={key:0,class:"d-flex flex-row-reverse align-center ga-2"},de={class:"d-flex flex-column align-end"},fe={class:"font-weight-bold text-sm"},me={class:"font-weight-bold text-sm"},ge={key:1,class:"font-weight-bold"},_e={__name:"ShowAccountsFacebookDialog",props:{isDialogVisible:{type:Boolean,required:!1}},emits:["update:isDialogVisible"],setup(j,{emit:V}){const w=j,b=V,d=G(),g=c(!1),v=c(!1),f=c([]),m=c(null),_=Q({get(){return w.isDialogVisible},set(r){b("update:isDialogVisible",r)}}),B=async()=>{var r,s,n,t;try{g.value=!0;const e=await x.get("/facebook-accounts");if((e==null?void 0:e.data.success)!==void 0&&!(e!=null&&e.data.success))_.value=!1,f.value=[];else{const F=(s=(r=e==null?void 0:e.data)==null?void 0:r.data)==null?void 0:s.accounts.filter(A=>A.instagram_business_account),I=(t=(n=e==null?void 0:e.data)==null?void 0:n.data)==null?void 0:t.accounts.filter(A=>!A.instagram_business_account),L=[...F,...I];f.value=L}}catch{N("خطا در دریافت اطلاعات")}finally{g.value=!1}},D=r=>{m.value=r},T=async()=>{try{v.value=!0;const r=S("pageId").value;await x.post("/facebook-accounts",{fb_id:m.value,pageId:r}),_.value=!1,await O("عملیات با موفقیت انجام شد."),window.open("https://prest.manymessage.com/pages","_self")}catch{N("خطا در انجام عملیات")}finally{v.value=!1}};return d.query.redirect==="1"&&B(),(r,s)=>{const n=K,t=J;return i(),h(Y,{"max-width":"600","model-value":l(_),"onUpdate:modelValue":s[2]||(s[2]=e=>b("update:isDialogVisible",e))},{default:o(()=>[a(n,{onClick:s[0]||(s[0]=e=>_.value=!1)}),a(M,{"max-width":"600",class:"pa-4"},{default:o(()=>[a(X,null,{default:o(()=>[a(H,{icon:"tabler-users",color:"rgb(193, 53, 132)"}),k(" حساب های کاربری فیس بوک ")]),_:1}),a(R,null,{default:o(()=>[l(g)?(i(),p(y,{key:0},q(5,e=>a(z,{key:e,type:"avatar, sentences, divider"})),64)):(i(),p(y,{key:1},[l(f).length?(i(),p(y,{key:0},[a(te,{modelValue:l(m),"onUpdate:modelValue":[s[1]||(s[1]=e=>E(m)?m.value=e:null),D]},{default:o(()=>[(i(!0),p(y,null,q(l(f),e=>(i(),h(oe,{value:e.id,disabled:(e==null?void 0:e.instagram_business_account)===void 0,class:"cursor-pointer"},{default:o(()=>{var F,I;return[u("div",{class:"d-flex justify-space-between align-center ga-2",onClick:L=>D(e.id)},[(e==null?void 0:e.instagram_business_account)!==void 0?(i(),p("div",ue,[a(U,{src:(F=e==null?void 0:e.instagram_business_account)==null?void 0:F.profile_picture_url,width:"40",height:"40",rounded:""},null,8,["src"]),u("div",de,[u("span",fe,C((I=e==null?void 0:e.instagram_business_account)==null?void 0:I.username),1),u("span",me,C(e.name),1)])])):(i(),p("div",ge,C(e.name),1)),a($,{variant:"text",icon:"tabler-brand-facebook",color:"blue",size:"30px",class:"d-none d-md-block"})],8,re)]}),_:2},1032,["value","disabled"]))),256))]),_:1},8,["modelValue"]),a($,{variant:"flat",block:"",loading:l(v),disabled:!l(m),class:"mt-5",onClick:T},{default:o(()=>[k(" ورود به اکانت ")]),_:1},8,["loading","disabled"])],64)):(i(),h(t,{key:1}))],64))]),_:1})]),_:1})]),_:1},8,["model-value"])}}};const pe={class:"italic"},ve={class:"d-flex flex-column ga-4"},be=u("p",{class:"text-justify mx-auto"}," بعد از کلیک روی دکمه ورود با فیسبوک وارد سایت فیسبوک میشوید. سپس بعد از طی کردن مراحل لازم دسترسی های مورد نیاز را انتخاب کرده و در نهایت اتصال را تایید کنید. ",-1),ke=u("span",null,"ورود با facebook",-1),ye={class:"font-weight-bold text-lg text-center mt-5"},he=u("p",null,"کاربر گرامی هم اکنون به حساب کاربری اینستاگرام متصل هستید.",-1),je={__name:"index",setup(j){Z();const V=G(),w=c(!1),b=c(!1),d=c(1),g=c(!1),v=c(!1),f=c(),m=c(!1),_=c(!1),B=c();S("userData");const D=async()=>{try{b.value=!0;const s=S("pageId").value,n=await x.get(`/loginToFacebook?page=${s}`);if(n.data.success){const t=n.data.data;window.open(t,"_self")}}catch(s){console.log("connectToFaceBook => ",s)}finally{b.value=!1}},T=async()=>{try{_.value=!0;const s=S("pageId").value;let n="/get-page-status";s&&(n=`/get-page-status/${s}`);let t=await x.get(n);if(t=t.data,!t.success&&t.success!==void 0){m.value=!1;return}m.value=t.message.status=="ok",t.data.all_permission_granted||(f.value="شما همه دسترسی های مورد نیاز شاپ را نداده اید"),t.message.status=="ok"?(v.value=t.message.status=="ok",B.value=t.data.insta_username,d.value=2):f.value=t.message}catch(s){console.log("connectToFaceBook => ",s)}finally{_.value=!1}},r=async()=>{if((await se({title:"آیا از قطع اتصال اینستاگرام مطمئن هستید؟",showDenyButton:!0,showCancelButton:!1,confirmButtonText:"بله قطع کن",denyButtonText:"خیر"})).isConfirmed){w.value=!0;try{const n=await x("/facebook-accounts",{method:"DELETE"});v.value=!n.data.success,d.value=1}catch(n){console.log("disconnectFromInsta => ",n)}w.value=!1}};return ee(()=>{var s;((s=V==null?void 0:V.query)==null?void 0:s.redirect)==="1"&&(g.value=!0),T()}),(s,n)=>(i(),p(y,null,[l(_)?(i(),h(M,{key:0,class:"mb-5"},{default:o(()=>[a(R,null,{default:o(()=>[a(z,{type:"list-item-avatar-three-line"})]),_:1})]),_:1})):(i(),p(y,{key:1},[l(v)&&!l(f)?(i(),h(P,{key:0,color:"rgba(var(--v-theme-success), 0.7)",icon:"tabler-brand-facebook",class:"mb-5"},{default:o(()=>[k(" اتصال شما با موفقیت برقرار شد. ")]),_:1})):(i(),h(P,{key:1,color:"rgba(var(--v-theme-error), 0.7)",icon:"tabler-brand-facebook",class:"mb-5"},{default:o(()=>[k(" اتصال شما کامل نیست. لطفا دوباره اتصال به اینستاگرام را طی‌فرمایید "),u("span",pe,C(l(f)),1)]),_:1}))],64)),a(M,{class:"h-card-connect-instagram"},{default:o(()=>[a(R,null,{default:o(()=>[a(ie,{modelValue:l(d),"onUpdate:modelValue":n[0]||(n[0]=t=>E(d)?d.value=t:null),"non-linear":"",items:[null,null],"hide-actions":"","alt-labels":"",dir:"rtl"},ae({_:2},[l(d)===1?{name:"item.1",fn:o(()=>[a(ce,{align:"center"},{default:o(()=>[a(W,{cols:"12"},{default:o(()=>[u("div",ve,[a(U,{src:l(ne),height:"300"},null,8,["src"]),be])]),_:1}),a(W,{cols:"12"},{default:o(()=>[a($,{color:"#395693",block:"",loading:l(b),onClick:D},{default:o(()=>[a(H,{icon:"tabler-brand-facebook"}),ke]),_:1},8,["loading"])]),_:1})]),_:1})]),key:"0"}:void 0,l(d)===2?{name:"item.2",fn:o(()=>[a(U,{src:l(le),cover:"",alt:"step1",width:"300",class:"mx-auto"},null,8,["src"]),u("div",ye,[he,u("p",null,[k("اکانت متصل : "),u("span",null,C(l(B)),1)]),a($,{color:"error",loading:l(w),onClick:r},{default:o(()=>[k(" قطع اتصال فیسبوک ")]),_:1},8,["loading"])])]),key:"1"}:void 0]),1032,["modelValue"])]),_:1})]),_:1}),a(_e,{"is-dialog-visible":l(g),"onUpdate:isDialogVisible":n[1]||(n[1]=t=>E(g)?g.value=t:null)},null,8,["is-dialog-visible"])],64))}};export{je as default};
