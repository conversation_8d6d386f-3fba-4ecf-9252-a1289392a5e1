<?php

namespace App\Http\Controllers;

use App\Http\Requests\ordersRequest;
use App\Models\order;
use App\Models\Products;
use App\Models\DeliveryMethod;
use App\Models\PaymentMethod;
use App\Models\Settings;
use App\Models\Customers;
use App\Models\Page;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;
use App\Services\PaymentGatewayService;

class OrderController extends Controller
{
    public function index(Request $request){
        $search = $request->search;
        $status = $request->status;
        if($status==='waiting'){
            $status = ['waiting','ordered'];
        }elseif($status==='rejected'){
            $status = ['canceled','rejected'];
        }elseif($status==='approved'){
            $status = ['approved'];
        }elseif($status==='delivered'){
            $status = ['delivered'];
        }
        if($search&&$status){
            $orders = order::search($search)
                ->query(function (Builder $query) use ($status,$search) {
                    $query->with(['customer','productPrices','product','deliveryMethod','paymentMethod']);
                    $query->where('user_id',auth()->user()->id);
                    $query->whereIn('status',$status);
                })
                ->orderBy('created_at','DESC')
                ->paginate(10);
            return $orders;
        }
        if($search){
            $orders = order::search($search)
                ->query(function (Builder $query)use($search) {
                    // $query->join('customers', 'orders.customer_id', '=', 'customers.id');
                    $query->with(['customer','productPrices','product','deliveryMethod','paymentMethod']);
                    $query->where('user_id',auth()->user()->id);
                })
                ->orderBy('created_at','DESC')
                ->paginate(10);
                // ->get();
            return $orders;
        }
        if($status){
            $orders = order::where('user_id',auth()->user()->id)
                ->whereIn('status',$status)
                ->with(['customer','productPrices','product','deliveryMethod','paymentMethod'])
                ->orderBy('created_at','desc')
                ->paginate(10);
            return $orders;
        }
        $orders = order::where('user_id',auth()->user()->id)
            ->orderBy('created_at','desc')
            ->with(['customer','productPrices','product','deliveryMethod','paymentMethod'])
            ->paginate(10);
        return $orders;
    }
    public function tune(){
        $users = User::all();
        foreach($users as $user){
            if($user->id!==4){
                continue;
            }
            $page = Page::where('user_id',$user->id)->where('id',32)->first();
            if(!$page){
                continue;
            }
            $orders = order::where('user_id',$user->id)->get();
            $customers = Customers::where('user_id',$user->id)->get();
            $products = Products::where('user_id',$user->id)->get();
            $deliveryMethods = DeliveryMethod::where('user_id',$user->id)->get();
            $paymentMethods = PaymentMethod::where('user_id',$user->id)->get();
            $settings = Settings::where('user_id',$user->id)->get();
            // foreach($orders as $order){
            //     $order->page_id = $page->id;
            //     $order->save();
            // }

            foreach($products as $product){
                $product->page_id = $page->id;
                $product->save();
            }
            foreach($customers as $customer){
                $customer->page_id = $page->id;
                $customer->save();
            }
            foreach($deliveryMethods as $deliveryMethod){
                $deliveryMethod->page_id = $page->id;
                $deliveryMethod->save();
            }
            foreach($paymentMethods as $paymentMethod){
                $paymentMethod->page_id = $page->id;
                $paymentMethod->save();
            }
            foreach($settings as $setting){
                $setting->page_id = $page->id;
                $setting->save();
            }
        }
    }
    public function indexPage(Request $request,$id=null){
        $search = $request->search;
        $status = $request->status;
        $pageId = null;
        if($id){
            $page = Page::where('id',$id)->where('user_id',auth()->user()->id)->first();
            if(!$page){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $page->id;
        }
        if($status==='waiting'){
            $status = ['waiting','ordered'];
        }elseif($status==='rejected'){
            $status = ['canceled','rejected'];
        }elseif($status==='approved'){
            $status = ['approved'];
        }elseif($status==='delivered'){
            $status = ['delivered'];
        }
        if($search&&$status){
            $orders = order::search($search)
                ->query(function (Builder $query) use ($status,$search,$pageId) {
                    $query->with(['customer','product','productPrices','deliveryMethod','paymentMethod']);
                    $query->where('user_id', auth()->user()->id);

                    if($pageId){
                        $query->where('page_id',$pageId);
                    }

                    $query->whereIn('status',$status);
                })
                ->orderBy('created_at','DESC')
                ->paginate(10);
            return $orders;
        }
        if($search){
            $orders = order::search($search)
                ->query(function (Builder $query)use($search,$pageId) {
                    $query->with(['customer','productPrices','product','deliveryMethod','paymentMethod']);
                    $query->where('user_id', auth()->user()->id);
//                        ->where(function ($query) use ($search) {
//
//                            $query->orWhere('tracking_code', 'like', "%{$search}%");
//                            $query->orWhere('tag', 'like', "%{$search}%");
//                            $query->orWhere('payment_amount', 'like', "%{$search}%");
//                        });

                    if($pageId){
                        $query->where('page_id',$pageId);
                    }

                })
                ->orderBy('created_at','DESC')
                ->paginate(10);

            return $orders;
        }
        if($status){
            $orders = order::where('user_id',auth()->user()->id);
            if($pageId){
                $orders = $orders->where('page_id',$pageId);
            }
            $orders = $orders
                ->whereIn('status',$status)
                ->with(['customer','productPrices','product','deliveryMethod','paymentMethod'])
                ->orderBy('created_at','desc')
                ->paginate(10);
            return $orders;
        }
        $orders = order::where('user_id',auth()->user()->id);
        if($pageId){
            $orders = $orders->where('page_id',$pageId);
        }
        $orders = $orders
            ->orderBy('created_at','desc')
            ->with(['customer','productPrices','product','deliveryMethod','paymentMethod'])
            ->paginate(10);
        return $orders;
    }
    public function create(ordersRequest $request){
        $order = new order();
        $order->status = $request->status;
        $order->user_id = auth()->user()->id;
        $order->save();
        return $order;
    }
    public function show($id){
        $order = Order::where('id', $id)
            ->where('user_id', auth()->user()->id)
            ->with(['orderAttributes'=> function($query){
                $query->orderBy('created_at','ASC');
            }, 'orderAttributes.product','productPrices', 'orderAttributes.productAttributes','customer'])
            ->firstOrFail();
        return $order;
    }
    private function getPaymentMethodData($paymentMethod)
    {
        if ($paymentMethod->payment_method === 'snapp') {
            $data = json_decode($paymentMethod->data, true);
            return [
                'user' => $data['user'],
                'pass' => $data['pass'],
                'clientId' => $data['clientId']
            ];
        }
        return [];
    }
    public function update(Request $request, $id){
        $order = order::whereId($id)->where('user_id',auth()->user()->id)->with(['paymentMethod'])->firstOrFail();
        if ($order->paymentMethod) {
            if($order->paymentMethod->payment_method==='snapp'){
                $payment = new PaymentGatewayService($order->paymentMethod->payment_method, $order->paymentMethod->token, $this->getPaymentMethodData($order->paymentMethod));
                if($order->status === 'approved' && $request->status === 'rejected'){
                    $payment->cancel(['paymentToken'=>$order->token]);
                }
            }
        }
        $order->status = $request->status;
        $order->delivery_tracking_code = $request->delivery_tracking_code;
        $order->save();
        return $order;
    }
    public function destroy($id){
        $order = order::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        $order->delete();
        return $order;
    }
}
