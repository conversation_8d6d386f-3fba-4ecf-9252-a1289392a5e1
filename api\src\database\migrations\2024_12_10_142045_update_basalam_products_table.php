<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('basalam_products', function (Blueprint $table) {
            Schema::table('basalam_products', function (Blueprint $table) {
                $table->dropUnique(['product_id']);

                $table->timestamp('product_unique_time')->nullable();

                $table->unique(['product_id', 'product_unique_time']);
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('basalam_products', function (Blueprint $table) {
            Schema::table('basalam_products', function (Blueprint $table) {
                $table->dropUnique(['product_id', 'product_unique_time']);

                $table->dropColumn('product_unique_time');
            });
        });
    }
};
