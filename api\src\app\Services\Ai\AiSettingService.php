<?php

namespace App\Services\Ai;

use App\Models\Ai\AiSetting;
use App\Repositories\Interfaces\AiSettingRepositoryInterface as AiSettingRepoInterface;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class AiSettingService
 *
 * Service class responsible for managing AI settings, including creation, update,
 * and checking the existence of AI settings. This class interacts with the repository
 * to perform CRUD operations on the `AiSetting` model.
 */
class AiSettingService
{
    /**
     * The repository instance for interacting with AI settings.
     *
     * @var AiSettingRepoInterface
     */
    private AiSettingRepoInterface $repository;

    public function __construct(AiSettingRepoInterface $aiSettingRepository)
    {
        $this->repository = $aiSettingRepository;
    }

    /**
     * Retrieve all AI settings for a specific user.
     *
     * This method fetches all AI settings associated with the given `userId` by querying
     * the `AiSettingRepository`. It returns a collection of `AiSetting` models that belong
     * to the specified user. If no settings are found, an empty collection will be returned.
     *
     * @param int $userId The user ID to retrieve the AI settings for
     * @return Collection A collection of `AiSetting` instances
     */
    public function getActiveUserAiSettings(int $userId): Collection
    {
        return $this->repository->getByUserId($userId);
    }

    /**
     * Creates a new AI setting in the database.
     *
     * This method interacts with the repository to create a new AI setting
     * based on the provided data. The data should include the necessary attributes
     * for creating an AI setting, such as `user_id`, `post_id`, `ai_driver`, etc.
     *
     * @param array $data The data to create the AI setting
     * @return AiSetting The newly created `AiSetting` instance
     */
    public function createAiSetting(array $data): AiSetting
    {
        return $this->repository->create($data);
    }

    /**
     * Updates an existing AI setting in the database.
     *
     * This method interacts with the repository to update an AI setting
     * identified by the provided `id` using the provided data.
     *
     * @param int $id The ID of the AI setting to update
     * @param array $data The data to update the AI setting with
     * @return AiSetting The updated `AiSetting` instance
     */
    public function updateAiSetting(int $id, array $data): AiSetting
    {
        return $this->repository->update($id, $data);
    }

    /**
     * Retrieves an AI setting by its ID.
     *
     * This method interacts with the repository to fetch an `AiSetting` instance
     * based on the provided `id`. If no AI setting is found, it returns `null`.
     *
     * @param int $id The ID of the AI setting to retrieve
     * @return AiSetting|null The `AiSetting` instance if found, `null` if not found
     */
    public function getAiSetting(int $id): ?AiSetting
    {
        return $this->repository->find($id);
    }

    /**
     * Soft deletes an AI setting by its ID.
     *
     * This method delegates to the repository to perform a soft delete on the `AiSetting`
     * identified by the provided `id`. The setting is not permanently removed from the
     * database, allowing it to be restored later if needed.
     *
     * @param int $id The ID of the AI setting to be soft deleted
     * @return void
     */
    public function removeAiSetting(int $id): void
    {
        $this->repository->remove($id);
    }

    /**
     * Checks the limits and conditions for a given action (create or update).
     *
     * It handles the validation for both creating and updating AI settings.
     *
     * - For the `create` action: It checks if AI settings already exist for the given post ID
     *   and whether the user has already created one AI setting.
     * - For the `update` action: It checks if the post can still be edited and decrements
     *   the remaining edit count.
     *
     * @param array $data The data related to the action, including `post_id`, `user_id`, etc.
     * @param string $actionType The type of action to validate, either 'create' or 'update'
     * @return array An associative array with `success` status and an optional `message`
     */
    public function checkActionLimits(array $data, string $actionType): array
    {
        switch ($actionType) {
            case 'create':
                if ($this->hasAiSetting($data['post_id'])) {
                    return [
                        'success' => false,
                        'message' => 'AI settings already exist for this post ID.',
                    ];
                }

                if ($this->countAiSettingsForUser($data['user_id']) >= 2) {
                    return [
                        'success' => false,
                        'message' => 'AI settings already created two times for this user.',
                    ];
                }
                break;

            case 'update':
                $remainingEditablePosts = $this->getRemainingEditPosts($data['previous_post_id']);
                if ($remainingEditablePosts <= 0) {
                    return [
                        'success' => false,
                        'message' => 'You are allowed to edit target Post more than one time',
                    ];
                }

                $this->decrementRemainingEditPosts($data['previous_post_id']);
                break;
        }

        return ['success' => true];
    }

    /**
     * Checks if AI settings exist for the given post ID.
     *
     * This method queries the repository to check if an AI setting with the given
     * `post_id` already exists in the database.
     *
     * @param string $postId The post ID to check for the existence of an AI setting
     * @return bool `true` if the AI setting exists for the given post ID, `false` otherwise
     */
    private function hasAiSetting(string $postId): bool
    {
        return (bool) $this->repository->getByPostId($postId);
    }

    /**
     * Counts the number of AI settings for the specified user.
     *
     * This method interacts with the repository to fetch all AI settings associated
     * with the given `user_id`, and returns the total count of AI settings for the user.
     *
     * @param int $userId The user ID to count the AI settings for
     * @return int The count of AI settings for the specified user
     */
    private function countAiSettingsForUser(int $userId): int
    {
        $aiSettings = $this->getAllUserAiSettings($userId);
        return $aiSettings->count();
    }

    /**
     * Decrements the remaining editable posts for the given post ID.
     *
     * This method calls the repository to decrement the `remaining_edit_posts` field
     * for the AI setting associated with the given `post_id`. It is typically used to
     * limit the number of times a post can be edited.
     *
     * @param string $postId The post ID whose remaining editable posts will be decremented
     * @return void
     */
    private function decrementRemainingEditPosts(string $postId): void
    {
        $this->repository->decrementRemainingEditPosts($postId);
    }

    /**
     * Retrieves the number of remaining editable posts for the given post ID.
     *
     * This method queries the repository to retrieve the `remaining_edit_posts` value
     * for the specified `post_id`, which indicates how many more times the post can be edited.
     *
     * @param string $postId The post ID to get the remaining editable posts count for
     * @return int The number of remaining editable posts for the given post ID
     */
    private function getRemainingEditPosts(string $postId): int
    {
        $aiSetting = $this->repository->getByPostId($postId);
        return $aiSetting->remaining_edit_posts;
    }

    /**
     * Retrieves all AI settings for a specific user, including soft-deleted records.
     *
     * Use this method when you need to audit, restore, or manage both active and
     * deleted AI settings for a specific user.
     * If no matching records are found, an empty collection is returned.
     *
     * @param int $userId The ID of the user whose AI settings should be retrieved
     * @return Collection A collection of `AiSetting` models, including both
     * active and soft-deleted entries
     */
    private function getAllUserAiSettings(int $userId): Collection
    {
        return $this->repository->withTrashed()
            ->getByUserId($userId);
    }
}
