import{_ as S,V as z}from"./VSkeletonLoader-4c44fbcf.js";import{_ as H}from"./DialogCloseBtn-b32209d9.js";import{E as C}from"./endpoints-454f23f6.js";import{k as K,j as M,r as o,H as O,o as c,f as g,e as t,a9 as y,al as b,b as a,ak as U,n as i,a6 as q,d as n,v as f,x as m,aj as A,s as G,aa as J,c as v,i as B,F as V,af as E,ai as Q,aK as W,a4 as j}from"./index-169996dc.js";import{V as D,a as I}from"./VRow-6c1d54f3.js";import{V as X}from"./VContainer-63fa55f4.js";const Y={class:"d-flex justify-space-between items-center"},Z=n("strong",null,"نام محصول:",-1),ee={class:"d-flex align-center justify-center fill-height"},ae={class:"d-flex justify-space-between mt-4"},te={class:"mt-4"},de={__name:"[id].products",setup(se){K();const h=M(),k=o(),P=o(),R=o();o();const d=o(!1),u=o(!1);o(1);const p=o(),$=async()=>{try{const{data:l}=await b.get(C.pages+"/"+h.params.id);k.value=l.title,P.value=l.access_token,R.value=l.page_user_id,d.value=!1}catch{throw new Error(e)}finally{d.value=!1}},N=async()=>{try{d.value=!0;const{data:l}=await b.get(C.pages+"/"+h.params.id+"/contents");p.value=l.data}catch(l){throw new Error(l)}finally{d.value=!1}},T=()=>{N()};return O(()=>{$()}),(l,_)=>{const F=H,L=S;return c(),g(y,{class:"w-100"},{default:t(()=>[a(E,null,{default:t(()=>[a(U,{modelValue:i(u),"onUpdate:modelValue":_[1]||(_[1]=r=>q(u)?u.value=r:null),width:"900",class:"p-4"},{activator:t(({props:r})=>[n("div",Y,[n("div",null,[Z,f(" "+m(i(k)||"-"),1)]),a(A,G(r,{onClick:T}),{default:t(()=>[f(" گرفتن محصولات از اینستاگرام ")]),_:2},1040)])]),default:t(()=>[a(F,{onClick:_[0]||(_[0]=r=>u.value=!i(u))}),a(y,{elevation:"5",title:"پست های اینستاگرام"},{default:t(()=>[a(J,null,{default:t(()=>[a(D,{class:"my-5"},{default:t(()=>{var r;return[i(d)?(c(),v(V,{key:0},B(9,s=>a(I,{key:s,cols:"4"},{default:t(()=>[a(z,{type:"card-avatar"})]),_:2},1024)),64)):(c(),v(V,{key:1},[(r=i(p))!=null&&r.length?(c(!0),v(V,{key:0},B(i(p),s=>(c(),g(I,{key:s.id,cols:"4"},{default:t(()=>[a(y,{border:"",class:"w-100"},{default:t(()=>[a(E,null,{default:t(()=>{var w,x;return[a(Q,{class:"w-100","lazy-src":"https://picsum.photos/id/11/100/60",src:s.media_url},{placeholder:t(()=>[n("div",ee,[a(W,{color:"grey-lighten-4",indeterminate:""})])]),_:2},1032,["src"]),n("div",ae,[n("div",null,[a(j,{icon:"tabler-heart",color:"red"}),f(" "+m(s.like_count),1)]),n("div",null,[a(j,{icon:"tabler-message-circle",color:"yellow"}),f(" "+m(s.comments_count),1)])]),n("p",te,m(((w=s==null?void 0:s.caption)==null?void 0:w.length)>30?((x=s.caption)==null?void 0:x.substr(0,30))+"...":s==null?void 0:s.caption),1)]}),_:2},1024)]),_:2},1024)]),_:2},1024))),128)):(c(),g(X,{key:1},{default:t(()=>[a(D,null,{default:t(()=>[a(L)]),_:1})]),_:1}))],64))]}),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})}}};export{de as default};
