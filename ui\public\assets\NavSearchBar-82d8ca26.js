import{_ as ln,ae as cn,r as Ge,k as an,w as fn,a as un,o as z,c as me,d as X,b as M,e as k,a4 as Te,n as ie,z as bt,s as dn,af as hn,f as Qe,F as Ie,i as Fe,x as Be,v as yt,a6 as pn,ag as mn,ah as gn,R as bn,U as yn}from"./index-169996dc.js";import{u as _n,w as wn}from"./index-d1f9a878.js";import{V as xn,a as vn}from"./VRow-6c1d54f3.js";import{V as Sn,a as _t,d as On,b as En}from"./VList-349a1ccf.js";import"./index-00c7d20d.js";import"./ssrBoot-c101cd97.js";import"./VDivider-12bfa926.js";/*! shepherd.js 11.2.0 */var An=function(t){return Cn(t)&&!Tn(t)};function Cn(e){return!!e&&typeof e=="object"}function Tn(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||Pn(e)}var In=typeof Symbol=="function"&&Symbol.for,kn=In?Symbol.for("react.element"):60103;function Pn(e){return e.$$typeof===kn}function Ln(e){return Array.isArray(e)?[]:{}}function Pe(e,t){return t.clone!==!1&&t.isMergeableObject(e)?xe(Ln(e),e,t):e}function Rn(e,t,n){return e.concat(t).map(function(i){return Pe(i,n)})}function Mn(e,t){if(!t.customMerge)return xe;var n=t.customMerge(e);return typeof n=="function"?n:xe}function jn(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[]}function wt(e){return Object.keys(e).concat(jn(e))}function Vt(e,t){try{return t in e}catch{return!1}}function Fn(e,t){return Vt(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function Bn(e,t,n){var i={};return n.isMergeableObject(e)&&wt(e).forEach(function(s){i[s]=Pe(e[s],n)}),wt(t).forEach(function(s){Fn(e,s)||(Vt(e,s)&&n.isMergeableObject(t[s])?i[s]=Mn(s,n)(e[s],t[s],n):i[s]=Pe(t[s],n))}),i}function xe(e,t,n){n=n||{},n.arrayMerge=n.arrayMerge||Rn,n.isMergeableObject=n.isMergeableObject||An,n.cloneUnlessOtherwiseSpecified=Pe;var i=Array.isArray(t),s=Array.isArray(e),r=i===s;return r?i?n.arrayMerge(e,t,n):Bn(e,t,n):Pe(t,n)}xe.all=function(t,n){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(i,s){return xe(i,s,n)},{})};var Dn=xe,st=Dn;function Nn(e){return e instanceof Element}function ot(e){return e instanceof HTMLElement}function ce(e){return typeof e=="function"}function Le(e){return typeof e=="string"}function A(e){return e===void 0}class rt{on(t,n,i,s=!1){return A(this.bindings)&&(this.bindings={}),A(this.bindings[t])&&(this.bindings[t]=[]),this.bindings[t].push({handler:n,ctx:i,once:s}),this}once(t,n,i){return this.on(t,n,i,!0)}off(t,n){return A(this.bindings)||A(this.bindings[t])?this:(A(n)?delete this.bindings[t]:this.bindings[t].forEach((i,s)=>{i.handler===n&&this.bindings[t].splice(s,1)}),this)}trigger(t,...n){return!A(this.bindings)&&this.bindings[t]&&this.bindings[t].forEach((i,s)=>{const{ctx:r,handler:o,once:l}=i,c=r||this;o.apply(c,n),l&&this.bindings[t].splice(s,1)}),this}}function Ht(e){const t=Object.getOwnPropertyNames(e.constructor.prototype);for(let n=0;n<t.length;n++){const i=t[n],s=e[i];i!=="constructor"&&typeof s=="function"&&(e[i]=s.bind(e))}return e}function Vn(e,t){return n=>{if(t.isOpen()){const i=t.el&&n.currentTarget===t.el;(!A(e)&&n.currentTarget.matches(e)||i)&&t.tour.next()}}}function Hn(e){const{event:t,selector:n}=e.options.advanceOn||{};if(t){const i=Vn(n,e);let s;try{s=document.querySelector(n)}catch{}if(!A(n)&&!s)return console.error(`No element was found for the selector supplied to advanceOn: ${n}`);s?(s.addEventListener(t,i),e.on("destroy",()=>s.removeEventListener(t,i))):(document.body.addEventListener(t,i,!0),e.on("destroy",()=>document.body.removeEventListener(t,i,!0)))}else return console.error("advanceOn was defined, but no event name was passed.")}function Wt(e){return!Le(e)||e===""?"":e.charAt(e.length-1)!=="-"?`${e}-`:e}function Wn(e){const t=e.options.attachTo||{},n=Object.assign({},t);if(ce(n.element)&&(n.element=n.element.call(e)),Le(n.element)){try{n.element=document.querySelector(n.element)}catch{}n.element||console.error(`The element for this Shepherd step was not found ${t.element}`)}return n}function zt(e){return e==null?!0:!e.element||!e.on}function lt(){let e=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const n=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(t=="x"?n:n&3|8).toString(16)})}function C(){return C=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},C.apply(this,arguments)}function Ut(e,t){if(e==null)return{};var n={},i=Object.keys(e),s,r;for(r=0;r<i.length;r++)s=i[r],!(t.indexOf(s)>=0)&&(n[s]=e[s]);return n}const ve=Math.min,oe=Math.max,Ve=Math.round,De=Math.floor,Q=e=>({x:e,y:e}),zn={left:"right",right:"left",bottom:"top",top:"bottom"},Un={start:"end",end:"start"};function Je(e,t,n){return oe(e,ve(t,n))}function Se(e,t){return typeof e=="function"?e(t):e}function ae(e){return e.split("-")[0]}function qe(e){return e.split("-")[1]}function ct(e){return e==="x"?"y":"x"}function at(e){return e==="y"?"height":"width"}function $e(e){return["top","bottom"].includes(ae(e))?"y":"x"}function ft(e){return ct($e(e))}function qn(e,t,n){n===void 0&&(n=!1);const i=qe(e),s=ft(e),r=at(s);let o=s==="x"?i===(n?"end":"start")?"right":"left":i==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(o=He(o)),[o,He(o)]}function $n(e){const t=He(e);return[et(e),t,et(t)]}function et(e){return e.replace(/start|end/g,t=>Un[t])}function Yn(e,t,n){const i=["left","right"],s=["right","left"],r=["top","bottom"],o=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:i:t?i:s;case"left":case"right":return t?r:o;default:return[]}}function Kn(e,t,n,i){const s=qe(e);let r=Yn(ae(e),n==="start",i);return s&&(r=r.map(o=>o+"-"+s),t&&(r=r.concat(r.map(et)))),r}function He(e){return e.replace(/left|right|bottom|top/g,t=>zn[t])}function Xn(e){return C({top:0,right:0,bottom:0,left:0},e)}function qt(e){return typeof e!="number"?Xn(e):{top:e,right:e,bottom:e,left:e}}function We(e){return C({},e,{top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height})}const Gn=["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"],Qn=["mainAxis","crossAxis","limiter"];function xt(e,t,n){let{reference:i,floating:s}=e;const r=$e(t),o=ft(t),l=at(o),c=ae(t),a=r==="y",d=i.x+i.width/2-s.width/2,u=i.y+i.height/2-s.height/2,f=i[l]/2-s[l]/2;let h;switch(c){case"top":h={x:d,y:i.y-s.height};break;case"bottom":h={x:d,y:i.y+i.height};break;case"right":h={x:i.x+i.width,y:u};break;case"left":h={x:i.x-s.width,y:u};break;default:h={x:i.x,y:i.y}}switch(qe(t)){case"start":h[o]-=f*(n&&a?-1:1);break;case"end":h[o]+=f*(n&&a?-1:1);break}return h}const Zn=async(e,t,n)=>{const{placement:i="bottom",strategy:s="absolute",middleware:r=[],platform:o}=n,l=r.filter(Boolean),c=await(o.isRTL==null?void 0:o.isRTL(t));let a=await o.getElementRects({reference:e,floating:t,strategy:s}),{x:d,y:u}=xt(a,i,c),f=i,h={},m=0;for(let g=0;g<l.length;g++){const{name:_,fn:y}=l[g],{x,y:S,data:b,reset:p}=await y({x:d,y:u,initialPlacement:i,placement:f,strategy:s,middlewareData:h,rects:a,platform:o,elements:{reference:e,floating:t}});if(d=x??d,u=S??u,h=C({},h,{[_]:C({},h[_],b)}),p&&m<=50){m++,typeof p=="object"&&(p.placement&&(f=p.placement),p.rects&&(a=p.rects===!0?await o.getElementRects({reference:e,floating:t,strategy:s}):p.rects),{x:d,y:u}=xt(a,f,c)),g=-1;continue}}return{x:d,y:u,placement:f,strategy:s,middlewareData:h}};async function $t(e,t){var n;t===void 0&&(t={});const{x:i,y:s,platform:r,rects:o,elements:l,strategy:c}=e,{boundary:a="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:f=!1,padding:h=0}=Se(t,e),m=qt(h),_=l[f?u==="floating"?"reference":"floating":u],y=We(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(_)))==null||n?_:_.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(l.floating)),boundary:a,rootBoundary:d,strategy:c})),x=u==="floating"?C({},o.floating,{x:i,y:s}):o.reference,S=await(r.getOffsetParent==null?void 0:r.getOffsetParent(l.floating)),b=await(r.isElement==null?void 0:r.isElement(S))?await(r.getScale==null?void 0:r.getScale(S))||{x:1,y:1}:{x:1,y:1},p=We(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({rect:x,offsetParent:S,strategy:c}):x);return{top:(y.top-p.top+m.top)/b.y,bottom:(p.bottom-y.bottom+m.bottom)/b.y,left:(y.left-p.left+m.left)/b.x,right:(p.right-y.right+m.right)/b.x}}const Jn=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:i,placement:s,rects:r,platform:o,elements:l}=t,{element:c,padding:a=0}=Se(e,t)||{};if(c==null)return{};const d=qt(a),u={x:n,y:i},f=ft(s),h=at(f),m=await o.getDimensions(c),g=f==="y",_=g?"top":"left",y=g?"bottom":"right",x=g?"clientHeight":"clientWidth",S=r.reference[h]+r.reference[f]-u[f]-r.floating[h],b=u[f]-r.reference[f],p=await(o.getOffsetParent==null?void 0:o.getOffsetParent(c));let w=p?p[x]:0;(!w||!await(o.isElement==null?void 0:o.isElement(p)))&&(w=l.floating[x]||r.floating[h]);const I=S/2-b/2,R=w/2-m[h]/2-1,D=ve(d[_],R),te=ve(d[y],R),H=D,ne=w-m[h]-te,T=w/2-m[h]/2+I,de=Je(H,T,ne),W=qe(s)!=null&&T!=de&&r.reference[h]/2-(T<H?D:te)-m[h]/2<0?T<H?H-T:ne-T:0;return{[f]:u[f]-W,data:{[f]:de,centerOffset:T-de+W}}}}),ei=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(n){var i;const{placement:s,middlewareData:r,rects:o,initialPlacement:l,platform:c,elements:a}=n,d=Se(t,n),{mainAxis:u=!0,crossAxis:f=!0,fallbackPlacements:h,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:_=!0}=d,y=Ut(d,Gn),x=ae(s),S=ae(l)===l,b=await(c.isRTL==null?void 0:c.isRTL(a.floating)),p=h||(S||!_?[He(l)]:$n(l));!h&&g!=="none"&&p.push(...Kn(l,_,g,b));const w=[l,...p],I=await $t(n,y),R=[];let D=((i=r.flip)==null?void 0:i.overflows)||[];if(u&&R.push(I[x]),f){const T=qn(s,o,b);R.push(I[T[0]],I[T[1]])}if(D=[...D,{placement:s,overflows:R}],!R.every(T=>T<=0)){var te,H;const T=(((te=r.flip)==null?void 0:te.index)||0)+1,de=w[T];if(de)return{data:{index:T,overflows:D},reset:{placement:de}};let he=(H=D.filter(W=>W.overflows[0]<=0).sort((W,pe)=>W.overflows[1]-pe.overflows[1])[0])==null?void 0:H.placement;if(!he)switch(m){case"bestFit":{var ne;const W=(ne=D.map(pe=>[pe.placement,pe.overflows.filter(Ce=>Ce>0).reduce((Ce,rn)=>Ce+rn,0)]).sort((pe,Ce)=>pe[1]-Ce[1])[0])==null?void 0:ne[0];W&&(he=W);break}case"initialPlacement":he=l;break}if(s!==he)return{reset:{placement:he}}}return{}}}},ti=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(n){const{x:i,y:s,placement:r}=n,o=Se(t,n),{mainAxis:l=!0,crossAxis:c=!1,limiter:a={fn:x=>{let{x:S,y:b}=x;return{x:S,y:b}}}}=o,d=Ut(o,Qn),u={x:i,y:s},f=await $t(n,d),h=$e(ae(r)),m=ct(h);let g=u[m],_=u[h];if(l){const x=m==="y"?"top":"left",S=m==="y"?"bottom":"right",b=g+f[x],p=g-f[S];g=Je(b,g,p)}if(c){const x=h==="y"?"top":"left",S=h==="y"?"bottom":"right",b=_+f[x],p=_-f[S];_=Je(b,_,p)}const y=a.fn(C({},n,{[m]:g,[h]:_}));return C({},y,{data:{x:y.x-i,y:y.y-s}})}}},ni=function(t){return t===void 0&&(t={}),{options:t,fn(n){const{x:i,y:s,placement:r,rects:o,middlewareData:l}=n,{offset:c=0,mainAxis:a=!0,crossAxis:d=!0}=Se(t,n),u={x:i,y:s},f=$e(r),h=ct(f);let m=u[h],g=u[f];const _=Se(c,n),y=typeof _=="number"?{mainAxis:_,crossAxis:0}:C({mainAxis:0,crossAxis:0},_);if(a){const b=h==="y"?"height":"width",p=o.reference[h]-o.floating[b]+y.mainAxis,w=o.reference[h]+o.reference[b]-y.mainAxis;m<p?m=p:m>w&&(m=w)}if(d){var x,S;const b=h==="y"?"width":"height",p=["top","left"].includes(ae(r)),w=o.reference[f]-o.floating[b]+(p&&((x=l.offset)==null?void 0:x[f])||0)+(p?0:y.crossAxis),I=o.reference[f]+o.reference[b]+(p?0:((S=l.offset)==null?void 0:S[f])||0)-(p?y.crossAxis:0);g<w?g=w:g>I&&(g=I)}return{[h]:m,[f]:g}}}};function Z(e){return Yt(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function q(e){var t;return(t=(Yt(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Yt(e){return e instanceof Node||e instanceof P(e).Node}function U(e){return e instanceof Element||e instanceof P(e).Element}function N(e){return e instanceof HTMLElement||e instanceof P(e).HTMLElement}function vt(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof P(e).ShadowRoot}function Me(e){const{overflow:t,overflowX:n,overflowY:i,display:s}=j(e);return/auto|scroll|overlay|hidden|clip/.test(t+i+n)&&!["inline","contents"].includes(s)}function ii(e){return["table","td","th"].includes(Z(e))}function ut(e){const t=dt(),n=j(e);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(i=>(n.willChange||"").includes(i))||["paint","layout","strict","content"].some(i=>(n.contain||"").includes(i))}function si(e){let t=Oe(e);for(;N(t)&&!Ye(t);){if(ut(t))return t;t=Oe(t)}return null}function dt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ye(e){return["html","body","#document"].includes(Z(e))}function j(e){return P(e).getComputedStyle(e)}function Ke(e){return U(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Oe(e){if(Z(e)==="html")return e;const t=e.assignedSlot||e.parentNode||vt(e)&&e.host||q(e);return vt(t)?t.host:t}function Kt(e){const t=Oe(e);return Ye(t)?e.ownerDocument?e.ownerDocument.body:e.body:N(t)&&Me(t)?t:Kt(t)}function ze(e,t){var n;t===void 0&&(t=[]);const i=Kt(e),s=i===((n=e.ownerDocument)==null?void 0:n.body),r=P(i);return s?t.concat(r,r.visualViewport||[],Me(i)?i:[]):t.concat(i,ze(i))}function Xt(e){const t=j(e);let n=parseFloat(t.width)||0,i=parseFloat(t.height)||0;const s=N(e),r=s?e.offsetWidth:n,o=s?e.offsetHeight:i,l=Ve(n)!==r||Ve(i)!==o;return l&&(n=r,i=o),{width:n,height:i,$:l}}function ht(e){return U(e)?e:e.contextElement}function _e(e){const t=ht(e);if(!N(t))return Q(1);const n=t.getBoundingClientRect(),{width:i,height:s,$:r}=Xt(t);let o=(r?Ve(n.width):n.width)/i,l=(r?Ve(n.height):n.height)/s;return(!o||!Number.isFinite(o))&&(o=1),(!l||!Number.isFinite(l))&&(l=1),{x:o,y:l}}const oi=Q(0);function Gt(e){const t=P(e);return!dt()||!t.visualViewport?oi:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function ri(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==P(e)?!1:t}function fe(e,t,n,i){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),r=ht(e);let o=Q(1);t&&(i?U(i)&&(o=_e(i)):o=_e(e));const l=ri(r,n,i)?Gt(r):Q(0);let c=(s.left+l.x)/o.x,a=(s.top+l.y)/o.y,d=s.width/o.x,u=s.height/o.y;if(r){const f=P(r),h=i&&U(i)?P(i):i;let m=f.frameElement;for(;m&&i&&h!==f;){const g=_e(m),_=m.getBoundingClientRect(),y=j(m),x=_.left+(m.clientLeft+parseFloat(y.paddingLeft))*g.x,S=_.top+(m.clientTop+parseFloat(y.paddingTop))*g.y;c*=g.x,a*=g.y,d*=g.x,u*=g.y,c+=x,a+=S,m=P(m).frameElement}}return We({width:d,height:u,x:c,y:a})}function li(e){let{rect:t,offsetParent:n,strategy:i}=e;const s=N(n),r=q(n);if(n===r)return t;let o={scrollLeft:0,scrollTop:0},l=Q(1);const c=Q(0);if((s||!s&&i!=="fixed")&&((Z(n)!=="body"||Me(r))&&(o=Ke(n)),N(n))){const a=fe(n);l=_e(n),c.x=a.x+n.clientLeft,c.y=a.y+n.clientTop}return{width:t.width*l.x,height:t.height*l.y,x:t.x*l.x-o.scrollLeft*l.x+c.x,y:t.y*l.y-o.scrollTop*l.y+c.y}}function ci(e){return Array.from(e.getClientRects())}function Qt(e){return fe(q(e)).left+Ke(e).scrollLeft}function ai(e){const t=q(e),n=Ke(e),i=e.ownerDocument.body,s=oe(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),r=oe(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight);let o=-n.scrollLeft+Qt(e);const l=-n.scrollTop;return j(i).direction==="rtl"&&(o+=oe(t.clientWidth,i.clientWidth)-s),{width:s,height:r,x:o,y:l}}function fi(e,t){const n=P(e),i=q(e),s=n.visualViewport;let r=i.clientWidth,o=i.clientHeight,l=0,c=0;if(s){r=s.width,o=s.height;const a=dt();(!a||a&&t==="fixed")&&(l=s.offsetLeft,c=s.offsetTop)}return{width:r,height:o,x:l,y:c}}function ui(e,t){const n=fe(e,!0,t==="fixed"),i=n.top+e.clientTop,s=n.left+e.clientLeft,r=N(e)?_e(e):Q(1),o=e.clientWidth*r.x,l=e.clientHeight*r.y,c=s*r.x,a=i*r.y;return{width:o,height:l,x:c,y:a}}function St(e,t,n){let i;if(t==="viewport")i=fi(e,n);else if(t==="document")i=ai(q(e));else if(U(t))i=ui(t,n);else{const s=Gt(e);i=C({},t,{x:t.x-s.x,y:t.y-s.y})}return We(i)}function Zt(e,t){const n=Oe(e);return n===t||!U(n)||Ye(n)?!1:j(n).position==="fixed"||Zt(n,t)}function di(e,t){const n=t.get(e);if(n)return n;let i=ze(e).filter(l=>U(l)&&Z(l)!=="body"),s=null;const r=j(e).position==="fixed";let o=r?Oe(e):e;for(;U(o)&&!Ye(o);){const l=j(o),c=ut(o);!c&&l.position==="fixed"&&(s=null),(r?!c&&!s:!c&&l.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||Me(o)&&!c&&Zt(e,o))?i=i.filter(d=>d!==o):s=l,o=Oe(o)}return t.set(e,i),i}function hi(e){let{element:t,boundary:n,rootBoundary:i,strategy:s}=e;const o=[...n==="clippingAncestors"?di(t,this._c):[].concat(n),i],l=o[0],c=o.reduce((a,d)=>{const u=St(t,d,s);return a.top=oe(u.top,a.top),a.right=ve(u.right,a.right),a.bottom=ve(u.bottom,a.bottom),a.left=oe(u.left,a.left),a},St(t,l,s));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function pi(e){return Xt(e)}function mi(e,t,n){const i=N(t),s=q(t),r=n==="fixed",o=fe(e,!0,r,t);let l={scrollLeft:0,scrollTop:0};const c=Q(0);if(i||!i&&!r)if((Z(t)!=="body"||Me(s))&&(l=Ke(t)),i){const a=fe(t,!0,r,t);c.x=a.x+t.clientLeft,c.y=a.y+t.clientTop}else s&&(c.x=Qt(s));return{x:o.left+l.scrollLeft-c.x,y:o.top+l.scrollTop-c.y,width:o.width,height:o.height}}function Ot(e,t){return!N(e)||j(e).position==="fixed"?null:t?t(e):e.offsetParent}function Jt(e,t){const n=P(e);if(!N(e))return n;let i=Ot(e,t);for(;i&&ii(i)&&j(i).position==="static";)i=Ot(i,t);return i&&(Z(i)==="html"||Z(i)==="body"&&j(i).position==="static"&&!ut(i))?n:i||si(e)||n}const gi=async function(t){let{reference:n,floating:i,strategy:s}=t;const r=this.getOffsetParent||Jt,o=this.getDimensions;return{reference:mi(n,await r(i),s),floating:C({x:0,y:0},await o(i))}};function bi(e){return j(e).direction==="rtl"}const yi={convertOffsetParentRelativeRectToViewportRelativeRect:li,getDocumentElement:q,getClippingRect:hi,getOffsetParent:Jt,getElementRects:gi,getClientRects:ci,getDimensions:pi,getScale:_e,isElement:U,isRTL:bi};function _i(e,t){let n=null,i;const s=q(e);function r(){clearTimeout(i),n&&n.disconnect(),n=null}function o(l,c){l===void 0&&(l=!1),c===void 0&&(c=1),r();const{left:a,top:d,width:u,height:f}=e.getBoundingClientRect();if(l||t(),!u||!f)return;const h=De(d),m=De(s.clientWidth-(a+u)),g=De(s.clientHeight-(d+f)),_=De(a),x={rootMargin:-h+"px "+-m+"px "+-g+"px "+-_+"px",threshold:oe(0,ve(1,c))||1};let S=!0;function b(p){const w=p[0].intersectionRatio;if(w!==c){if(!S)return o();w?o(!1,w):i=setTimeout(()=>{o(!1,1e-7)},100)}S=!1}try{n=new IntersectionObserver(b,C({},x,{root:s.ownerDocument}))}catch{n=new IntersectionObserver(b,x)}n.observe(e)}return o(!0),r}function wi(e,t,n,i){i===void 0&&(i={});const{ancestorScroll:s=!0,ancestorResize:r=!0,elementResize:o=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:c=!1}=i,a=ht(e),d=s||r?[...a?ze(a):[],...ze(t)]:[];d.forEach(y=>{s&&y.addEventListener("scroll",n,{passive:!0}),r&&y.addEventListener("resize",n)});const u=a&&l?_i(a,n):null;let f=-1,h=null;o&&(h=new ResizeObserver(y=>{let[x]=y;x&&x.target===a&&h&&(h.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{h&&h.observe(t)})),n()}),a&&!c&&h.observe(a),h.observe(t));let m,g=c?fe(e):null;c&&_();function _(){const y=fe(e);g&&(y.x!==g.x||y.y!==g.y||y.width!==g.width||y.height!==g.height)&&n(),g=y,m=requestAnimationFrame(_)}return n(),()=>{d.forEach(y=>{s&&y.removeEventListener("scroll",n),r&&y.removeEventListener("resize",n)}),u&&u(),h&&h.disconnect(),h=null,c&&cancelAnimationFrame(m)}}const xi=(e,t,n)=>{const i=new Map,s=C({platform:yi},n),r=C({},s.platform,{_c:i});return Zn(e,t,C({},s,{platform:r}))};function vi(e){e.cleanup&&e.cleanup();const t=e._getResolvedAttachToOptions();let n=t.element;const i=Ti(t,e),s=zt(t);return s&&(n=document.body,e.shepherdElementComponent.getElement().classList.add("shepherd-centered")),e.cleanup=wi(n,e.el,()=>{if(!e.el){e.cleanup();return}Ei(n,e,i,s)}),e.target=t.element,i}function Si(e,t){return{floatingUIOptions:st(e.floatingUIOptions||{},t.floatingUIOptions||{})}}function Oi(e){e.cleanup&&e.cleanup(),e.cleanup=null}function Ei(e,t,n,i){return xi(e,t.el,n).then(Ai(t,i)).then(s=>new Promise(r=>{setTimeout(()=>r(s),300)})).then(s=>{s&&s.el&&s.el.focus({preventScroll:!0})})}function Ai(e,t){return({x:n,y:i,placement:s,middlewareData:r})=>(e.el&&(t?Object.assign(e.el.style,{position:"fixed",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}):Object.assign(e.el.style,{position:"absolute",left:`${n}px`,top:`${i}px`}),e.el.dataset.popperPlacement=s,Ci(e.el,r)),e)}function Ci(e,t){const n=e.querySelector(".shepherd-arrow");if(n&&t.arrow){const{x:i,y:s}=t.arrow;Object.assign(n.style,{left:i!=null?`${i}px`:"",top:s!=null?`${s}px`:""})}}function Ti(e,t){const n={strategy:"absolute",middleware:[]},i=Ii(t);return zt(e)||(n.middleware.push(ei(),ti({limiter:ni(),crossAxis:!0})),i&&n.middleware.push(Jn({element:i})),n.placement=e.on),st(t.options.floatingUIOptions||{},n)}function Ii(e){return e.options.arrow&&e.el?e.el.querySelector(".shepherd-arrow"):!1}function L(){}function ki(e,t){for(const n in t)e[n]=t[n];return e}function en(e){return e()}function Et(){return Object.create(null)}function je(e){e.forEach(en)}function pt(e){return typeof e=="function"}function $(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function Pi(e){return Object.keys(e).length===0}function Ee(e,t){e.appendChild(t)}function B(e,t,n){e.insertBefore(t,n||null)}function F(e){e.parentNode&&e.parentNode.removeChild(e)}function Li(e,t){for(let n=0;n<e.length;n+=1)e[n]&&e[n].d(t)}function V(e){return document.createElement(e)}function At(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function tn(e){return document.createTextNode(e)}function Ue(){return tn(" ")}function Ri(){return tn("")}function Xe(e,t,n,i){return e.addEventListener(t,n,i),()=>e.removeEventListener(t,n,i)}function O(e,t,n){n==null?e.removeAttribute(t):e.getAttribute(t)!==n&&e.setAttribute(t,n)}const Mi=["width","height"];function Ct(e,t){const n=Object.getOwnPropertyDescriptors(e.__proto__);for(const i in t)t[i]==null?e.removeAttribute(i):i==="style"?e.style.cssText=t[i]:i==="__value"?e.value=e[i]=t[i]:n[i]&&n[i].set&&Mi.indexOf(i)===-1?e[i]=t[i]:O(e,i,t[i])}function ji(e){return Array.from(e.childNodes)}function ge(e,t,n){e.classList[n?"add":"remove"](t)}let Re;function ke(e){Re=e}function nn(){if(!Re)throw new Error("Function called outside component initialization");return Re}function Fi(e){nn().$$.on_mount.push(e)}function mt(e){nn().$$.after_update.push(e)}const ye=[],Ae=[];let we=[];const Tt=[],Bi=Promise.resolve();let tt=!1;function Di(){tt||(tt=!0,Bi.then(sn))}function nt(e){we.push(e)}const Ze=new Set;let be=0;function sn(){if(be!==0)return;const e=Re;do{try{for(;be<ye.length;){const t=ye[be];be++,ke(t),Ni(t.$$)}}catch(t){throw ye.length=0,be=0,t}for(ke(null),ye.length=0,be=0;Ae.length;)Ae.pop()();for(let t=0;t<we.length;t+=1){const n=we[t];Ze.has(n)||(Ze.add(n),n())}we.length=0}while(ye.length);for(;Tt.length;)Tt.pop()();tt=!1,Ze.clear(),ke(e)}function Ni(e){if(e.fragment!==null){e.update(),je(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(nt)}}function Vi(e){const t=[],n=[];we.forEach(i=>e.indexOf(i)===-1?t.push(i):n.push(i)),n.forEach(i=>i()),we=t}const Ne=new Set;let se;function re(){se={r:0,c:[],p:se}}function le(){se.r||je(se.c),se=se.p}function v(e,t){e&&e.i&&(Ne.delete(e),e.i(t))}function E(e,t,n,i){if(e&&e.o){if(Ne.has(e))return;Ne.add(e),se.c.push(()=>{Ne.delete(e),i&&(n&&e.d(1),i())}),e.o(t)}else i&&i()}function Hi(e,t){const n={},i={},s={$$scope:1};let r=e.length;for(;r--;){const o=e[r],l=t[r];if(l){for(const c in o)c in l||(i[c]=1);for(const c in l)s[c]||(n[c]=l[c],s[c]=1);e[r]=l}else for(const c in o)s[c]=1}for(const o in i)o in n||(n[o]=void 0);return n}function ue(e){e&&e.c()}function J(e,t,n,i){const{fragment:s,after_update:r}=e.$$;s&&s.m(t,n),i||nt(()=>{const o=e.$$.on_mount.map(en).filter(pt);e.$$.on_destroy?e.$$.on_destroy.push(...o):je(o),e.$$.on_mount=[]}),r.forEach(nt)}function ee(e,t){const n=e.$$;n.fragment!==null&&(Vi(n.after_update),je(n.on_destroy),n.fragment&&n.fragment.d(t),n.on_destroy=n.fragment=null,n.ctx=[])}function Wi(e,t){e.$$.dirty[0]===-1&&(ye.push(e),Di(),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31}function Y(e,t,n,i,s,r,o,l=[-1]){const c=Re;ke(e);const a=e.$$={fragment:null,ctx:[],props:r,update:L,not_equal:s,bound:Et(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(c?c.$$.context:[])),callbacks:Et(),dirty:l,skip_bound:!1,root:t.target||c.$$.root};o&&o(a.root);let d=!1;if(a.ctx=n?n(e,t.props||{},(u,f,...h)=>{const m=h.length?h[0]:f;return a.ctx&&s(a.ctx[u],a.ctx[u]=m)&&(!a.skip_bound&&a.bound[u]&&a.bound[u](m),d&&Wi(e,u)),f}):[],a.update(),d=!0,je(a.before_update),a.fragment=i?i(a.ctx):!1,t.target){if(t.hydrate){const u=ji(t.target);a.fragment&&a.fragment.l(u),u.forEach(F)}else a.fragment&&a.fragment.c();t.intro&&v(e.$$.fragment),J(e,t.target,t.anchor,t.customElement),sn()}ke(c)}class K{$destroy(){ee(this,1),this.$destroy=L}$on(t,n){if(!pt(n))return L;const i=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return i.push(n),()=>{const s=i.indexOf(n);s!==-1&&i.splice(s,1)}}$set(t){this.$$set&&!Pi(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}function zi(e){let t,n,i,s,r;return{c(){t=V("button"),O(t,"aria-label",n=e[3]?e[3]:null),O(t,"class",i=`${e[1]||""} shepherd-button ${e[4]?"shepherd-button-secondary":""}`),t.disabled=e[2],O(t,"tabindex","0")},m(o,l){B(o,t,l),t.innerHTML=e[5],s||(r=Xe(t,"click",function(){pt(e[0])&&e[0].apply(this,arguments)}),s=!0)},p(o,[l]){e=o,l&32&&(t.innerHTML=e[5]),l&8&&n!==(n=e[3]?e[3]:null)&&O(t,"aria-label",n),l&18&&i!==(i=`${e[1]||""} shepherd-button ${e[4]?"shepherd-button-secondary":""}`)&&O(t,"class",i),l&4&&(t.disabled=e[2])},i:L,o:L,d(o){o&&F(t),s=!1,r()}}}function Ui(e,t,n){let{config:i,step:s}=t,r,o,l,c,a,d;function u(f){return ce(f)?f=f.call(s):f}return e.$$set=f=>{"config"in f&&n(6,i=f.config),"step"in f&&n(7,s=f.step)},e.$$.update=()=>{e.$$.dirty&192&&(n(0,r=i.action?i.action.bind(s.tour):null),n(1,o=i.classes),n(2,l=i.disabled?u(i.disabled):!1),n(3,c=i.label?u(i.label):null),n(4,a=i.secondary),n(5,d=i.text?u(i.text):null))},[r,o,l,c,a,d,i,s]}class qi extends K{constructor(t){super(),Y(this,t,Ui,zi,$,{config:6,step:7})}}function It(e,t,n){const i=e.slice();return i[2]=t[n],i}function kt(e){let t,n,i=e[1],s=[];for(let o=0;o<i.length;o+=1)s[o]=Pt(It(e,i,o));const r=o=>E(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();t=Ri()},m(o,l){for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(o,l);B(o,t,l),n=!0},p(o,l){if(l&3){i=o[1];let c;for(c=0;c<i.length;c+=1){const a=It(o,i,c);s[c]?(s[c].p(a,l),v(s[c],1)):(s[c]=Pt(a),s[c].c(),v(s[c],1),s[c].m(t.parentNode,t))}for(re(),c=i.length;c<s.length;c+=1)r(c);le()}},i(o){if(!n){for(let l=0;l<i.length;l+=1)v(s[l]);n=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)E(s[l]);n=!1},d(o){Li(s,o),o&&F(t)}}}function Pt(e){let t,n;return t=new qi({props:{config:e[2],step:e[0]}}),{c(){ue(t.$$.fragment)},m(i,s){J(t,i,s),n=!0},p(i,s){const r={};s&2&&(r.config=i[2]),s&1&&(r.step=i[0]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){E(t.$$.fragment,i),n=!1},d(i){ee(t,i)}}}function $i(e){let t,n,i=e[1]&&kt(e);return{c(){t=V("footer"),i&&i.c(),O(t,"class","shepherd-footer")},m(s,r){B(s,t,r),i&&i.m(t,null),n=!0},p(s,[r]){s[1]?i?(i.p(s,r),r&2&&v(i,1)):(i=kt(s),i.c(),v(i,1),i.m(t,null)):i&&(re(),E(i,1,1,()=>{i=null}),le())},i(s){n||(v(i),n=!0)},o(s){E(i),n=!1},d(s){s&&F(t),i&&i.d()}}}function Yi(e,t,n){let i,{step:s}=t;return e.$$set=r=>{"step"in r&&n(0,s=r.step)},e.$$.update=()=>{e.$$.dirty&1&&n(1,i=s.options.buttons)},[s,i]}class Ki extends K{constructor(t){super(),Y(this,t,Yi,$i,$,{step:0})}}function Xi(e){let t,n,i,s,r;return{c(){t=V("button"),n=V("span"),n.textContent="×",O(n,"aria-hidden","true"),O(t,"aria-label",i=e[0].label?e[0].label:"Close Tour"),O(t,"class","shepherd-cancel-icon"),O(t,"type","button")},m(o,l){B(o,t,l),Ee(t,n),s||(r=Xe(t,"click",e[1]),s=!0)},p(o,[l]){l&1&&i!==(i=o[0].label?o[0].label:"Close Tour")&&O(t,"aria-label",i)},i:L,o:L,d(o){o&&F(t),s=!1,r()}}}function Gi(e,t,n){let{cancelIcon:i,step:s}=t;const r=o=>{o.preventDefault(),s.cancel()};return e.$$set=o=>{"cancelIcon"in o&&n(0,i=o.cancelIcon),"step"in o&&n(2,s=o.step)},[i,r,s]}class Qi extends K{constructor(t){super(),Y(this,t,Gi,Xi,$,{cancelIcon:0,step:2})}}function Zi(e){let t;return{c(){t=V("h3"),O(t,"id",e[1]),O(t,"class","shepherd-title")},m(n,i){B(n,t,i),e[3](t)},p(n,[i]){i&2&&O(t,"id",n[1])},i:L,o:L,d(n){n&&F(t),e[3](null)}}}function Ji(e,t,n){let{labelId:i,element:s,title:r}=t;mt(()=>{ce(r)&&n(2,r=r()),n(0,s.innerHTML=r,s)});function o(l){Ae[l?"unshift":"push"](()=>{s=l,n(0,s)})}return e.$$set=l=>{"labelId"in l&&n(1,i=l.labelId),"element"in l&&n(0,s=l.element),"title"in l&&n(2,r=l.title)},[s,i,r,o]}class es extends K{constructor(t){super(),Y(this,t,Ji,Zi,$,{labelId:1,element:0,title:2})}}function Lt(e){let t,n;return t=new es({props:{labelId:e[0],title:e[2]}}),{c(){ue(t.$$.fragment)},m(i,s){J(t,i,s),n=!0},p(i,s){const r={};s&1&&(r.labelId=i[0]),s&4&&(r.title=i[2]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){E(t.$$.fragment,i),n=!1},d(i){ee(t,i)}}}function Rt(e){let t,n;return t=new Qi({props:{cancelIcon:e[3],step:e[1]}}),{c(){ue(t.$$.fragment)},m(i,s){J(t,i,s),n=!0},p(i,s){const r={};s&8&&(r.cancelIcon=i[3]),s&2&&(r.step=i[1]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){E(t.$$.fragment,i),n=!1},d(i){ee(t,i)}}}function ts(e){let t,n,i,s=e[2]&&Lt(e),r=e[3]&&e[3].enabled&&Rt(e);return{c(){t=V("header"),s&&s.c(),n=Ue(),r&&r.c(),O(t,"class","shepherd-header")},m(o,l){B(o,t,l),s&&s.m(t,null),Ee(t,n),r&&r.m(t,null),i=!0},p(o,[l]){o[2]?s?(s.p(o,l),l&4&&v(s,1)):(s=Lt(o),s.c(),v(s,1),s.m(t,n)):s&&(re(),E(s,1,1,()=>{s=null}),le()),o[3]&&o[3].enabled?r?(r.p(o,l),l&8&&v(r,1)):(r=Rt(o),r.c(),v(r,1),r.m(t,null)):r&&(re(),E(r,1,1,()=>{r=null}),le())},i(o){i||(v(s),v(r),i=!0)},o(o){E(s),E(r),i=!1},d(o){o&&F(t),s&&s.d(),r&&r.d()}}}function ns(e,t,n){let{labelId:i,step:s}=t,r,o;return e.$$set=l=>{"labelId"in l&&n(0,i=l.labelId),"step"in l&&n(1,s=l.step)},e.$$.update=()=>{e.$$.dirty&2&&(n(2,r=s.options.title),n(3,o=s.options.cancelIcon))},[i,s,r,o]}class is extends K{constructor(t){super(),Y(this,t,ns,ts,$,{labelId:0,step:1})}}function ss(e){let t;return{c(){t=V("div"),O(t,"class","shepherd-text"),O(t,"id",e[1])},m(n,i){B(n,t,i),e[3](t)},p(n,[i]){i&2&&O(t,"id",n[1])},i:L,o:L,d(n){n&&F(t),e[3](null)}}}function os(e,t,n){let{descriptionId:i,element:s,step:r}=t;mt(()=>{let{text:l}=r.options;ce(l)&&(l=l.call(r)),ot(l)?s.appendChild(l):n(0,s.innerHTML=l,s)});function o(l){Ae[l?"unshift":"push"](()=>{s=l,n(0,s)})}return e.$$set=l=>{"descriptionId"in l&&n(1,i=l.descriptionId),"element"in l&&n(0,s=l.element),"step"in l&&n(2,r=l.step)},[s,i,r,o]}class rs extends K{constructor(t){super(),Y(this,t,os,ss,$,{descriptionId:1,element:0,step:2})}}function Mt(e){let t,n;return t=new is({props:{labelId:e[1],step:e[2]}}),{c(){ue(t.$$.fragment)},m(i,s){J(t,i,s),n=!0},p(i,s){const r={};s&2&&(r.labelId=i[1]),s&4&&(r.step=i[2]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){E(t.$$.fragment,i),n=!1},d(i){ee(t,i)}}}function jt(e){let t,n;return t=new rs({props:{descriptionId:e[0],step:e[2]}}),{c(){ue(t.$$.fragment)},m(i,s){J(t,i,s),n=!0},p(i,s){const r={};s&1&&(r.descriptionId=i[0]),s&4&&(r.step=i[2]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){E(t.$$.fragment,i),n=!1},d(i){ee(t,i)}}}function Ft(e){let t,n;return t=new Ki({props:{step:e[2]}}),{c(){ue(t.$$.fragment)},m(i,s){J(t,i,s),n=!0},p(i,s){const r={};s&4&&(r.step=i[2]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){E(t.$$.fragment,i),n=!1},d(i){ee(t,i)}}}function ls(e){let t,n=!A(e[2].options.title)||e[2].options.cancelIcon&&e[2].options.cancelIcon.enabled,i,s=!A(e[2].options.text),r,o=Array.isArray(e[2].options.buttons)&&e[2].options.buttons.length,l,c=n&&Mt(e),a=s&&jt(e),d=o&&Ft(e);return{c(){t=V("div"),c&&c.c(),i=Ue(),a&&a.c(),r=Ue(),d&&d.c(),O(t,"class","shepherd-content")},m(u,f){B(u,t,f),c&&c.m(t,null),Ee(t,i),a&&a.m(t,null),Ee(t,r),d&&d.m(t,null),l=!0},p(u,[f]){f&4&&(n=!A(u[2].options.title)||u[2].options.cancelIcon&&u[2].options.cancelIcon.enabled),n?c?(c.p(u,f),f&4&&v(c,1)):(c=Mt(u),c.c(),v(c,1),c.m(t,i)):c&&(re(),E(c,1,1,()=>{c=null}),le()),f&4&&(s=!A(u[2].options.text)),s?a?(a.p(u,f),f&4&&v(a,1)):(a=jt(u),a.c(),v(a,1),a.m(t,r)):a&&(re(),E(a,1,1,()=>{a=null}),le()),f&4&&(o=Array.isArray(u[2].options.buttons)&&u[2].options.buttons.length),o?d?(d.p(u,f),f&4&&v(d,1)):(d=Ft(u),d.c(),v(d,1),d.m(t,null)):d&&(re(),E(d,1,1,()=>{d=null}),le())},i(u){l||(v(c),v(a),v(d),l=!0)},o(u){E(c),E(a),E(d),l=!1},d(u){u&&F(t),c&&c.d(),a&&a.d(),d&&d.d()}}}function cs(e,t,n){let{descriptionId:i,labelId:s,step:r}=t;return e.$$set=o=>{"descriptionId"in o&&n(0,i=o.descriptionId),"labelId"in o&&n(1,s=o.labelId),"step"in o&&n(2,r=o.step)},[i,s,r]}class as extends K{constructor(t){super(),Y(this,t,cs,ls,$,{descriptionId:0,labelId:1,step:2})}}function Bt(e){let t;return{c(){t=V("div"),O(t,"class","shepherd-arrow"),O(t,"data-popper-arrow","")},m(n,i){B(n,t,i)},d(n){n&&F(t)}}}function fs(e){let t,n,i,s,r,o,l,c,a=e[4].options.arrow&&e[4].options.attachTo&&e[4].options.attachTo.element&&e[4].options.attachTo.on&&Bt();i=new as({props:{descriptionId:e[2],labelId:e[3],step:e[4]}});let d=[{"aria-describedby":s=A(e[4].options.text)?null:e[2]},{"aria-labelledby":r=e[4].options.title?e[3]:null},e[1],{role:"dialog"},{tabindex:"0"}],u={};for(let f=0;f<d.length;f+=1)u=ki(u,d[f]);return{c(){t=V("div"),a&&a.c(),n=Ue(),ue(i.$$.fragment),Ct(t,u),ge(t,"shepherd-has-cancel-icon",e[5]),ge(t,"shepherd-has-title",e[6]),ge(t,"shepherd-element",!0)},m(f,h){B(f,t,h),a&&a.m(t,null),Ee(t,n),J(i,t,null),e[13](t),o=!0,l||(c=Xe(t,"keydown",e[7]),l=!0)},p(f,[h]){f[4].options.arrow&&f[4].options.attachTo&&f[4].options.attachTo.element&&f[4].options.attachTo.on?a||(a=Bt(),a.c(),a.m(t,n)):a&&(a.d(1),a=null);const m={};h&4&&(m.descriptionId=f[2]),h&8&&(m.labelId=f[3]),h&16&&(m.step=f[4]),i.$set(m),Ct(t,u=Hi(d,[(!o||h&20&&s!==(s=A(f[4].options.text)?null:f[2]))&&{"aria-describedby":s},(!o||h&24&&r!==(r=f[4].options.title?f[3]:null))&&{"aria-labelledby":r},h&2&&f[1],{role:"dialog"},{tabindex:"0"}])),ge(t,"shepherd-has-cancel-icon",f[5]),ge(t,"shepherd-has-title",f[6]),ge(t,"shepherd-element",!0)},i(f){o||(v(i.$$.fragment,f),o=!0)},o(f){E(i.$$.fragment,f),o=!1},d(f){f&&F(t),a&&a.d(),ee(i),e[13](null),l=!1,c()}}}const us=9,ds=27,hs=37,ps=39;function Dt(e){return e.split(" ").filter(t=>!!t.length)}function ms(e,t,n){let{classPrefix:i,element:s,descriptionId:r,firstFocusableElement:o,focusableElements:l,labelId:c,lastFocusableElement:a,step:d,dataStepId:u}=t,f,h,m;const g=()=>s;Fi(()=>{n(1,u={[`data-${i}shepherd-step-id`]:d.id}),n(9,l=s.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]')),n(8,o=l[0]),n(10,a=l[l.length-1])}),mt(()=>{m!==d.options.classes&&_()});function _(){y(m),m=d.options.classes,x(m)}function y(p){if(Le(p)){const w=Dt(p);w.length&&s.classList.remove(...w)}}function x(p){if(Le(p)){const w=Dt(p);w.length&&s.classList.add(...w)}}const S=p=>{const{tour:w}=d;switch(p.keyCode){case us:if(l.length===0){p.preventDefault();break}p.shiftKey?(document.activeElement===o||document.activeElement.classList.contains("shepherd-element"))&&(p.preventDefault(),a.focus()):document.activeElement===a&&(p.preventDefault(),o.focus());break;case ds:w.options.exitOnEsc&&(p.stopPropagation(),d.cancel());break;case hs:w.options.keyboardNavigation&&(p.stopPropagation(),w.back());break;case ps:w.options.keyboardNavigation&&(p.stopPropagation(),w.next());break}};function b(p){Ae[p?"unshift":"push"](()=>{s=p,n(0,s)})}return e.$$set=p=>{"classPrefix"in p&&n(11,i=p.classPrefix),"element"in p&&n(0,s=p.element),"descriptionId"in p&&n(2,r=p.descriptionId),"firstFocusableElement"in p&&n(8,o=p.firstFocusableElement),"focusableElements"in p&&n(9,l=p.focusableElements),"labelId"in p&&n(3,c=p.labelId),"lastFocusableElement"in p&&n(10,a=p.lastFocusableElement),"step"in p&&n(4,d=p.step),"dataStepId"in p&&n(1,u=p.dataStepId)},e.$$.update=()=>{e.$$.dirty&16&&(n(5,f=d.options&&d.options.cancelIcon&&d.options.cancelIcon.enabled),n(6,h=d.options&&d.options.title))},[s,u,r,c,d,f,h,S,o,l,a,i,g,b]}class gs extends K{constructor(t){super(),Y(this,t,ms,fs,$,{classPrefix:11,element:0,descriptionId:2,firstFocusableElement:8,focusableElements:9,labelId:3,lastFocusableElement:10,step:4,dataStepId:1,getElement:12})}get getElement(){return this.$$.ctx[12]}}class it extends rt{constructor(t,n={}){return super(t,n),this.tour=t,this.classPrefix=this.tour.options?Wt(this.tour.options.classPrefix):"",this.styles=t.styles,this._resolvedAttachTo=null,Ht(this),this._setOptions(n),this}cancel(){this.tour.cancel(),this.trigger("cancel")}complete(){this.tour.complete(),this.trigger("complete")}destroy(){Oi(this),ot(this.el)&&(this.el.remove(),this.el=null),this._updateStepTargetOnHide(),this.trigger("destroy")}getTour(){return this.tour}hide(){this.tour.modal.hide(),this.trigger("before-hide"),this.el&&(this.el.hidden=!0),this._updateStepTargetOnHide(),this.trigger("hide")}_resolveAttachToOptions(){return this._resolvedAttachTo=Wn(this),this._resolvedAttachTo}_getResolvedAttachToOptions(){return this._resolvedAttachTo===null?this._resolveAttachToOptions():this._resolvedAttachTo}isOpen(){return!!(this.el&&!this.el.hidden)}show(){return ce(this.options.beforeShowPromise)?Promise.resolve(this.options.beforeShowPromise()).then(()=>this._show()):Promise.resolve(this._show())}updateStepOptions(t){Object.assign(this.options,t),this.shepherdElementComponent&&this.shepherdElementComponent.$set({step:this})}getElement(){return this.el}getTarget(){return this.target}_createTooltipContent(){const t=`${this.id}-description`,n=`${this.id}-label`;return this.shepherdElementComponent=new gs({target:this.tour.options.stepsContainer||document.body,props:{classPrefix:this.classPrefix,descriptionId:t,labelId:n,step:this,styles:this.styles}}),this.shepherdElementComponent.getElement()}_scrollTo(t){const{element:n}=this._getResolvedAttachToOptions();ce(this.options.scrollToHandler)?this.options.scrollToHandler(n):Nn(n)&&typeof n.scrollIntoView=="function"&&n.scrollIntoView(t)}_getClassOptions(t){const n=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions,i=t.classes?t.classes:"",s=n&&n.classes?n.classes:"",r=[...i.split(" "),...s.split(" ")],o=new Set(r);return Array.from(o).join(" ").trim()}_setOptions(t={}){let n=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;n=st({},n||{}),this.options=Object.assign({arrow:!0},n,t,Si(n,t));const{when:i}=this.options;this.options.classes=this._getClassOptions(t),this.destroy(),this.id=this.options.id||`step-${lt()}`,i&&Object.keys(i).forEach(s=>{this.on(s,i[s],this)})}_setupElements(){A(this.el)||this.destroy(),this.el=this._createTooltipContent(),this.options.advanceOn&&Hn(this),vi(this)}_show(){this.trigger("before-show"),this._resolveAttachToOptions(),this._setupElements(),this.tour.modal||this.tour._setupModal(),this.tour.modal.setupForStep(this),this._styleTargetElementForStep(this),this.el.hidden=!1,this.options.scrollTo&&setTimeout(()=>{this._scrollTo(this.options.scrollTo)}),this.el.hidden=!1;const t=this.shepherdElementComponent.getElement(),n=this.target||document.body;n.classList.add(`${this.classPrefix}shepherd-enabled`),n.classList.add(`${this.classPrefix}shepherd-target`),t.classList.add("shepherd-enabled"),this.trigger("show")}_styleTargetElementForStep(t){const n=t.target;n&&(t.options.highlightClass&&n.classList.add(t.options.highlightClass),n.classList.remove("shepherd-target-click-disabled"),t.options.canClickTarget===!1&&n.classList.add("shepherd-target-click-disabled"))}_updateStepTargetOnHide(){const t=this.target||document.body;this.options.highlightClass&&t.classList.remove(this.options.highlightClass),t.classList.remove("shepherd-target-click-disabled",`${this.classPrefix}shepherd-enabled`,`${this.classPrefix}shepherd-target`)}}function bs(e){if(e){const{steps:t}=e;t.forEach(n=>{n.options&&n.options.canClickTarget===!1&&n.options.attachTo&&n.target instanceof HTMLElement&&n.target.classList.remove("shepherd-target-click-disabled")})}}function ys({width:e,height:t,x:n=0,y:i=0,r:s=0}){const{innerWidth:r,innerHeight:o}=window,{topLeft:l=0,topRight:c=0,bottomRight:a=0,bottomLeft:d=0}=typeof s=="number"?{topLeft:s,topRight:s,bottomRight:s,bottomLeft:s}:s;return`M${r},${o}H0V0H${r}V${o}ZM${n+l},${i}a${l},${l},0,0,0-${l},${l}V${t+i-d}a${d},${d},0,0,0,${d},${d}H${e+n-a}a${a},${a},0,0,0,${a}-${a}V${i+c}a${c},${c},0,0,0-${c}-${c}Z`}function _s(e){let t,n,i,s,r;return{c(){t=At("svg"),n=At("path"),O(n,"d",e[2]),O(t,"class",i=`${e[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)},m(o,l){B(o,t,l),Ee(t,n),e[11](t),s||(r=Xe(t,"touchmove",e[3]),s=!0)},p(o,[l]){l&4&&O(n,"d",o[2]),l&2&&i!==(i=`${o[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)&&O(t,"class",i)},i:L,o:L,d(o){o&&F(t),e[11](null),s=!1,r()}}}function on(e){if(!e)return null;const n=e instanceof HTMLElement&&window.getComputedStyle(e).overflowY;return n!=="hidden"&&n!=="visible"&&e.scrollHeight>=e.clientHeight?e:on(e.parentElement)}function ws(e,t){const n=e.getBoundingClientRect();let i=n.y||n.top,s=n.bottom||i+n.height;if(t){const o=t.getBoundingClientRect(),l=o.y||o.top,c=o.bottom||l+o.height;i=Math.max(i,l),s=Math.min(s,c)}const r=Math.max(s-i,0);return{y:i,height:r}}function xs(e,t,n){let{element:i,openingProperties:s}=t;lt();let r=!1,o,l;a();const c=()=>i;function a(){n(4,s={width:0,height:0,x:0,y:0,r:0})}function d(){n(1,r=!1),y()}function u(b=0,p=0,w,I){if(I){const{y:R,height:D}=ws(I,w),{x:te,width:H,left:ne}=I.getBoundingClientRect();n(4,s={width:H+b*2,height:D+b*2,x:(te||ne)-b,y:R-b,r:p})}else a()}function f(b){y(),b.tour.options.useModalOverlay?(x(b),h()):d()}function h(){n(1,r=!0)}const m=b=>{b.preventDefault()},g=b=>{b.stopPropagation()};function _(){window.addEventListener("touchmove",m,{passive:!1})}function y(){o&&(cancelAnimationFrame(o),o=void 0),window.removeEventListener("touchmove",m,{passive:!1})}function x(b){const{modalOverlayOpeningPadding:p,modalOverlayOpeningRadius:w}=b.options,I=on(b.target),R=()=>{o=void 0,u(p,w,I,b.target),o=requestAnimationFrame(R)};R(),_()}function S(b){Ae[b?"unshift":"push"](()=>{i=b,n(0,i)})}return e.$$set=b=>{"element"in b&&n(0,i=b.element),"openingProperties"in b&&n(4,s=b.openingProperties)},e.$$.update=()=>{e.$$.dirty&16&&n(2,l=ys(s))},[i,r,l,g,s,c,a,d,u,f,h,S]}class vs extends K{constructor(t){super(),Y(this,t,xs,_s,$,{element:0,openingProperties:4,getElement:5,closeModalOpening:6,hide:7,positionModal:8,setupForStep:9,show:10})}get getElement(){return this.$$.ctx[5]}get closeModalOpening(){return this.$$.ctx[6]}get hide(){return this.$$.ctx[7]}get positionModal(){return this.$$.ctx[8]}get setupForStep(){return this.$$.ctx[9]}get show(){return this.$$.ctx[10]}}const G=new rt;class Ss extends rt{constructor(t={}){super(t),Ht(this);const n={exitOnEsc:!0,keyboardNavigation:!0};return this.options=Object.assign({},n,t),this.classPrefix=Wt(this.options.classPrefix),this.steps=[],this.addSteps(this.options.steps),["active","cancel","complete","inactive","show","start"].map(s=>{(r=>{this.on(r,o=>{o=o||{},o.tour=this,G.trigger(r,o)})})(s)}),this._setTourID(),this}addStep(t,n){let i=t;return i instanceof it?i.tour=this:i=new it(this,i),A(n)?this.steps.push(i):this.steps.splice(n,0,i),i}addSteps(t){return Array.isArray(t)&&t.forEach(n=>{this.addStep(n)}),this}back(){const t=this.steps.indexOf(this.currentStep);this.show(t-1,!1)}async cancel(){if(this.options.confirmCancel){const t=typeof this.options.confirmCancel=="function",n=this.options.confirmCancelMessage||"Are you sure you want to stop the tour?";(t?await this.options.confirmCancel():window.confirm(n))&&this._done("cancel")}else this._done("cancel")}complete(){this._done("complete")}getById(t){return this.steps.find(n=>n.id===t)}getCurrentStep(){return this.currentStep}hide(){const t=this.getCurrentStep();if(t)return t.hide()}isActive(){return G.activeTour===this}next(){const t=this.steps.indexOf(this.currentStep);t===this.steps.length-1?this.complete():this.show(t+1,!0)}removeStep(t){const n=this.getCurrentStep();this.steps.some((i,s)=>{if(i.id===t)return i.isOpen()&&i.hide(),i.destroy(),this.steps.splice(s,1),!0}),n&&n.id===t&&(this.currentStep=void 0,this.steps.length?this.show(0):this.cancel())}show(t=0,n=!0){const i=Le(t)?this.getById(t):this.steps[t];i&&(this._updateStateBeforeShow(),ce(i.options.showOn)&&!i.options.showOn()?this._skipStep(i,n):(this.trigger("show",{step:i,previous:this.currentStep}),this.currentStep=i,i.show()))}start(){this.trigger("start"),this.focusedElBeforeOpen=document.activeElement,this.currentStep=null,this._setupModal(),this._setupActiveTour(),this.next()}_done(t){const n=this.steps.indexOf(this.currentStep);if(Array.isArray(this.steps)&&this.steps.forEach(i=>i.destroy()),bs(this),this.trigger(t,{index:n}),G.activeTour=null,this.trigger("inactive",{tour:this}),this.modal&&this.modal.hide(),(t==="cancel"||t==="complete")&&this.modal){const i=document.querySelector(".shepherd-modal-overlay-container");i&&i.remove()}ot(this.focusedElBeforeOpen)&&this.focusedElBeforeOpen.focus()}_setupActiveTour(){this.trigger("active",{tour:this}),G.activeTour=this}_setupModal(){this.modal=new vs({target:this.options.modalContainer||document.body,props:{classPrefix:this.classPrefix,styles:this.styles}})}_skipStep(t,n){const i=this.steps.indexOf(t);if(i===this.steps.length-1)this.complete();else{const s=n?i+1:i-1;this.show(s,n)}}_updateStateBeforeShow(){this.currentStep&&this.currentStep.hide(),this.isActive()||this._setupActiveTour()}_setTourID(){const t=this.options.tourName||"tour";this.id=`${t}--${lt()}`}}const Os=typeof window>"u";class Nt{constructor(){}}Os?Object.assign(G,{Tour:Nt,Step:Nt}):Object.assign(G,{Tour:Ss,Step:it});const gt=e=>(bn("data-v-8cc45fb3"),e=e(),yn(),e),Es=gt(()=>X("span",{class:"me-3"},"Search",-1)),As=gt(()=>X("span",{class:"meta-key"},"⌘K",-1)),Cs=[Es,As],Ts={class:"text-xs text-disabled text-uppercase"},Is={class:"mt-8"},ks=gt(()=>X("span",{class:"d-flex justify-center text-disabled"},"Try searching for",-1)),Ps=["onClick"],Ls={class:"text-sm"},Rs=Object.assign({inheritAttrs:!1},{__name:"NavSearchBar",setup(e){const t=cn(),n=Ge(!1),i=[{title:"Popular Searches",content:[{icon:"tabler-chart-donut",title:"Analytics",url:{name:"dashboards-analytics"}},{icon:"tabler-chart-bubble",title:"CRM",url:{name:"dashboards-crm"}},{icon:"tabler-file",title:"Landing Page",url:{name:"front-pages-landing-page"}},{icon:"tabler-users",title:"User List",url:{name:"apps-user-list"}}]},{title:"Apps & Pages",content:[{icon:"tabler-calendar",title:"Calendar",url:{name:"apps-calendar"}},{icon:"tabler-shopping-cart",title:"ECommerce Product",url:{name:"apps-ecommerce-product-list"}},{icon:"tabler-school",title:"Academy",url:{name:"apps-academy-dashboard"}},{icon:"tabler-truck",title:"Logistic Fleet",url:{name:"apps-logistics-fleet"}}]},{title:"User Interface",content:[{icon:"tabler-letter-a",title:"Typography",url:{name:"pages-typography"}},{icon:"tabler-square",title:"Tabs",url:{name:"components-tabs"}},{icon:"tabler-map",title:"Tour",url:{name:"extensions-tour"}},{icon:"tabler-keyboard",title:"Statistics",url:{name:"pages-cards-card-statistics"}}]},{title:"Popular Searches",content:[{icon:"tabler-list",title:"Select",url:{name:"forms-select"}},{icon:"tabler-currency-dollar",title:"Payment",url:{name:"front-pages-payment"}},{icon:"tabler-calendar",title:"Date & Time Picker",url:{name:"forms-date-time-picker"}},{icon:"tabler-home",title:"Property Listing Wizard",url:{name:"wizard-examples-property-listing"}}]}],s=[{title:"Analytics Dashboard",icon:"tabler-shopping-cart",url:{name:"dashboards-analytics"}},{title:"Account Settings",icon:"tabler-user",url:{name:"pages-account-settings-tab",params:{tab:"account"}}},{title:"Pricing Page",icon:"tabler-cash",url:{name:"pages-pricing"}}],r=Ge(""),o=an(),l=Ge([]);fn(r,async()=>{const{data:u}=await _n(wn("/app-bar/search",{q:r.value}));l.value=u.value});const a=u=>{o.push(u.url),n.value=!1,r.value=""},d=mn(()=>gn(()=>import("./AppBarSearch-545de207.js"),["assets/AppBarSearch-545de207.js","assets/index-169996dc.js","assets/index-af792bbb.css","assets/vue3-perfect-scrollbar.esm-e1f82a0e.js","assets/VTextField-a8984053.js","assets/VField-150a934a.js","assets/index-00c7d20d.js","assets/VInput-c4d3942a.js","assets/VInput-d87a4e0f.css","assets/VField-********.css","assets/VDivider-12bfa926.js","assets/VDivider-a07982d1.css","assets/VList-349a1ccf.js","assets/ssrBoot-c101cd97.js","assets/VList-ed4977a0.css","assets/AppBarSearch-397d0879.css"]));return(u,f)=>{const h=un("IconBtn");return z(),me(Ie,null,[X("div",dn({class:"d-flex align-center cursor-pointer"},u.$attrs,{style:{"user-select":"none"},onClick:f[2]||(f[2]=m=>n.value=!ie(n))}),[M(h,{class:"me-1",onClick:f[0]||(f[0]=m=>{var g;return(g=ie(G).activeTour)==null?void 0:g.cancel()})},{default:k(()=>[M(Te,{size:"26",icon:"tabler-search"})]),_:1}),ie(t).appContentLayoutNav==="vertical"?(z(),me("span",{key:0,class:"d-none d-md-flex align-center text-disabled",onClick:f[1]||(f[1]=m=>{var g;return(g=ie(G).activeTour)==null?void 0:g.cancel()})},Cs)):bt("",!0)],16),M(ie(d),{isDialogVisible:ie(n),"onUpdate:isDialogVisible":f[3]||(f[3]=m=>pn(n)?n.value=m:null),"search-results":ie(l),onSearch:f[4]||(f[4]=m=>r.value=m)},{suggestions:k(()=>[M(hn,{class:"app-bar-search-suggestions h-100 pa-10"},{default:k(()=>[i?(z(),Qe(xn,{key:0,class:"gap-y-4"},{default:k(()=>[(z(),me(Ie,null,Fe(i,m=>M(vn,{key:m.title,cols:"12",sm:"6",class:"ps-6"},{default:k(()=>[X("p",Ts,Be(m.title),1),M(Sn,{class:"card-list"},{default:k(()=>[(z(!0),me(Ie,null,Fe(m.content,g=>(z(),Qe(_t,{key:g.title,link:"",title:g.title,class:"app-bar-search-suggestion",onClick:_=>a(g)},{prepend:k(()=>[M(Te,{icon:g.icon,size:"20",class:"me-2"},null,8,["icon"])]),_:2},1032,["title","onClick"]))),128))]),_:2},1024)]),_:2},1024)),64))]),_:1})):bt("",!0)]),_:1})]),noDataSuggestion:k(()=>[X("div",Is,[ks,(z(),me(Ie,null,Fe(s,m=>X("h6",{key:m.title,class:"app-bar-search-suggestion text-sm font-weight-regular cursor-pointer mt-3",onClick:g=>a(m)},[M(Te,{size:"20",icon:m.icon,class:"me-3"},null,8,["icon"]),X("span",Ls,Be(m.title),1)],8,Ps)),64))])]),searchResult:k(({item:m})=>[M(On,{class:"text-disabled"},{default:k(()=>[yt(Be(m.title),1)]),_:2},1024),(z(!0),me(Ie,null,Fe(m.children,g=>(z(),Qe(_t,{key:g.title,link:"",onClick:_=>a(g)},{prepend:k(()=>[M(Te,{size:"20",icon:g.icon,class:"me-3"},null,8,["icon"])]),append:k(()=>[M(Te,{size:"20",icon:"tabler-corner-down-left",class:"enter-icon text-disabled"})]),default:k(()=>[M(En,null,{default:k(()=>[yt(Be(g.title),1)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1},8,["isDialogVisible","search-results"])],64)}}}),Hs=ln(Rs,[["__scopeId","data-v-8cc45fb3"]]);export{Hs as default};
