import { Module } from '@nestjs/common';
import { MetaService } from './meta.service';
import { InstagramApiService } from './services/instagram-api.service';
import { FacebookApiService } from './services/facebook-api.service';
import { MetaApiClientService } from './services/meta-api-client.service';
import { MetaApiHandlerFactoryService } from './services/meta-api-handler-factory.service';
import { DatabaseModule } from '../database/database.module';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [DatabaseModule, HttpModule],
  providers: [
    MetaService,
    InstagramApiService,
    FacebookApiService,
    MetaApiClientService,
    MetaApiHandlerFactoryService,
  ],
  exports: [MetaService],
})
export class MetaModule {}
