<?php

use App\Http\Controllers\CustomersController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\BillTriggerController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\WebhookController;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProductsController;
use App\Http\Controllers\ProductAttributesController;
use App\Http\Controllers\ProductsGalleryController;
use App\Http\Controllers\ProductsMediaController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\OrderAttributesController;
use App\Http\Controllers\DeliveryMethodController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\ProductPricesController;
use App\Http\Controllers\PlansController;
use App\Http\Controllers\Monitoring\TestingPrometheusController;
use Illuminate\Support\Facades\Redis;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

//other route files
require base_path('routes/health.php');
require base_path('routes/webservice.php');
require base_path('routes/ai_services.php');
require base_path('routes/instagram-app.php');


Route::post('facebook-accounts/{user_id}', [PageController::class, 'saveFacebookAccountsToMongo']);
Route::post('save-insta-to-mongo/{user_id}', [PageController::class, 'saveInstagramAccountToMongo']);
Route::post('instagram-account', [PageController::class, 'connectToInstagramAccount']);
Route::post('test-metrics', [TestingPrometheusController::class, 'testMetrics']);


Route::match(['get', 'post'],'paymentCallbackL',[WebhookController::class, 'paymentCallbackL']);
Route::post('paymentCallback',[WebhookController::class, 'paymentCallback']);
Route::post('transactionCallback',[PlansController::class, 'callback']);
Route::match(['get', 'post'],'/webhook', [WebhookController::class, 'handle']);
Route::get('redirectPayment', [WebhookController::class, 'redirectPayment']);
Route::middleware(['throttle:10,1'])->group(function () {
    Route::post('/authentication', [UserController::class, 'auth']);
    Route::post('/reSendOtp',[UserController::class,'reSendOtp']);
});
// Route::get('testredis',function(){
//     Redis::set('name', 'Taylor2');
//     return Redis::get('name');
// });
Route::middleware(['throttle:1000,3'])->group(function () {

    Route::get('/login',function (){
        return [
            "verified"=>false,
            "message"=>"unauthorized",
        ];
    })->name('loginAPI');

    Route::post('/check_code',[UserController::class,'checkCode']);
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/user', function (Request $request) {
            return $request->user();
        });
        Route::get('loginToFacebook', [PageController::class, 'loginToFacebook']);
        Route::get('loginToInstagram', [PageController::class, 'loginToInstagram']);
        Route::get('facebook-accounts', [PageController::class, 'getFacebookAccounts']);
        Route::post('facebook-accounts', [PageController::class, 'connectToFacebookAccount']);
        Route::delete('facebook-accounts', [PageController::class, 'disconnectFromMeta']);
        Route::delete('facebook-accounts/{id}', [PageController::class, 'disconnectFromMeta']);

        Route::get('get-page-status', [PageController::class, 'getPageStatus']);
        Route::get('get-page-status/{id}', [PageController::class, 'getPageStatus']);
        Route::post('/user',[UserController::class,'update']);
        Route::get('/logout', [UserController::class, 'logout']);
        Route::get('/dashboard', [DashboardController::class, 'index']);
        Route::get('/dashboard/show/{id}', [DashboardController::class, 'index']);
        Route::get('/dashboard/allOrdersPrice', [DashboardController::class, 'allOrdersPrice']);
        Route::get('/dashboard/allOrdersPrice/{id}', [DashboardController::class, 'allOrdersPrice']);
        Route::prefix('products')->group(function () {
            Route::post('/', [ProductsController::class, 'create']);
            Route::post('/page/{id}', [ProductsController::class, 'create']);
            Route::get('/', [ProductsController::class, 'index']);
            Route::get('/page/{id}', [ProductsController::class, 'indexPage']);
            Route::get('/{id}', [ProductsController::class, 'show']);
            Route::put('/{id}', [ProductsController::class, 'update']);
            Route::delete('/{id}', [ProductsController::class, 'destroy']);
        });
        Route::prefix('plans')->group(function () {
            Route::get('/', [PlansController::class, 'index']);
            Route::post('/buy', [PlansController::class, 'buy']);
            Route::post('/show/{tag}', [PlansController::class, 'show']);
        });
        Route::prefix('deliveryMethods')->group(function () {
            Route::post('/', [DeliveryMethodController::class, 'create']);
            Route::get('/', [DeliveryMethodController::class, 'index']);
            Route::get('/page/{id}', [DeliveryMethodController::class, 'indexPage']);
            Route::get('/{id}', [DeliveryMethodController::class, 'show']);
            Route::put('/{id}', [DeliveryMethodController::class, 'update']);
            Route::delete('/{id}', [DeliveryMethodController::class, 'destroy']);
        });
        Route::prefix('productAttributes')->group(function () {
            Route::post('/', [ProductAttributesController::class, 'create']);
            Route::get('/', [ProductAttributesController::class, 'index']);
            Route::get('/{id}', [ProductAttributesController::class, 'show']);
            Route::get('/product/{id}', [ProductAttributesController::class, 'showProduct']);
            Route::put('/{id}', [ProductAttributesController::class, 'update']);
            Route::delete('/{id}', [ProductAttributesController::class, 'destroy']);
        });
        Route::prefix('productsPrices')->group(function () {
            Route::post('/', [ProductPricesController::class, 'create']);
            // Route::get('/', [ProductPricesController::class, 'index']);
            // Route::get('/test', [ProductsGalleryController::class, 'test']);
            Route::get('/{id}', [ProductPricesController::class, 'show']);
            Route::post('/{id}', [ProductPricesController::class, 'update']);
            Route::delete('/{id}', [ProductPricesController::class, 'destroy']);
        });
        Route::prefix('productsGallery')->group(function () {
            Route::post('/', [ProductsGalleryController::class, 'create']);
            Route::get('/', [ProductsGalleryController::class, 'index']);
            // Route::get('/test', [ProductsGalleryController::class, 'test']);
            Route::get('/{id}', [ProductsGalleryController::class, 'show']);
            Route::post('/{id}', [ProductsGalleryController::class, 'update']);
            Route::delete('/{id}', [ProductsGalleryController::class, 'destroy']);
        });
        Route::prefix('productsMedia')->group(function () {
            Route::post('/', [ProductsMediaController::class, 'create']);
            Route::get('/', [ProductsMediaController::class, 'index']);
            Route::get('/{id}', [ProductsMediaController::class, 'show']);
            Route::get('/product/{id}', [ProductsMediaController::class, 'productShow']);
            Route::post('/{id}', [ProductsMediaController::class, 'update']);
            Route::delete('/{id}', [ProductsMediaController::class, 'destroy']);
        });
        Route::prefix('settings')->group(function () {
            Route::post('/show', [SettingsController::class, 'show']);
            Route::post('/show/{id}', [SettingsController::class, 'show']);
            Route::post('/', [SettingsController::class, 'create']);
            Route::post('/{id}', [SettingsController::class, 'create']);

        });
        Route::prefix('orders')->group(function () {
            Route::post('/', [OrderController::class, 'create']);
            Route::get('/', [OrderController::class, 'index']);
            Route::get('/page/{id}', [OrderController::class, 'indexPage']);
            Route::get('/{id}', [OrderController::class, 'show']);
            Route::put('/{id}', [OrderController::class, 'update']);
            Route::delete('/{id}', [OrderController::class, 'destroy']);
        });

        Route::prefix('customers')->group(function () {
//            Route::post('/', [CustomersController::class, 'create']);
            Route::get('/', [CustomersController::class, 'index']);
            Route::get('/page/{id}', [CustomersController::class, 'indexPage']);

            Route::get('/{id}', [CustomersController::class, 'show']);
//            Route::put('/{id}', [CustomersController::class, 'update']);
//            Route::delete('/{id}', [CustomersController::class, 'destroy']);
        });
        Route::prefix('paymentMethod')->group(function () {
            Route::post('/', [PaymentMethodController::class, 'create']);
            Route::get('/', [PaymentMethodController::class, 'index']);
            Route::get('/page/{id}', [PaymentMethodController::class, 'indexPage']);
            Route::post('/page/{id}', [PaymentMethodController::class, 'create']);
            Route::get('/{id}', [PaymentMethodController::class, 'show']);
            Route::put('/{id}', [PaymentMethodController::class, 'update']);
            Route::delete('/{id}', [PaymentMethodController::class, 'destroy']);
        });
        Route::prefix('orderAttrbiutes')->group(function () {
            Route::post('/', [OrderAttributesController::class, 'create']);
            Route::get('/', [OrderAttributesController::class, 'index']);
            Route::get('/{id}', [OrderAttributesController::class, 'show']);
            Route::put('/{id}', [OrderAttributesController::class, 'update']);
            Route::delete('/{id}', [OrderAttributesController::class, 'destroy']);
        });
        Route::prefix('pages')->group(function () {
            Route::post('/', [PageController::class, 'create']);
            Route::get('/', [PageController::class, 'index']);
            Route::get('/export', [PageController::class, 'exportRequest']);
            Route::get('/{id}', [PageController::class, 'show']);
            Route::put('/{id}', [PageController::class, 'update']);
            Route::delete('/{id}', [PageController::class, 'destroy']);
            Route::get('{id}/contents', [PageController::class, 'getPageContents']);
        });
//        Route::prefix('pageContents')->group(function () {
//            Route::post('/', [PageContentController::class, 'create']);
//            Route::get('/', [PageContentController::class, 'index']);
//            Route::get('/{id}', [PageContentController::class, 'show']);
//            Route::put('/{id}', [PageContentController::class, 'update']);
//            Route::delete('/{id}', [PageContentController::class, 'destroy']);
//        });


        // New:

        Route::get('/bill-triggers', [BillTriggerController::class, 'getAllTriggers']);
        Route::post('/bill-triggers', [BillTriggerController::class, 'updateOrCreateTriggers']);

    });

});
