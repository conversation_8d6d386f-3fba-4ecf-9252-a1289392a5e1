import{aM as e}from"./index-169996dc.js";const c=t=>[r=>r?!0:`${t} الزامی است `],i=(t,r)=>{const o={...t},[n]=Object.keys(o),s=o[n];return e({text:r===500?"خطای سرور لطفا دوباره تلاش کنید.":s[0],icon:"error",buttonsStyling:!0,confirmButtonText:"متوجه شدم!"})},a=t=>e({text:t,icon:"error",buttonsStyling:!0,confirmButtonText:"متوجه شدم!"}),l=t=>e({text:t,icon:"success",buttonsStyling:!0,confirmButtonText:"متوجه شدم!"});export{i as a,c as r,l as s,a as v};
