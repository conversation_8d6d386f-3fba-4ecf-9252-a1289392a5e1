export const useFollowUpStore = defineStore('follow_up', {
  state: () => ({
    followUpSteps: [
      {
        hours: null,
        minutes: null,
        text: null,
        from: null,
        to: null,
        trigger: null,
      },
    ],
  }),
  getters: {
    toJson: state =>{
      const res = []

      state.followUpSteps.forEach(step => {
        
        const temp = {
          'trigger': step.trigger,
          'message': step.text,
          'hours': 0,
          'minutes': 0,
          'from': step.from,
          'to': step.to,
          'trigger': step.trigger,
        }

        if(step.hours){
          temp['hours']= step.hours.split(' ')[0]
        }

        if(step.minutes){
          temp['minutes']= step.minutes.split(' ')[0]
        }
        res.push(temp)
      })

      return res

    },
  },
  actions: {
    resetState(){

      this.followUpSteps =  [
        {
          hours: null,
          minutes: null,
          text: null,
          voice: null,
        },
      ]
    },
    async checkAll(){
      let allOkFlag = true
      await this.followUpSteps.every(step => {
        console.log('step => ', step)
        if((!step.hours && !step.minutes)||(!step.voice && !step.text)){
          const swalData = {
            title: 'لطفا قبل از ذخیره فالو آپ همه پیام را به درستی پر کنید',
            icon: 'error',
            confirmButtonText: 'باشه',
          }

          swalFire(swalData)
          allOkFlag =false
          
          return false

        }
      })
      
      return allOkFlag
    },
    addFollowUpStep() {
      const followUpSteps = this.followUpSteps
      const followUpStepsCount  = followUpSteps.length
      if(followUpStepsCount >= 5)return
      const lastStep =followUpSteps[followUpStepsCount-1]
      if((!lastStep.hours && !lastStep.minutes)||(!lastStep.voice && !lastStep.text)){
        const swalData = {
          title: 'لطفا قبل از ساخت پیام جدید همه پیام های قبلی را به درستی پر کنید',
          icon: 'error',
          confirmButtonText: 'باشه',
        }

        swalFire(swalData)
        
        return
      }
      this.followUpSteps.push(
        {
          hours: null,
          minutes: null,
          text: null,
          voice: null,
        },
      )
    },
    removeFollowUpStep(index){
      this.followUpSteps.splice(index, 1)

    },
    changeFollowUpStep(index, step) {
      this.followUpSteps[index] = step
    },

    setAllSteps(steps){
      this.followUpSteps = steps
    },
  },
})
  