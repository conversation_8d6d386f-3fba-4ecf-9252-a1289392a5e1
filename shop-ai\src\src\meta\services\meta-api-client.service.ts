import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { lastValueFrom } from 'rxjs';

/**
 * ApiRequestService is responsible for making HTTP requests.
 * This service abstracts the API request logic and can be reused for other API calls.
 */
@Injectable()
export class MetaApiClientService {
  constructor(private readonly httpService: HttpService) {}

  /**
   * Sends a POST request to the given URL with the provided headers, params, and data.
   * @param {string} apiUrl - The API URL to send the POST request to.
   * @param {object} headers - The headers to be included in the request (e.g., Authorization).
   * @param {object} params - The query parameters to be sent with the request.
   * @param {any} data - The data to be sent in the body of the request (optional).
   * @returns {Promise<any>} - The API response.
   */
  public async sendPostRequest(
    apiUrl: string,
    headers: Record<string, string>,
    params: Record<string, any>,
    data: any = null,
  ): Promise<MetaResponse> {
    try {
      const response: AxiosResponse = await lastValueFrom(
        this.httpService.post(apiUrl, data, {
          headers: headers,
          params: params,
        }),
      );
      return response.data;
    } catch (error) {
      console.error(
        'Error sending request to Meta API:',
        error.response?.data || error.message,
      );
      throw new Error('Error sending request to Meta API');
    }
  }
}
