import{_ as At,o as _e,f as Oe,e as De,b as je,a9 as ns,af as ga,y as Ut,a4 as dt,d as Ne,x as Dt,r as Ue,a8 as Kn,w as Nt,cN as Sn,H as nn,n as Ve,c as Ge,z as Ye,F as mt,aK as fn,i as Jt,R as Rs,U as Bs,aN as Er,a as ss,bf as vi,aj as on,v as cn,cO as Qo,c<PERSON> as Ko,aO as Jo,J as el,cQ as tl,a6 as nl,ak as sl,j as rl,al as pt,aM as Je,bs as il,aL as al,cR as ol}from"./index-169996dc.js";import{a as bt,V as Xt}from"./VRow-6c1d54f3.js";import{V as $r}from"./VAlert-fc722507.js";import{a as ll}from"./profile_ph-c1153e1f.js";import{_ as cl}from"./DialogCloseBtn-b32209d9.js";import{E as mn}from"./endpoints-454f23f6.js";function bi(t){return t!==null&&typeof t=="object"&&"constructor"in t&&t.constructor===Object}function Vr(t,e){t===void 0&&(t={}),e===void 0&&(e={}),Object.keys(e).forEach(n=>{typeof t[n]>"u"?t[n]=e[n]:bi(e[n])&&bi(t[n])&&Object.keys(e[n]).length>0&&Vr(t[n],e[n])})}const va={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function gt(){const t=typeof document<"u"?document:{};return Vr(t,va),t}const ul={document:va,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(t){return typeof setTimeout>"u"?(t(),null):setTimeout(t,0)},cancelAnimationFrame(t){typeof setTimeout>"u"||clearTimeout(t)}};function it(){const t=typeof window<"u"?window:{};return Vr(t,ul),t}function dl(t){const e=t;Object.keys(e).forEach(n=>{try{e[n]=null}catch{}try{delete e[n]}catch{}})}function Mn(t,e){return e===void 0&&(e=0),setTimeout(t,e)}function Ot(){return Date.now()}function fl(t){const e=it();let n;return e.getComputedStyle&&(n=e.getComputedStyle(t,null)),!n&&t.currentStyle&&(n=t.currentStyle),n||(n=t.style),n}function Tr(t,e){e===void 0&&(e="x");const n=it();let s,r,i;const l=fl(t);return n.WebKitCSSMatrix?(r=l.transform||l.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map(c=>c.replace(",",".")).join(", ")),i=new n.WebKitCSSMatrix(r==="none"?"":r)):(i=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=i.toString().split(",")),e==="x"&&(n.WebKitCSSMatrix?r=i.m41:s.length===16?r=parseFloat(s[12]):r=parseFloat(s[4])),e==="y"&&(n.WebKitCSSMatrix?r=i.m42:s.length===16?r=parseFloat(s[13]):r=parseFloat(s[5])),r||0}function Un(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"}function pl(t){return typeof window<"u"&&typeof window.HTMLElement<"u"?t instanceof HTMLElement:t&&(t.nodeType===1||t.nodeType===11)}function It(){const t=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const s=n<0||arguments.length<=n?void 0:arguments[n];if(s!=null&&!pl(s)){const r=Object.keys(Object(s)).filter(i=>e.indexOf(i)<0);for(let i=0,l=r.length;i<l;i+=1){const c=r[i],a=Object.getOwnPropertyDescriptor(s,c);a!==void 0&&a.enumerable&&(Un(t[c])&&Un(s[c])?s[c].__swiper__?t[c]=s[c]:It(t[c],s[c]):!Un(t[c])&&Un(s[c])?(t[c]={},s[c].__swiper__?t[c]=s[c]:It(t[c],s[c])):t[c]=s[c])}}}return t}function Zn(t,e,n){t.style.setProperty(e,n)}function ba(t){let{swiper:e,targetPosition:n,side:s}=t;const r=it(),i=-e.translate;let l=null,c;const a=e.params.speed;e.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(e.cssModeFrameID);const u=n>i?"next":"prev",d=(p,_)=>u==="next"&&p>=_||u==="prev"&&p<=_,o=()=>{c=new Date().getTime(),l===null&&(l=c);const p=Math.max(Math.min((c-l)/a,1),0),_=.5-Math.cos(p*Math.PI)/2;let w=i+_*(n-i);if(d(w,n)&&(w=n),e.wrapperEl.scrollTo({[s]:w}),d(w,n)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[s]:w})}),r.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=r.requestAnimationFrame(o)};o()}function Cn(t){return t.querySelector(".swiper-slide-transform")||t.shadowRoot&&t.shadowRoot.querySelector(".swiper-slide-transform")||t}function ht(t,e){return e===void 0&&(e=""),[...t.children].filter(n=>n.matches(e))}function Ht(t,e){e===void 0&&(e=[]);const n=document.createElement(t);return n.classList.add(...Array.isArray(e)?e:[e]),n}function Ss(t){const e=it(),n=gt(),s=t.getBoundingClientRect(),r=n.body,i=t.clientTop||r.clientTop||0,l=t.clientLeft||r.clientLeft||0,c=t===e?e.scrollY:t.scrollTop,a=t===e?e.scrollX:t.scrollLeft;return{top:s.top+c-i,left:s.left+a-l}}function hl(t,e){const n=[];for(;t.previousElementSibling;){const s=t.previousElementSibling;e?s.matches(e)&&n.push(s):n.push(s),t=s}return n}function ml(t,e){const n=[];for(;t.nextElementSibling;){const s=t.nextElementSibling;e?s.matches(e)&&n.push(s):n.push(s),t=s}return n}function un(t,e){return it().getComputedStyle(t,null).getPropertyValue(e)}function Jn(t){let e=t,n;if(e){for(n=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(n+=1);return n}}function En(t,e){const n=[];let s=t.parentElement;for(;s;)e?s.matches(e)&&n.push(s):n.push(s),s=s.parentElement;return n}function Qn(t,e){function n(s){s.target===t&&(e.call(t,s),t.removeEventListener("transitionend",n))}e&&t.addEventListener("transitionend",n)}function Mr(t,e,n){const s=it();return n?t[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom")):t.offsetWidth}let tr;function _l(){const t=it(),e=gt();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in t||t.DocumentTouch&&e instanceof t.DocumentTouch)}}function wa(){return tr||(tr=_l()),tr}let nr;function gl(t){let{userAgent:e}=t===void 0?{}:t;const n=wa(),s=it(),r=s.navigator.platform,i=e||s.navigator.userAgent,l={ios:!1,android:!1},c=s.screen.width,a=s.screen.height,u=i.match(/(Android);?[\s\/]+([\d.]+)?/);let d=i.match(/(iPad).*OS\s([\d_]+)/);const o=i.match(/(iPod)(.*OS\s([\d_]+))?/),p=!d&&i.match(/(iPhone\sOS|iOS)\s([\d_]+)/),_=r==="Win32";let w=r==="MacIntel";const b=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!d&&w&&n.touch&&b.indexOf(`${c}x${a}`)>=0&&(d=i.match(/(Version)\/([\d.]+)/),d||(d=[0,1,"13_0_0"]),w=!1),u&&!_&&(l.os="android",l.android=!0),(d||p||o)&&(l.os="ios",l.ios=!0),l}function vl(t){return t===void 0&&(t={}),nr||(nr=gl(t)),nr}let sr;function bl(){const t=it();let e=!1;function n(){const s=t.navigator.userAgent.toLowerCase();return s.indexOf("safari")>=0&&s.indexOf("chrome")<0&&s.indexOf("android")<0}if(n()){const s=String(t.navigator.userAgent);if(s.includes("Version/")){const[r,i]=s.split("Version/")[1].split(" ")[0].split(".").map(l=>Number(l));e=r<16||r===16&&i<2}}return{isSafari:e||n(),needPerspectiveFix:e,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent)}}function wl(){return sr||(sr=bl()),sr}function yl(t){let{swiper:e,on:n,emit:s}=t;const r=it();let i=null,l=null;const c=()=>{!e||e.destroyed||!e.initialized||(s("beforeResize"),s("resize"))},a=()=>{!e||e.destroyed||!e.initialized||(i=new ResizeObserver(o=>{l=r.requestAnimationFrame(()=>{const{width:p,height:_}=e;let w=p,b=_;o.forEach(g=>{let{contentBoxSize:E,contentRect:T,target:m}=g;m&&m!==e.el||(w=T?T.width:(E[0]||E).inlineSize,b=T?T.height:(E[0]||E).blockSize)}),(w!==p||b!==_)&&c()})}),i.observe(e.el))},u=()=>{l&&r.cancelAnimationFrame(l),i&&i.unobserve&&e.el&&(i.unobserve(e.el),i=null)},d=()=>{!e||e.destroyed||!e.initialized||s("orientationchange")};n("init",()=>{if(e.params.resizeObserver&&typeof r.ResizeObserver<"u"){a();return}r.addEventListener("resize",c),r.addEventListener("orientationchange",d)}),n("destroy",()=>{u(),r.removeEventListener("resize",c),r.removeEventListener("orientationchange",d)})}function Sl(t){let{swiper:e,extendParams:n,on:s,emit:r}=t;const i=[],l=it(),c=function(d,o){o===void 0&&(o={});const p=l.MutationObserver||l.WebkitMutationObserver,_=new p(w=>{if(e.__preventObserver__)return;if(w.length===1){r("observerUpdate",w[0]);return}const b=function(){r("observerUpdate",w[0])};l.requestAnimationFrame?l.requestAnimationFrame(b):l.setTimeout(b,0)});_.observe(d,{attributes:typeof o.attributes>"u"?!0:o.attributes,childList:typeof o.childList>"u"?!0:o.childList,characterData:typeof o.characterData>"u"?!0:o.characterData}),i.push(_)},a=()=>{if(e.params.observer){if(e.params.observeParents){const d=En(e.hostEl);for(let o=0;o<d.length;o+=1)c(d[o])}c(e.hostEl,{childList:e.params.observeSlideChildren}),c(e.wrapperEl,{attributes:!1})}},u=()=>{i.forEach(d=>{d.disconnect()}),i.splice(0,i.length)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",a),s("destroy",u)}var El={on(t,e,n){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;const r=n?"unshift":"push";return t.split(" ").forEach(i=>{s.eventsListeners[i]||(s.eventsListeners[i]=[]),s.eventsListeners[i][r](e)}),s},once(t,e,n){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;function r(){s.off(t,r),r.__emitterProxy&&delete r.__emitterProxy;for(var i=arguments.length,l=new Array(i),c=0;c<i;c++)l[c]=arguments[c];e.apply(s,l)}return r.__emitterProxy=e,s.on(t,r,n)},onAny(t,e){const n=this;if(!n.eventsListeners||n.destroyed||typeof t!="function")return n;const s=e?"unshift":"push";return n.eventsAnyListeners.indexOf(t)<0&&n.eventsAnyListeners[s](t),n},offAny(t){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const n=e.eventsAnyListeners.indexOf(t);return n>=0&&e.eventsAnyListeners.splice(n,1),e},off(t,e){const n=this;return!n.eventsListeners||n.destroyed||!n.eventsListeners||t.split(" ").forEach(s=>{typeof e>"u"?n.eventsListeners[s]=[]:n.eventsListeners[s]&&n.eventsListeners[s].forEach((r,i)=>{(r===e||r.__emitterProxy&&r.__emitterProxy===e)&&n.eventsListeners[s].splice(i,1)})}),n},emit(){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsListeners)return t;let e,n,s;for(var r=arguments.length,i=new Array(r),l=0;l<r;l++)i[l]=arguments[l];return typeof i[0]=="string"||Array.isArray(i[0])?(e=i[0],n=i.slice(1,i.length),s=t):(e=i[0].events,n=i[0].data,s=i[0].context||t),n.unshift(s),(Array.isArray(e)?e:e.split(" ")).forEach(a=>{t.eventsAnyListeners&&t.eventsAnyListeners.length&&t.eventsAnyListeners.forEach(u=>{u.apply(s,[a,...n])}),t.eventsListeners&&t.eventsListeners[a]&&t.eventsListeners[a].forEach(u=>{u.apply(s,n)})}),t}};function Tl(){const t=this;let e,n;const s=t.el;typeof t.params.width<"u"&&t.params.width!==null?e=t.params.width:e=s.clientWidth,typeof t.params.height<"u"&&t.params.height!==null?n=t.params.height:n=s.clientHeight,!(e===0&&t.isHorizontal()||n===0&&t.isVertical())&&(e=e-parseInt(un(s,"padding-left")||0,10)-parseInt(un(s,"padding-right")||0,10),n=n-parseInt(un(s,"padding-top")||0,10)-parseInt(un(s,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(n)&&(n=0),Object.assign(t,{width:e,height:n,size:t.isHorizontal()?e:n}))}function Ml(){const t=this;function e(N){return t.isHorizontal()?N:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[N]}function n(N,L){return parseFloat(N.getPropertyValue(e(L))||0)}const s=t.params,{wrapperEl:r,slidesEl:i,size:l,rtlTranslate:c,wrongRTL:a}=t,u=t.virtual&&s.virtual.enabled,d=u?t.virtual.slides.length:t.slides.length,o=ht(i,`.${t.params.slideClass}, swiper-slide`),p=u?t.virtual.slides.length:o.length;let _=[];const w=[],b=[];let g=s.slidesOffsetBefore;typeof g=="function"&&(g=s.slidesOffsetBefore.call(t));let E=s.slidesOffsetAfter;typeof E=="function"&&(E=s.slidesOffsetAfter.call(t));const T=t.snapGrid.length,m=t.slidesGrid.length;let x=s.spaceBetween,f=-g,A=0,D=0;if(typeof l>"u")return;typeof x=="string"&&x.indexOf("%")>=0?x=parseFloat(x.replace("%",""))/100*l:typeof x=="string"&&(x=parseFloat(x)),t.virtualSize=-x,o.forEach(N=>{c?N.style.marginLeft="":N.style.marginRight="",N.style.marginBottom="",N.style.marginTop=""}),s.centeredSlides&&s.cssMode&&(Zn(r,"--swiper-centered-offset-before",""),Zn(r,"--swiper-centered-offset-after",""));const V=s.grid&&s.grid.rows>1&&t.grid;V&&t.grid.initSlides(p);let O;const q=s.slidesPerView==="auto"&&s.breakpoints&&Object.keys(s.breakpoints).filter(N=>typeof s.breakpoints[N].slidesPerView<"u").length>0;for(let N=0;N<p;N+=1){O=0;let L;if(o[N]&&(L=o[N]),V&&t.grid.updateSlide(N,L,p,e),!(o[N]&&un(L,"display")==="none")){if(s.slidesPerView==="auto"){q&&(o[N].style[e("width")]="");const I=getComputedStyle(L),h=L.style.transform,k=L.style.webkitTransform;if(h&&(L.style.transform="none"),k&&(L.style.webkitTransform="none"),s.roundLengths)O=t.isHorizontal()?Mr(L,"width",!0):Mr(L,"height",!0);else{const H=n(I,"width"),R=n(I,"padding-left"),F=n(I,"padding-right"),Y=n(I,"margin-left"),X=n(I,"margin-right"),M=I.getPropertyValue("box-sizing");if(M&&M==="border-box")O=H+Y+X;else{const{clientWidth:y,offsetWidth:v}=L;O=H+R+F+Y+X+(v-y)}}h&&(L.style.transform=h),k&&(L.style.webkitTransform=k),s.roundLengths&&(O=Math.floor(O))}else O=(l-(s.slidesPerView-1)*x)/s.slidesPerView,s.roundLengths&&(O=Math.floor(O)),o[N]&&(o[N].style[e("width")]=`${O}px`);o[N]&&(o[N].swiperSlideSize=O),b.push(O),s.centeredSlides?(f=f+O/2+A/2+x,A===0&&N!==0&&(f=f-l/2-x),N===0&&(f=f-l/2-x),Math.abs(f)<1/1e3&&(f=0),s.roundLengths&&(f=Math.floor(f)),D%s.slidesPerGroup===0&&_.push(f),w.push(f)):(s.roundLengths&&(f=Math.floor(f)),(D-Math.min(t.params.slidesPerGroupSkip,D))%t.params.slidesPerGroup===0&&_.push(f),w.push(f),f=f+O+x),t.virtualSize+=O+x,A=O,D+=1}}if(t.virtualSize=Math.max(t.virtualSize,l)+E,c&&a&&(s.effect==="slide"||s.effect==="coverflow")&&(r.style.width=`${t.virtualSize+x}px`),s.setWrapperSize&&(r.style[e("width")]=`${t.virtualSize+x}px`),V&&t.grid.updateWrapperSize(O,_,e),!s.centeredSlides){const N=[];for(let L=0;L<_.length;L+=1){let I=_[L];s.roundLengths&&(I=Math.floor(I)),_[L]<=t.virtualSize-l&&N.push(I)}_=N,Math.floor(t.virtualSize-l)-Math.floor(_[_.length-1])>1&&_.push(t.virtualSize-l)}if(u&&s.loop){const N=b[0]+x;if(s.slidesPerGroup>1){const L=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/s.slidesPerGroup),I=N*s.slidesPerGroup;for(let h=0;h<L;h+=1)_.push(_[_.length-1]+I)}for(let L=0;L<t.virtual.slidesBefore+t.virtual.slidesAfter;L+=1)s.slidesPerGroup===1&&_.push(_[_.length-1]+N),w.push(w[w.length-1]+N),t.virtualSize+=N}if(_.length===0&&(_=[0]),x!==0){const N=t.isHorizontal()&&c?"marginLeft":e("marginRight");o.filter((L,I)=>!s.cssMode||s.loop?!0:I!==o.length-1).forEach(L=>{L.style[N]=`${x}px`})}if(s.centeredSlides&&s.centeredSlidesBounds){let N=0;b.forEach(I=>{N+=I+(x||0)}),N-=x;const L=N-l;_=_.map(I=>I<=0?-g:I>L?L+E:I)}if(s.centerInsufficientSlides){let N=0;if(b.forEach(L=>{N+=L+(x||0)}),N-=x,N<l){const L=(l-N)/2;_.forEach((I,h)=>{_[h]=I-L}),w.forEach((I,h)=>{w[h]=I+L})}}if(Object.assign(t,{slides:o,snapGrid:_,slidesGrid:w,slidesSizesGrid:b}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){Zn(r,"--swiper-centered-offset-before",`${-_[0]}px`),Zn(r,"--swiper-centered-offset-after",`${t.size/2-b[b.length-1]/2}px`);const N=-t.snapGrid[0],L=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(I=>I+N),t.slidesGrid=t.slidesGrid.map(I=>I+L)}if(p!==d&&t.emit("slidesLengthChange"),_.length!==T&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),w.length!==m&&t.emit("slidesGridLengthChange"),s.watchSlidesProgress&&t.updateSlidesOffset(),!u&&!s.cssMode&&(s.effect==="slide"||s.effect==="fade")){const N=`${s.containerModifierClass}backface-hidden`,L=t.el.classList.contains(N);p<=s.maxBackfaceHiddenSlides?L||t.el.classList.add(N):L&&t.el.classList.remove(N)}}function Al(t){const e=this,n=[],s=e.virtual&&e.params.virtual.enabled;let r=0,i;typeof t=="number"?e.setTransition(t):t===!0&&e.setTransition(e.params.speed);const l=c=>s?e.slides[e.getSlideIndexByData(c)]:e.slides[c];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(c=>{n.push(c)});else for(i=0;i<Math.ceil(e.params.slidesPerView);i+=1){const c=e.activeIndex+i;if(c>e.slides.length&&!s)break;n.push(l(c))}else n.push(l(e.activeIndex));for(i=0;i<n.length;i+=1)if(typeof n[i]<"u"){const c=n[i].offsetHeight;r=c>r?c:r}(r||r===0)&&(e.wrapperEl.style.height=`${r}px`)}function xl(){const t=this,e=t.slides,n=t.isElement?t.isHorizontal()?t.wrapperEl.offsetLeft:t.wrapperEl.offsetTop:0;for(let s=0;s<e.length;s+=1)e[s].swiperSlideOffset=(t.isHorizontal()?e[s].offsetLeft:e[s].offsetTop)-n-t.cssOverflowAdjustment()}function Cl(t){t===void 0&&(t=this&&this.translate||0);const e=this,n=e.params,{slides:s,rtlTranslate:r,snapGrid:i}=e;if(s.length===0)return;typeof s[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let l=-t;r&&(l=t),s.forEach(a=>{a.classList.remove(n.slideVisibleClass)}),e.visibleSlidesIndexes=[],e.visibleSlides=[];let c=n.spaceBetween;typeof c=="string"&&c.indexOf("%")>=0?c=parseFloat(c.replace("%",""))/100*e.size:typeof c=="string"&&(c=parseFloat(c));for(let a=0;a<s.length;a+=1){const u=s[a];let d=u.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(d-=s[0].swiperSlideOffset);const o=(l+(n.centeredSlides?e.minTranslate():0)-d)/(u.swiperSlideSize+c),p=(l-i[0]+(n.centeredSlides?e.minTranslate():0)-d)/(u.swiperSlideSize+c),_=-(l-d),w=_+e.slidesSizesGrid[a];(_>=0&&_<e.size-1||w>1&&w<=e.size||_<=0&&w>=e.size)&&(e.visibleSlides.push(u),e.visibleSlidesIndexes.push(a),s[a].classList.add(n.slideVisibleClass)),u.progress=r?-o:o,u.originalProgress=r?-p:p}}function kl(t){const e=this;if(typeof t>"u"){const d=e.rtlTranslate?-1:1;t=e&&e.translate&&e.translate*d||0}const n=e.params,s=e.maxTranslate()-e.minTranslate();let{progress:r,isBeginning:i,isEnd:l,progressLoop:c}=e;const a=i,u=l;if(s===0)r=0,i=!0,l=!0;else{r=(t-e.minTranslate())/s;const d=Math.abs(t-e.minTranslate())<1,o=Math.abs(t-e.maxTranslate())<1;i=d||r<=0,l=o||r>=1,d&&(r=0),o&&(r=1)}if(n.loop){const d=e.getSlideIndexByData(0),o=e.getSlideIndexByData(e.slides.length-1),p=e.slidesGrid[d],_=e.slidesGrid[o],w=e.slidesGrid[e.slidesGrid.length-1],b=Math.abs(t);b>=p?c=(b-p)/w:c=(b+w-_)/w,c>1&&(c-=1)}Object.assign(e,{progress:r,progressLoop:c,isBeginning:i,isEnd:l}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&e.updateSlidesProgress(t),i&&!a&&e.emit("reachBeginning toEdge"),l&&!u&&e.emit("reachEnd toEdge"),(a&&!i||u&&!l)&&e.emit("fromEdge"),e.emit("progress",r)}function Pl(){const t=this,{slides:e,params:n,slidesEl:s,activeIndex:r}=t,i=t.virtual&&n.virtual.enabled,l=a=>ht(s,`.${n.slideClass}${a}, swiper-slide${a}`)[0];e.forEach(a=>{a.classList.remove(n.slideActiveClass,n.slideNextClass,n.slidePrevClass)});let c;if(i)if(n.loop){let a=r-t.virtual.slidesBefore;a<0&&(a=t.virtual.slides.length+a),a>=t.virtual.slides.length&&(a-=t.virtual.slides.length),c=l(`[data-swiper-slide-index="${a}"]`)}else c=l(`[data-swiper-slide-index="${r}"]`);else c=e[r];if(c){c.classList.add(n.slideActiveClass);let a=ml(c,`.${n.slideClass}, swiper-slide`)[0];n.loop&&!a&&(a=e[0]),a&&a.classList.add(n.slideNextClass);let u=hl(c,`.${n.slideClass}, swiper-slide`)[0];n.loop&&!u===0&&(u=e[e.length-1]),u&&u.classList.add(n.slidePrevClass)}t.emitSlidesClasses()}const bs=(t,e)=>{if(!t||t.destroyed||!t.params)return;const n=()=>t.isElement?"swiper-slide":`.${t.params.slideClass}`,s=e.closest(n());if(s){let r=s.querySelector(`.${t.params.lazyPreloaderClass}`);!r&&t.isElement&&(s.shadowRoot?r=s.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(r=s.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`),r&&r.remove())})),r&&r.remove()}},rr=(t,e)=>{if(!t.slides[e])return;const n=t.slides[e].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},Ar=t=>{if(!t||t.destroyed||!t.params)return;let e=t.params.lazyPreloadPrevNext;const n=t.slides.length;if(!n||!e||e<0)return;e=Math.min(e,n);const s=t.params.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(t.params.slidesPerView),r=t.activeIndex;if(t.params.grid&&t.params.grid.rows>1){const l=r,c=[l-e];c.push(...Array.from({length:e}).map((a,u)=>l+s+u)),t.slides.forEach((a,u)=>{c.includes(a.column)&&rr(t,u)});return}const i=r+s-1;if(t.params.rewind||t.params.loop)for(let l=r-e;l<=i+e;l+=1){const c=(l%n+n)%n;(c<r||c>i)&&rr(t,c)}else for(let l=Math.max(r-e,0);l<=Math.min(i+e,n-1);l+=1)l!==r&&(l>i||l<r)&&rr(t,l)};function Il(t){const{slidesGrid:e,params:n}=t,s=t.rtlTranslate?t.translate:-t.translate;let r;for(let i=0;i<e.length;i+=1)typeof e[i+1]<"u"?s>=e[i]&&s<e[i+1]-(e[i+1]-e[i])/2?r=i:s>=e[i]&&s<e[i+1]&&(r=i+1):s>=e[i]&&(r=i);return n.normalizeSlideIndex&&(r<0||typeof r>"u")&&(r=0),r}function Rl(t){const e=this,n=e.rtlTranslate?e.translate:-e.translate,{snapGrid:s,params:r,activeIndex:i,realIndex:l,snapIndex:c}=e;let a=t,u;const d=p=>{let _=p-e.virtual.slidesBefore;return _<0&&(_=e.virtual.slides.length+_),_>=e.virtual.slides.length&&(_-=e.virtual.slides.length),_};if(typeof a>"u"&&(a=Il(e)),s.indexOf(n)>=0)u=s.indexOf(n);else{const p=Math.min(r.slidesPerGroupSkip,a);u=p+Math.floor((a-p)/r.slidesPerGroup)}if(u>=s.length&&(u=s.length-1),a===i){u!==c&&(e.snapIndex=u,e.emit("snapIndexChange")),e.params.loop&&e.virtual&&e.params.virtual.enabled&&(e.realIndex=d(a));return}let o;e.virtual&&r.virtual.enabled&&r.loop?o=d(a):e.slides[a]?o=parseInt(e.slides[a].getAttribute("data-swiper-slide-index")||a,10):o=a,Object.assign(e,{previousSnapIndex:c,snapIndex:u,previousRealIndex:l,realIndex:o,previousIndex:i,activeIndex:a}),e.initialized&&Ar(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(l!==o&&e.emit("realIndexChange"),e.emit("slideChange"))}function Bl(t,e){const n=this,s=n.params;let r=t.closest(`.${s.slideClass}, swiper-slide`);!r&&n.isElement&&e&&e.length>1&&e.includes(t)&&[...e.slice(e.indexOf(t)+1,e.length)].forEach(c=>{!r&&c.matches&&c.matches(`.${s.slideClass}, swiper-slide`)&&(r=c)});let i=!1,l;if(r){for(let c=0;c<n.slides.length;c+=1)if(n.slides[c]===r){i=!0,l=c;break}}if(r&&i)n.clickedSlide=r,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=l;else{n.clickedSlide=void 0,n.clickedIndex=void 0;return}s.slideToClickedSlide&&n.clickedIndex!==void 0&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}var Ll={updateSize:Tl,updateSlides:Ml,updateAutoHeight:Al,updateSlidesOffset:xl,updateSlidesProgress:Cl,updateProgress:kl,updateSlidesClasses:Pl,updateActiveIndex:Rl,updateClickedSlide:Bl};function Ol(t){t===void 0&&(t=this.isHorizontal()?"x":"y");const e=this,{params:n,rtlTranslate:s,translate:r,wrapperEl:i}=e;if(n.virtualTranslate)return s?-r:r;if(n.cssMode)return r;let l=Tr(i,t);return l+=e.cssOverflowAdjustment(),s&&(l=-l),l||0}function Dl(t,e){const n=this,{rtlTranslate:s,params:r,wrapperEl:i,progress:l}=n;let c=0,a=0;const u=0;n.isHorizontal()?c=s?-t:t:a=t,r.roundLengths&&(c=Math.floor(c),a=Math.floor(a)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?c:a,r.cssMode?i[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-c:-a:r.virtualTranslate||(n.isHorizontal()?c-=n.cssOverflowAdjustment():a-=n.cssOverflowAdjustment(),i.style.transform=`translate3d(${c}px, ${a}px, ${u}px)`);let d;const o=n.maxTranslate()-n.minTranslate();o===0?d=0:d=(t-n.minTranslate())/o,d!==l&&n.updateProgress(t),n.emit("setTranslate",n.translate,e)}function Nl(){return-this.snapGrid[0]}function zl(){return-this.snapGrid[this.snapGrid.length-1]}function $l(t,e,n,s,r){t===void 0&&(t=0),e===void 0&&(e=this.params.speed),n===void 0&&(n=!0),s===void 0&&(s=!0);const i=this,{params:l,wrapperEl:c}=i;if(i.animating&&l.preventInteractionOnTransition)return!1;const a=i.minTranslate(),u=i.maxTranslate();let d;if(s&&t>a?d=a:s&&t<u?d=u:d=t,i.updateProgress(d),l.cssMode){const o=i.isHorizontal();if(e===0)c[o?"scrollLeft":"scrollTop"]=-d;else{if(!i.support.smoothScroll)return ba({swiper:i,targetPosition:-d,side:o?"left":"top"}),!0;c.scrollTo({[o?"left":"top"]:-d,behavior:"smooth"})}return!0}return e===0?(i.setTransition(0),i.setTranslate(d),n&&(i.emit("beforeTransitionStart",e,r),i.emit("transitionEnd"))):(i.setTransition(e),i.setTranslate(d),n&&(i.emit("beforeTransitionStart",e,r),i.emit("transitionStart")),i.animating||(i.animating=!0,i.onTranslateToWrapperTransitionEnd||(i.onTranslateToWrapperTransitionEnd=function(p){!i||i.destroyed||p.target===this&&(i.wrapperEl.removeEventListener("transitionend",i.onTranslateToWrapperTransitionEnd),i.onTranslateToWrapperTransitionEnd=null,delete i.onTranslateToWrapperTransitionEnd,n&&i.emit("transitionEnd"))}),i.wrapperEl.addEventListener("transitionend",i.onTranslateToWrapperTransitionEnd))),!0}var Vl={getTranslate:Ol,setTranslate:Dl,minTranslate:Nl,maxTranslate:zl,translateTo:$l};function Hl(t,e){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration=`${t}ms`,n.wrapperEl.style.transitionDelay=t===0?"0ms":""),n.emit("setTransition",t,e)}function ya(t){let{swiper:e,runCallbacks:n,direction:s,step:r}=t;const{activeIndex:i,previousIndex:l}=e;let c=s;if(c||(i>l?c="next":i<l?c="prev":c="reset"),e.emit(`transition${r}`),n&&i!==l){if(c==="reset"){e.emit(`slideResetTransition${r}`);return}e.emit(`slideChangeTransition${r}`),c==="next"?e.emit(`slideNextTransition${r}`):e.emit(`slidePrevTransition${r}`)}}function Xl(t,e){t===void 0&&(t=!0);const n=this,{params:s}=n;s.cssMode||(s.autoHeight&&n.updateAutoHeight(),ya({swiper:n,runCallbacks:t,direction:e,step:"Start"}))}function Fl(t,e){t===void 0&&(t=!0);const n=this,{params:s}=n;n.animating=!1,!s.cssMode&&(n.setTransition(0),ya({swiper:n,runCallbacks:t,direction:e,step:"End"}))}var Yl={setTransition:Hl,transitionStart:Xl,transitionEnd:Fl};function Wl(t,e,n,s,r){t===void 0&&(t=0),e===void 0&&(e=this.params.speed),n===void 0&&(n=!0),typeof t=="string"&&(t=parseInt(t,10));const i=this;let l=t;l<0&&(l=0);const{params:c,snapGrid:a,slidesGrid:u,previousIndex:d,activeIndex:o,rtlTranslate:p,wrapperEl:_,enabled:w}=i;if(i.animating&&c.preventInteractionOnTransition||!w&&!s&&!r)return!1;const b=Math.min(i.params.slidesPerGroupSkip,l);let g=b+Math.floor((l-b)/i.params.slidesPerGroup);g>=a.length&&(g=a.length-1);const E=-a[g];if(c.normalizeSlideIndex)for(let m=0;m<u.length;m+=1){const x=-Math.floor(E*100),f=Math.floor(u[m]*100),A=Math.floor(u[m+1]*100);typeof u[m+1]<"u"?x>=f&&x<A-(A-f)/2?l=m:x>=f&&x<A&&(l=m+1):x>=f&&(l=m)}if(i.initialized&&l!==o&&(!i.allowSlideNext&&(p?E>i.translate&&E>i.minTranslate():E<i.translate&&E<i.minTranslate())||!i.allowSlidePrev&&E>i.translate&&E>i.maxTranslate()&&(o||0)!==l))return!1;l!==(d||0)&&n&&i.emit("beforeSlideChangeStart"),i.updateProgress(E);let T;if(l>o?T="next":l<o?T="prev":T="reset",p&&-E===i.translate||!p&&E===i.translate)return i.updateActiveIndex(l),c.autoHeight&&i.updateAutoHeight(),i.updateSlidesClasses(),c.effect!=="slide"&&i.setTranslate(E),T!=="reset"&&(i.transitionStart(n,T),i.transitionEnd(n,T)),!1;if(c.cssMode){const m=i.isHorizontal(),x=p?E:-E;if(e===0){const f=i.virtual&&i.params.virtual.enabled;f&&(i.wrapperEl.style.scrollSnapType="none",i._immediateVirtual=!0),f&&!i._cssModeVirtualInitialSet&&i.params.initialSlide>0?(i._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{_[m?"scrollLeft":"scrollTop"]=x})):_[m?"scrollLeft":"scrollTop"]=x,f&&requestAnimationFrame(()=>{i.wrapperEl.style.scrollSnapType="",i._immediateVirtual=!1})}else{if(!i.support.smoothScroll)return ba({swiper:i,targetPosition:x,side:m?"left":"top"}),!0;_.scrollTo({[m?"left":"top"]:x,behavior:"smooth"})}return!0}return i.setTransition(e),i.setTranslate(E),i.updateActiveIndex(l),i.updateSlidesClasses(),i.emit("beforeTransitionStart",e,s),i.transitionStart(n,T),e===0?i.transitionEnd(n,T):i.animating||(i.animating=!0,i.onSlideToWrapperTransitionEnd||(i.onSlideToWrapperTransitionEnd=function(x){!i||i.destroyed||x.target===this&&(i.wrapperEl.removeEventListener("transitionend",i.onSlideToWrapperTransitionEnd),i.onSlideToWrapperTransitionEnd=null,delete i.onSlideToWrapperTransitionEnd,i.transitionEnd(n,T))}),i.wrapperEl.addEventListener("transitionend",i.onSlideToWrapperTransitionEnd)),!0}function Gl(t,e,n,s){t===void 0&&(t=0),e===void 0&&(e=this.params.speed),n===void 0&&(n=!0),typeof t=="string"&&(t=parseInt(t,10));const r=this;let i=t;return r.params.loop&&(r.virtual&&r.params.virtual.enabled?i=i+r.virtual.slidesBefore:i=r.getSlideIndexByData(i)),r.slideTo(i,e,n,s)}function jl(t,e,n){t===void 0&&(t=this.params.speed),e===void 0&&(e=!0);const s=this,{enabled:r,params:i,animating:l}=s;if(!r)return s;let c=i.slidesPerGroup;i.slidesPerView==="auto"&&i.slidesPerGroup===1&&i.slidesPerGroupAuto&&(c=Math.max(s.slidesPerViewDynamic("current",!0),1));const a=s.activeIndex<i.slidesPerGroupSkip?1:c,u=s.virtual&&i.virtual.enabled;if(i.loop){if(l&&!u&&i.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&i.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+a,t,e,n)}),!0}return i.rewind&&s.isEnd?s.slideTo(0,t,e,n):s.slideTo(s.activeIndex+a,t,e,n)}function ql(t,e,n){t===void 0&&(t=this.params.speed),e===void 0&&(e=!0);const s=this,{params:r,snapGrid:i,slidesGrid:l,rtlTranslate:c,enabled:a,animating:u}=s;if(!a)return s;const d=s.virtual&&r.virtual.enabled;if(r.loop){if(u&&!d&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}const o=c?s.translate:-s.translate;function p(E){return E<0?-Math.floor(Math.abs(E)):Math.floor(E)}const _=p(o),w=i.map(E=>p(E));let b=i[w.indexOf(_)-1];if(typeof b>"u"&&r.cssMode){let E;i.forEach((T,m)=>{_>=T&&(E=m)}),typeof E<"u"&&(b=i[E>0?E-1:E])}let g=0;if(typeof b<"u"&&(g=l.indexOf(b),g<0&&(g=s.activeIndex-1),r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(g=g-s.slidesPerViewDynamic("previous",!0)+1,g=Math.max(g,0))),r.rewind&&s.isBeginning){const E=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(E,t,e,n)}else if(r.loop&&s.activeIndex===0&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(g,t,e,n)}),!0;return s.slideTo(g,t,e,n)}function Ul(t,e,n){t===void 0&&(t=this.params.speed),e===void 0&&(e=!0);const s=this;return s.slideTo(s.activeIndex,t,e,n)}function Zl(t,e,n,s){t===void 0&&(t=this.params.speed),e===void 0&&(e=!0),s===void 0&&(s=.5);const r=this;let i=r.activeIndex;const l=Math.min(r.params.slidesPerGroupSkip,i),c=l+Math.floor((i-l)/r.params.slidesPerGroup),a=r.rtlTranslate?r.translate:-r.translate;if(a>=r.snapGrid[c]){const u=r.snapGrid[c],d=r.snapGrid[c+1];a-u>(d-u)*s&&(i+=r.params.slidesPerGroup)}else{const u=r.snapGrid[c-1],d=r.snapGrid[c];a-u<=(d-u)*s&&(i-=r.params.slidesPerGroup)}return i=Math.max(i,0),i=Math.min(i,r.slidesGrid.length-1),r.slideTo(i,t,e,n)}function Ql(){const t=this,{params:e,slidesEl:n}=t,s=e.slidesPerView==="auto"?t.slidesPerViewDynamic():e.slidesPerView;let r=t.clickedIndex,i;const l=t.isElement?"swiper-slide":`.${e.slideClass}`;if(e.loop){if(t.animating)return;i=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?r<t.loopedSlides-s/2||r>t.slides.length-t.loopedSlides+s/2?(t.loopFix(),r=t.getSlideIndex(ht(n,`${l}[data-swiper-slide-index="${i}"]`)[0]),Mn(()=>{t.slideTo(r)})):t.slideTo(r):r>t.slides.length-s?(t.loopFix(),r=t.getSlideIndex(ht(n,`${l}[data-swiper-slide-index="${i}"]`)[0]),Mn(()=>{t.slideTo(r)})):t.slideTo(r)}else t.slideTo(r)}var Kl={slideTo:Wl,slideToLoop:Gl,slideNext:jl,slidePrev:ql,slideReset:Ul,slideToClosest:Zl,slideToClickedSlide:Ql};function Jl(t){const e=this,{params:n,slidesEl:s}=e;if(!n.loop||e.virtual&&e.params.virtual.enabled)return;ht(s,`.${n.slideClass}, swiper-slide`).forEach((i,l)=>{i.setAttribute("data-swiper-slide-index",l)}),e.loopFix({slideRealIndex:t,direction:n.centeredSlides?void 0:"next"})}function ec(t){let{slideRealIndex:e,slideTo:n=!0,direction:s,setTranslate:r,activeSlideIndex:i,byController:l,byMousewheel:c}=t===void 0?{}:t;const a=this;if(!a.params.loop)return;a.emit("beforeLoopFix");const{slides:u,allowSlidePrev:d,allowSlideNext:o,slidesEl:p,params:_}=a;if(a.allowSlidePrev=!0,a.allowSlideNext=!0,a.virtual&&_.virtual.enabled){n&&(!_.centeredSlides&&a.snapIndex===0?a.slideTo(a.virtual.slides.length,0,!1,!0):_.centeredSlides&&a.snapIndex<_.slidesPerView?a.slideTo(a.virtual.slides.length+a.snapIndex,0,!1,!0):a.snapIndex===a.snapGrid.length-1&&a.slideTo(a.virtual.slidesBefore,0,!1,!0)),a.allowSlidePrev=d,a.allowSlideNext=o,a.emit("loopFix");return}const w=_.slidesPerView==="auto"?a.slidesPerViewDynamic():Math.ceil(parseFloat(_.slidesPerView,10));let b=_.loopedSlides||w;b%_.slidesPerGroup!==0&&(b+=_.slidesPerGroup-b%_.slidesPerGroup),a.loopedSlides=b;const g=[],E=[];let T=a.activeIndex;typeof i>"u"?i=a.getSlideIndex(a.slides.filter(D=>D.classList.contains(_.slideActiveClass))[0]):T=i;const m=s==="next"||!s,x=s==="prev"||!s;let f=0,A=0;if(i<b){f=Math.max(b-i,_.slidesPerGroup);for(let D=0;D<b-i;D+=1){const V=D-Math.floor(D/u.length)*u.length;g.push(u.length-V-1)}}else if(i>a.slides.length-b*2){A=Math.max(i-(a.slides.length-b*2),_.slidesPerGroup);for(let D=0;D<A;D+=1){const V=D-Math.floor(D/u.length)*u.length;E.push(V)}}if(x&&g.forEach(D=>{a.slides[D].swiperLoopMoveDOM=!0,p.prepend(a.slides[D]),a.slides[D].swiperLoopMoveDOM=!1}),m&&E.forEach(D=>{a.slides[D].swiperLoopMoveDOM=!0,p.append(a.slides[D]),a.slides[D].swiperLoopMoveDOM=!1}),a.recalcSlides(),_.slidesPerView==="auto"&&a.updateSlides(),_.watchSlidesProgress&&a.updateSlidesOffset(),n){if(g.length>0&&x)if(typeof e>"u"){const D=a.slidesGrid[T],O=a.slidesGrid[T+f]-D;c?a.setTranslate(a.translate-O):(a.slideTo(T+f,0,!1,!0),r&&(a.touches[a.isHorizontal()?"startX":"startY"]+=O,a.touchEventsData.currentTranslate=a.translate))}else r&&(a.slideToLoop(e,0,!1,!0),a.touchEventsData.currentTranslate=a.translate);else if(E.length>0&&m)if(typeof e>"u"){const D=a.slidesGrid[T],O=a.slidesGrid[T-A]-D;c?a.setTranslate(a.translate-O):(a.slideTo(T-A,0,!1,!0),r&&(a.touches[a.isHorizontal()?"startX":"startY"]+=O,a.touchEventsData.currentTranslate=a.translate))}else a.slideToLoop(e,0,!1,!0)}if(a.allowSlidePrev=d,a.allowSlideNext=o,a.controller&&a.controller.control&&!l){const D={slideRealIndex:e,direction:s,setTranslate:r,activeSlideIndex:i,byController:!0};Array.isArray(a.controller.control)?a.controller.control.forEach(V=>{!V.destroyed&&V.params.loop&&V.loopFix({...D,slideTo:V.params.slidesPerView===_.slidesPerView?n:!1})}):a.controller.control instanceof a.constructor&&a.controller.control.params.loop&&a.controller.control.loopFix({...D,slideTo:a.controller.control.params.slidesPerView===_.slidesPerView?n:!1})}a.emit("loopFix")}function tc(){const t=this,{params:e,slidesEl:n}=t;if(!e.loop||t.virtual&&t.params.virtual.enabled)return;t.recalcSlides();const s=[];t.slides.forEach(r=>{const i=typeof r.swiperSlideIndex>"u"?r.getAttribute("data-swiper-slide-index")*1:r.swiperSlideIndex;s[i]=r}),t.slides.forEach(r=>{r.removeAttribute("data-swiper-slide-index")}),s.forEach(r=>{n.append(r)}),t.recalcSlides(),t.slideTo(t.realIndex,0)}var nc={loopCreate:Jl,loopFix:ec,loopDestroy:tc};function sc(t){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const n=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=t?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function rc(){const t=this;t.params.watchOverflow&&t.isLocked||t.params.cssMode||(t.isElement&&(t.__preventObserver__=!0),t[t.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1}))}var ic={setGrabCursor:sc,unsetGrabCursor:rc};function ac(t,e){e===void 0&&(e=this);function n(s){if(!s||s===gt()||s===it())return null;s.assignedSlot&&(s=s.assignedSlot);const r=s.closest(t);return!r&&!s.getRootNode?null:r||n(s.getRootNode().host)}return n(e)}function oc(t){const e=this,n=gt(),s=it(),r=e.touchEventsData;r.evCache.push(t);const{params:i,touches:l,enabled:c}=e;if(!c||!i.simulateTouch&&t.pointerType==="mouse"||e.animating&&i.preventInteractionOnTransition)return;!e.animating&&i.cssMode&&i.loop&&e.loopFix();let a=t;a.originalEvent&&(a=a.originalEvent);let u=a.target;if(i.touchEventsTarget==="wrapper"&&!e.wrapperEl.contains(u)||"which"in a&&a.which===3||"button"in a&&a.button>0||r.isTouched&&r.isMoved)return;const d=!!i.noSwipingClass&&i.noSwipingClass!=="",o=t.composedPath?t.composedPath():t.path;d&&a.target&&a.target.shadowRoot&&o&&(u=o[0]);const p=i.noSwipingSelector?i.noSwipingSelector:`.${i.noSwipingClass}`,_=!!(a.target&&a.target.shadowRoot);if(i.noSwiping&&(_?ac(p,u):u.closest(p))){e.allowClick=!0;return}if(i.swipeHandler&&!u.closest(i.swipeHandler))return;l.currentX=a.pageX,l.currentY=a.pageY;const w=l.currentX,b=l.currentY,g=i.edgeSwipeDetection||i.iOSEdgeSwipeDetection,E=i.edgeSwipeThreshold||i.iOSEdgeSwipeThreshold;if(g&&(w<=E||w>=s.innerWidth-E))if(g==="prevent")t.preventDefault();else return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=w,l.startY=b,r.touchStartTime=Ot(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,i.threshold>0&&(r.allowThresholdMove=!1);let T=!0;u.matches(r.focusableElements)&&(T=!1,u.nodeName==="SELECT"&&(r.isTouched=!1)),n.activeElement&&n.activeElement.matches(r.focusableElements)&&n.activeElement!==u&&n.activeElement.blur();const m=T&&e.allowTouchMove&&i.touchStartPreventDefault;(i.touchStartForcePreventDefault||m)&&!u.isContentEditable&&a.preventDefault(),i.freeMode&&i.freeMode.enabled&&e.freeMode&&e.animating&&!i.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",a)}function lc(t){const e=gt(),n=this,s=n.touchEventsData,{params:r,touches:i,rtlTranslate:l,enabled:c}=n;if(!c||!r.simulateTouch&&t.pointerType==="mouse")return;let a=t;if(a.originalEvent&&(a=a.originalEvent),!s.isTouched){s.startMoving&&s.isScrolling&&n.emit("touchMoveOpposite",a);return}const u=s.evCache.findIndex(D=>D.pointerId===a.pointerId);u>=0&&(s.evCache[u]=a);const d=s.evCache.length>1?s.evCache[0]:a,o=d.pageX,p=d.pageY;if(a.preventedByNestedSwiper){i.startX=o,i.startY=p;return}if(!n.allowTouchMove){a.target.matches(s.focusableElements)||(n.allowClick=!1),s.isTouched&&(Object.assign(i,{startX:o,startY:p,prevX:n.touches.currentX,prevY:n.touches.currentY,currentX:o,currentY:p}),s.touchStartTime=Ot());return}if(r.touchReleaseOnEdges&&!r.loop){if(n.isVertical()){if(p<i.startY&&n.translate<=n.maxTranslate()||p>i.startY&&n.translate>=n.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else if(o<i.startX&&n.translate<=n.maxTranslate()||o>i.startX&&n.translate>=n.minTranslate())return}if(e.activeElement&&a.target===e.activeElement&&a.target.matches(s.focusableElements)){s.isMoved=!0,n.allowClick=!1;return}if(s.allowTouchCallbacks&&n.emit("touchMove",a),a.targetTouches&&a.targetTouches.length>1)return;i.currentX=o,i.currentY=p;const _=i.currentX-i.startX,w=i.currentY-i.startY;if(n.params.threshold&&Math.sqrt(_**2+w**2)<n.params.threshold)return;if(typeof s.isScrolling>"u"){let D;n.isHorizontal()&&i.currentY===i.startY||n.isVertical()&&i.currentX===i.startX?s.isScrolling=!1:_*_+w*w>=25&&(D=Math.atan2(Math.abs(w),Math.abs(_))*180/Math.PI,s.isScrolling=n.isHorizontal()?D>r.touchAngle:90-D>r.touchAngle)}if(s.isScrolling&&n.emit("touchMoveOpposite",a),typeof s.startMoving>"u"&&(i.currentX!==i.startX||i.currentY!==i.startY)&&(s.startMoving=!0),s.isScrolling||n.zoom&&n.params.zoom&&n.params.zoom.enabled&&s.evCache.length>1){s.isTouched=!1;return}if(!s.startMoving)return;n.allowClick=!1,!r.cssMode&&a.cancelable&&a.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&a.stopPropagation();let b=n.isHorizontal()?_:w,g=n.isHorizontal()?i.currentX-i.previousX:i.currentY-i.previousY;r.oneWayMovement&&(b=Math.abs(b)*(l?1:-1),g=Math.abs(g)*(l?1:-1)),i.diff=b,b*=r.touchRatio,l&&(b=-b,g=-g);const E=n.touchesDirection;n.swipeDirection=b>0?"prev":"next",n.touchesDirection=g>0?"prev":"next";const T=n.params.loop&&!r.cssMode,m=n.swipeDirection==="next"&&n.allowSlideNext||n.swipeDirection==="prev"&&n.allowSlidePrev;if(!s.isMoved){if(T&&m&&n.loopFix({direction:n.swipeDirection}),s.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const D=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});n.wrapperEl.dispatchEvent(D)}s.allowMomentumBounce=!1,r.grabCursor&&(n.allowSlideNext===!0||n.allowSlidePrev===!0)&&n.setGrabCursor(!0),n.emit("sliderFirstMove",a)}let x;s.isMoved&&E!==n.touchesDirection&&T&&m&&Math.abs(b)>=1&&(n.loopFix({direction:n.swipeDirection,setTranslate:!0}),x=!0),n.emit("sliderMove",a),s.isMoved=!0,s.currentTranslate=b+s.startTranslate;let f=!0,A=r.resistanceRatio;if(r.touchReleaseOnEdges&&(A=0),b>0?(T&&m&&!x&&s.currentTranslate>(r.centeredSlides?n.minTranslate()-n.size/2:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>n.minTranslate()&&(f=!1,r.resistance&&(s.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+s.startTranslate+b)**A))):b<0&&(T&&m&&!x&&s.currentTranslate<(r.centeredSlides?n.maxTranslate()+n.size/2:n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-(r.slidesPerView==="auto"?n.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),s.currentTranslate<n.maxTranslate()&&(f=!1,r.resistance&&(s.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-s.startTranslate-b)**A))),f&&(a.preventedByNestedSwiper=!0),!n.allowSlideNext&&n.swipeDirection==="next"&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!n.allowSlidePrev&&n.swipeDirection==="prev"&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),!n.allowSlidePrev&&!n.allowSlideNext&&(s.currentTranslate=s.startTranslate),r.threshold>0)if(Math.abs(b)>r.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,i.startX=i.currentX,i.startY=i.currentY,s.currentTranslate=s.startTranslate,i.diff=n.isHorizontal()?i.currentX-i.startX:i.currentY-i.startY;return}}else{s.currentTranslate=s.startTranslate;return}!r.followFinger||r.cssMode||((r.freeMode&&r.freeMode.enabled&&n.freeMode||r.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(s.currentTranslate),n.setTranslate(s.currentTranslate))}function cc(t){const e=this,n=e.touchEventsData,s=n.evCache.findIndex(m=>m.pointerId===t.pointerId);if(s>=0&&n.evCache.splice(s,1),["pointercancel","pointerout","pointerleave","contextmenu"].includes(t.type)&&!(["pointercancel","contextmenu"].includes(t.type)&&(e.browser.isSafari||e.browser.isWebView)))return;const{params:r,touches:i,rtlTranslate:l,slidesGrid:c,enabled:a}=e;if(!a||!r.simulateTouch&&t.pointerType==="mouse")return;let u=t;if(u.originalEvent&&(u=u.originalEvent),n.allowTouchCallbacks&&e.emit("touchEnd",u),n.allowTouchCallbacks=!1,!n.isTouched){n.isMoved&&r.grabCursor&&e.setGrabCursor(!1),n.isMoved=!1,n.startMoving=!1;return}r.grabCursor&&n.isMoved&&n.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const d=Ot(),o=d-n.touchStartTime;if(e.allowClick){const m=u.path||u.composedPath&&u.composedPath();e.updateClickedSlide(m&&m[0]||u.target,m),e.emit("tap click",u),o<300&&d-n.lastClickTime<300&&e.emit("doubleTap doubleClick",u)}if(n.lastClickTime=Ot(),Mn(()=>{e.destroyed||(e.allowClick=!0)}),!n.isTouched||!n.isMoved||!e.swipeDirection||i.diff===0||n.currentTranslate===n.startTranslate){n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;return}n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;let p;if(r.followFinger?p=l?e.translate:-e.translate:p=-n.currentTranslate,r.cssMode)return;if(r.freeMode&&r.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:p});return}let _=0,w=e.slidesSizesGrid[0];for(let m=0;m<c.length;m+=m<r.slidesPerGroupSkip?1:r.slidesPerGroup){const x=m<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;typeof c[m+x]<"u"?p>=c[m]&&p<c[m+x]&&(_=m,w=c[m+x]-c[m]):p>=c[m]&&(_=m,w=c[c.length-1]-c[c.length-2])}let b=null,g=null;r.rewind&&(e.isBeginning?g=r.virtual&&r.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(b=0));const E=(p-c[_])/w,T=_<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(o>r.longSwipesMs){if(!r.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(E>=r.longSwipesRatio?e.slideTo(r.rewind&&e.isEnd?b:_+T):e.slideTo(_)),e.swipeDirection==="prev"&&(E>1-r.longSwipesRatio?e.slideTo(_+T):g!==null&&E<0&&Math.abs(E)>r.longSwipesRatio?e.slideTo(g):e.slideTo(_))}else{if(!r.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(u.target===e.navigation.nextEl||u.target===e.navigation.prevEl)?u.target===e.navigation.nextEl?e.slideTo(_+T):e.slideTo(_):(e.swipeDirection==="next"&&e.slideTo(b!==null?b:_+T),e.swipeDirection==="prev"&&e.slideTo(g!==null?g:_))}}function wi(){const t=this,{params:e,el:n}=t;if(n&&n.offsetWidth===0)return;e.breakpoints&&t.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:r,snapGrid:i}=t,l=t.virtual&&t.params.virtual.enabled;t.allowSlideNext=!0,t.allowSlidePrev=!0,t.updateSize(),t.updateSlides(),t.updateSlidesClasses();const c=l&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&t.isEnd&&!t.isBeginning&&!t.params.centeredSlides&&!c?t.slideTo(t.slides.length-1,0,!1,!0):t.params.loop&&!l?t.slideToLoop(t.realIndex,0,!1,!0):t.slideTo(t.activeIndex,0,!1,!0),t.autoplay&&t.autoplay.running&&t.autoplay.paused&&(clearTimeout(t.autoplay.resizeTimeout),t.autoplay.resizeTimeout=setTimeout(()=>{t.autoplay&&t.autoplay.running&&t.autoplay.paused&&t.autoplay.resume()},500)),t.allowSlidePrev=r,t.allowSlideNext=s,t.params.watchOverflow&&i!==t.snapGrid&&t.checkOverflow()}function uc(t){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&t.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(t.stopPropagation(),t.stopImmediatePropagation())))}function dc(){const t=this,{wrapperEl:e,rtlTranslate:n,enabled:s}=t;if(!s)return;t.previousTranslate=t.translate,t.isHorizontal()?t.translate=-e.scrollLeft:t.translate=-e.scrollTop,t.translate===0&&(t.translate=0),t.updateActiveIndex(),t.updateSlidesClasses();let r;const i=t.maxTranslate()-t.minTranslate();i===0?r=0:r=(t.translate-t.minTranslate())/i,r!==t.progress&&t.updateProgress(n?-t.translate:t.translate),t.emit("setTranslate",t.translate,!1)}function fc(t){const e=this;bs(e,t.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}let yi=!1;function pc(){}const Sa=(t,e)=>{const n=gt(),{params:s,el:r,wrapperEl:i,device:l}=t,c=!!s.nested,a=e==="on"?"addEventListener":"removeEventListener",u=e;r[a]("pointerdown",t.onTouchStart,{passive:!1}),n[a]("pointermove",t.onTouchMove,{passive:!1,capture:c}),n[a]("pointerup",t.onTouchEnd,{passive:!0}),n[a]("pointercancel",t.onTouchEnd,{passive:!0}),n[a]("pointerout",t.onTouchEnd,{passive:!0}),n[a]("pointerleave",t.onTouchEnd,{passive:!0}),n[a]("contextmenu",t.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[a]("click",t.onClick,!0),s.cssMode&&i[a]("scroll",t.onScroll),s.updateOnWindowResize?t[u](l.ios||l.android?"resize orientationchange observerUpdate":"resize observerUpdate",wi,!0):t[u]("observerUpdate",wi,!0),r[a]("load",t.onLoad,{capture:!0})};function hc(){const t=this,e=gt(),{params:n}=t;t.onTouchStart=oc.bind(t),t.onTouchMove=lc.bind(t),t.onTouchEnd=cc.bind(t),n.cssMode&&(t.onScroll=dc.bind(t)),t.onClick=uc.bind(t),t.onLoad=fc.bind(t),yi||(e.addEventListener("touchstart",pc),yi=!0),Sa(t,"on")}function mc(){Sa(this,"off")}var _c={attachEvents:hc,detachEvents:mc};const Si=(t,e)=>t.grid&&e.grid&&e.grid.rows>1;function gc(){const t=this,{realIndex:e,initialized:n,params:s,el:r}=t,i=s.breakpoints;if(!i||i&&Object.keys(i).length===0)return;const l=t.getBreakpoint(i,t.params.breakpointsBase,t.el);if(!l||t.currentBreakpoint===l)return;const a=(l in i?i[l]:void 0)||t.originalParams,u=Si(t,s),d=Si(t,a),o=s.enabled;u&&!d?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),t.emitContainerClasses()):!u&&d&&(r.classList.add(`${s.containerModifierClass}grid`),(a.grid.fill&&a.grid.fill==="column"||!a.grid.fill&&s.grid.fill==="column")&&r.classList.add(`${s.containerModifierClass}grid-column`),t.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(E=>{if(typeof a[E]>"u")return;const T=s[E]&&s[E].enabled,m=a[E]&&a[E].enabled;T&&!m&&t[E].disable(),!T&&m&&t[E].enable()});const p=a.direction&&a.direction!==s.direction,_=s.loop&&(a.slidesPerView!==s.slidesPerView||p),w=s.loop;p&&n&&t.changeDirection(),It(t.params,a);const b=t.params.enabled,g=t.params.loop;Object.assign(t,{allowTouchMove:t.params.allowTouchMove,allowSlideNext:t.params.allowSlideNext,allowSlidePrev:t.params.allowSlidePrev}),o&&!b?t.disable():!o&&b&&t.enable(),t.currentBreakpoint=l,t.emit("_beforeBreakpoint",a),n&&(_?(t.loopDestroy(),t.loopCreate(e),t.updateSlides()):!w&&g?(t.loopCreate(e),t.updateSlides()):w&&!g&&t.loopDestroy()),t.emit("breakpoint",a)}function vc(t,e,n){if(e===void 0&&(e="window"),!t||e==="container"&&!n)return;let s=!1;const r=it(),i=e==="window"?r.innerHeight:n.clientHeight,l=Object.keys(t).map(c=>{if(typeof c=="string"&&c.indexOf("@")===0){const a=parseFloat(c.substr(1));return{value:i*a,point:c}}return{value:c,point:c}});l.sort((c,a)=>parseInt(c.value,10)-parseInt(a.value,10));for(let c=0;c<l.length;c+=1){const{point:a,value:u}=l[c];e==="window"?r.matchMedia(`(min-width: ${u}px)`).matches&&(s=a):u<=n.clientWidth&&(s=a)}return s||"max"}var bc={setBreakpoint:gc,getBreakpoint:vc};function wc(t,e){const n=[];return t.forEach(s=>{typeof s=="object"?Object.keys(s).forEach(r=>{s[r]&&n.push(e+r)}):typeof s=="string"&&n.push(e+s)}),n}function yc(){const t=this,{classNames:e,params:n,rtl:s,el:r,device:i}=t,l=wc(["initialized",n.direction,{"free-mode":t.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:s},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&n.grid.fill==="column"},{android:i.android},{ios:i.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);e.push(...l),r.classList.add(...e),t.emitContainerClasses()}function Sc(){const t=this,{el:e,classNames:n}=t;e.classList.remove(...n),t.emitContainerClasses()}var Ec={addClasses:yc,removeClasses:Sc};function Tc(){const t=this,{isLocked:e,params:n}=t,{slidesOffsetBefore:s}=n;if(s){const r=t.slides.length-1,i=t.slidesGrid[r]+t.slidesSizesGrid[r]+s*2;t.isLocked=t.size>i}else t.isLocked=t.snapGrid.length===1;n.allowSlideNext===!0&&(t.allowSlideNext=!t.isLocked),n.allowSlidePrev===!0&&(t.allowSlidePrev=!t.isLocked),e&&e!==t.isLocked&&(t.isEnd=!1),e!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}var Mc={checkOverflow:Tc},xr={init:!0,direction:"horizontal",oneWayMovement:!1,touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopedSlides:null,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Ac(t,e){return function(s){s===void 0&&(s={});const r=Object.keys(s)[0],i=s[r];if(typeof i!="object"||i===null){It(e,s);return}if(t[r]===!0&&(t[r]={enabled:!0}),r==="navigation"&&t[r]&&t[r].enabled&&!t[r].prevEl&&!t[r].nextEl&&(t[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&t[r]&&t[r].enabled&&!t[r].el&&(t[r].auto=!0),!(r in t&&"enabled"in i)){It(e,s);return}typeof t[r]=="object"&&!("enabled"in t[r])&&(t[r].enabled=!0),t[r]||(t[r]={enabled:!1}),It(e,s)}}const ir={eventsEmitter:El,update:Ll,translate:Vl,transition:Yl,slide:Kl,loop:nc,grabCursor:ic,events:_c,breakpoints:bc,checkOverflow:Mc,classes:Ec},ar={};class Pt{constructor(){let e,n;for(var s=arguments.length,r=new Array(s),i=0;i<s;i++)r[i]=arguments[i];r.length===1&&r[0].constructor&&Object.prototype.toString.call(r[0]).slice(8,-1)==="Object"?n=r[0]:[e,n]=r,n||(n={}),n=It({},n),e&&!n.el&&(n.el=e);const l=gt();if(n.el&&typeof n.el=="string"&&l.querySelectorAll(n.el).length>1){const d=[];return l.querySelectorAll(n.el).forEach(o=>{const p=It({},n,{el:o});d.push(new Pt(p))}),d}const c=this;c.__swiper__=!0,c.support=wa(),c.device=vl({userAgent:n.userAgent}),c.browser=wl(),c.eventsListeners={},c.eventsAnyListeners=[],c.modules=[...c.__modules__],n.modules&&Array.isArray(n.modules)&&c.modules.push(...n.modules);const a={};c.modules.forEach(d=>{d({params:n,swiper:c,extendParams:Ac(n,a),on:c.on.bind(c),once:c.once.bind(c),off:c.off.bind(c),emit:c.emit.bind(c)})});const u=It({},xr,a);return c.params=It({},u,ar,n),c.originalParams=It({},c.params),c.passedParams=It({},n),c.params&&c.params.on&&Object.keys(c.params.on).forEach(d=>{c.on(d,c.params.on[d])}),c.params&&c.params.onAny&&c.onAny(c.params.onAny),Object.assign(c,{enabled:c.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return c.params.direction==="horizontal"},isVertical(){return c.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:c.params.allowSlideNext,allowSlidePrev:c.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:c.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,evCache:[]},allowClick:!0,allowTouchMove:c.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),c.emit("_swiper"),c.params.init&&c.init(),c}getSlideIndex(e){const{slidesEl:n,params:s}=this,r=ht(n,`.${s.slideClass}, swiper-slide`),i=Jn(r[0]);return Jn(e)-i}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter(n=>n.getAttribute("data-swiper-slide-index")*1===e)[0])}recalcSlides(){const e=this,{slidesEl:n,params:s}=e;e.slides=ht(n,`.${s.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,n){const s=this;e=Math.min(Math.max(e,0),1);const r=s.minTranslate(),l=(s.maxTranslate()-r)*e+r;s.translateTo(l,typeof n>"u"?0:n),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const n=e.el.className.split(" ").filter(s=>s.indexOf("swiper")===0||s.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",n.join(" "))}getSlideClasses(e){const n=this;return n.destroyed?"":e.className.split(" ").filter(s=>s.indexOf("swiper-slide")===0||s.indexOf(n.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const n=[];e.slides.forEach(s=>{const r=e.getSlideClasses(s);n.push({slideEl:s,classNames:r}),e.emit("_slideClass",s,r)}),e.emit("_slideClasses",n)}slidesPerViewDynamic(e,n){e===void 0&&(e="current"),n===void 0&&(n=!1);const s=this,{params:r,slides:i,slidesGrid:l,slidesSizesGrid:c,size:a,activeIndex:u}=s;let d=1;if(typeof r.slidesPerView=="number")return r.slidesPerView;if(r.centeredSlides){let o=i[u]?i[u].swiperSlideSize:0,p;for(let _=u+1;_<i.length;_+=1)i[_]&&!p&&(o+=i[_].swiperSlideSize,d+=1,o>a&&(p=!0));for(let _=u-1;_>=0;_-=1)i[_]&&!p&&(o+=i[_].swiperSlideSize,d+=1,o>a&&(p=!0))}else if(e==="current")for(let o=u+1;o<i.length;o+=1)(n?l[o]+c[o]-l[u]<a:l[o]-l[u]<a)&&(d+=1);else for(let o=u-1;o>=0;o-=1)l[u]-l[o]<a&&(d+=1);return d}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:n,params:s}=e;s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(l=>{l.complete&&bs(e,l)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function r(){const l=e.rtlTranslate?e.translate*-1:e.translate,c=Math.min(Math.max(l,e.maxTranslate()),e.minTranslate());e.setTranslate(c),e.updateActiveIndex(),e.updateSlidesClasses()}let i;if(s.freeMode&&s.freeMode.enabled&&!s.cssMode)r(),s.autoHeight&&e.updateAutoHeight();else{if((s.slidesPerView==="auto"||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const l=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;i=e.slideTo(l.length-1,0,!1,!0)}else i=e.slideTo(e.activeIndex,0,!1,!0);i||r()}s.watchOverflow&&n!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,n){n===void 0&&(n=!0);const s=this,r=s.params.direction;return e||(e=r==="horizontal"?"vertical":"horizontal"),e===r||e!=="horizontal"&&e!=="vertical"||(s.el.classList.remove(`${s.params.containerModifierClass}${r}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach(i=>{e==="vertical"?i.style.width="":i.style.height=""}),s.emit("changeDirection"),n&&s.update()),s}changeLanguageDirection(e){const n=this;n.rtl&&e==="rtl"||!n.rtl&&e==="ltr"||(n.rtl=e==="rtl",n.rtlTranslate=n.params.direction==="horizontal"&&n.rtl,n.rtl?(n.el.classList.add(`${n.params.containerModifierClass}rtl`),n.el.dir="rtl"):(n.el.classList.remove(`${n.params.containerModifierClass}rtl`),n.el.dir="ltr"),n.update())}mount(e){const n=this;if(n.mounted)return!0;let s=e||n.params.el;if(typeof s=="string"&&(s=document.querySelector(s)),!s)return!1;s.swiper=n,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName==="SWIPER-CONTAINER"&&(n.isElement=!0);const r=()=>`.${(n.params.wrapperClass||"").trim().split(" ").join(".")}`;let l=(()=>s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(r()):ht(s,r())[0])();return!l&&n.params.createElements&&(l=Ht("div",n.params.wrapperClass),s.append(l),ht(s,`.${n.params.slideClass}`).forEach(c=>{l.append(c)})),Object.assign(n,{el:s,wrapperEl:l,slidesEl:n.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:l,hostEl:n.isElement?s.parentNode.host:s,mounted:!0,rtl:s.dir.toLowerCase()==="rtl"||un(s,"direction")==="rtl",rtlTranslate:n.params.direction==="horizontal"&&(s.dir.toLowerCase()==="rtl"||un(s,"direction")==="rtl"),wrongRTL:un(l,"display")==="-webkit-box"}),!0}init(e){const n=this;if(n.initialized||n.mount(e)===!1)return n;n.emit("beforeInit"),n.params.breakpoints&&n.setBreakpoint(),n.addClasses(),n.updateSize(),n.updateSlides(),n.params.watchOverflow&&n.checkOverflow(),n.params.grabCursor&&n.enabled&&n.setGrabCursor(),n.params.loop&&n.virtual&&n.params.virtual.enabled?n.slideTo(n.params.initialSlide+n.virtual.slidesBefore,0,n.params.runCallbacksOnInit,!1,!0):n.slideTo(n.params.initialSlide,0,n.params.runCallbacksOnInit,!1,!0),n.params.loop&&n.loopCreate(),n.attachEvents();const r=[...n.el.querySelectorAll('[loading="lazy"]')];return n.isElement&&r.push(...n.hostEl.querySelectorAll('[loading="lazy"]')),r.forEach(i=>{i.complete?bs(n,i):i.addEventListener("load",l=>{bs(n,l.target)})}),Ar(n),n.initialized=!0,Ar(n),n.emit("init"),n.emit("afterInit"),n}destroy(e,n){e===void 0&&(e=!0),n===void 0&&(n=!0);const s=this,{params:r,el:i,wrapperEl:l,slides:c}=s;return typeof s.params>"u"||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),r.loop&&s.loopDestroy(),n&&(s.removeClasses(),i.removeAttribute("style"),l.removeAttribute("style"),c&&c.length&&c.forEach(a=>{a.classList.remove(r.slideVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),a.removeAttribute("style"),a.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(a=>{s.off(a)}),e!==!1&&(s.el.swiper=null,dl(s)),s.destroyed=!0),null}static extendDefaults(e){It(ar,e)}static get extendedDefaults(){return ar}static get defaults(){return xr}static installModule(e){Pt.prototype.__modules__||(Pt.prototype.__modules__=[]);const n=Pt.prototype.__modules__;typeof e=="function"&&n.indexOf(e)<0&&n.push(e)}static use(e){return Array.isArray(e)?(e.forEach(n=>Pt.installModule(n)),Pt):(Pt.installModule(e),Pt)}}Object.keys(ir).forEach(t=>{Object.keys(ir[t]).forEach(e=>{Pt.prototype[e]=ir[t][e]})});Pt.use([yl,Sl]);function xc(t){let{swiper:e,extendParams:n,on:s,emit:r}=t;n({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}});let i;const l=gt();e.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};const c=l.createElement("div");function a(w,b){const g=e.params.virtual;if(g.cache&&e.virtual.cache[b])return e.virtual.cache[b];let E;return g.renderSlide?(E=g.renderSlide.call(e,w,b),typeof E=="string"&&(c.innerHTML=E,E=c.children[0])):e.isElement?E=Ht("swiper-slide"):E=Ht("div",e.params.slideClass),E.setAttribute("data-swiper-slide-index",b),g.renderSlide||(E.innerHTML=w),g.cache&&(e.virtual.cache[b]=E),E}function u(w){const{slidesPerView:b,slidesPerGroup:g,centeredSlides:E,loop:T}=e.params,{addSlidesBefore:m,addSlidesAfter:x}=e.params.virtual,{from:f,to:A,slides:D,slidesGrid:V,offset:O}=e.virtual;e.params.cssMode||e.updateActiveIndex();const q=e.activeIndex||0;let N;e.rtlTranslate?N="right":N=e.isHorizontal()?"left":"top";let L,I;E?(L=Math.floor(b/2)+g+x,I=Math.floor(b/2)+g+m):(L=b+(g-1)+x,I=(T?b:g)+m);let h=q-I,k=q+L;T||(h=Math.max(h,0),k=Math.min(k,D.length-1));let H=(e.slidesGrid[h]||0)-(e.slidesGrid[0]||0);T&&q>=I?(h-=I,E||(H+=e.slidesGrid[0])):T&&q<I&&(h=-I,E&&(H+=e.slidesGrid[0])),Object.assign(e.virtual,{from:h,to:k,offset:H,slidesGrid:e.slidesGrid,slidesBefore:I,slidesAfter:L});function R(){e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),r("virtualUpdate")}if(f===h&&A===k&&!w){e.slidesGrid!==V&&H!==O&&e.slides.forEach(v=>{v.style[N]=`${H-Math.abs(e.cssOverflowAdjustment())}px`}),e.updateProgress(),r("virtualUpdate");return}if(e.params.virtual.renderExternal){e.params.virtual.renderExternal.call(e,{offset:H,from:h,to:k,slides:function(){const C=[];for(let $=h;$<=k;$+=1)C.push(D[$]);return C}()}),e.params.virtual.renderExternalUpdate?R():r("virtualUpdate");return}const F=[],Y=[],X=v=>{let C=v;return v<0?C=D.length+v:C>=D.length&&(C=C-D.length),C};if(w)e.slides.filter(v=>v.matches(`.${e.params.slideClass}, swiper-slide`)).forEach(v=>{v.remove()});else for(let v=f;v<=A;v+=1)if(v<h||v>k){const C=X(v);e.slides.filter($=>$.matches(`.${e.params.slideClass}[data-swiper-slide-index="${C}"], swiper-slide[data-swiper-slide-index="${C}"]`)).forEach($=>{$.remove()})}const M=T?-D.length:0,y=T?D.length*2:D.length;for(let v=M;v<y;v+=1)if(v>=h&&v<=k){const C=X(v);typeof A>"u"||w?Y.push(C):(v>A&&Y.push(C),v<f&&F.push(C))}if(Y.forEach(v=>{e.slidesEl.append(a(D[v],v))}),T)for(let v=F.length-1;v>=0;v-=1){const C=F[v];e.slidesEl.prepend(a(D[C],C))}else F.sort((v,C)=>C-v),F.forEach(v=>{e.slidesEl.prepend(a(D[v],v))});ht(e.slidesEl,".swiper-slide, swiper-slide").forEach(v=>{v.style[N]=`${H-Math.abs(e.cssOverflowAdjustment())}px`}),R()}function d(w){if(typeof w=="object"&&"length"in w)for(let b=0;b<w.length;b+=1)w[b]&&e.virtual.slides.push(w[b]);else e.virtual.slides.push(w);u(!0)}function o(w){const b=e.activeIndex;let g=b+1,E=1;if(Array.isArray(w)){for(let T=0;T<w.length;T+=1)w[T]&&e.virtual.slides.unshift(w[T]);g=b+w.length,E=w.length}else e.virtual.slides.unshift(w);if(e.params.virtual.cache){const T=e.virtual.cache,m={};Object.keys(T).forEach(x=>{const f=T[x],A=f.getAttribute("data-swiper-slide-index");A&&f.setAttribute("data-swiper-slide-index",parseInt(A,10)+E),m[parseInt(x,10)+E]=f}),e.virtual.cache=m}u(!0),e.slideTo(g,0)}function p(w){if(typeof w>"u"||w===null)return;let b=e.activeIndex;if(Array.isArray(w))for(let g=w.length-1;g>=0;g-=1)e.params.virtual.cache&&(delete e.virtual.cache[w[g]],Object.keys(e.virtual.cache).forEach(E=>{E>w&&(e.virtual.cache[E-1]=e.virtual.cache[E],e.virtual.cache[E-1].setAttribute("data-swiper-slide-index",E-1),delete e.virtual.cache[E])})),e.virtual.slides.splice(w[g],1),w[g]<b&&(b-=1),b=Math.max(b,0);else e.params.virtual.cache&&(delete e.virtual.cache[w],Object.keys(e.virtual.cache).forEach(g=>{g>w&&(e.virtual.cache[g-1]=e.virtual.cache[g],e.virtual.cache[g-1].setAttribute("data-swiper-slide-index",g-1),delete e.virtual.cache[g])})),e.virtual.slides.splice(w,1),w<b&&(b-=1),b=Math.max(b,0);u(!0),e.slideTo(b,0)}function _(){e.virtual.slides=[],e.params.virtual.cache&&(e.virtual.cache={}),u(!0),e.slideTo(0,0)}s("beforeInit",()=>{if(!e.params.virtual.enabled)return;let w;if(typeof e.passedParams.virtual.slides>"u"){const b=[...e.slidesEl.children].filter(g=>g.matches(`.${e.params.slideClass}, swiper-slide`));b&&b.length&&(e.virtual.slides=[...b],w=!0,b.forEach((g,E)=>{g.setAttribute("data-swiper-slide-index",E),e.virtual.cache[E]=g,g.remove()}))}w||(e.virtual.slides=e.params.virtual.slides),e.classNames.push(`${e.params.containerModifierClass}virtual`),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0,u()}),s("setTranslate",()=>{e.params.virtual.enabled&&(e.params.cssMode&&!e._immediateVirtual?(clearTimeout(i),i=setTimeout(()=>{u()},100)):u())}),s("init update resize",()=>{e.params.virtual.enabled&&e.params.cssMode&&Zn(e.wrapperEl,"--swiper-virtual-size",`${e.virtualSize}px`)}),Object.assign(e.virtual,{appendSlide:d,prependSlide:o,removeSlide:p,removeAllSlides:_,update:u})}function Cc(t){let{swiper:e,extendParams:n,on:s,emit:r}=t;const i=gt(),l=it();e.keyboard={enabled:!1},n({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}});function c(d){if(!e.enabled)return;const{rtlTranslate:o}=e;let p=d;p.originalEvent&&(p=p.originalEvent);const _=p.keyCode||p.charCode,w=e.params.keyboard.pageUpDown,b=w&&_===33,g=w&&_===34,E=_===37,T=_===39,m=_===38,x=_===40;if(!e.allowSlideNext&&(e.isHorizontal()&&T||e.isVertical()&&x||g)||!e.allowSlidePrev&&(e.isHorizontal()&&E||e.isVertical()&&m||b))return!1;if(!(p.shiftKey||p.altKey||p.ctrlKey||p.metaKey)&&!(i.activeElement&&i.activeElement.nodeName&&(i.activeElement.nodeName.toLowerCase()==="input"||i.activeElement.nodeName.toLowerCase()==="textarea"))){if(e.params.keyboard.onlyInViewport&&(b||g||E||T||m||x)){let f=!1;if(En(e.el,`.${e.params.slideClass}, swiper-slide`).length>0&&En(e.el,`.${e.params.slideActiveClass}`).length===0)return;const A=e.el,D=A.clientWidth,V=A.clientHeight,O=l.innerWidth,q=l.innerHeight,N=Ss(A);o&&(N.left-=A.scrollLeft);const L=[[N.left,N.top],[N.left+D,N.top],[N.left,N.top+V],[N.left+D,N.top+V]];for(let I=0;I<L.length;I+=1){const h=L[I];if(h[0]>=0&&h[0]<=O&&h[1]>=0&&h[1]<=q){if(h[0]===0&&h[1]===0)continue;f=!0}}if(!f)return}e.isHorizontal()?((b||g||E||T)&&(p.preventDefault?p.preventDefault():p.returnValue=!1),((g||T)&&!o||(b||E)&&o)&&e.slideNext(),((b||E)&&!o||(g||T)&&o)&&e.slidePrev()):((b||g||m||x)&&(p.preventDefault?p.preventDefault():p.returnValue=!1),(g||x)&&e.slideNext(),(b||m)&&e.slidePrev()),r("keyPress",_)}}function a(){e.keyboard.enabled||(i.addEventListener("keydown",c),e.keyboard.enabled=!0)}function u(){e.keyboard.enabled&&(i.removeEventListener("keydown",c),e.keyboard.enabled=!1)}s("init",()=>{e.params.keyboard.enabled&&a()}),s("destroy",()=>{e.keyboard.enabled&&u()}),Object.assign(e.keyboard,{enable:a,disable:u})}function kc(t){let{swiper:e,extendParams:n,on:s,emit:r}=t;const i=it();n({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null,noMousewheelClass:"swiper-no-mousewheel"}}),e.mousewheel={enabled:!1};let l,c=Ot(),a;const u=[];function d(m){let D=0,V=0,O=0,q=0;return"detail"in m&&(V=m.detail),"wheelDelta"in m&&(V=-m.wheelDelta/120),"wheelDeltaY"in m&&(V=-m.wheelDeltaY/120),"wheelDeltaX"in m&&(D=-m.wheelDeltaX/120),"axis"in m&&m.axis===m.HORIZONTAL_AXIS&&(D=V,V=0),O=D*10,q=V*10,"deltaY"in m&&(q=m.deltaY),"deltaX"in m&&(O=m.deltaX),m.shiftKey&&!O&&(O=q,q=0),(O||q)&&m.deltaMode&&(m.deltaMode===1?(O*=40,q*=40):(O*=800,q*=800)),O&&!D&&(D=O<1?-1:1),q&&!V&&(V=q<1?-1:1),{spinX:D,spinY:V,pixelX:O,pixelY:q}}function o(){e.enabled&&(e.mouseEntered=!0)}function p(){e.enabled&&(e.mouseEntered=!1)}function _(m){return e.params.mousewheel.thresholdDelta&&m.delta<e.params.mousewheel.thresholdDelta||e.params.mousewheel.thresholdTime&&Ot()-c<e.params.mousewheel.thresholdTime?!1:m.delta>=6&&Ot()-c<60?!0:(m.direction<0?(!e.isEnd||e.params.loop)&&!e.animating&&(e.slideNext(),r("scroll",m.raw)):(!e.isBeginning||e.params.loop)&&!e.animating&&(e.slidePrev(),r("scroll",m.raw)),c=new i.Date().getTime(),!1)}function w(m){const x=e.params.mousewheel;if(m.direction<0){if(e.isEnd&&!e.params.loop&&x.releaseOnEdges)return!0}else if(e.isBeginning&&!e.params.loop&&x.releaseOnEdges)return!0;return!1}function b(m){let x=m,f=!0;if(!e.enabled||m.target.closest(`.${e.params.mousewheel.noMousewheelClass}`))return;const A=e.params.mousewheel;e.params.cssMode&&x.preventDefault();let D=e.el;e.params.mousewheel.eventsTarget!=="container"&&(D=document.querySelector(e.params.mousewheel.eventsTarget));const V=D&&D.contains(x.target);if(!e.mouseEntered&&!V&&!A.releaseOnEdges)return!0;x.originalEvent&&(x=x.originalEvent);let O=0;const q=e.rtlTranslate?-1:1,N=d(x);if(A.forceToAxis)if(e.isHorizontal())if(Math.abs(N.pixelX)>Math.abs(N.pixelY))O=-N.pixelX*q;else return!0;else if(Math.abs(N.pixelY)>Math.abs(N.pixelX))O=-N.pixelY;else return!0;else O=Math.abs(N.pixelX)>Math.abs(N.pixelY)?-N.pixelX*q:-N.pixelY;if(O===0)return!0;A.invert&&(O=-O);let L=e.getTranslate()+O*A.sensitivity;if(L>=e.minTranslate()&&(L=e.minTranslate()),L<=e.maxTranslate()&&(L=e.maxTranslate()),f=e.params.loop?!0:!(L===e.minTranslate()||L===e.maxTranslate()),f&&e.params.nested&&x.stopPropagation(),!e.params.freeMode||!e.params.freeMode.enabled){const I={time:Ot(),delta:Math.abs(O),direction:Math.sign(O),raw:m};u.length>=2&&u.shift();const h=u.length?u[u.length-1]:void 0;if(u.push(I),h?(I.direction!==h.direction||I.delta>h.delta||I.time>h.time+150)&&_(I):_(I),w(I))return!0}else{const I={time:Ot(),delta:Math.abs(O),direction:Math.sign(O)},h=a&&I.time<a.time+500&&I.delta<=a.delta&&I.direction===a.direction;if(!h){a=void 0;let k=e.getTranslate()+O*A.sensitivity;const H=e.isBeginning,R=e.isEnd;if(k>=e.minTranslate()&&(k=e.minTranslate()),k<=e.maxTranslate()&&(k=e.maxTranslate()),e.setTransition(0),e.setTranslate(k),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses(),(!H&&e.isBeginning||!R&&e.isEnd)&&e.updateSlidesClasses(),e.params.loop&&e.loopFix({direction:I.direction<0?"next":"prev",byMousewheel:!0}),e.params.freeMode.sticky){clearTimeout(l),l=void 0,u.length>=15&&u.shift();const F=u.length?u[u.length-1]:void 0,Y=u[0];if(u.push(I),F&&(I.delta>F.delta||I.direction!==F.direction))u.splice(0);else if(u.length>=15&&I.time-Y.time<500&&Y.delta-I.delta>=1&&I.delta<=6){const X=O>0?.8:.2;a=I,u.splice(0),l=Mn(()=>{e.slideToClosest(e.params.speed,!0,void 0,X)},0)}l||(l=Mn(()=>{a=I,u.splice(0),e.slideToClosest(e.params.speed,!0,void 0,.5)},500))}if(h||r("scroll",x),e.params.autoplay&&e.params.autoplayDisableOnInteraction&&e.autoplay.stop(),A.releaseOnEdges&&(k===e.minTranslate()||k===e.maxTranslate()))return!0}}return x.preventDefault?x.preventDefault():x.returnValue=!1,!1}function g(m){let x=e.el;e.params.mousewheel.eventsTarget!=="container"&&(x=document.querySelector(e.params.mousewheel.eventsTarget)),x[m]("mouseenter",o),x[m]("mouseleave",p),x[m]("wheel",b)}function E(){return e.params.cssMode?(e.wrapperEl.removeEventListener("wheel",b),!0):e.mousewheel.enabled?!1:(g("addEventListener"),e.mousewheel.enabled=!0,!0)}function T(){return e.params.cssMode?(e.wrapperEl.addEventListener(event,b),!0):e.mousewheel.enabled?(g("removeEventListener"),e.mousewheel.enabled=!1,!0):!1}s("init",()=>{!e.params.mousewheel.enabled&&e.params.cssMode&&T(),e.params.mousewheel.enabled&&E()}),s("destroy",()=>{e.params.cssMode&&E(),e.mousewheel.enabled&&T()}),Object.assign(e.mousewheel,{enable:E,disable:T})}function Hr(t,e,n,s){return t.params.createElements&&Object.keys(s).forEach(r=>{if(!n[r]&&n.auto===!0){let i=ht(t.el,`.${s[r]}`)[0];i||(i=Ht("div",s[r]),i.className=s[r],t.el.append(i)),n[r]=i,e[r]=i}}),n}function Pc(t){let{swiper:e,extendParams:n,on:s,emit:r}=t;n({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};const i=b=>(Array.isArray(b)?b:[b]).filter(g=>!!g);function l(b){let g;return b&&typeof b=="string"&&e.isElement&&(g=e.el.querySelector(b),g)?g:(b&&(typeof b=="string"&&(g=[...document.querySelectorAll(b)]),e.params.uniqueNavElements&&typeof b=="string"&&g.length>1&&e.el.querySelectorAll(b).length===1&&(g=e.el.querySelector(b))),b&&!g?b:g)}function c(b,g){const E=e.params.navigation;b=i(b),b.forEach(T=>{T&&(T.classList[g?"add":"remove"](...E.disabledClass.split(" ")),T.tagName==="BUTTON"&&(T.disabled=g),e.params.watchOverflow&&e.enabled&&T.classList[e.isLocked?"add":"remove"](E.lockClass))})}function a(){const{nextEl:b,prevEl:g}=e.navigation;if(e.params.loop){c(g,!1),c(b,!1);return}c(g,e.isBeginning&&!e.params.rewind),c(b,e.isEnd&&!e.params.rewind)}function u(b){b.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),r("navigationPrev"))}function d(b){b.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),r("navigationNext"))}function o(){const b=e.params.navigation;if(e.params.navigation=Hr(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(b.nextEl||b.prevEl))return;let g=l(b.nextEl),E=l(b.prevEl);Object.assign(e.navigation,{nextEl:g,prevEl:E}),g=i(g),E=i(E);const T=(m,x)=>{m&&m.addEventListener("click",x==="next"?d:u),!e.enabled&&m&&m.classList.add(...b.lockClass.split(" "))};g.forEach(m=>T(m,"next")),E.forEach(m=>T(m,"prev"))}function p(){let{nextEl:b,prevEl:g}=e.navigation;b=i(b),g=i(g);const E=(T,m)=>{T.removeEventListener("click",m==="next"?d:u),T.classList.remove(...e.params.navigation.disabledClass.split(" "))};b.forEach(T=>E(T,"next")),g.forEach(T=>E(T,"prev"))}s("init",()=>{e.params.navigation.enabled===!1?w():(o(),a())}),s("toEdge fromEdge lock unlock",()=>{a()}),s("destroy",()=>{p()}),s("enable disable",()=>{let{nextEl:b,prevEl:g}=e.navigation;if(b=i(b),g=i(g),e.enabled){a();return}[...b,...g].filter(E=>!!E).forEach(E=>E.classList.add(e.params.navigation.lockClass))}),s("click",(b,g)=>{let{nextEl:E,prevEl:T}=e.navigation;E=i(E),T=i(T);const m=g.target;if(e.params.navigation.hideOnClick&&!T.includes(m)&&!E.includes(m)){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===m||e.pagination.el.contains(m)))return;let x;E.length?x=E[0].classList.contains(e.params.navigation.hiddenClass):T.length&&(x=T[0].classList.contains(e.params.navigation.hiddenClass)),r(x===!0?"navigationShow":"navigationHide"),[...E,...T].filter(f=>!!f).forEach(f=>f.classList.toggle(e.params.navigation.hiddenClass))}});const _=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),o(),a()},w=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),p()};Object.assign(e.navigation,{enable:_,disable:w,update:a,init:o,destroy:p})}function ln(t){return t===void 0&&(t=""),`.${t.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function Ic(t){let{swiper:e,extendParams:n,on:s,emit:r}=t;const i="swiper-pagination";n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:T=>T,formatFractionTotal:T=>T,bulletClass:`${i}-bullet`,bulletActiveClass:`${i}-bullet-active`,modifierClass:`${i}-`,currentClass:`${i}-current`,totalClass:`${i}-total`,hiddenClass:`${i}-hidden`,progressbarFillClass:`${i}-progressbar-fill`,progressbarOppositeClass:`${i}-progressbar-opposite`,clickableClass:`${i}-clickable`,lockClass:`${i}-lock`,horizontalClass:`${i}-horizontal`,verticalClass:`${i}-vertical`,paginationDisabledClass:`${i}-disabled`}}),e.pagination={el:null,bullets:[]};let l,c=0;const a=T=>(Array.isArray(T)?T:[T]).filter(m=>!!m);function u(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function d(T,m){const{bulletActiveClass:x}=e.params.pagination;T&&(T=T[`${m==="prev"?"previous":"next"}ElementSibling`],T&&(T.classList.add(`${x}-${m}`),T=T[`${m==="prev"?"previous":"next"}ElementSibling`],T&&T.classList.add(`${x}-${m}-${m}`)))}function o(T){const m=T.target.closest(ln(e.params.pagination.bulletClass));if(!m)return;T.preventDefault();const x=Jn(m)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===x)return;const f=e.realIndex,A=e.getSlideIndexByData(x),D=e.getSlideIndexByData(e.realIndex),V=O=>{const q=e.activeIndex;e.loopFix({direction:O,activeSlideIndex:A,slideTo:!1});const N=e.activeIndex;q===N&&e.slideToLoop(f,0,!1,!0)};if(A>e.slides.length-e.loopedSlides)V(A>D?"next":"prev");else if(e.params.centeredSlides){const O=e.params.slidesPerView==="auto"?e.slidesPerViewDynamic():Math.ceil(parseFloat(e.params.slidesPerView,10));A<Math.floor(O/2)&&V("prev")}e.slideToLoop(x)}else e.slideTo(x)}function p(){const T=e.rtl,m=e.params.pagination;if(u())return;let x=e.pagination.el;x=a(x);let f,A;const D=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,V=e.params.loop?Math.ceil(D/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(A=e.previousRealIndex||0,f=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(f=e.snapIndex,A=e.previousSnapIndex):(A=e.previousIndex||0,f=e.activeIndex||0),m.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const O=e.pagination.bullets;let q,N,L;if(m.dynamicBullets&&(l=Mr(O[0],e.isHorizontal()?"width":"height",!0),x.forEach(I=>{I.style[e.isHorizontal()?"width":"height"]=`${l*(m.dynamicMainBullets+4)}px`}),m.dynamicMainBullets>1&&A!==void 0&&(c+=f-(A||0),c>m.dynamicMainBullets-1?c=m.dynamicMainBullets-1:c<0&&(c=0)),q=Math.max(f-c,0),N=q+(Math.min(O.length,m.dynamicMainBullets)-1),L=(N+q)/2),O.forEach(I=>{const h=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(k=>`${m.bulletActiveClass}${k}`)].map(k=>typeof k=="string"&&k.includes(" ")?k.split(" "):k).flat();I.classList.remove(...h)}),x.length>1)O.forEach(I=>{const h=Jn(I);h===f?I.classList.add(...m.bulletActiveClass.split(" ")):e.isElement&&I.setAttribute("part","bullet"),m.dynamicBullets&&(h>=q&&h<=N&&I.classList.add(...`${m.bulletActiveClass}-main`.split(" ")),h===q&&d(I,"prev"),h===N&&d(I,"next"))});else{const I=O[f];if(I&&I.classList.add(...m.bulletActiveClass.split(" ")),e.isElement&&O.forEach((h,k)=>{h.setAttribute("part",k===f?"bullet-active":"bullet")}),m.dynamicBullets){const h=O[q],k=O[N];for(let H=q;H<=N;H+=1)O[H]&&O[H].classList.add(...`${m.bulletActiveClass}-main`.split(" "));d(h,"prev"),d(k,"next")}}if(m.dynamicBullets){const I=Math.min(O.length,m.dynamicMainBullets+4),h=(l*I-l)/2-L*l,k=T?"right":"left";O.forEach(H=>{H.style[e.isHorizontal()?k:"top"]=`${h}px`})}}x.forEach((O,q)=>{if(m.type==="fraction"&&(O.querySelectorAll(ln(m.currentClass)).forEach(N=>{N.textContent=m.formatFractionCurrent(f+1)}),O.querySelectorAll(ln(m.totalClass)).forEach(N=>{N.textContent=m.formatFractionTotal(V)})),m.type==="progressbar"){let N;m.progressbarOpposite?N=e.isHorizontal()?"vertical":"horizontal":N=e.isHorizontal()?"horizontal":"vertical";const L=(f+1)/V;let I=1,h=1;N==="horizontal"?I=L:h=L,O.querySelectorAll(ln(m.progressbarFillClass)).forEach(k=>{k.style.transform=`translate3d(0,0,0) scaleX(${I}) scaleY(${h})`,k.style.transitionDuration=`${e.params.speed}ms`})}m.type==="custom"&&m.renderCustom?(O.innerHTML=m.renderCustom(e,f+1,V),q===0&&r("paginationRender",O)):(q===0&&r("paginationRender",O),r("paginationUpdate",O)),e.params.watchOverflow&&e.enabled&&O.classList[e.isLocked?"add":"remove"](m.lockClass)})}function _(){const T=e.params.pagination;if(u())return;const m=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length;let x=e.pagination.el;x=a(x);let f="";if(T.type==="bullets"){let A=e.params.loop?Math.ceil(m/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&A>m&&(A=m);for(let D=0;D<A;D+=1)T.renderBullet?f+=T.renderBullet.call(e,D,T.bulletClass):f+=`<${T.bulletElement} ${e.isElement?'part="bullet"':""} class="${T.bulletClass}"></${T.bulletElement}>`}T.type==="fraction"&&(T.renderFraction?f=T.renderFraction.call(e,T.currentClass,T.totalClass):f=`<span class="${T.currentClass}"></span> / <span class="${T.totalClass}"></span>`),T.type==="progressbar"&&(T.renderProgressbar?f=T.renderProgressbar.call(e,T.progressbarFillClass):f=`<span class="${T.progressbarFillClass}"></span>`),e.pagination.bullets=[],x.forEach(A=>{T.type!=="custom"&&(A.innerHTML=f||""),T.type==="bullets"&&e.pagination.bullets.push(...A.querySelectorAll(ln(T.bulletClass)))}),T.type!=="custom"&&r("paginationRender",x[0])}function w(){e.params.pagination=Hr(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const T=e.params.pagination;if(!T.el)return;let m;typeof T.el=="string"&&e.isElement&&(m=e.el.querySelector(T.el)),!m&&typeof T.el=="string"&&(m=[...document.querySelectorAll(T.el)]),m||(m=T.el),!(!m||m.length===0)&&(e.params.uniqueNavElements&&typeof T.el=="string"&&Array.isArray(m)&&m.length>1&&(m=[...e.el.querySelectorAll(T.el)],m.length>1&&(m=m.filter(x=>En(x,".swiper")[0]===e.el)[0])),Array.isArray(m)&&m.length===1&&(m=m[0]),Object.assign(e.pagination,{el:m}),m=a(m),m.forEach(x=>{T.type==="bullets"&&T.clickable&&x.classList.add(...(T.clickableClass||"").split(" ")),x.classList.add(T.modifierClass+T.type),x.classList.add(e.isHorizontal()?T.horizontalClass:T.verticalClass),T.type==="bullets"&&T.dynamicBullets&&(x.classList.add(`${T.modifierClass}${T.type}-dynamic`),c=0,T.dynamicMainBullets<1&&(T.dynamicMainBullets=1)),T.type==="progressbar"&&T.progressbarOpposite&&x.classList.add(T.progressbarOppositeClass),T.clickable&&x.addEventListener("click",o),e.enabled||x.classList.add(T.lockClass)}))}function b(){const T=e.params.pagination;if(u())return;let m=e.pagination.el;m&&(m=a(m),m.forEach(x=>{x.classList.remove(T.hiddenClass),x.classList.remove(T.modifierClass+T.type),x.classList.remove(e.isHorizontal()?T.horizontalClass:T.verticalClass),T.clickable&&(x.classList.remove(...(T.clickableClass||"").split(" ")),x.removeEventListener("click",o))})),e.pagination.bullets&&e.pagination.bullets.forEach(x=>x.classList.remove(...T.bulletActiveClass.split(" ")))}s("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const T=e.params.pagination;let{el:m}=e.pagination;m=a(m),m.forEach(x=>{x.classList.remove(T.horizontalClass,T.verticalClass),x.classList.add(e.isHorizontal()?T.horizontalClass:T.verticalClass)})}),s("init",()=>{e.params.pagination.enabled===!1?E():(w(),_(),p())}),s("activeIndexChange",()=>{typeof e.snapIndex>"u"&&p()}),s("snapIndexChange",()=>{p()}),s("snapGridLengthChange",()=>{_(),p()}),s("destroy",()=>{b()}),s("enable disable",()=>{let{el:T}=e.pagination;T&&(T=a(T),T.forEach(m=>m.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),s("lock unlock",()=>{p()}),s("click",(T,m)=>{const x=m.target,f=a(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&f&&f.length>0&&!x.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&x===e.navigation.nextEl||e.navigation.prevEl&&x===e.navigation.prevEl))return;const A=f[0].classList.contains(e.params.pagination.hiddenClass);r(A===!0?"paginationShow":"paginationHide"),f.forEach(D=>D.classList.toggle(e.params.pagination.hiddenClass))}});const g=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:T}=e.pagination;T&&(T=a(T),T.forEach(m=>m.classList.remove(e.params.pagination.paginationDisabledClass))),w(),_(),p()},E=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:T}=e.pagination;T&&(T=a(T),T.forEach(m=>m.classList.add(e.params.pagination.paginationDisabledClass))),b()};Object.assign(e.pagination,{enable:g,disable:E,render:_,update:p,init:w,destroy:b})}function Rc(t){let{swiper:e,extendParams:n,on:s,emit:r}=t;const i=gt();let l=!1,c=null,a=null,u,d,o,p;n({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),e.scrollbar={el:null,dragEl:null};function _(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:L,rtlTranslate:I}=e,{dragEl:h,el:k}=L,H=e.params.scrollbar,R=e.params.loop?e.progressLoop:e.progress;let F=d,Y=(o-d)*R;I?(Y=-Y,Y>0?(F=d-Y,Y=0):-Y+d>o&&(F=o+Y)):Y<0?(F=d+Y,Y=0):Y+d>o&&(F=o-Y),e.isHorizontal()?(h.style.transform=`translate3d(${Y}px, 0, 0)`,h.style.width=`${F}px`):(h.style.transform=`translate3d(0px, ${Y}px, 0)`,h.style.height=`${F}px`),H.hide&&(clearTimeout(c),k.style.opacity=1,c=setTimeout(()=>{k.style.opacity=0,k.style.transitionDuration="400ms"},1e3))}function w(L){!e.params.scrollbar.el||!e.scrollbar.el||(e.scrollbar.dragEl.style.transitionDuration=`${L}ms`)}function b(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;const{scrollbar:L}=e,{dragEl:I,el:h}=L;I.style.width="",I.style.height="",o=e.isHorizontal()?h.offsetWidth:h.offsetHeight,p=e.size/(e.virtualSize+e.params.slidesOffsetBefore-(e.params.centeredSlides?e.snapGrid[0]:0)),e.params.scrollbar.dragSize==="auto"?d=o*p:d=parseInt(e.params.scrollbar.dragSize,10),e.isHorizontal()?I.style.width=`${d}px`:I.style.height=`${d}px`,p>=1?h.style.display="none":h.style.display="",e.params.scrollbar.hide&&(h.style.opacity=0),e.params.watchOverflow&&e.enabled&&L.el.classList[e.isLocked?"add":"remove"](e.params.scrollbar.lockClass)}function g(L){return e.isHorizontal()?L.clientX:L.clientY}function E(L){const{scrollbar:I,rtlTranslate:h}=e,{el:k}=I;let H;H=(g(L)-Ss(k)[e.isHorizontal()?"left":"top"]-(u!==null?u:d/2))/(o-d),H=Math.max(Math.min(H,1),0),h&&(H=1-H);const R=e.minTranslate()+(e.maxTranslate()-e.minTranslate())*H;e.updateProgress(R),e.setTranslate(R),e.updateActiveIndex(),e.updateSlidesClasses()}function T(L){const I=e.params.scrollbar,{scrollbar:h,wrapperEl:k}=e,{el:H,dragEl:R}=h;l=!0,u=L.target===R?g(L)-L.target.getBoundingClientRect()[e.isHorizontal()?"left":"top"]:null,L.preventDefault(),L.stopPropagation(),k.style.transitionDuration="100ms",R.style.transitionDuration="100ms",E(L),clearTimeout(a),H.style.transitionDuration="0ms",I.hide&&(H.style.opacity=1),e.params.cssMode&&(e.wrapperEl.style["scroll-snap-type"]="none"),r("scrollbarDragStart",L)}function m(L){const{scrollbar:I,wrapperEl:h}=e,{el:k,dragEl:H}=I;l&&(L.preventDefault?L.preventDefault():L.returnValue=!1,E(L),h.style.transitionDuration="0ms",k.style.transitionDuration="0ms",H.style.transitionDuration="0ms",r("scrollbarDragMove",L))}function x(L){const I=e.params.scrollbar,{scrollbar:h,wrapperEl:k}=e,{el:H}=h;l&&(l=!1,e.params.cssMode&&(e.wrapperEl.style["scroll-snap-type"]="",k.style.transitionDuration=""),I.hide&&(clearTimeout(a),a=Mn(()=>{H.style.opacity=0,H.style.transitionDuration="400ms"},1e3)),r("scrollbarDragEnd",L),I.snapOnRelease&&e.slideToClosest())}function f(L){const{scrollbar:I,params:h}=e,k=I.el;if(!k)return;const H=k,R=h.passiveListeners?{passive:!1,capture:!1}:!1,F=h.passiveListeners?{passive:!0,capture:!1}:!1;if(!H)return;const Y=L==="on"?"addEventListener":"removeEventListener";H[Y]("pointerdown",T,R),i[Y]("pointermove",m,R),i[Y]("pointerup",x,F)}function A(){!e.params.scrollbar.el||!e.scrollbar.el||f("on")}function D(){!e.params.scrollbar.el||!e.scrollbar.el||f("off")}function V(){const{scrollbar:L,el:I}=e;e.params.scrollbar=Hr(e,e.originalParams.scrollbar,e.params.scrollbar,{el:"swiper-scrollbar"});const h=e.params.scrollbar;if(!h.el)return;let k;typeof h.el=="string"&&e.isElement&&(k=e.el.querySelector(h.el)),!k&&typeof h.el=="string"?k=i.querySelectorAll(h.el):k||(k=h.el),e.params.uniqueNavElements&&typeof h.el=="string"&&k.length>1&&I.querySelectorAll(h.el).length===1&&(k=I.querySelector(h.el)),k.length>0&&(k=k[0]),k.classList.add(e.isHorizontal()?h.horizontalClass:h.verticalClass);let H;k&&(H=k.querySelector(`.${e.params.scrollbar.dragClass}`),H||(H=Ht("div",e.params.scrollbar.dragClass),k.append(H))),Object.assign(L,{el:k,dragEl:H}),h.draggable&&A(),k&&k.classList[e.enabled?"remove":"add"](e.params.scrollbar.lockClass)}function O(){const L=e.params.scrollbar,I=e.scrollbar.el;I&&I.classList.remove(e.isHorizontal()?L.horizontalClass:L.verticalClass),D()}s("init",()=>{e.params.scrollbar.enabled===!1?N():(V(),b(),_())}),s("update resize observerUpdate lock unlock",()=>{b()}),s("setTranslate",()=>{_()}),s("setTransition",(L,I)=>{w(I)}),s("enable disable",()=>{const{el:L}=e.scrollbar;L&&L.classList[e.enabled?"remove":"add"](e.params.scrollbar.lockClass)}),s("destroy",()=>{O()});const q=()=>{e.el.classList.remove(e.params.scrollbar.scrollbarDisabledClass),e.scrollbar.el&&e.scrollbar.el.classList.remove(e.params.scrollbar.scrollbarDisabledClass),V(),b(),_()},N=()=>{e.el.classList.add(e.params.scrollbar.scrollbarDisabledClass),e.scrollbar.el&&e.scrollbar.el.classList.add(e.params.scrollbar.scrollbarDisabledClass),O()};Object.assign(e.scrollbar,{enable:q,disable:N,updateSize:b,setTranslate:_,init:V,destroy:O})}function Bc(t){let{swiper:e,extendParams:n,on:s}=t;n({parallax:{enabled:!1}});const r="[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]",i=(a,u)=>{const{rtl:d}=e,o=d?-1:1,p=a.getAttribute("data-swiper-parallax")||"0";let _=a.getAttribute("data-swiper-parallax-x"),w=a.getAttribute("data-swiper-parallax-y");const b=a.getAttribute("data-swiper-parallax-scale"),g=a.getAttribute("data-swiper-parallax-opacity"),E=a.getAttribute("data-swiper-parallax-rotate");if(_||w?(_=_||"0",w=w||"0"):e.isHorizontal()?(_=p,w="0"):(w=p,_="0"),_.indexOf("%")>=0?_=`${parseInt(_,10)*u*o}%`:_=`${_*u*o}px`,w.indexOf("%")>=0?w=`${parseInt(w,10)*u}%`:w=`${w*u}px`,typeof g<"u"&&g!==null){const m=g-(g-1)*(1-Math.abs(u));a.style.opacity=m}let T=`translate3d(${_}, ${w}, 0px)`;if(typeof b<"u"&&b!==null){const m=b-(b-1)*(1-Math.abs(u));T+=` scale(${m})`}if(E&&typeof E<"u"&&E!==null){const m=E*u*-1;T+=` rotate(${m}deg)`}a.style.transform=T},l=()=>{const{el:a,slides:u,progress:d,snapGrid:o,isElement:p}=e,_=ht(a,r);e.isElement&&_.push(...ht(e.hostEl,r)),_.forEach(w=>{i(w,d)}),u.forEach((w,b)=>{let g=w.progress;e.params.slidesPerGroup>1&&e.params.slidesPerView!=="auto"&&(g+=Math.ceil(b/2)-d*(o.length-1)),g=Math.min(Math.max(g,-1),1),w.querySelectorAll(`${r}, [data-swiper-parallax-rotate]`).forEach(E=>{i(E,g)})})},c=function(a){a===void 0&&(a=e.params.speed);const{el:u,hostEl:d}=e,o=[...u.querySelectorAll(r)];e.isElement&&o.push(...d.querySelectorAll(r)),o.forEach(p=>{let _=parseInt(p.getAttribute("data-swiper-parallax-duration"),10)||a;a===0&&(_=0),p.style.transitionDuration=`${_}ms`})};s("beforeInit",()=>{e.params.parallax.enabled&&(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)}),s("init",()=>{e.params.parallax.enabled&&l()}),s("setTranslate",()=>{e.params.parallax.enabled&&l()}),s("setTransition",(a,u)=>{e.params.parallax.enabled&&c(u)})}function Lc(t){let{swiper:e,extendParams:n,on:s,emit:r}=t;const i=it();n({zoom:{enabled:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),e.zoom={enabled:!1};let l=1,c=!1,a,u;const d=[],o={originX:0,originY:0,slideEl:void 0,slideWidth:void 0,slideHeight:void 0,imageEl:void 0,imageWrapEl:void 0,maxRatio:3},p={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},_={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0};let w=1;Object.defineProperty(e.zoom,"scale",{get(){return w},set(R){if(w!==R){const F=o.imageEl,Y=o.slideEl;r("zoomChange",R,F,Y)}w=R}});function b(){if(d.length<2)return 1;const R=d[0].pageX,F=d[0].pageY,Y=d[1].pageX,X=d[1].pageY;return Math.sqrt((Y-R)**2+(X-F)**2)}function g(){if(d.length<2)return{x:null,y:null};const R=o.imageEl.getBoundingClientRect();return[(d[0].pageX+(d[1].pageX-d[0].pageX)/2-R.x-i.scrollX)/l,(d[0].pageY+(d[1].pageY-d[0].pageY)/2-R.y-i.scrollY)/l]}function E(){return e.isElement?"swiper-slide":`.${e.params.slideClass}`}function T(R){const F=E();return!!(R.target.matches(F)||e.slides.filter(Y=>Y.contains(R.target)).length>0)}function m(R){const F=`.${e.params.zoom.containerClass}`;return!!(R.target.matches(F)||[...e.hostEl.querySelectorAll(F)].filter(Y=>Y.contains(R.target)).length>0)}function x(R){if(R.pointerType==="mouse"&&d.splice(0,d.length),!T(R))return;const F=e.params.zoom;if(a=!1,u=!1,d.push(R),!(d.length<2)){if(a=!0,o.scaleStart=b(),!o.slideEl){o.slideEl=R.target.closest(`.${e.params.slideClass}, swiper-slide`),o.slideEl||(o.slideEl=e.slides[e.activeIndex]);let Y=o.slideEl.querySelector(`.${F.containerClass}`);if(Y&&(Y=Y.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),o.imageEl=Y,Y?o.imageWrapEl=En(o.imageEl,`.${F.containerClass}`)[0]:o.imageWrapEl=void 0,!o.imageWrapEl){o.imageEl=void 0;return}o.maxRatio=o.imageWrapEl.getAttribute("data-swiper-zoom")||F.maxRatio}if(o.imageEl){const[Y,X]=g();o.originX=Y,o.originY=X,o.imageEl.style.transitionDuration="0ms"}c=!0}}function f(R){if(!T(R))return;const F=e.params.zoom,Y=e.zoom,X=d.findIndex(M=>M.pointerId===R.pointerId);X>=0&&(d[X]=R),!(d.length<2)&&(u=!0,o.scaleMove=b(),o.imageEl&&(Y.scale=o.scaleMove/o.scaleStart*l,Y.scale>o.maxRatio&&(Y.scale=o.maxRatio-1+(Y.scale-o.maxRatio+1)**.5),Y.scale<F.minRatio&&(Y.scale=F.minRatio+1-(F.minRatio-Y.scale+1)**.5),o.imageEl.style.transform=`translate3d(0,0,0) scale(${Y.scale})`))}function A(R){if(!T(R)||R.pointerType==="mouse"&&R.type==="pointerout")return;const F=e.params.zoom,Y=e.zoom,X=d.findIndex(M=>M.pointerId===R.pointerId);X>=0&&d.splice(X,1),!(!a||!u)&&(a=!1,u=!1,o.imageEl&&(Y.scale=Math.max(Math.min(Y.scale,o.maxRatio),F.minRatio),o.imageEl.style.transitionDuration=`${e.params.speed}ms`,o.imageEl.style.transform=`translate3d(0,0,0) scale(${Y.scale})`,l=Y.scale,c=!1,Y.scale>1&&o.slideEl?o.slideEl.classList.add(`${F.zoomedSlideClass}`):Y.scale<=1&&o.slideEl&&o.slideEl.classList.remove(`${F.zoomedSlideClass}`),Y.scale===1&&(o.originX=0,o.originY=0,o.slideEl=void 0)))}function D(R){const F=e.device;if(!o.imageEl||p.isTouched)return;F.android&&R.cancelable&&R.preventDefault(),p.isTouched=!0;const Y=d.length>0?d[0]:R;p.touchesStart.x=Y.pageX,p.touchesStart.y=Y.pageY}function V(R){if(!T(R)||!m(R))return;const F=e.zoom;if(!o.imageEl||!p.isTouched||!o.slideEl)return;p.isMoved||(p.width=o.imageEl.offsetWidth,p.height=o.imageEl.offsetHeight,p.startX=Tr(o.imageWrapEl,"x")||0,p.startY=Tr(o.imageWrapEl,"y")||0,o.slideWidth=o.slideEl.offsetWidth,o.slideHeight=o.slideEl.offsetHeight,o.imageWrapEl.style.transitionDuration="0ms");const Y=p.width*F.scale,X=p.height*F.scale;if(Y<o.slideWidth&&X<o.slideHeight)return;if(p.minX=Math.min(o.slideWidth/2-Y/2,0),p.maxX=-p.minX,p.minY=Math.min(o.slideHeight/2-X/2,0),p.maxY=-p.minY,p.touchesCurrent.x=d.length>0?d[0].pageX:R.pageX,p.touchesCurrent.y=d.length>0?d[0].pageY:R.pageY,Math.max(Math.abs(p.touchesCurrent.x-p.touchesStart.x),Math.abs(p.touchesCurrent.y-p.touchesStart.y))>5&&(e.allowClick=!1),!p.isMoved&&!c){if(e.isHorizontal()&&(Math.floor(p.minX)===Math.floor(p.startX)&&p.touchesCurrent.x<p.touchesStart.x||Math.floor(p.maxX)===Math.floor(p.startX)&&p.touchesCurrent.x>p.touchesStart.x)){p.isTouched=!1;return}if(!e.isHorizontal()&&(Math.floor(p.minY)===Math.floor(p.startY)&&p.touchesCurrent.y<p.touchesStart.y||Math.floor(p.maxY)===Math.floor(p.startY)&&p.touchesCurrent.y>p.touchesStart.y)){p.isTouched=!1;return}}R.cancelable&&R.preventDefault(),R.stopPropagation(),p.isMoved=!0;const y=(F.scale-l)/(o.maxRatio-e.params.zoom.minRatio),{originX:v,originY:C}=o;p.currentX=p.touchesCurrent.x-p.touchesStart.x+p.startX+y*(p.width-v*2),p.currentY=p.touchesCurrent.y-p.touchesStart.y+p.startY+y*(p.height-C*2),p.currentX<p.minX&&(p.currentX=p.minX+1-(p.minX-p.currentX+1)**.8),p.currentX>p.maxX&&(p.currentX=p.maxX-1+(p.currentX-p.maxX+1)**.8),p.currentY<p.minY&&(p.currentY=p.minY+1-(p.minY-p.currentY+1)**.8),p.currentY>p.maxY&&(p.currentY=p.maxY-1+(p.currentY-p.maxY+1)**.8),_.prevPositionX||(_.prevPositionX=p.touchesCurrent.x),_.prevPositionY||(_.prevPositionY=p.touchesCurrent.y),_.prevTime||(_.prevTime=Date.now()),_.x=(p.touchesCurrent.x-_.prevPositionX)/(Date.now()-_.prevTime)/2,_.y=(p.touchesCurrent.y-_.prevPositionY)/(Date.now()-_.prevTime)/2,Math.abs(p.touchesCurrent.x-_.prevPositionX)<2&&(_.x=0),Math.abs(p.touchesCurrent.y-_.prevPositionY)<2&&(_.y=0),_.prevPositionX=p.touchesCurrent.x,_.prevPositionY=p.touchesCurrent.y,_.prevTime=Date.now(),o.imageWrapEl.style.transform=`translate3d(${p.currentX}px, ${p.currentY}px,0)`}function O(){const R=e.zoom;if(!o.imageEl)return;if(!p.isTouched||!p.isMoved){p.isTouched=!1,p.isMoved=!1;return}p.isTouched=!1,p.isMoved=!1;let F=300,Y=300;const X=_.x*F,M=p.currentX+X,y=_.y*Y,v=p.currentY+y;_.x!==0&&(F=Math.abs((M-p.currentX)/_.x)),_.y!==0&&(Y=Math.abs((v-p.currentY)/_.y));const C=Math.max(F,Y);p.currentX=M,p.currentY=v;const $=p.width*R.scale,Z=p.height*R.scale;p.minX=Math.min(o.slideWidth/2-$/2,0),p.maxX=-p.minX,p.minY=Math.min(o.slideHeight/2-Z/2,0),p.maxY=-p.minY,p.currentX=Math.max(Math.min(p.currentX,p.maxX),p.minX),p.currentY=Math.max(Math.min(p.currentY,p.maxY),p.minY),o.imageWrapEl.style.transitionDuration=`${C}ms`,o.imageWrapEl.style.transform=`translate3d(${p.currentX}px, ${p.currentY}px,0)`}function q(){const R=e.zoom;o.slideEl&&e.activeIndex!==e.slides.indexOf(o.slideEl)&&(o.imageEl&&(o.imageEl.style.transform="translate3d(0,0,0) scale(1)"),o.imageWrapEl&&(o.imageWrapEl.style.transform="translate3d(0,0,0)"),o.slideEl.classList.remove(`${e.params.zoom.zoomedSlideClass}`),R.scale=1,l=1,o.slideEl=void 0,o.imageEl=void 0,o.imageWrapEl=void 0,o.originX=0,o.originY=0)}function N(R){const F=e.zoom,Y=e.params.zoom;if(!o.slideEl){R&&R.target&&(o.slideEl=R.target.closest(`.${e.params.slideClass}, swiper-slide`)),o.slideEl||(e.params.virtual&&e.params.virtual.enabled&&e.virtual?o.slideEl=ht(e.slidesEl,`.${e.params.slideActiveClass}`)[0]:o.slideEl=e.slides[e.activeIndex]);let S=o.slideEl.querySelector(`.${Y.containerClass}`);S&&(S=S.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),o.imageEl=S,S?o.imageWrapEl=En(o.imageEl,`.${Y.containerClass}`)[0]:o.imageWrapEl=void 0}if(!o.imageEl||!o.imageWrapEl)return;e.params.cssMode&&(e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.touchAction="none"),o.slideEl.classList.add(`${Y.zoomedSlideClass}`);let X,M,y,v,C,$,Z,se,j,ee,ce,me,ye,Me,Se,ie,ue,ze;typeof p.touchesStart.x>"u"&&R?(X=R.pageX,M=R.pageY):(X=p.touchesStart.x,M=p.touchesStart.y);const qe=typeof R=="number"?R:null;l===1&&qe&&(X=void 0,M=void 0),F.scale=qe||o.imageWrapEl.getAttribute("data-swiper-zoom")||Y.maxRatio,l=qe||o.imageWrapEl.getAttribute("data-swiper-zoom")||Y.maxRatio,R&&!(l===1&&qe)?(ue=o.slideEl.offsetWidth,ze=o.slideEl.offsetHeight,y=Ss(o.slideEl).left+i.scrollX,v=Ss(o.slideEl).top+i.scrollY,C=y+ue/2-X,$=v+ze/2-M,j=o.imageEl.offsetWidth,ee=o.imageEl.offsetHeight,ce=j*F.scale,me=ee*F.scale,ye=Math.min(ue/2-ce/2,0),Me=Math.min(ze/2-me/2,0),Se=-ye,ie=-Me,Z=C*F.scale,se=$*F.scale,Z<ye&&(Z=ye),Z>Se&&(Z=Se),se<Me&&(se=Me),se>ie&&(se=ie)):(Z=0,se=0),qe&&F.scale===1&&(o.originX=0,o.originY=0),o.imageWrapEl.style.transitionDuration="300ms",o.imageWrapEl.style.transform=`translate3d(${Z}px, ${se}px,0)`,o.imageEl.style.transitionDuration="300ms",o.imageEl.style.transform=`translate3d(0,0,0) scale(${F.scale})`}function L(){const R=e.zoom,F=e.params.zoom;if(!o.slideEl){e.params.virtual&&e.params.virtual.enabled&&e.virtual?o.slideEl=ht(e.slidesEl,`.${e.params.slideActiveClass}`)[0]:o.slideEl=e.slides[e.activeIndex];let Y=o.slideEl.querySelector(`.${F.containerClass}`);Y&&(Y=Y.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),o.imageEl=Y,Y?o.imageWrapEl=En(o.imageEl,`.${F.containerClass}`)[0]:o.imageWrapEl=void 0}!o.imageEl||!o.imageWrapEl||(e.params.cssMode&&(e.wrapperEl.style.overflow="",e.wrapperEl.style.touchAction=""),R.scale=1,l=1,o.imageWrapEl.style.transitionDuration="300ms",o.imageWrapEl.style.transform="translate3d(0,0,0)",o.imageEl.style.transitionDuration="300ms",o.imageEl.style.transform="translate3d(0,0,0) scale(1)",o.slideEl.classList.remove(`${F.zoomedSlideClass}`),o.slideEl=void 0,o.originX=0,o.originY=0)}function I(R){const F=e.zoom;F.scale&&F.scale!==1?L():N(R)}function h(){const R=e.params.passiveListeners?{passive:!0,capture:!1}:!1,F=e.params.passiveListeners?{passive:!1,capture:!0}:!0;return{passiveListener:R,activeListenerWithCapture:F}}function k(){const R=e.zoom;if(R.enabled)return;R.enabled=!0;const{passiveListener:F,activeListenerWithCapture:Y}=h();e.wrapperEl.addEventListener("pointerdown",x,F),e.wrapperEl.addEventListener("pointermove",f,Y),["pointerup","pointercancel","pointerout"].forEach(X=>{e.wrapperEl.addEventListener(X,A,F)}),e.wrapperEl.addEventListener("pointermove",V,Y)}function H(){const R=e.zoom;if(!R.enabled)return;R.enabled=!1;const{passiveListener:F,activeListenerWithCapture:Y}=h();e.wrapperEl.removeEventListener("pointerdown",x,F),e.wrapperEl.removeEventListener("pointermove",f,Y),["pointerup","pointercancel","pointerout"].forEach(X=>{e.wrapperEl.removeEventListener(X,A,F)}),e.wrapperEl.removeEventListener("pointermove",V,Y)}s("init",()=>{e.params.zoom.enabled&&k()}),s("destroy",()=>{H()}),s("touchStart",(R,F)=>{e.zoom.enabled&&D(F)}),s("touchEnd",(R,F)=>{e.zoom.enabled&&O()}),s("doubleTap",(R,F)=>{!e.animating&&e.params.zoom.enabled&&e.zoom.enabled&&e.params.zoom.toggle&&I(F)}),s("transitionEnd",()=>{e.zoom.enabled&&e.params.zoom.enabled&&q()}),s("slideChange",()=>{e.zoom.enabled&&e.params.zoom.enabled&&e.params.cssMode&&q()}),Object.assign(e.zoom,{enable:k,disable:H,in:N,out:L,toggle:I})}function Oc(t){let{swiper:e,extendParams:n,on:s}=t;n({controller:{control:void 0,inverse:!1,by:"slide"}}),e.controller={control:void 0};function r(u,d){const o=function(){let b,g,E;return(T,m)=>{for(g=-1,b=T.length;b-g>1;)E=b+g>>1,T[E]<=m?g=E:b=E;return b}}();this.x=u,this.y=d,this.lastIndex=u.length-1;let p,_;return this.interpolate=function(b){return b?(_=o(this.x,b),p=_-1,(b-this.x[p])*(this.y[_]-this.y[p])/(this.x[_]-this.x[p])+this.y[p]):0},this}function i(u){e.controller.spline=e.params.loop?new r(e.slidesGrid,u.slidesGrid):new r(e.snapGrid,u.snapGrid)}function l(u,d){const o=e.controller.control;let p,_;const w=e.constructor;function b(g){if(g.destroyed)return;const E=e.rtlTranslate?-e.translate:e.translate;e.params.controller.by==="slide"&&(i(g),_=-e.controller.spline.interpolate(-E)),(!_||e.params.controller.by==="container")&&(p=(g.maxTranslate()-g.minTranslate())/(e.maxTranslate()-e.minTranslate()),(Number.isNaN(p)||!Number.isFinite(p))&&(p=1),_=(E-e.minTranslate())*p+g.minTranslate()),e.params.controller.inverse&&(_=g.maxTranslate()-_),g.updateProgress(_),g.setTranslate(_,e),g.updateActiveIndex(),g.updateSlidesClasses()}if(Array.isArray(o))for(let g=0;g<o.length;g+=1)o[g]!==d&&o[g]instanceof w&&b(o[g]);else o instanceof w&&d!==o&&b(o)}function c(u,d){const o=e.constructor,p=e.controller.control;let _;function w(b){b.destroyed||(b.setTransition(u,e),u!==0&&(b.transitionStart(),b.params.autoHeight&&Mn(()=>{b.updateAutoHeight()}),Qn(b.wrapperEl,()=>{p&&b.transitionEnd()})))}if(Array.isArray(p))for(_=0;_<p.length;_+=1)p[_]!==d&&p[_]instanceof o&&w(p[_]);else p instanceof o&&d!==p&&w(p)}function a(){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)}s("beforeInit",()=>{if(typeof window<"u"&&(typeof e.params.controller.control=="string"||e.params.controller.control instanceof HTMLElement)){const u=document.querySelector(e.params.controller.control);if(u&&u.swiper)e.controller.control=u.swiper;else if(u){const d=o=>{e.controller.control=o.detail[0],e.update(),u.removeEventListener("init",d)};u.addEventListener("init",d)}return}e.controller.control=e.params.controller.control}),s("update",()=>{a()}),s("resize",()=>{a()}),s("observerUpdate",()=>{a()}),s("setTranslate",(u,d,o)=>{!e.controller.control||e.controller.control.destroyed||e.controller.setTranslate(d,o)}),s("setTransition",(u,d,o)=>{!e.controller.control||e.controller.control.destroyed||e.controller.setTransition(d,o)}),Object.assign(e.controller,{setTranslate:l,setTransition:c})}function Dc(t){let{swiper:e,extendParams:n,on:s}=t;n({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null}}),e.a11y={clicked:!1};let r=null;function i(h){const k=r;k.length!==0&&(k.innerHTML="",k.innerHTML=h)}const l=h=>(Array.isArray(h)?h:[h]).filter(k=>!!k);function c(h){h===void 0&&(h=16);const k=()=>Math.round(16*Math.random()).toString(16);return"x".repeat(h).replace(/x/g,k)}function a(h){h=l(h),h.forEach(k=>{k.setAttribute("tabIndex","0")})}function u(h){h=l(h),h.forEach(k=>{k.setAttribute("tabIndex","-1")})}function d(h,k){h=l(h),h.forEach(H=>{H.setAttribute("role",k)})}function o(h,k){h=l(h),h.forEach(H=>{H.setAttribute("aria-roledescription",k)})}function p(h,k){h=l(h),h.forEach(H=>{H.setAttribute("aria-controls",k)})}function _(h,k){h=l(h),h.forEach(H=>{H.setAttribute("aria-label",k)})}function w(h,k){h=l(h),h.forEach(H=>{H.setAttribute("id",k)})}function b(h,k){h=l(h),h.forEach(H=>{H.setAttribute("aria-live",k)})}function g(h){h=l(h),h.forEach(k=>{k.setAttribute("aria-disabled",!0)})}function E(h){h=l(h),h.forEach(k=>{k.setAttribute("aria-disabled",!1)})}function T(h){if(h.keyCode!==13&&h.keyCode!==32)return;const k=e.params.a11y,H=h.target;e.pagination&&e.pagination.el&&(H===e.pagination.el||e.pagination.el.contains(h.target))&&!h.target.matches(ln(e.params.pagination.bulletClass))||(e.navigation&&e.navigation.nextEl&&H===e.navigation.nextEl&&(e.isEnd&&!e.params.loop||e.slideNext(),e.isEnd?i(k.lastSlideMessage):i(k.nextSlideMessage)),e.navigation&&e.navigation.prevEl&&H===e.navigation.prevEl&&(e.isBeginning&&!e.params.loop||e.slidePrev(),e.isBeginning?i(k.firstSlideMessage):i(k.prevSlideMessage)),e.pagination&&H.matches(ln(e.params.pagination.bulletClass))&&H.click())}function m(){if(e.params.loop||e.params.rewind||!e.navigation)return;const{nextEl:h,prevEl:k}=e.navigation;k&&(e.isBeginning?(g(k),u(k)):(E(k),a(k))),h&&(e.isEnd?(g(h),u(h)):(E(h),a(h)))}function x(){return e.pagination&&e.pagination.bullets&&e.pagination.bullets.length}function f(){return x()&&e.params.pagination.clickable}function A(){const h=e.params.a11y;x()&&e.pagination.bullets.forEach(k=>{e.params.pagination.clickable&&(a(k),e.params.pagination.renderBullet||(d(k,"button"),_(k,h.paginationBulletMessage.replace(/\{\{index\}\}/,Jn(k)+1)))),k.matches(ln(e.params.pagination.bulletActiveClass))?k.setAttribute("aria-current","true"):k.removeAttribute("aria-current")})}const D=(h,k,H)=>{a(h),h.tagName!=="BUTTON"&&(d(h,"button"),h.addEventListener("keydown",T)),_(h,H),p(h,k)},V=()=>{e.a11y.clicked=!0},O=()=>{requestAnimationFrame(()=>{requestAnimationFrame(()=>{e.destroyed||(e.a11y.clicked=!1)})})},q=h=>{if(e.a11y.clicked)return;const k=h.target.closest(`.${e.params.slideClass}, swiper-slide`);if(!k||!e.slides.includes(k))return;const H=e.slides.indexOf(k)===e.activeIndex,R=e.params.watchSlidesProgress&&e.visibleSlides&&e.visibleSlides.includes(k);H||R||h.sourceCapabilities&&h.sourceCapabilities.firesTouchEvents||(e.isHorizontal()?e.el.scrollLeft=0:e.el.scrollTop=0,e.slideTo(e.slides.indexOf(k),0))},N=()=>{const h=e.params.a11y;h.itemRoleDescriptionMessage&&o(e.slides,h.itemRoleDescriptionMessage),h.slideRole&&d(e.slides,h.slideRole);const k=e.slides.length;h.slideLabelMessage&&e.slides.forEach((H,R)=>{const F=e.params.loop?parseInt(H.getAttribute("data-swiper-slide-index"),10):R,Y=h.slideLabelMessage.replace(/\{\{index\}\}/,F+1).replace(/\{\{slidesLength\}\}/,k);_(H,Y)})},L=()=>{const h=e.params.a11y;e.el.append(r);const k=e.el;h.containerRoleDescriptionMessage&&o(k,h.containerRoleDescriptionMessage),h.containerMessage&&_(k,h.containerMessage);const H=e.wrapperEl,R=h.id||H.getAttribute("id")||`swiper-wrapper-${c(16)}`,F=e.params.autoplay&&e.params.autoplay.enabled?"off":"polite";w(H,R),b(H,F),N();let{nextEl:Y,prevEl:X}=e.navigation?e.navigation:{};Y=l(Y),X=l(X),Y&&Y.forEach(M=>D(M,R,h.nextSlideMessage)),X&&X.forEach(M=>D(M,R,h.prevSlideMessage)),f()&&(Array.isArray(e.pagination.el)?e.pagination.el:[e.pagination.el]).forEach(y=>{y.addEventListener("keydown",T)}),e.el.addEventListener("focus",q,!0),e.el.addEventListener("pointerdown",V,!0),e.el.addEventListener("pointerup",O,!0)};function I(){r&&r.remove();let{nextEl:h,prevEl:k}=e.navigation?e.navigation:{};h=l(h),k=l(k),h&&h.forEach(H=>H.removeEventListener("keydown",T)),k&&k.forEach(H=>H.removeEventListener("keydown",T)),f()&&(Array.isArray(e.pagination.el)?e.pagination.el:[e.pagination.el]).forEach(R=>{R.removeEventListener("keydown",T)}),e.el.removeEventListener("focus",q,!0),e.el.removeEventListener("pointerdown",V,!0),e.el.removeEventListener("pointerup",O,!0)}s("beforeInit",()=>{r=Ht("span",e.params.a11y.notificationClass),r.setAttribute("aria-live","assertive"),r.setAttribute("aria-atomic","true")}),s("afterInit",()=>{e.params.a11y.enabled&&L()}),s("slidesLengthChange snapGridLengthChange slidesGridLengthChange",()=>{e.params.a11y.enabled&&N()}),s("fromEdge toEdge afterInit lock unlock",()=>{e.params.a11y.enabled&&m()}),s("paginationUpdate",()=>{e.params.a11y.enabled&&A()}),s("destroy",()=>{e.params.a11y.enabled&&I()})}function Nc(t){let{swiper:e,extendParams:n,on:s}=t;n({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}});let r=!1,i={};const l=_=>_.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,""),c=_=>{const w=it();let b;_?b=new URL(_):b=w.location;const g=b.pathname.slice(1).split("/").filter(x=>x!==""),E=g.length,T=g[E-2],m=g[E-1];return{key:T,value:m}},a=(_,w)=>{const b=it();if(!r||!e.params.history.enabled)return;let g;e.params.url?g=new URL(e.params.url):g=b.location;const E=e.slides[w];let T=l(E.getAttribute("data-history"));if(e.params.history.root.length>0){let x=e.params.history.root;x[x.length-1]==="/"&&(x=x.slice(0,x.length-1)),T=`${x}/${_?`${_}/`:""}${T}`}else g.pathname.includes(_)||(T=`${_?`${_}/`:""}${T}`);e.params.history.keepQuery&&(T+=g.search);const m=b.history.state;m&&m.value===T||(e.params.history.replaceState?b.history.replaceState({value:T},null,T):b.history.pushState({value:T},null,T))},u=(_,w,b)=>{if(w)for(let g=0,E=e.slides.length;g<E;g+=1){const T=e.slides[g];if(l(T.getAttribute("data-history"))===w){const x=e.getSlideIndex(T);e.slideTo(x,_,b)}}else e.slideTo(0,_,b)},d=()=>{i=c(e.params.url),u(e.params.speed,i.value,!1)},o=()=>{const _=it();if(e.params.history){if(!_.history||!_.history.pushState){e.params.history.enabled=!1,e.params.hashNavigation.enabled=!0;return}if(r=!0,i=c(e.params.url),!i.key&&!i.value){e.params.history.replaceState||_.addEventListener("popstate",d);return}u(0,i.value,e.params.runCallbacksOnInit),e.params.history.replaceState||_.addEventListener("popstate",d)}},p=()=>{const _=it();e.params.history.replaceState||_.removeEventListener("popstate",d)};s("init",()=>{e.params.history.enabled&&o()}),s("destroy",()=>{e.params.history.enabled&&p()}),s("transitionEnd _freeModeNoMomentumRelease",()=>{r&&a(e.params.history.key,e.activeIndex)}),s("slideChange",()=>{r&&e.params.cssMode&&a(e.params.history.key,e.activeIndex)})}function zc(t){let{swiper:e,extendParams:n,emit:s,on:r}=t,i=!1;const l=gt(),c=it();n({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1,getSlideIndex(p,_){if(e.virtual&&e.params.virtual.enabled){const w=e.slides.filter(g=>g.getAttribute("data-hash")===_)[0];return w?parseInt(w.getAttribute("data-swiper-slide-index"),10):0}return e.getSlideIndex(ht(e.slidesEl,`.${e.params.slideClass}[data-hash="${_}"], swiper-slide[data-hash="${_}"]`)[0])}}});const a=()=>{s("hashChange");const p=l.location.hash.replace("#",""),_=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex],w=_?_.getAttribute("data-hash"):"";if(p!==w){const b=e.params.hashNavigation.getSlideIndex(e,p);if(typeof b>"u"||Number.isNaN(b))return;e.slideTo(b)}},u=()=>{if(!i||!e.params.hashNavigation.enabled)return;const p=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex],_=p?p.getAttribute("data-hash")||p.getAttribute("data-history"):"";e.params.hashNavigation.replaceState&&c.history&&c.history.replaceState?(c.history.replaceState(null,null,`#${_}`||""),s("hashSet")):(l.location.hash=_||"",s("hashSet"))},d=()=>{if(!e.params.hashNavigation.enabled||e.params.history&&e.params.history.enabled)return;i=!0;const p=l.location.hash.replace("#","");if(p){const w=e.params.hashNavigation.getSlideIndex(e,p);e.slideTo(w||0,0,e.params.runCallbacksOnInit,!0)}e.params.hashNavigation.watchState&&c.addEventListener("hashchange",a)},o=()=>{e.params.hashNavigation.watchState&&c.removeEventListener("hashchange",a)};r("init",()=>{e.params.hashNavigation.enabled&&d()}),r("destroy",()=>{e.params.hashNavigation.enabled&&o()}),r("transitionEnd _freeModeNoMomentumRelease",()=>{i&&u()}),r("slideChange",()=>{i&&e.params.cssMode&&u()})}function $c(t){let{swiper:e,extendParams:n,on:s,emit:r,params:i}=t;e.autoplay={running:!1,paused:!1,timeLeft:0},n({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let l,c,a=i&&i.autoplay?i.autoplay.delay:3e3,u=i&&i.autoplay?i.autoplay.delay:3e3,d,o=new Date().getTime,p,_,w,b,g,E;function T(R){!e||e.destroyed||!e.wrapperEl||R.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",T),O())}const m=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?p=!0:p&&(u=d,p=!1);const R=e.autoplay.paused?d:o+u-new Date().getTime();e.autoplay.timeLeft=R,r("autoplayTimeLeft",R,R/a),c=requestAnimationFrame(()=>{m()})},x=()=>{let R;return e.virtual&&e.params.virtual.enabled?R=e.slides.filter(Y=>Y.classList.contains("swiper-slide-active"))[0]:R=e.slides[e.activeIndex],R?parseInt(R.getAttribute("data-swiper-autoplay"),10):void 0},f=R=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(c),m();let F=typeof R>"u"?e.params.autoplay.delay:R;a=e.params.autoplay.delay,u=e.params.autoplay.delay;const Y=x();!Number.isNaN(Y)&&Y>0&&typeof R>"u"&&(F=Y,a=Y,u=Y),d=F;const X=e.params.speed,M=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(X,!0,!0),r("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,X,!0,!0),r("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(X,!0,!0),r("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,X,!0,!0),r("autoplay")),e.params.cssMode&&(o=new Date().getTime(),requestAnimationFrame(()=>{f()})))};return F>0?(clearTimeout(l),l=setTimeout(()=>{M()},F)):requestAnimationFrame(()=>{M()}),F},A=()=>{e.autoplay.running=!0,f(),r("autoplayStart")},D=()=>{e.autoplay.running=!1,clearTimeout(l),cancelAnimationFrame(c),r("autoplayStop")},V=(R,F)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(l),R||(E=!0);const Y=()=>{r("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",T):O()};if(e.autoplay.paused=!0,F){g&&(d=e.params.autoplay.delay),g=!1,Y();return}d=(d||e.params.autoplay.delay)-(new Date().getTime()-o),!(e.isEnd&&d<0&&!e.params.loop)&&(d<0&&(d=0),Y())},O=()=>{e.isEnd&&d<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(o=new Date().getTime(),E?(E=!1,f(d)):f(),e.autoplay.paused=!1,r("autoplayResume"))},q=()=>{if(e.destroyed||!e.autoplay.running)return;const R=gt();R.visibilityState==="hidden"&&(E=!0,V(!0)),R.visibilityState==="visible"&&O()},N=R=>{R.pointerType==="mouse"&&(E=!0,!(e.animating||e.autoplay.paused)&&V(!0))},L=R=>{R.pointerType==="mouse"&&e.autoplay.paused&&O()},I=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",N),e.el.addEventListener("pointerleave",L))},h=()=>{e.el.removeEventListener("pointerenter",N),e.el.removeEventListener("pointerleave",L)},k=()=>{gt().addEventListener("visibilitychange",q)},H=()=>{gt().removeEventListener("visibilitychange",q)};s("init",()=>{e.params.autoplay.enabled&&(I(),k(),o=new Date().getTime(),A())}),s("destroy",()=>{h(),H(),e.autoplay.running&&D()}),s("beforeTransitionStart",(R,F,Y)=>{e.destroyed||!e.autoplay.running||(Y||!e.params.autoplay.disableOnInteraction?V(!0,!0):D())}),s("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){D();return}_=!0,w=!1,E=!1,b=setTimeout(()=>{E=!0,w=!0,V(!0)},200)}}),s("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!_)){if(clearTimeout(b),clearTimeout(l),e.params.autoplay.disableOnInteraction){w=!1,_=!1;return}w&&e.params.cssMode&&O(),w=!1,_=!1}}),s("slideChange",()=>{e.destroyed||!e.autoplay.running||(g=!0)}),Object.assign(e.autoplay,{start:A,stop:D,pause:V,resume:O})}function Vc(t){let{swiper:e,extendParams:n,on:s}=t;n({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let r=!1,i=!1;e.thumbs={swiper:null};function l(){const u=e.thumbs.swiper;if(!u||u.destroyed)return;const d=u.clickedIndex,o=u.clickedSlide;if(o&&o.classList.contains(e.params.thumbs.slideThumbActiveClass)||typeof d>"u"||d===null)return;let p;u.params.loop?p=parseInt(u.clickedSlide.getAttribute("data-swiper-slide-index"),10):p=d,e.params.loop?e.slideToLoop(p):e.slideTo(p)}function c(){const{thumbs:u}=e.params;if(r)return!1;r=!0;const d=e.constructor;if(u.swiper instanceof d)e.thumbs.swiper=u.swiper,Object.assign(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper.update();else if(Un(u.swiper)){const o=Object.assign({},u.swiper);Object.assign(o,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper=new d(o),i=!0}return e.thumbs.swiper.el.classList.add(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",l),!0}function a(u){const d=e.thumbs.swiper;if(!d||d.destroyed)return;const o=d.params.slidesPerView==="auto"?d.slidesPerViewDynamic():d.params.slidesPerView;let p=1;const _=e.params.thumbs.slideThumbActiveClass;if(e.params.slidesPerView>1&&!e.params.centeredSlides&&(p=e.params.slidesPerView),e.params.thumbs.multipleActiveThumbs||(p=1),p=Math.floor(p),d.slides.forEach(g=>g.classList.remove(_)),d.params.loop||d.params.virtual&&d.params.virtual.enabled)for(let g=0;g<p;g+=1)ht(d.slidesEl,`[data-swiper-slide-index="${e.realIndex+g}"]`).forEach(E=>{E.classList.add(_)});else for(let g=0;g<p;g+=1)d.slides[e.realIndex+g]&&d.slides[e.realIndex+g].classList.add(_);const w=e.params.thumbs.autoScrollOffset,b=w&&!d.params.loop;if(e.realIndex!==d.realIndex||b){const g=d.activeIndex;let E,T;if(d.params.loop){const m=d.slides.filter(x=>x.getAttribute("data-swiper-slide-index")===`${e.realIndex}`)[0];E=d.slides.indexOf(m),T=e.activeIndex>e.previousIndex?"next":"prev"}else E=e.realIndex,T=E>e.previousIndex?"next":"prev";b&&(E+=T==="next"?w:-1*w),d.visibleSlidesIndexes&&d.visibleSlidesIndexes.indexOf(E)<0&&(d.params.centeredSlides?E>g?E=E-Math.floor(o/2)+1:E=E+Math.floor(o/2)-1:E>g&&d.params.slidesPerGroup,d.slideTo(E,u?0:void 0))}}s("beforeInit",()=>{const{thumbs:u}=e.params;if(!(!u||!u.swiper))if(typeof u.swiper=="string"||u.swiper instanceof HTMLElement){const d=gt(),o=()=>{const _=typeof u.swiper=="string"?d.querySelector(u.swiper):u.swiper;if(_&&_.swiper)u.swiper=_.swiper,c(),a(!0);else if(_){const w=b=>{u.swiper=b.detail[0],_.removeEventListener("init",w),c(),a(!0),u.swiper.update(),e.update()};_.addEventListener("init",w)}return _},p=()=>{if(e.destroyed)return;o()||requestAnimationFrame(p)};requestAnimationFrame(p)}else c(),a(!0)}),s("slideChange update resize observerUpdate",()=>{a()}),s("setTransition",(u,d)=>{const o=e.thumbs.swiper;!o||o.destroyed||o.setTransition(d)}),s("beforeDestroy",()=>{const u=e.thumbs.swiper;!u||u.destroyed||i&&u.destroy()}),Object.assign(e.thumbs,{init:c,update:a})}function Hc(t){let{swiper:e,extendParams:n,emit:s,once:r}=t;n({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}});function i(){if(e.params.cssMode)return;const a=e.getTranslate();e.setTranslate(a),e.setTransition(0),e.touchEventsData.velocities.length=0,e.freeMode.onTouchEnd({currentPos:e.rtl?e.translate:-e.translate})}function l(){if(e.params.cssMode)return;const{touchEventsData:a,touches:u}=e;a.velocities.length===0&&a.velocities.push({position:u[e.isHorizontal()?"startX":"startY"],time:a.touchStartTime}),a.velocities.push({position:u[e.isHorizontal()?"currentX":"currentY"],time:Ot()})}function c(a){let{currentPos:u}=a;if(e.params.cssMode)return;const{params:d,wrapperEl:o,rtlTranslate:p,snapGrid:_,touchEventsData:w}=e,g=Ot()-w.touchStartTime;if(u<-e.minTranslate()){e.slideTo(e.activeIndex);return}if(u>-e.maxTranslate()){e.slides.length<_.length?e.slideTo(_.length-1):e.slideTo(e.slides.length-1);return}if(d.freeMode.momentum){if(w.velocities.length>1){const V=w.velocities.pop(),O=w.velocities.pop(),q=V.position-O.position,N=V.time-O.time;e.velocity=q/N,e.velocity/=2,Math.abs(e.velocity)<d.freeMode.minimumVelocity&&(e.velocity=0),(N>150||Ot()-V.time>300)&&(e.velocity=0)}else e.velocity=0;e.velocity*=d.freeMode.momentumVelocityRatio,w.velocities.length=0;let E=1e3*d.freeMode.momentumRatio;const T=e.velocity*E;let m=e.translate+T;p&&(m=-m);let x=!1,f;const A=Math.abs(e.velocity)*20*d.freeMode.momentumBounceRatio;let D;if(m<e.maxTranslate())d.freeMode.momentumBounce?(m+e.maxTranslate()<-A&&(m=e.maxTranslate()-A),f=e.maxTranslate(),x=!0,w.allowMomentumBounce=!0):m=e.maxTranslate(),d.loop&&d.centeredSlides&&(D=!0);else if(m>e.minTranslate())d.freeMode.momentumBounce?(m-e.minTranslate()>A&&(m=e.minTranslate()+A),f=e.minTranslate(),x=!0,w.allowMomentumBounce=!0):m=e.minTranslate(),d.loop&&d.centeredSlides&&(D=!0);else if(d.freeMode.sticky){let V;for(let O=0;O<_.length;O+=1)if(_[O]>-m){V=O;break}Math.abs(_[V]-m)<Math.abs(_[V-1]-m)||e.swipeDirection==="next"?m=_[V]:m=_[V-1],m=-m}if(D&&r("transitionEnd",()=>{e.loopFix()}),e.velocity!==0){if(p?E=Math.abs((-m-e.translate)/e.velocity):E=Math.abs((m-e.translate)/e.velocity),d.freeMode.sticky){const V=Math.abs((p?-m:m)-e.translate),O=e.slidesSizesGrid[e.activeIndex];V<O?E=d.speed:V<2*O?E=d.speed*1.5:E=d.speed*2.5}}else if(d.freeMode.sticky){e.slideToClosest();return}d.freeMode.momentumBounce&&x?(e.updateProgress(f),e.setTransition(E),e.setTranslate(m),e.transitionStart(!0,e.swipeDirection),e.animating=!0,Qn(o,()=>{!e||e.destroyed||!w.allowMomentumBounce||(s("momentumBounce"),e.setTransition(d.speed),setTimeout(()=>{e.setTranslate(f),Qn(o,()=>{!e||e.destroyed||e.transitionEnd()})},0))})):e.velocity?(s("_freeModeNoMomentumRelease"),e.updateProgress(m),e.setTransition(E),e.setTranslate(m),e.transitionStart(!0,e.swipeDirection),e.animating||(e.animating=!0,Qn(o,()=>{!e||e.destroyed||e.transitionEnd()}))):e.updateProgress(m),e.updateActiveIndex(),e.updateSlidesClasses()}else if(d.freeMode.sticky){e.slideToClosest();return}else d.freeMode&&s("_freeModeNoMomentumRelease");(!d.freeMode.momentum||g>=d.longSwipesMs)&&(e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses())}Object.assign(e,{freeMode:{onTouchStart:i,onTouchMove:l,onTouchEnd:c}})}function Xc(t){let{swiper:e,extendParams:n,on:s}=t;n({grid:{rows:1,fill:"column"}});let r,i,l,c;const a=()=>{let w=e.params.spaceBetween;return typeof w=="string"&&w.indexOf("%")>=0?w=parseFloat(w.replace("%",""))/100*e.size:typeof w=="string"&&(w=parseFloat(w)),w},u=w=>{const{slidesPerView:b}=e.params,{rows:g,fill:E}=e.params.grid;l=Math.floor(w/g),Math.floor(w/g)===w/g?r=w:r=Math.ceil(w/g)*g,b!=="auto"&&E==="row"&&(r=Math.max(r,b*g)),i=r/g},d=(w,b,g,E)=>{const{slidesPerGroup:T}=e.params,m=a(),{rows:x,fill:f}=e.params.grid;let A,D,V;if(f==="row"&&T>1){const O=Math.floor(w/(T*x)),q=w-x*T*O,N=O===0?T:Math.min(Math.ceil((g-O*x*T)/x),T);V=Math.floor(q/N),D=q-V*N+O*T,A=D+V*r/x,b.style.order=A}else f==="column"?(D=Math.floor(w/x),V=w-D*x,(D>l||D===l&&V===x-1)&&(V+=1,V>=x&&(V=0,D+=1))):(V=Math.floor(w/i),D=w-V*i);b.row=V,b.column=D,b.style[E("margin-top")]=V!==0?m&&`${m}px`:""},o=(w,b,g)=>{const{centeredSlides:E,roundLengths:T}=e.params,m=a(),{rows:x}=e.params.grid;if(e.virtualSize=(w+m)*r,e.virtualSize=Math.ceil(e.virtualSize/x)-m,e.wrapperEl.style[g("width")]=`${e.virtualSize+m}px`,E){const f=[];for(let A=0;A<b.length;A+=1){let D=b[A];T&&(D=Math.floor(D)),b[A]<e.virtualSize+b[0]&&f.push(D)}b.splice(0,b.length),b.push(...f)}},p=()=>{c=e.params.grid&&e.params.grid.rows>1},_=()=>{const{params:w,el:b}=e,g=w.grid&&w.grid.rows>1;c&&!g?(b.classList.remove(`${w.containerModifierClass}grid`,`${w.containerModifierClass}grid-column`),l=1,e.emitContainerClasses()):!c&&g&&(b.classList.add(`${w.containerModifierClass}grid`),w.grid.fill==="column"&&b.classList.add(`${w.containerModifierClass}grid-column`),e.emitContainerClasses()),c=g};s("init",p),s("update",_),e.grid={initSlides:u,updateSlide:d,updateWrapperSize:o}}function Fc(t){const e=this,{params:n,slidesEl:s}=e;n.loop&&e.loopDestroy();const r=i=>{if(typeof i=="string"){const l=document.createElement("div");l.innerHTML=i,s.append(l.children[0]),l.innerHTML=""}else s.append(i)};if(typeof t=="object"&&"length"in t)for(let i=0;i<t.length;i+=1)t[i]&&r(t[i]);else r(t);e.recalcSlides(),n.loop&&e.loopCreate(),(!n.observer||e.isElement)&&e.update()}function Yc(t){const e=this,{params:n,activeIndex:s,slidesEl:r}=e;n.loop&&e.loopDestroy();let i=s+1;const l=c=>{if(typeof c=="string"){const a=document.createElement("div");a.innerHTML=c,r.prepend(a.children[0]),a.innerHTML=""}else r.prepend(c)};if(typeof t=="object"&&"length"in t){for(let c=0;c<t.length;c+=1)t[c]&&l(t[c]);i=s+t.length}else l(t);e.recalcSlides(),n.loop&&e.loopCreate(),(!n.observer||e.isElement)&&e.update(),e.slideTo(i,0,!1)}function Wc(t,e){const n=this,{params:s,activeIndex:r,slidesEl:i}=n;let l=r;s.loop&&(l-=n.loopedSlides,n.loopDestroy(),n.recalcSlides());const c=n.slides.length;if(t<=0){n.prependSlide(e);return}if(t>=c){n.appendSlide(e);return}let a=l>t?l+1:l;const u=[];for(let d=c-1;d>=t;d-=1){const o=n.slides[d];o.remove(),u.unshift(o)}if(typeof e=="object"&&"length"in e){for(let d=0;d<e.length;d+=1)e[d]&&i.append(e[d]);a=l>t?l+e.length:l}else i.append(e);for(let d=0;d<u.length;d+=1)i.append(u[d]);n.recalcSlides(),s.loop&&n.loopCreate(),(!s.observer||n.isElement)&&n.update(),s.loop?n.slideTo(a+n.loopedSlides,0,!1):n.slideTo(a,0,!1)}function Gc(t){const e=this,{params:n,activeIndex:s}=e;let r=s;n.loop&&(r-=e.loopedSlides,e.loopDestroy());let i=r,l;if(typeof t=="object"&&"length"in t){for(let c=0;c<t.length;c+=1)l=t[c],e.slides[l]&&e.slides[l].remove(),l<i&&(i-=1);i=Math.max(i,0)}else l=t,e.slides[l]&&e.slides[l].remove(),l<i&&(i-=1),i=Math.max(i,0);e.recalcSlides(),n.loop&&e.loopCreate(),(!n.observer||e.isElement)&&e.update(),n.loop?e.slideTo(i+e.loopedSlides,0,!1):e.slideTo(i,0,!1)}function jc(){const t=this,e=[];for(let n=0;n<t.slides.length;n+=1)e.push(n);t.removeSlide(e)}function qc(t){let{swiper:e}=t;Object.assign(e,{appendSlide:Fc.bind(e),prependSlide:Yc.bind(e),addSlide:Wc.bind(e),removeSlide:Gc.bind(e),removeAllSlides:jc.bind(e)})}function Vn(t){const{effect:e,swiper:n,on:s,setTranslate:r,setTransition:i,overwriteParams:l,perspective:c,recreateShadows:a,getEffectParams:u}=t;s("beforeInit",()=>{if(n.params.effect!==e)return;n.classNames.push(`${n.params.containerModifierClass}${e}`),c&&c()&&n.classNames.push(`${n.params.containerModifierClass}3d`);const o=l?l():{};Object.assign(n.params,o),Object.assign(n.originalParams,o)}),s("setTranslate",()=>{n.params.effect===e&&r()}),s("setTransition",(o,p)=>{n.params.effect===e&&i(p)}),s("transitionEnd",()=>{if(n.params.effect===e&&a){if(!u||!u().slideShadows)return;n.slides.forEach(o=>{o.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(p=>p.remove())}),a()}});let d;s("virtualUpdate",()=>{n.params.effect===e&&(n.slides.length||(d=!0),requestAnimationFrame(()=>{d&&n.slides&&n.slides.length&&(r(),d=!1)}))})}function rs(t,e){const n=Cn(e);return n!==e&&(n.style.backfaceVisibility="hidden",n.style["-webkit-backface-visibility"]="hidden"),n}function Ls(t){let{swiper:e,duration:n,transformElements:s,allSlides:r}=t;const{activeIndex:i}=e,l=c=>c.parentElement?c.parentElement:e.slides.filter(u=>u.shadowRoot&&u.shadowRoot===c.parentNode)[0];if(e.params.virtualTranslate&&n!==0){let c=!1,a;r?a=s:a=s.filter(u=>{const d=u.classList.contains("swiper-slide-transform")?l(u):u;return e.getSlideIndex(d)===i}),a.forEach(u=>{Qn(u,()=>{if(c||!e||e.destroyed)return;c=!0,e.animating=!1;const d=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});e.wrapperEl.dispatchEvent(d)})})}}function Uc(t){let{swiper:e,extendParams:n,on:s}=t;n({fadeEffect:{crossFade:!1}}),Vn({effect:"fade",swiper:e,on:s,setTranslate:()=>{const{slides:l}=e,c=e.params.fadeEffect;for(let a=0;a<l.length;a+=1){const u=e.slides[a];let o=-u.swiperSlideOffset;e.params.virtualTranslate||(o-=e.translate);let p=0;e.isHorizontal()||(p=o,o=0);const _=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(u.progress),0):1+Math.min(Math.max(u.progress,-1),0),w=rs(c,u);w.style.opacity=_,w.style.transform=`translate3d(${o}px, ${p}px, 0px)`}},setTransition:l=>{const c=e.slides.map(a=>Cn(a));c.forEach(a=>{a.style.transitionDuration=`${l}ms`}),Ls({swiper:e,duration:l,transformElements:c,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})}function Zc(t){let{swiper:e,extendParams:n,on:s}=t;n({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});const r=(a,u,d)=>{let o=d?a.querySelector(".swiper-slide-shadow-left"):a.querySelector(".swiper-slide-shadow-top"),p=d?a.querySelector(".swiper-slide-shadow-right"):a.querySelector(".swiper-slide-shadow-bottom");o||(o=Ht("div",`swiper-slide-shadow-cube swiper-slide-shadow-${d?"left":"top"}`.split(" ")),a.append(o)),p||(p=Ht("div",`swiper-slide-shadow-cube swiper-slide-shadow-${d?"right":"bottom"}`.split(" ")),a.append(p)),o&&(o.style.opacity=Math.max(-u,0)),p&&(p.style.opacity=Math.max(u,0))};Vn({effect:"cube",swiper:e,on:s,setTranslate:()=>{const{el:a,wrapperEl:u,slides:d,width:o,height:p,rtlTranslate:_,size:w,browser:b}=e,g=e.params.cubeEffect,E=e.isHorizontal(),T=e.virtual&&e.params.virtual.enabled;let m=0,x;g.shadow&&(E?(x=e.wrapperEl.querySelector(".swiper-cube-shadow"),x||(x=Ht("div","swiper-cube-shadow"),e.wrapperEl.append(x)),x.style.height=`${o}px`):(x=a.querySelector(".swiper-cube-shadow"),x||(x=Ht("div","swiper-cube-shadow"),a.append(x))));for(let A=0;A<d.length;A+=1){const D=d[A];let V=A;T&&(V=parseInt(D.getAttribute("data-swiper-slide-index"),10));let O=V*90,q=Math.floor(O/360);_&&(O=-O,q=Math.floor(-O/360));const N=Math.max(Math.min(D.progress,1),-1);let L=0,I=0,h=0;V%4===0?(L=-q*4*w,h=0):(V-1)%4===0?(L=0,h=-q*4*w):(V-2)%4===0?(L=w+q*4*w,h=w):(V-3)%4===0&&(L=-w,h=3*w+w*4*q),_&&(L=-L),E||(I=L,L=0);const k=`rotateX(${E?0:-O}deg) rotateY(${E?O:0}deg) translate3d(${L}px, ${I}px, ${h}px)`;N<=1&&N>-1&&(m=V*90+N*90,_&&(m=-V*90-N*90)),D.style.transform=k,g.slideShadows&&r(D,N,E)}if(u.style.transformOrigin=`50% 50% -${w/2}px`,u.style["-webkit-transform-origin"]=`50% 50% -${w/2}px`,g.shadow)if(E)x.style.transform=`translate3d(0px, ${o/2+g.shadowOffset}px, ${-o/2}px) rotateX(90deg) rotateZ(0deg) scale(${g.shadowScale})`;else{const A=Math.abs(m)-Math.floor(Math.abs(m)/90)*90,D=1.5-(Math.sin(A*2*Math.PI/360)/2+Math.cos(A*2*Math.PI/360)/2),V=g.shadowScale,O=g.shadowScale/D,q=g.shadowOffset;x.style.transform=`scale3d(${V}, 1, ${O}) translate3d(0px, ${p/2+q}px, ${-p/2/O}px) rotateX(-90deg)`}const f=(b.isSafari||b.isWebView)&&b.needPerspectiveFix?-w/2:0;u.style.transform=`translate3d(0px,0,${f}px) rotateX(${e.isHorizontal()?0:m}deg) rotateY(${e.isHorizontal()?-m:0}deg)`,u.style.setProperty("--swiper-cube-translate-z",`${f}px`)},setTransition:a=>{const{el:u,slides:d}=e;if(d.forEach(o=>{o.style.transitionDuration=`${a}ms`,o.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(p=>{p.style.transitionDuration=`${a}ms`})}),e.params.cubeEffect.shadow&&!e.isHorizontal()){const o=u.querySelector(".swiper-cube-shadow");o&&(o.style.transitionDuration=`${a}ms`)}},recreateShadows:()=>{const a=e.isHorizontal();e.slides.forEach(u=>{const d=Math.max(Math.min(u.progress,1),-1);r(u,d,a)})},getEffectParams:()=>e.params.cubeEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})}function Nn(t,e,n){const s=`swiper-slide-shadow${n?`-${n}`:""}${t?` swiper-slide-shadow-${t}`:""}`,r=Cn(e);let i=r.querySelector(`.${s.split(" ").join(".")}`);return i||(i=Ht("div",s.split(" ")),r.append(i)),i}function Qc(t){let{swiper:e,extendParams:n,on:s}=t;n({flipEffect:{slideShadows:!0,limitRotation:!0}});const r=(a,u)=>{let d=e.isHorizontal()?a.querySelector(".swiper-slide-shadow-left"):a.querySelector(".swiper-slide-shadow-top"),o=e.isHorizontal()?a.querySelector(".swiper-slide-shadow-right"):a.querySelector(".swiper-slide-shadow-bottom");d||(d=Nn("flip",a,e.isHorizontal()?"left":"top")),o||(o=Nn("flip",a,e.isHorizontal()?"right":"bottom")),d&&(d.style.opacity=Math.max(-u,0)),o&&(o.style.opacity=Math.max(u,0))};Vn({effect:"flip",swiper:e,on:s,setTranslate:()=>{const{slides:a,rtlTranslate:u}=e,d=e.params.flipEffect;for(let o=0;o<a.length;o+=1){const p=a[o];let _=p.progress;e.params.flipEffect.limitRotation&&(_=Math.max(Math.min(p.progress,1),-1));const w=p.swiperSlideOffset;let g=-180*_,E=0,T=e.params.cssMode?-w-e.translate:-w,m=0;e.isHorizontal()?u&&(g=-g):(m=T,T=0,E=-g,g=0),p.style.zIndex=-Math.abs(Math.round(_))+a.length,d.slideShadows&&r(p,_);const x=`translate3d(${T}px, ${m}px, 0px) rotateX(${E}deg) rotateY(${g}deg)`,f=rs(d,p);f.style.transform=x}},setTransition:a=>{const u=e.slides.map(d=>Cn(d));u.forEach(d=>{d.style.transitionDuration=`${a}ms`,d.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(o=>{o.style.transitionDuration=`${a}ms`})}),Ls({swiper:e,duration:a,transformElements:u})},recreateShadows:()=>{e.params.flipEffect,e.slides.forEach(a=>{let u=a.progress;e.params.flipEffect.limitRotation&&(u=Math.max(Math.min(a.progress,1),-1)),r(a,u)})},getEffectParams:()=>e.params.flipEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})}function Kc(t){let{swiper:e,extendParams:n,on:s}=t;n({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),Vn({effect:"coverflow",swiper:e,on:s,setTranslate:()=>{const{width:l,height:c,slides:a,slidesSizesGrid:u}=e,d=e.params.coverflowEffect,o=e.isHorizontal(),p=e.translate,_=o?-p+l/2:-p+c/2,w=o?d.rotate:-d.rotate,b=d.depth;for(let g=0,E=a.length;g<E;g+=1){const T=a[g],m=u[g],x=T.swiperSlideOffset,f=(_-x-m/2)/m,A=typeof d.modifier=="function"?d.modifier(f):f*d.modifier;let D=o?w*A:0,V=o?0:w*A,O=-b*Math.abs(A),q=d.stretch;typeof q=="string"&&q.indexOf("%")!==-1&&(q=parseFloat(d.stretch)/100*m);let N=o?0:q*A,L=o?q*A:0,I=1-(1-d.scale)*Math.abs(A);Math.abs(L)<.001&&(L=0),Math.abs(N)<.001&&(N=0),Math.abs(O)<.001&&(O=0),Math.abs(D)<.001&&(D=0),Math.abs(V)<.001&&(V=0),Math.abs(I)<.001&&(I=0);const h=`translate3d(${L}px,${N}px,${O}px)  rotateX(${V}deg) rotateY(${D}deg) scale(${I})`,k=rs(d,T);if(k.style.transform=h,T.style.zIndex=-Math.abs(Math.round(A))+1,d.slideShadows){let H=o?T.querySelector(".swiper-slide-shadow-left"):T.querySelector(".swiper-slide-shadow-top"),R=o?T.querySelector(".swiper-slide-shadow-right"):T.querySelector(".swiper-slide-shadow-bottom");H||(H=Nn("coverflow",T,o?"left":"top")),R||(R=Nn("coverflow",T,o?"right":"bottom")),H&&(H.style.opacity=A>0?A:0),R&&(R.style.opacity=-A>0?-A:0)}}},setTransition:l=>{e.slides.map(a=>Cn(a)).forEach(a=>{a.style.transitionDuration=`${l}ms`,a.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(u=>{u.style.transitionDuration=`${l}ms`})})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})}function Jc(t){let{swiper:e,extendParams:n,on:s}=t;n({creativeEffect:{limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});const r=c=>typeof c=="string"?c:`${c}px`;Vn({effect:"creative",swiper:e,on:s,setTranslate:()=>{const{slides:c,wrapperEl:a,slidesSizesGrid:u}=e,d=e.params.creativeEffect,{progressMultiplier:o}=d,p=e.params.centeredSlides;if(p){const _=u[0]/2-e.params.slidesOffsetBefore||0;a.style.transform=`translateX(calc(50% - ${_}px))`}for(let _=0;_<c.length;_+=1){const w=c[_],b=w.progress,g=Math.min(Math.max(w.progress,-d.limitProgress),d.limitProgress);let E=g;p||(E=Math.min(Math.max(w.originalProgress,-d.limitProgress),d.limitProgress));const T=w.swiperSlideOffset,m=[e.params.cssMode?-T-e.translate:-T,0,0],x=[0,0,0];let f=!1;e.isHorizontal()||(m[1]=m[0],m[0]=0);let A={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};g<0?(A=d.next,f=!0):g>0&&(A=d.prev,f=!0),m.forEach((I,h)=>{m[h]=`calc(${I}px + (${r(A.translate[h])} * ${Math.abs(g*o)}))`}),x.forEach((I,h)=>{x[h]=A.rotate[h]*Math.abs(g*o)}),w.style.zIndex=-Math.abs(Math.round(b))+c.length;const D=m.join(", "),V=`rotateX(${x[0]}deg) rotateY(${x[1]}deg) rotateZ(${x[2]}deg)`,O=E<0?`scale(${1+(1-A.scale)*E*o})`:`scale(${1-(1-A.scale)*E*o})`,q=E<0?1+(1-A.opacity)*E*o:1-(1-A.opacity)*E*o,N=`translate3d(${D}) ${V} ${O}`;if(f&&A.shadow||!f){let I=w.querySelector(".swiper-slide-shadow");if(!I&&A.shadow&&(I=Nn("creative",w)),I){const h=d.shadowPerProgress?g*(1/d.limitProgress):g;I.style.opacity=Math.min(Math.max(Math.abs(h),0),1)}}const L=rs(d,w);L.style.transform=N,L.style.opacity=q,A.origin&&(L.style.transformOrigin=A.origin)}},setTransition:c=>{const a=e.slides.map(u=>Cn(u));a.forEach(u=>{u.style.transitionDuration=`${c}ms`,u.querySelectorAll(".swiper-slide-shadow").forEach(d=>{d.style.transitionDuration=`${c}ms`})}),Ls({swiper:e,duration:c,transformElements:a,allSlides:!0})},perspective:()=>e.params.creativeEffect.perspective,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})}function eu(t){let{swiper:e,extendParams:n,on:s}=t;n({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}}),Vn({effect:"cards",swiper:e,on:s,setTranslate:()=>{const{slides:l,activeIndex:c,rtlTranslate:a}=e,u=e.params.cardsEffect,{startTranslate:d,isTouched:o}=e.touchEventsData,p=a?-e.translate:e.translate;for(let _=0;_<l.length;_+=1){const w=l[_],b=w.progress,g=Math.min(Math.max(b,-4),4);let E=w.swiperSlideOffset;e.params.centeredSlides&&!e.params.cssMode&&(e.wrapperEl.style.transform=`translateX(${e.minTranslate()}px)`),e.params.centeredSlides&&e.params.cssMode&&(E-=l[0].swiperSlideOffset);let T=e.params.cssMode?-E-e.translate:-E,m=0;const x=-100*Math.abs(g);let f=1,A=-u.perSlideRotate*g,D=u.perSlideOffset-Math.abs(g)*.75;const V=e.virtual&&e.params.virtual.enabled?e.virtual.from+_:_,O=(V===c||V===c-1)&&g>0&&g<1&&(o||e.params.cssMode)&&p<d,q=(V===c||V===c+1)&&g<0&&g>-1&&(o||e.params.cssMode)&&p>d;if(O||q){const h=(1-Math.abs((Math.abs(g)-.5)/.5))**.5;A+=-28*g*h,f+=-.5*h,D+=96*h,m=`${-25*h*Math.abs(g)}%`}if(g<0?T=`calc(${T}px ${a?"-":"+"} (${D*Math.abs(g)}%))`:g>0?T=`calc(${T}px ${a?"-":"+"} (-${D*Math.abs(g)}%))`:T=`${T}px`,!e.isHorizontal()){const h=m;m=T,T=h}const N=g<0?`${1+(1-f)*g}`:`${1-(1-f)*g}`,L=`
        translate3d(${T}, ${m}, ${x}px)
        rotateZ(${u.rotate?a?-A:A:0}deg)
        scale(${N})
      `;if(u.slideShadows){let h=w.querySelector(".swiper-slide-shadow");h||(h=Nn("cards",w)),h&&(h.style.opacity=Math.min(Math.max((Math.abs(g)-.5)/.5,0),1))}w.style.zIndex=-Math.abs(Math.round(b))+l.length;const I=rs(u,w);I.style.transform=L}},setTransition:l=>{const c=e.slides.map(a=>Cn(a));c.forEach(a=>{a.style.transitionDuration=`${l}ms`,a.querySelectorAll(".swiper-slide-shadow").forEach(u=>{u.style.transitionDuration=`${l}ms`})}),Ls({swiper:e,duration:l,transformElements:c})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})}const tu=[xc,Cc,kc,Pc,Ic,Rc,Bc,Lc,Oc,Dc,Nc,zc,$c,Vc,Hc,Xc,qc,Uc,Zc,Qc,Kc,Jc,eu];Pt.use(tu);const Os=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopedSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideNextClass","slidePrevClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function zn(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"&&!t.__swiper__}function Es(t,e){const n=["__proto__","constructor","prototype"];Object.keys(e).filter(s=>n.indexOf(s)<0).forEach(s=>{typeof t[s]>"u"?t[s]=e[s]:zn(e[s])&&zn(t[s])&&Object.keys(e[s]).length>0?e[s].__swiper__?t[s]=e[s]:Es(t[s],e[s]):t[s]=e[s]})}function nu(t){return t===void 0&&(t={}),t.navigation&&typeof t.navigation.nextEl>"u"&&typeof t.navigation.prevEl>"u"}function su(t){return t===void 0&&(t={}),t.pagination&&typeof t.pagination.el>"u"}function ru(t){return t===void 0&&(t={}),t.scrollbar&&typeof t.scrollbar.el>"u"}function ws(t){return t===void 0&&(t=""),t.replace(/-[a-z]/g,e=>e.toUpperCase().replace("-",""))}function iu(t){let{swiper:e,slides:n,passedParams:s,changedParams:r,nextEl:i,prevEl:l,scrollbarEl:c,paginationEl:a}=t;const u=r.filter(O=>O!=="children"&&O!=="direction"&&O!=="wrapperClass"),{params:d,pagination:o,navigation:p,scrollbar:_,virtual:w,thumbs:b}=e;let g,E,T,m,x,f,A,D;r.includes("thumbs")&&s.thumbs&&s.thumbs.swiper&&d.thumbs&&!d.thumbs.swiper&&(g=!0),r.includes("controller")&&s.controller&&s.controller.control&&d.controller&&!d.controller.control&&(E=!0),r.includes("pagination")&&s.pagination&&(s.pagination.el||a)&&(d.pagination||d.pagination===!1)&&o&&!o.el&&(T=!0),r.includes("scrollbar")&&s.scrollbar&&(s.scrollbar.el||c)&&(d.scrollbar||d.scrollbar===!1)&&_&&!_.el&&(m=!0),r.includes("navigation")&&s.navigation&&(s.navigation.prevEl||l)&&(s.navigation.nextEl||i)&&(d.navigation||d.navigation===!1)&&p&&!p.prevEl&&!p.nextEl&&(x=!0);const V=O=>{e[O]&&(e[O].destroy(),O==="navigation"?(e.isElement&&(e[O].prevEl.remove(),e[O].nextEl.remove()),d[O].prevEl=void 0,d[O].nextEl=void 0,e[O].prevEl=void 0,e[O].nextEl=void 0):(e.isElement&&e[O].el.remove(),d[O].el=void 0,e[O].el=void 0))};r.includes("loop")&&e.isElement&&(d.loop&&!s.loop?f=!0:!d.loop&&s.loop?A=!0:D=!0),u.forEach(O=>{if(zn(d[O])&&zn(s[O]))Es(d[O],s[O]),(O==="navigation"||O==="pagination"||O==="scrollbar")&&"enabled"in s[O]&&!s[O].enabled&&V(O);else{const q=s[O];(q===!0||q===!1)&&(O==="navigation"||O==="pagination"||O==="scrollbar")?q===!1&&V(O):d[O]=s[O]}}),u.includes("controller")&&!E&&e.controller&&e.controller.control&&d.controller&&d.controller.control&&(e.controller.control=d.controller.control),r.includes("children")&&n&&w&&d.virtual.enabled&&(w.slides=n,w.update(!0)),r.includes("children")&&n&&d.loop&&(D=!0),g&&b.init()&&b.update(!0),E&&(e.controller.control=d.controller.control),T&&(e.isElement&&(!a||typeof a=="string")&&(a=document.createElement("div"),a.classList.add("swiper-pagination"),a.part.add("pagination"),e.el.appendChild(a)),a&&(d.pagination.el=a),o.init(),o.render(),o.update()),m&&(e.isElement&&(!c||typeof c=="string")&&(c=document.createElement("div"),c.classList.add("swiper-scrollbar"),c.part.add("scrollbar"),e.el.appendChild(c)),c&&(d.scrollbar.el=c),_.init(),_.updateSize(),_.setTranslate()),x&&(e.isElement&&((!i||typeof i=="string")&&(i=document.createElement("div"),i.classList.add("swiper-button-next"),i.innerHTML=e.hostEl.constructor.nextButtonSvg,i.part.add("button-next"),e.el.appendChild(i)),(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-button-prev"),l.innerHTML=e.hostEl.constructor.prevButtonSvg,l.part.add("button-prev"),e.el.appendChild(l))),i&&(d.navigation.nextEl=i),l&&(d.navigation.prevEl=l),p.init(),p.update()),r.includes("allowSlideNext")&&(e.allowSlideNext=s.allowSlideNext),r.includes("allowSlidePrev")&&(e.allowSlidePrev=s.allowSlidePrev),r.includes("direction")&&e.changeDirection(s.direction,!1),(f||D)&&e.loopDestroy(),(A||D)&&e.loopCreate(),e.update()}const Ei=t=>{if(parseFloat(t)===Number(t))return Number(t);if(t==="true"||t==="")return!0;if(t==="false")return!1;if(t==="null")return null;if(t!=="undefined"){if(typeof t=="string"&&t.includes("{")&&t.includes("}")&&t.includes('"')){let e;try{e=JSON.parse(t)}catch{e=t}return e}return t}},Ti=["a11y","autoplay","controller","cards-effect","coverflow-effect","creative-effect","cube-effect","fade-effect","flip-effect","free-mode","grid","hash-navigation","history","keyboard","mousewheel","navigation","pagination","parallax","scrollbar","thumbs","virtual","zoom"];function Mi(t,e,n){const s={},r={};Es(s,xr);const i=[...Os,"on"],l=i.map(a=>a.replace(/_/,""));i.forEach(a=>{a=a.replace("_",""),typeof t[a]<"u"&&(r[a]=t[a])});const c=[...t.attributes];return typeof e=="string"&&typeof n<"u"&&c.push({name:e,value:zn(n)?{...n}:n}),c.forEach(a=>{const u=Ti.filter(d=>a.name.indexOf(`${d}-`)===0)[0];if(u){const d=ws(u),o=ws(a.name.split(`${u}-`)[1]);typeof r[d]>"u"&&(r[d]={}),r[d]===!0&&(r[d]={enabled:!0}),r[d][o]=Ei(a.value)}else{const d=ws(a.name);if(!l.includes(d))return;const o=Ei(a.value);r[d]&&Ti.includes(a.name)&&!zn(o)?(r[d].constructor!==Object&&(r[d]={}),r[d].enabled=!!o):r[d]=o}}),Es(s,r),s.navigation?s.navigation={prevEl:".swiper-button-prev",nextEl:".swiper-button-next",...s.navigation!==!0?s.navigation:{}}:s.navigation===!1&&delete s.navigation,s.scrollbar?s.scrollbar={el:".swiper-scrollbar",...s.scrollbar!==!0?s.scrollbar:{}}:s.scrollbar===!1&&delete s.scrollbar,s.pagination?s.pagination={el:".swiper-pagination",...s.pagination!==!0?s.pagination:{}}:s.pagination===!1&&delete s.pagination,{params:s,passedParams:r}}const au=":host{--swiper-theme-color:#007aff}:host{position:relative;display:block;margin-left:auto;margin-right:auto;z-index:1}.swiper{width:100%;height:100%;margin-left:auto;margin-right:auto;position:relative;overflow:hidden;overflow:clip;list-style:none;padding:0;z-index:1;display:block}.swiper-vertical>.swiper-wrapper{flex-direction:column}.swiper-wrapper{position:relative;width:100%;height:100%;z-index:1;display:flex;transition-property:transform;transition-timing-function:var(--swiper-wrapper-transition-timing-function,initial);box-sizing:content-box}.swiper-android ::slotted(swiper-slide),.swiper-ios ::slotted(swiper-slide),.swiper-wrapper{transform:translate3d(0px,0,0)}.swiper-horizontal{touch-action:pan-y}.swiper-vertical{touch-action:pan-x}::slotted(swiper-slide){flex-shrink:0;width:100%;height:100%;position:relative;transition-property:transform;display:block}::slotted(.swiper-slide-invisible-blank){visibility:hidden}.swiper-autoheight,.swiper-autoheight ::slotted(swiper-slide){height:auto}.swiper-autoheight .swiper-wrapper{align-items:flex-start;transition-property:transform,height}.swiper-backface-hidden ::slotted(swiper-slide){transform:translateZ(0);-webkit-backface-visibility:hidden;backface-visibility:hidden}.swiper-3d.swiper-css-mode .swiper-wrapper{perspective:1200px}.swiper-3d .swiper-wrapper{transform-style:preserve-3d}.swiper-3d{perspective:1200px}.swiper-3d .swiper-cube-shadow,.swiper-3d ::slotted(swiper-slide){transform-style:preserve-3d}.swiper-css-mode>.swiper-wrapper{overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar{display:none}.swiper-css-mode ::slotted(swiper-slide){scroll-snap-align:start start}.swiper-css-mode.swiper-horizontal>.swiper-wrapper{scroll-snap-type:x mandatory}.swiper-css-mode.swiper-vertical>.swiper-wrapper{scroll-snap-type:y mandatory}.swiper-css-mode.swiper-free-mode>.swiper-wrapper{scroll-snap-type:none}.swiper-css-mode.swiper-free-mode ::slotted(swiper-slide){scroll-snap-align:none}.swiper-css-mode.swiper-centered>.swiper-wrapper::before{content:'';flex-shrink:0;order:9999}.swiper-css-mode.swiper-centered ::slotted(swiper-slide){scroll-snap-align:center center;scroll-snap-stop:always}.swiper-css-mode.swiper-centered.swiper-horizontal ::slotted(swiper-slide):first-child{margin-inline-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper::before{height:100%;min-height:1px;width:var(--swiper-centered-offset-after)}.swiper-css-mode.swiper-centered.swiper-vertical ::slotted(swiper-slide):first-child{margin-block-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper::before{width:100%;min-width:1px;height:var(--swiper-centered-offset-after)}.swiper-virtual ::slotted(swiper-slide){-webkit-backface-visibility:hidden;transform:translateZ(0)}.swiper-virtual.swiper-css-mode .swiper-wrapper::after{content:'';position:absolute;left:0;top:0;pointer-events:none}.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper::after{height:1px;width:var(--swiper-virtual-size)}.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper::after{width:1px;height:var(--swiper-virtual-size)}:host{--swiper-navigation-size:44px}.swiper-button-next,.swiper-button-prev{position:absolute;top:var(--swiper-navigation-top-offset,50%);width:calc(var(--swiper-navigation-size)/ 44 * 27);height:var(--swiper-navigation-size);margin-top:calc(0px - (var(--swiper-navigation-size)/ 2));z-index:10;cursor:pointer;display:flex;align-items:center;justify-content:center;color:var(--swiper-navigation-color,var(--swiper-theme-color))}.swiper-button-next.swiper-button-disabled,.swiper-button-prev.swiper-button-disabled{opacity:.35;cursor:auto;pointer-events:none}.swiper-button-next.swiper-button-hidden,.swiper-button-prev.swiper-button-hidden{opacity:0;cursor:auto;pointer-events:none}.swiper-navigation-disabled .swiper-button-next,.swiper-navigation-disabled .swiper-button-prev{display:none!important}.swiper-button-next svg,.swiper-button-prev svg{width:100%;height:100%;object-fit:contain;transform-origin:center}.swiper-rtl .swiper-button-next svg,.swiper-rtl .swiper-button-prev svg{transform:rotate(180deg)}.swiper-button-prev,.swiper-rtl .swiper-button-next{left:var(--swiper-navigation-sides-offset,10px);right:auto}.swiper-button-next,.swiper-rtl .swiper-button-prev{right:var(--swiper-navigation-sides-offset,10px);left:auto}.swiper-button-lock{display:none}.swiper-pagination{position:absolute;text-align:center;transition:.3s opacity;transform:translate3d(0,0,0);z-index:10}.swiper-pagination.swiper-pagination-hidden{opacity:0}.swiper-pagination-disabled>.swiper-pagination,.swiper-pagination.swiper-pagination-disabled{display:none!important}.swiper-horizontal>.swiper-pagination-bullets,.swiper-pagination-bullets.swiper-pagination-horizontal,.swiper-pagination-custom,.swiper-pagination-fraction{bottom:var(--swiper-pagination-bottom,8px);top:var(--swiper-pagination-top,auto);left:0;width:100%}.swiper-pagination-bullets-dynamic{overflow:hidden;font-size:0}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transform:scale(.33);position:relative}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active{transform:scale(1)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main{transform:scale(1)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev{transform:scale(.33)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next{transform:scale(.33)}.swiper-pagination-bullet{width:var(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,8px));height:var(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,8px));display:inline-block;border-radius:var(--swiper-pagination-bullet-border-radius,50%);background:var(--swiper-pagination-bullet-inactive-color,#000);opacity:var(--swiper-pagination-bullet-inactive-opacity, .2)}button.swiper-pagination-bullet{border:none;margin:0;padding:0;box-shadow:none;-webkit-appearance:none;appearance:none}.swiper-pagination-clickable .swiper-pagination-bullet{cursor:pointer}.swiper-pagination-bullet:only-child{display:none!important}.swiper-pagination-bullet-active{opacity:var(--swiper-pagination-bullet-opacity, 1);background:var(--swiper-pagination-color,var(--swiper-theme-color))}.swiper-pagination-vertical.swiper-pagination-bullets,.swiper-vertical>.swiper-pagination-bullets{right:var(--swiper-pagination-right,8px);left:var(--swiper-pagination-left,auto);top:50%;transform:translate3d(0px,-50%,0)}.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets .swiper-pagination-bullet{margin:var(--swiper-pagination-bullet-vertical-gap,6px) 0;display:block}.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{top:50%;transform:translateY(-50%);width:8px}.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{display:inline-block;transition:.2s transform,.2s top}.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet{margin:0 var(--swiper-pagination-bullet-horizontal-gap,4px)}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{left:50%;transform:translateX(-50%);white-space:nowrap}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s left}.swiper-horizontal.swiper-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s right}.swiper-pagination-fraction{color:var(--swiper-pagination-fraction-color,inherit)}.swiper-pagination-progressbar{background:var(--swiper-pagination-progressbar-bg-color,rgba(0,0,0,.25));position:absolute}.swiper-pagination-progressbar .swiper-pagination-progressbar-fill{background:var(--swiper-pagination-color,var(--swiper-theme-color));position:absolute;left:0;top:0;width:100%;height:100%;transform:scale(0);transform-origin:left top}.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill{transform-origin:right top}.swiper-horizontal>.swiper-pagination-progressbar,.swiper-pagination-progressbar.swiper-pagination-horizontal,.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite,.swiper-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite{width:100%;height:var(--swiper-pagination-progressbar-size,4px);left:0;top:0}.swiper-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-vertical,.swiper-vertical>.swiper-pagination-progressbar{width:var(--swiper-pagination-progressbar-size,4px);height:100%;left:0;top:0}.swiper-pagination-lock{display:none}.swiper-scrollbar{border-radius:var(--swiper-scrollbar-border-radius,10px);position:relative;touch-action:none;background:var(--swiper-scrollbar-bg-color,rgba(0,0,0,.1))}.swiper-scrollbar-disabled>.swiper-scrollbar,.swiper-scrollbar.swiper-scrollbar-disabled{display:none!important}.swiper-horizontal>.swiper-scrollbar,.swiper-scrollbar.swiper-scrollbar-horizontal{position:absolute;left:var(--swiper-scrollbar-sides-offset,1%);bottom:var(--swiper-scrollbar-bottom,4px);top:var(--swiper-scrollbar-top,auto);z-index:50;height:var(--swiper-scrollbar-size,4px);width:calc(100% - 2 * var(--swiper-scrollbar-sides-offset,1%))}.swiper-scrollbar.swiper-scrollbar-vertical,.swiper-vertical>.swiper-scrollbar{position:absolute;left:var(--swiper-scrollbar-left,auto);right:var(--swiper-scrollbar-right,4px);top:var(--swiper-scrollbar-sides-offset,1%);z-index:50;width:var(--swiper-scrollbar-size,4px);height:calc(100% - 2 * var(--swiper-scrollbar-sides-offset,1%))}.swiper-scrollbar-drag{height:100%;width:100%;position:relative;background:var(--swiper-scrollbar-drag-bg-color,rgba(0,0,0,.5));border-radius:var(--swiper-scrollbar-border-radius,10px);left:0;top:0}.swiper-scrollbar-cursor-drag{cursor:move}.swiper-scrollbar-lock{display:none}::slotted(.swiper-slide-zoomed){cursor:move;touch-action:none}.swiper .swiper-notification{position:absolute;left:0;top:0;pointer-events:none;opacity:0;z-index:-1000}.swiper-free-mode>.swiper-wrapper{transition-timing-function:ease-out;margin:0 auto}.swiper-grid>.swiper-wrapper{flex-wrap:wrap}.swiper-grid-column>.swiper-wrapper{flex-wrap:wrap;flex-direction:column}.swiper-fade.swiper-free-mode ::slotted(swiper-slide){transition-timing-function:ease-out}.swiper-fade ::slotted(swiper-slide){pointer-events:none;transition-property:opacity}.swiper-fade ::slotted(swiper-slide) ::slotted(swiper-slide){pointer-events:none}.swiper-fade ::slotted(.swiper-slide-active){pointer-events:auto}.swiper-fade ::slotted(.swiper-slide-active) ::slotted(.swiper-slide-active){pointer-events:auto}.swiper-cube{overflow:visible}.swiper-cube ::slotted(swiper-slide){pointer-events:none;-webkit-backface-visibility:hidden;backface-visibility:hidden;z-index:1;visibility:hidden;transform-origin:0 0;width:100%;height:100%}.swiper-cube ::slotted(swiper-slide) ::slotted(swiper-slide){pointer-events:none}.swiper-cube.swiper-rtl ::slotted(swiper-slide){transform-origin:100% 0}.swiper-cube ::slotted(.swiper-slide-active),.swiper-cube ::slotted(.swiper-slide-active) ::slotted(.swiper-slide-active){pointer-events:auto}.swiper-cube ::slotted(.swiper-slide-active),.swiper-cube ::slotted(.swiper-slide-next),.swiper-cube ::slotted(.swiper-slide-prev){pointer-events:auto;visibility:visible}.swiper-cube .swiper-cube-shadow{position:absolute;left:0;bottom:0px;width:100%;height:100%;opacity:.6;z-index:0}.swiper-cube .swiper-cube-shadow:before{content:'';background:#000;position:absolute;left:0;top:0;bottom:0;right:0;filter:blur(50px)}.swiper-cube ::slotted(.swiper-slide-next)+::slotted(swiper-slide){pointer-events:auto;visibility:visible}.swiper-flip{overflow:visible}.swiper-flip ::slotted(swiper-slide){pointer-events:none;-webkit-backface-visibility:hidden;backface-visibility:hidden;z-index:1}.swiper-flip ::slotted(swiper-slide) ::slotted(swiper-slide){pointer-events:none}.swiper-flip ::slotted(.swiper-slide-active),.swiper-flip ::slotted(.swiper-slide-active) ::slotted(.swiper-slide-active){pointer-events:auto}.swiper-creative ::slotted(swiper-slide){-webkit-backface-visibility:hidden;backface-visibility:hidden;overflow:hidden;transition-property:transform,opacity,height}.swiper-cards{overflow:visible}.swiper-cards ::slotted(swiper-slide){transform-origin:center bottom;-webkit-backface-visibility:hidden;backface-visibility:hidden;overflow:hidden}",ou="::slotted(.swiper-slide-shadow),::slotted(.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-top){position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none;z-index:10}::slotted(.swiper-slide-shadow){background:rgba(0,0,0,.15)}::slotted(.swiper-slide-shadow-left){background-image:linear-gradient(to left,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-right){background-image:linear-gradient(to right,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-top){background-image:linear-gradient(to top,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-bottom){background-image:linear-gradient(to bottom,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-lazy-preloader{animation:swiper-preloader-spin 1s infinite linear;width:42px;height:42px;position:absolute;left:50%;top:50%;margin-left:-21px;margin-top:-21px;z-index:10;transform-origin:50%;box-sizing:border-box;border:4px solid var(--swiper-preloader-color,var(--swiper-theme-color));border-radius:50%;border-top-color:transparent}@keyframes swiper-preloader-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-top){z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-top){z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}::slotted(.swiper-zoom-container){width:100%;height:100%;display:flex;justify-content:center;align-items:center;text-align:center}::slotted(.swiper-zoom-container)>canvas,::slotted(.swiper-zoom-container)>img,::slotted(.swiper-zoom-container)>svg{max-width:100%;max-height:100%;object-fit:contain}";class lu{}const Ea=typeof window>"u"||typeof HTMLElement>"u"?lu:HTMLElement,Ai=`<svg width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.38296 20.0762C0.111788 19.805 0.111788 19.3654 0.38296 19.0942L9.19758 10.2796L0.38296 1.46497C0.111788 1.19379 0.111788 0.754138 0.38296 0.482966C0.654131 0.211794 1.09379 0.211794 1.36496 0.482966L10.4341 9.55214C10.8359 9.9539 10.8359 10.6053 10.4341 11.007L1.36496 20.0762C1.09379 20.3474 0.654131 20.3474 0.38296 20.0762Z" fill="currentColor"/></svg>
    `,Ta=(t,e)=>{if(typeof CSSStyleSheet<"u"&&t.adoptedStyleSheets){const n=new CSSStyleSheet;n.replaceSync(e),t.adoptedStyleSheets=[n]}else{const n=document.createElement("style");n.rel="stylesheet",n.textContent=e,t.appendChild(n)}};class Ma extends Ea{constructor(){super(),this.attachShadow({mode:"open"})}static get nextButtonSvg(){return Ai}static get prevButtonSvg(){return Ai.replace("/></svg>",' transform-origin="center" transform="rotate(180)"/></svg>')}cssStyles(){return[au,...this.injectStyles&&Array.isArray(this.injectStyles)?this.injectStyles:[]].join(`
`)}cssLinks(){return this.injectStylesUrls||[]}calcSlideSlots(){const e=this.slideSlots||0,n=[...this.querySelectorAll("[slot^=slide-]")].map(s=>parseInt(s.getAttribute("slot").split("slide-")[1],10));if(this.slideSlots=n.length?Math.max(...n)+1:0,!!this.rendered){if(this.slideSlots>e)for(let s=e;s<this.slideSlots;s+=1){const r=document.createElement("swiper-slide");r.setAttribute("part",`slide slide-${s+1}`);const i=document.createElement("slot");i.setAttribute("name",`slide-${s+1}`),r.appendChild(i),this.shadowRoot.querySelector(".swiper-wrapper").appendChild(r)}else if(this.slideSlots<e){const s=this.swiper.slides;for(let r=s.length-1;r>=0;r-=1)r>this.slideSlots&&s[r].remove()}}}render(){if(this.rendered)return;this.calcSlideSlots();let e=this.cssStyles();this.slideSlots>0&&(e=e.replace(/::slotted\(([a-z-0-9.]*)\)/g,"$1")),e.length&&Ta(this.shadowRoot,e),this.cssLinks().forEach(s=>{if(this.shadowRoot.querySelector(`link[href="${s}"]`))return;const i=document.createElement("link");i.rel="stylesheet",i.href=s,this.shadowRoot.appendChild(i)});const n=document.createElement("div");n.classList.add("swiper"),n.part="container",n.innerHTML=`
      <slot name="container-start"></slot>
      <div class="swiper-wrapper" part="wrapper">
        <slot></slot>
        ${Array.from({length:this.slideSlots}).map((s,r)=>`
        <swiper-slide part="slide slide-${r}">
          <slot name="slide-${r}"></slot>
        </swiper-slide>
        `).join("")}
      </div>
      <slot name="container-end"></slot>
      ${nu(this.passedParams)?`
        <div part="button-prev" class="swiper-button-prev">${this.constructor.prevButtonSvg}</div>
        <div part="button-next" class="swiper-button-next">${this.constructor.nextButtonSvg}</div>
      `:""}
      ${su(this.passedParams)?`
        <div part="pagination" class="swiper-pagination"></div>
      `:""}
      ${ru(this.passedParams)?`
        <div part="scrollbar" class="swiper-scrollbar"></div>
      `:""}
    `,this.shadowRoot.appendChild(n),this.rendered=!0}initialize(){var e=this;if(this.initialized)return;this.initialized=!0;const{params:n,passedParams:s}=Mi(this);this.swiperParams=n,this.passedParams=s,delete this.swiperParams.init,this.render(),this.swiper=new Pt(this.shadowRoot.querySelector(".swiper"),{...n.virtual?{}:{observer:!0,observeSlideChildren:this.slideSlots>0},...n,touchEventsTarget:"container",onAny:function(r){r==="observerUpdate"&&e.calcSlideSlots();const i=n.eventsPrefix?`${n.eventsPrefix}${r.toLowerCase()}`:r.toLowerCase();for(var l=arguments.length,c=new Array(l>1?l-1:0),a=1;a<l;a++)c[a-1]=arguments[a];const u=new CustomEvent(i,{detail:c,bubbles:r!=="hashChange",cancelable:!0});e.dispatchEvent(u)}})}connectedCallback(){this.initialized&&this.nested&&this.closest("swiper-slide")&&this.closest("swiper-slide").swiperLoopMoveDOM||this.init===!1||this.getAttribute("init")==="false"||this.initialize()}disconnectedCallback(){this.nested&&this.closest("swiper-slide")&&this.closest("swiper-slide").swiperLoopMoveDOM||(this.swiper&&this.swiper.destroy&&this.swiper.destroy(),this.initialized=!1)}updateSwiperOnPropChange(e,n){const{params:s,passedParams:r}=Mi(this,e,n);this.passedParams=r,this.swiperParams=s,iu({swiper:this.swiper,passedParams:this.passedParams,changedParams:[ws(e)],...e==="navigation"&&r[e]?{prevEl:".swiper-button-prev",nextEl:".swiper-button-next"}:{},...e==="pagination"&&r[e]?{paginationEl:".swiper-pagination"}:{},...e==="scrollbar"&&r[e]?{scrollbarEl:".swiper-scrollbar"}:{}})}attributeChangedCallback(e,n,s){this.initialized&&(n==="true"&&s===null&&(s=!1),this.updateSwiperOnPropChange(e,s))}static get observedAttributes(){return Os.filter(n=>n.includes("_")).map(n=>n.replace(/[A-Z]/g,s=>`-${s}`).replace("_","").toLowerCase())}}Os.forEach(t=>{t!=="init"&&(t=t.replace("_",""),Object.defineProperty(Ma.prototype,t,{configurable:!0,get(){return(this.passedParams||{})[t]},set(e){this.passedParams||(this.passedParams={}),this.passedParams[t]=e,this.initialized&&this.updateSwiperOnPropChange(t,e)}}))});class cu extends Ea{constructor(){super(),this.attachShadow({mode:"open"})}render(){const e=this.lazy||this.getAttribute("lazy")===""||this.getAttribute("lazy")==="true";if(Ta(this.shadowRoot,ou),this.shadowRoot.appendChild(document.createElement("slot")),e){const n=document.createElement("div");n.classList.add("swiper-lazy-preloader"),n.part.add("preloader"),this.shadowRoot.appendChild(n)}}initialize(){this.render()}connectedCallback(){this.initialize()}}const Aa=()=>{typeof window>"u"||(window.customElements.get("swiper-container")||window.customElements.define("swiper-container",Ma),window.customElements.get("swiper-slide")||window.customElements.define("swiper-slide",cu))};typeof window<"u"&&(window.SwiperElementRegisterParams=t=>{Os.push(...t)});const uu={class:"text-lg font-weight-medium"},du={__name:"TemplateButton",props:{disable:{type:Boolean,default:!1},name:{type:String,required:!0},icon:{type:String,required:!0},emitName:{type:String,required:!0},isLast:{type:Boolean,required:!0}},emits:["addTemplate"],setup(t,{emit:e}){const n=t;return(s,r)=>(_e(),Oe(bt,{cols:"12",md:n.isLast?12:6},{default:De(()=>[je(ns,{flat:"",onClick:r[0]||(r[0]=i=>t.disable?{}:s.$emit("addTemplate",n.emitName))},{default:De(()=>[je(ga,{class:Ut(["template_button",[{"disabled-overlay":n.disable}]])},{default:De(()=>[je(dt,{icon:n.icon,size:"20",class:"me-3"},null,8,["icon"]),Ne("span",uu,Dt(n.name),1)]),_:1},8,["class"])]),_:1})]),_:1},8,["md"]))}},fu=At(du,[["__scopeId","data-v-891fa6d3"]]);const xa=t=>(Rs("data-v-51c15184"),t=t(),Bs(),t),pu={key:0,class:"overlay"},hu={class:"card"},mu={key:1,class:"mt-4"},_u={class:"text-inputs"},gu=["value"],vu=["value"],bu={class:"button_text"},wu=["value","onInput"],yu={class:"button_link"},Su=["value","onInput"],Eu=["onClick"],Tu=xa(()=>Ne("span",null,"+",-1)),Mu=xa(()=>Ne("span",null,"اضافه کردن دکمه",-1)),Au=[Tu,Mu],xu={__name:"CardTemplateSwiperSlide",props:{image:{type:String,allowNull:!0},description:{type:String,allowNull:!0},title:{type:String,allowNull:!0},card_item_keys:{type:[Array,Object,null],default:()=>[]},template_card_id:Number,id:Number},emits:["imageChanged","titleChanged","descriptionChanged","addButton","buttonChanged","removeButton"],setup(t,{emit:e}){const n=t,s=e,r=Ue(null),i=Ue(null),l=Ue(!1),c=()=>{i.value.click()},a=d=>{if(!d.target.files)return;const o=d.target.files[0];if(o.size>26214400){errorMessage.value="حجم عکس حداکثر 25 مگابایت میباشد";return}o&&o.type.startsWith("image/")?s("imageChanged",o):console.error("File is not an image")},u=Kn(()=>n.id===-1);return Nt(()=>n.image,d=>{const o=r.value.$el||r.value;if(d=="")l.value=!1;else if(o){const p=Sn(n.image,"images"),_=p.split("/");if(_[_.length-1]=="loading"){o.style.background="none",l.value=!0;return}l.value=!1,o.style.background=`url(${p})`,o.style.backgroundPosition="center center",o.style.backgroundRepeat="no-repeat",o.style.backgroundSize="contain"}}),nn(()=>{const d=r.value.$el||r.value;if(d&&n.image!=""){const o=Sn(n.image,"images");d.style.background=`url(${o})`,d.style.backgroundPosition="center center",d.style.backgroundRepeat="no-repeat",d.style.backgroundSize="contain"}}),(d,o)=>(_e(),Oe(ns,null,{default:De(()=>{var p;return[Ve(u)?(_e(),Ge("div",pu)):Ye("",!0),Ne("div",hu,[Ne("div",{ref_key:"imageDiv",ref:r,class:"upload-area",onClick:c},[Ve(l)?(_e(),Oe(fn,{key:1,indeterminate:""})):(_e(),Ge(mt,{key:0},[Ne("input",{ref_key:"fileInput",ref:i,type:"file",hidden:"",onChange:a},null,544),t.image==""||t.image==null?(_e(),Oe(dt,{key:0,icon:"tabler-photo",size:"40"})):Ye("",!0),t.image==""||t.image==null?(_e(),Ge("p",mu," برای آپلود عکس اینجا بزنید(حداکثر25مگابایت) ")):Ye("",!0)],64))],512),Ne("div",_u,[Ne("input",{variant:"plain",type:"text",placeholder:"تیتر را وارد کنید",maxlength:"80",value:n.title,onInput:o[0]||(o[0]=_=>d.$emit("titleChanged",_.target.value))},null,40,gu),Ne("textarea",{rows:"2",type:"text",variant:"plain",maxlength:"80",value:n.description,placeholder:"متن را وارد کنید",onInput:o[1]||(o[1]=_=>d.$emit("descriptionChanged",_.target.value))},null,40,vu)])]),(_e(!0),Ge(mt,null,Jt(n.card_item_keys,(_,w)=>(_e(),Ge("div",{key:w,class:"button_declaration"},[Ne("div",bu,[Ne("input",{maxlength:"30",placeholder:"متن",type:"text",value:_[0],onInput:b=>d.$emit("buttonChanged",{value:[b.target.value,_[1]],index:w})},null,40,wu)]),Ne("div",yu,[Ne("input",{maxlength:"2083",placeholder:"www.google.com",type:"text",value:_[1],onInput:b=>d.$emit("buttonChanged",{value:[_[0],b.target.value],index:w})},null,40,Su)]),Ne("div",{class:"button_close_btn",onClick:b=>d.$emit("removeButton",w)},[je(dt,{icon:"tabler-x"})],8,Eu)]))),128)),Ne("div",{class:Ut(["add_button",{"d-none":((p=n.card_item_keys)==null?void 0:p.length)>2}]),onClick:o[2]||(o[2]=_=>d.$emit("addButton"))},Au,2)]}),_:1}))}},Cu=At(xu,[["__scopeId","data-v-51c15184"]]);const ku={__name:"CardTemplate",props:{slides:{type:Array,required:!0},id:{type:Number,default:-1}},emits:["addSlide","slideChanged","delete","addButton","removeButton","buttonChanged"],setup(t,{emit:e}){const n=t,s=e;Aa();const r=Ue(null),i=Ue(!1),l=Ue(!0),c=Ue("ep-plus"),a=Kn(()=>n.slides.length>0?n.slides[n.slides.length-1].id!==-1:!1),u=()=>{if(!(!r.value||r.value.swiper.activeIndex==9)){if(i.value=!0,r.value.swiper.activeIndex!=n.slides.length-1){r.value.swiper.slideNext(),console.log(r.value.swiper.activeIndex),r.value.swiper.activeIndex==n.slides.length-1&&(c.value="ep-plus"),r.value.swiper.activeIndex==9&&(l.value=!1);return}r.value.swiper.activeIndex==8&&(l.value=!1),s("addSlide")}},d=()=>{r.value&&(l.value=!0,r.value.swiper.activeIndex==1&&(i.value=!1),c.value="tabler-arrow-big-left",r.value.swiper.slidePrev())};return nn(()=>{n.slides.length>1&&(c.value="tabler-arrow-big-left"),n.slides.forEach(o=>{try{o.card_item_keys=JSON.parse(o.card_item_keys)}catch(p){console.log("some error happend in json parsing card keys"),console.log("error : ",p),console.log("card_item_keys : ",o.card_item_keys)}})}),Nt(()=>n.slides.length,()=>{r.value&&Er(()=>{r.value.swiper.update(),r.value.swiper.slideNext()})}),(o,p)=>{const _=ss("IconBtn"),w=Cu;return _e(),Oe(Xt,null,{default:De(()=>[je(_,{class:"close-btn",size:"22"},{default:De(()=>[je(dt,{icon:"tabler-x",size:"20",onClick:p[0]||(p[0]=b=>o.showOverlay||!Ve(a)?{}:o.$emit("delete",Ve(r).swiper.activeIndex))})]),_:1}),je(bt,{class:"position-relative"},{default:De(()=>[Ne("swiper-container",{ref_key:"swiperEl",ref:r,class:"card-swiper","events-prefix":"swiper-",style:{"touch-action":"none"},"allow-touch-move":"false"},[(_e(!0),Ge(mt,null,Jt(n.slides,(b,g)=>(_e(),Ge("swiper-slide",{key:g},[je(w,{image:b.image,description:b.description,title:b.title,card_item_keys:b.card_item_keys,template_card_id:b.template_card_id,onTitleChanged:E=>o.$emit("slideChanged",{value:E,index:g,where:"title"}),onDescriptionChanged:E=>o.$emit("slideChanged",{value:E,index:g,where:"description"}),onImageChanged:E=>o.$emit("slideChanged",{value:E,index:g,where:"image"}),onAddButton:E=>o.$emit("addButton",g),onRemoveButton:E=>o.$emit("removeButton",{slideIndex:g,buttonIndex:E}),onButtonChanged:E=>o.$emit("buttonChanged",{slideIndex:g,buttonIndex:E.index,value:E.value})},null,8,["image","description","title","card_item_keys","template_card_id","onTitleChanged","onDescriptionChanged","onImageChanged","onAddButton","onRemoveButton","onButtonChanged"])]))),128))],512),je(_,{class:"add-button",disabled:!Ve(l)||t.id===-1||!Ve(a),onClick:u},{default:De(()=>[je(dt,{icon:Ve(c)},null,8,["icon"])]),_:1},8,["disabled"]),Ve(i)?(_e(),Oe(_,{key:0,class:"back-button",disabled:!Ve(i)||t.id===-1,onClick:d},{default:De(()=>[je(dt,{icon:"tabler-arrow-big-right"})]),_:1},8,["disabled"])):Ye("",!0)]),_:1})]),_:1})}}},Pu=At(ku,[["__scopeId","data-v-841983b2"]]);function Iu(t){return new Int8Array(t)}function Ca(t){return new Int16Array(t)}function ka(t){return new Int32Array(t)}function Pa(t){return new Float32Array(t)}function Ru(t){return new Float64Array(t)}function Ia(t){if(t.length==1)return Pa(t[0]);var e=t[0];t=t.slice(1);for(var n=[],s=0;s<e;s++)n.push(Ia(t));return n}function Ra(t){if(t.length==1)return ka(t[0]);var e=t[0];t=t.slice(1);for(var n=[],s=0;s<e;s++)n.push(Ra(t));return n}function Ba(t){if(t.length==1)return Ca(t[0]);var e=t[0];t=t.slice(1);for(var n=[],s=0;s<e;s++)n.push(Ba(t));return n}function La(t){if(t.length==1)return new Array(t[0]);var e=t[0];t=t.slice(1);for(var n=[],s=0;s<e;s++)n.push(La(t));return n}var Oa={};Oa.fill=function(t,e,n,s){if(arguments.length==2)for(var r=0;r<t.length;r++)t[r]=arguments[1];else for(var r=e;r<n;r++)t[r]=s};var is={};is.arraycopy=function(t,e,n,s,r){for(var i=e+r;e<i;)n[s++]=t[e++]};is.out={};is.out.println=function(t){console.log(t)};is.out.printf=function(){console.log.apply(console,arguments)};var Ds={};Ds.SQRT2=1.4142135623730951;Ds.FAST_LOG10=function(t){return Math.log10(t)};Ds.FAST_LOG10_X=function(t,e){return Math.log10(t)*e};function en(t){this.ordinal=t}en.short_block_allowed=new en(0);en.short_block_coupled=new en(1);en.short_block_dispensed=new en(2);en.short_block_forced=new en(3);var Da={};Da.MAX_VALUE=34028235e31;function Rt(t){this.ordinal=t}Rt.vbr_off=new Rt(0);Rt.vbr_mt=new Rt(1);Rt.vbr_rh=new Rt(2);Rt.vbr_abr=new Rt(3);Rt.vbr_mtrh=new Rt(4);Rt.vbr_default=Rt.vbr_mtrh;var Bu=function(t){},ft={System:is,VbrMode:Rt,Float:Da,ShortBlock:en,Util:Ds,Arrays:Oa,new_array_n:La,new_byte:Iu,new_double:Ru,new_float:Pa,new_float_n:Ia,new_int:ka,new_int_n:Ra,new_short:Ca,new_short_n:Ba,assert:Bu},or,xi;function Lu(){if(xi)return or;xi=1;var t=ft,e=t.System,n=t.Util,s=t.Arrays,r=t.new_float,i=vt();function l(){var c=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.035780907*n.SQRT2*.5/2384e-9,.017876148*n.SQRT2*.5/2384e-9,.003134727*n.SQRT2*.5/2384e-9,.002457142*n.SQRT2*.5/2384e-9,971317e-9*n.SQRT2*.5/2384e-9,218868e-9*n.SQRT2*.5/2384e-9,101566e-9*n.SQRT2*.5/2384e-9,13828e-9*n.SQRT2*.5/2384e-9,12804.797818791945,1945.5515939597317,313.4244966442953,20.801593959731544,1995.1556208053692,9.000838926174497,-29.20218120805369],a=12,u=36,d=[[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,940084909404969e-27,6423305872147839e-28,2382191739347918e-28,5456116108943412e-27,4878985199565852e-27,4240448995017367e-27,3559909094758252e-27,2858043359288075e-27,2156177623817898e-27,1475637723558783e-27,8371015190102974e-28,2599706096327376e-28,-5456116108943412e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758252e-27,-2858043359288076e-27,-2156177623817898e-27,-1475637723558783e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347923e-28,-6423305872147843e-28,-9400849094049696e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049694e-28,-642330587214784e-27,-2382191739347918e-28],[2382191739347913e-28,6423305872147834e-28,9400849094049688e-28,1122435026096556e-27,1183840321267481e-27,1122435026096556e-27,9400849094049688e-28,6423305872147841e-28,2382191739347918e-28,5456116108943413e-27,4878985199565852e-27,4240448995017367e-27,3559909094758253e-27,2858043359288075e-27,2156177623817898e-27,1475637723558782e-27,8371015190102975e-28,2599706096327376e-28,-5461314069809755e-27,-4921085770524055e-27,-4343405037091838e-27,-3732668368707687e-27,-3093523840190885e-27,-2430835727329465e-27,-1734679010007751e-27,-974825365660928e-27,-2797435120168326e-28,0,0,0,0,0,0,-2283748241799531e-28,-4037858874020686e-28,-2146547464825323e-28],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2283748241799531e-28,4037858874020686e-28,2146547464825323e-28,5461314069809755e-27,4921085770524055e-27,4343405037091838e-27,3732668368707687e-27,3093523840190885e-27,2430835727329466e-27,1734679010007751e-27,974825365660928e-27,2797435120168326e-28,-5456116108943413e-27,-4878985199565852e-27,-4240448995017367e-27,-3559909094758253e-27,-2858043359288075e-27,-2156177623817898e-27,-1475637723558782e-27,-8371015190102975e-28,-2599706096327376e-28,-2382191739347913e-28,-6423305872147834e-28,-9400849094049688e-28,-1122435026096556e-27,-1183840321267481e-27,-1122435026096556e-27,-9400849094049688e-28,-6423305872147841e-28,-2382191739347918e-28]],o=d[i.SHORT_TYPE],p=d[i.SHORT_TYPE],_=d[i.SHORT_TYPE],w=d[i.SHORT_TYPE],b=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];function g(m,x,f){for(var A=10,D=x+238-14-286,V=-15;V<0;V++){var O,q,N;O=c[A+-10],q=m[D+-224]*O,N=m[x+224]*O,O=c[A+-9],q+=m[D+-160]*O,N+=m[x+160]*O,O=c[A+-8],q+=m[D+-96]*O,N+=m[x+96]*O,O=c[A+-7],q+=m[D+-32]*O,N+=m[x+32]*O,O=c[A+-6],q+=m[D+32]*O,N+=m[x+-32]*O,O=c[A+-5],q+=m[D+96]*O,N+=m[x+-96]*O,O=c[A+-4],q+=m[D+160]*O,N+=m[x+-160]*O,O=c[A+-3],q+=m[D+224]*O,N+=m[x+-224]*O,O=c[A+-2],q+=m[x+-256]*O,N-=m[D+256]*O,O=c[A+-1],q+=m[x+-192]*O,N-=m[D+192]*O,O=c[A+0],q+=m[x+-128]*O,N-=m[D+128]*O,O=c[A+1],q+=m[x+-64]*O,N-=m[D+64]*O,O=c[A+2],q+=m[x+0]*O,N-=m[D+0]*O,O=c[A+3],q+=m[x+64]*O,N-=m[D+-64]*O,O=c[A+4],q+=m[x+128]*O,N-=m[D+-128]*O,O=c[A+5],q+=m[x+192]*O,N-=m[D+-192]*O,q*=c[A+6],O=N-q,f[30+V*2]=N+q,f[31+V*2]=c[A+7]*O,A+=18,x--,D++}{var q,N,L,I;N=m[x+-16]*c[A+-10],q=m[x+-32]*c[A+-2],N+=(m[x+-48]-m[x+16])*c[A+-9],q+=m[x+-96]*c[A+-1],N+=(m[x+-80]+m[x+48])*c[A+-8],q+=m[x+-160]*c[A+0],N+=(m[x+-112]-m[x+80])*c[A+-7],q+=m[x+-224]*c[A+1],N+=(m[x+-144]+m[x+112])*c[A+-6],q-=m[x+32]*c[A+2],N+=(m[x+-176]-m[x+144])*c[A+-5],q-=m[x+96]*c[A+3],N+=(m[x+-208]+m[x+176])*c[A+-4],q-=m[x+160]*c[A+4],N+=(m[x+-240]-m[x+208])*c[A+-3],q-=m[x+224],L=q-N,I=q+N,N=f[14],q=f[15]-N,f[31]=I+N,f[30]=L+q,f[15]=L-q,f[14]=I-N}{var h;h=f[28]-f[0],f[0]+=f[28],f[28]=h*c[A+-2*18+7],h=f[29]-f[1],f[1]+=f[29],f[29]=h*c[A+-2*18+7],h=f[26]-f[2],f[2]+=f[26],f[26]=h*c[A+-4*18+7],h=f[27]-f[3],f[3]+=f[27],f[27]=h*c[A+-4*18+7],h=f[24]-f[4],f[4]+=f[24],f[24]=h*c[A+-6*18+7],h=f[25]-f[5],f[5]+=f[25],f[25]=h*c[A+-6*18+7],h=f[22]-f[6],f[6]+=f[22],f[22]=h*n.SQRT2,h=f[23]-f[7],f[7]+=f[23],f[23]=h*n.SQRT2-f[7],f[7]-=f[6],f[22]-=f[7],f[23]-=f[22],h=f[6],f[6]=f[31]-h,f[31]=f[31]+h,h=f[7],f[7]=f[30]-h,f[30]=f[30]+h,h=f[22],f[22]=f[15]-h,f[15]=f[15]+h,h=f[23],f[23]=f[14]-h,f[14]=f[14]+h,h=f[20]-f[8],f[8]+=f[20],f[20]=h*c[A+-10*18+7],h=f[21]-f[9],f[9]+=f[21],f[21]=h*c[A+-10*18+7],h=f[18]-f[10],f[10]+=f[18],f[18]=h*c[A+-12*18+7],h=f[19]-f[11],f[11]+=f[19],f[19]=h*c[A+-12*18+7],h=f[16]-f[12],f[12]+=f[16],f[16]=h*c[A+-14*18+7],h=f[17]-f[13],f[13]+=f[17],f[17]=h*c[A+-14*18+7],h=-f[20]+f[24],f[20]+=f[24],f[24]=h*c[A+-12*18+7],h=-f[21]+f[25],f[21]+=f[25],f[25]=h*c[A+-12*18+7],h=f[4]-f[8],f[4]+=f[8],f[8]=h*c[A+-12*18+7],h=f[5]-f[9],f[5]+=f[9],f[9]=h*c[A+-12*18+7],h=f[0]-f[12],f[0]+=f[12],f[12]=h*c[A+-4*18+7],h=f[1]-f[13],f[1]+=f[13],f[13]=h*c[A+-4*18+7],h=f[16]-f[28],f[16]+=f[28],f[28]=h*c[A+-4*18+7],h=-f[17]+f[29],f[17]+=f[29],f[29]=h*c[A+-4*18+7],h=n.SQRT2*(f[2]-f[10]),f[2]+=f[10],f[10]=h,h=n.SQRT2*(f[3]-f[11]),f[3]+=f[11],f[11]=h,h=n.SQRT2*(-f[18]+f[26]),f[18]+=f[26],f[26]=h-f[18],h=n.SQRT2*(-f[19]+f[27]),f[19]+=f[27],f[27]=h-f[19],h=f[2],f[19]-=f[3],f[3]-=h,f[2]=f[31]-h,f[31]+=h,h=f[3],f[11]-=f[19],f[18]-=h,f[3]=f[30]-h,f[30]+=h,h=f[18],f[27]-=f[11],f[19]-=h,f[18]=f[15]-h,f[15]+=h,h=f[19],f[10]-=h,f[19]=f[14]-h,f[14]+=h,h=f[10],f[11]-=h,f[10]=f[23]-h,f[23]+=h,h=f[11],f[26]-=h,f[11]=f[22]-h,f[22]+=h,h=f[26],f[27]-=h,f[26]=f[7]-h,f[7]+=h,h=f[27],f[27]=f[6]-h,f[6]+=h,h=n.SQRT2*(f[0]-f[4]),f[0]+=f[4],f[4]=h,h=n.SQRT2*(f[1]-f[5]),f[1]+=f[5],f[5]=h,h=n.SQRT2*(f[16]-f[20]),f[16]+=f[20],f[20]=h,h=n.SQRT2*(f[17]-f[21]),f[17]+=f[21],f[21]=h,h=-n.SQRT2*(f[8]-f[12]),f[8]+=f[12],f[12]=h-f[8],h=-n.SQRT2*(f[9]-f[13]),f[9]+=f[13],f[13]=h-f[9],h=-n.SQRT2*(f[25]-f[29]),f[25]+=f[29],f[29]=h-f[25],h=-n.SQRT2*(f[24]+f[28]),f[24]-=f[28],f[28]=h-f[24],h=f[24]-f[16],f[24]=h,h=f[20]-h,f[20]=h,h=f[28]-h,f[28]=h,h=f[25]-f[17],f[25]=h,h=f[21]-h,f[21]=h,h=f[29]-h,f[29]=h,h=f[17]-f[1],f[17]=h,h=f[9]-h,f[9]=h,h=f[25]-h,f[25]=h,h=f[5]-h,f[5]=h,h=f[21]-h,f[21]=h,h=f[13]-h,f[13]=h,h=f[29]-h,f[29]=h,h=f[1]-f[0],f[1]=h,h=f[16]-h,f[16]=h,h=f[17]-h,f[17]=h,h=f[8]-h,f[8]=h,h=f[9]-h,f[9]=h,h=f[24]-h,f[24]=h,h=f[25]-h,f[25]=h,h=f[4]-h,f[4]=h,h=f[5]-h,f[5]=h,h=f[20]-h,f[20]=h,h=f[21]-h,f[21]=h,h=f[12]-h,f[12]=h,h=f[13]-h,f[13]=h,h=f[28]-h,f[28]=h,h=f[29]-h,f[29]=h,h=f[0],f[0]+=f[31],f[31]-=h,h=f[1],f[1]+=f[30],f[30]-=h,h=f[16],f[16]+=f[15],f[15]-=h,h=f[17],f[17]+=f[14],f[14]-=h,h=f[8],f[8]+=f[23],f[23]-=h,h=f[9],f[9]+=f[22],f[22]-=h,h=f[24],f[24]+=f[7],f[7]-=h,h=f[25],f[25]+=f[6],f[6]-=h,h=f[4],f[4]+=f[27],f[27]-=h,h=f[5],f[5]+=f[26],f[26]-=h,h=f[20],f[20]+=f[11],f[11]-=h,h=f[21],f[21]+=f[10],f[10]-=h,h=f[12],f[12]+=f[19],f[19]-=h,h=f[13],f[13]+=f[18],f[18]-=h,h=f[28],f[28]+=f[3],f[3]-=h,h=f[29],f[29]+=f[2],f[2]-=h}}function E(m,x){for(var f=0;f<3;f++){var A,D,V,O,q,N;O=m[x+2*3]*d[i.SHORT_TYPE][0]-m[x+5*3],A=m[x+0*3]*d[i.SHORT_TYPE][2]-m[x+3*3],D=O+A,V=O-A,O=m[x+5*3]*d[i.SHORT_TYPE][0]+m[x+2*3],A=m[x+3*3]*d[i.SHORT_TYPE][2]+m[x+0*3],q=O+A,N=-O+A,A=(m[x+1*3]*d[i.SHORT_TYPE][1]-m[x+4*3])*2069978111953089e-26,O=(m[x+4*3]*d[i.SHORT_TYPE][1]+m[x+1*3])*2069978111953089e-26,m[x+3*0]=D*190752519173728e-25+A,m[x+3*5]=-q*190752519173728e-25+O,V=V*.8660254037844387*1907525191737281e-26,q=q*.5*1907525191737281e-26+O,m[x+3*1]=V-q,m[x+3*2]=V+q,D=D*.5*1907525191737281e-26-A,N=N*.8660254037844387*1907525191737281e-26,m[x+3*3]=D+N,m[x+3*4]=D-N,x++}}function T(m,x,f){var A,D;{var V,O,q,N,L,I,h,k;V=f[17]-f[9],q=f[15]-f[11],N=f[14]-f[12],L=f[0]+f[8],I=f[1]+f[7],h=f[2]+f[6],k=f[3]+f[5],m[x+17]=L+h-k-(I-f[4]),D=(L+h-k)*p[12+7]+(I-f[4]),A=(V-q-N)*p[12+6],m[x+5]=A+D,m[x+6]=A-D,O=(f[16]-f[10])*p[12+6],I=I*p[12+7]+f[4],A=V*p[12+0]+O+q*p[12+1]+N*p[12+2],D=-L*p[12+4]+I-h*p[12+5]+k*p[12+3],m[x+1]=A+D,m[x+2]=A-D,A=V*p[12+1]-O-q*p[12+2]+N*p[12+0],D=-L*p[12+5]+I-h*p[12+3]+k*p[12+4],m[x+9]=A+D,m[x+10]=A-D,A=V*p[12+2]-O+q*p[12+0]-N*p[12+1],D=L*p[12+3]-I+h*p[12+4]-k*p[12+5],m[x+13]=A+D,m[x+14]=A-D}{var H,R,F,Y,X,M,y,v;H=f[8]-f[0],F=f[6]-f[2],Y=f[5]-f[3],X=f[17]+f[9],M=f[16]+f[10],y=f[15]+f[11],v=f[14]+f[12],m[x+0]=X+y+v+(M+f[13]),A=(X+y+v)*p[12+7]-(M+f[13]),D=(H-F+Y)*p[12+6],m[x+11]=A+D,m[x+12]=A-D,R=(f[7]-f[1])*p[12+6],M=f[13]-M*p[12+7],A=X*p[12+3]-M+y*p[12+4]+v*p[12+5],D=H*p[12+2]+R+F*p[12+0]+Y*p[12+1],m[x+3]=A+D,m[x+4]=A-D,A=-X*p[12+5]+M-y*p[12+3]-v*p[12+4],D=H*p[12+1]+R-F*p[12+2]-Y*p[12+0],m[x+7]=A+D,m[x+8]=A-D,A=-X*p[12+4]+M-y*p[12+5]-v*p[12+3],D=H*p[12+0]-R+F*p[12+1]-Y*p[12+2],m[x+15]=A+D,m[x+16]=A-D}}this.mdct_sub48=function(m,x,f){for(var A=x,D=286,V=0;V<m.channels_out;V++){for(var O=0;O<m.mode_gr;O++){for(var q,N=m.l3_side.tt[O][V],L=N.xr,I=0,h=m.sb_sample[V][1-O],k=0,H=0;H<18/2;H++)for(g(A,D,h[k]),g(A,D+32,h[k+1]),k+=2,D+=64,q=1;q<32;q+=2)h[k-1][q]*=-1;for(q=0;q<32;q++,I+=18){var R=N.block_type,F=m.sb_sample[V][O],Y=m.sb_sample[V][1-O];if(N.mixed_block_flag!=0&&q<2&&(R=0),m.amp_filter[q]<1e-12)s.fill(L,I+0,I+18,0);else{if(m.amp_filter[q]<1)for(var H=0;H<18;H++)Y[H][b[q]]*=m.amp_filter[q];if(R==i.SHORT_TYPE){for(var H=-a/4;H<0;H++){var X=d[i.SHORT_TYPE][H+3];L[I+H*3+9]=F[9+H][b[q]]*X-F[8-H][b[q]],L[I+H*3+18]=F[14-H][b[q]]*X+F[15+H][b[q]],L[I+H*3+10]=F[15+H][b[q]]*X-F[14-H][b[q]],L[I+H*3+19]=Y[2-H][b[q]]*X+Y[3+H][b[q]],L[I+H*3+11]=Y[3+H][b[q]]*X-Y[2-H][b[q]],L[I+H*3+20]=Y[8-H][b[q]]*X+Y[9+H][b[q]]}E(L,I)}else{for(var M=r(18),H=-u/4;H<0;H++){var y,v;y=d[R][H+27]*Y[H+9][b[q]]+d[R][H+36]*Y[8-H][b[q]],v=d[R][H+9]*F[H+9][b[q]]-d[R][H+18]*F[8-H][b[q]],M[H+9]=y-v*o[3+H+9],M[H+18]=y*o[3+H+9]+v}T(L,I,M)}}if(R!=i.SHORT_TYPE&&q!=0)for(var H=7;H>=0;--H){var C,$;C=L[I+H]*_[20+H]+L[I+-1-H]*w[28+H],$=L[I+H]*w[28+H]-L[I+-1-H]*_[20+H],L[I+-1-H]=C,L[I+H]=$}}}if(A=f,D=286,m.mode_gr==1)for(var Z=0;Z<18;Z++)e.arraycopy(m.sb_sample[V][1][Z],0,m.sb_sample[V][0][Z],0,32)}}}return or=l,or}var lr,Ci;function Na(){if(Ci)return lr;Ci=1;var t=vt(),e=ft,n=e.System,s=e.new_float,r=e.new_float_n;function i(){this.l=s(t.SBMAX_l),this.s=r([t.SBMAX_s,3]);var l=this;this.assign=function(c){n.arraycopy(c.l,0,l.l,0,t.SBMAX_l);for(var a=0;a<t.SBMAX_s;a++)for(var u=0;u<3;u++)l.s[a][u]=c.s[a][u]}}return lr=i,lr}var cr,ki;function Ou(){if(ki)return cr;ki=1;var t=Na();function e(){this.thm=new t,this.en=new t}return cr=e,cr}function Wt(t){var e=t;this.ordinal=function(){return e}}Wt.STEREO=new Wt(0);Wt.JOINT_STEREO=new Wt(1);Wt.DUAL_CHANNEL=new Wt(2);Wt.MONO=new Wt(3);Wt.NOT_SET=new Wt(4);var Ns=Wt,ur,Pi;function vt(){if(Pi)return ur;Pi=1;var t=ft,e=t.System,n=t.VbrMode,s=t.new_array_n,r=t.new_float,i=t.new_float_n,l=t.new_int,c=t.assert;a.ENCDELAY=576,a.POSTDELAY=1152,a.MDCTDELAY=48,a.FFTOFFSET=224+a.MDCTDELAY,a.DECDELAY=528,a.SBLIMIT=32,a.CBANDS=64,a.SBPSY_l=21,a.SBPSY_s=12,a.SBMAX_l=22,a.SBMAX_s=13,a.PSFB21=6,a.PSFB12=6,a.BLKSIZE=1024,a.HBLKSIZE=a.BLKSIZE/2+1,a.BLKSIZE_s=256,a.HBLKSIZE_s=a.BLKSIZE_s/2+1,a.NORM_TYPE=0,a.START_TYPE=1,a.SHORT_TYPE=2,a.STOP_TYPE=3,a.MPG_MD_LR_LR=0,a.MPG_MD_LR_I=1,a.MPG_MD_MS_LR=2,a.MPG_MD_MS_I=3,a.fircoef=[-.0207887*5,-.0378413*5,-.0432472*5,-.031183*5,779609e-23*5,.0467745*5,.10091*5,.151365*5,.187098*5];function a(){var u=Lu(),d=Ou(),o=Ns,p=a.FFTOFFSET,_=a.MPG_MD_MS_LR,w=null;this.psy=null;var b=null,g=null,E=null;this.setModules=function(A,D,V,O){w=A,this.psy=D,b=D,g=O,E=V};var T=new u;function m(A){var D,V;if(A.ATH.useAdjust==0){A.ATH.adjust=1;return}if(V=A.loudness_sq[0][0],D=A.loudness_sq[1][0],A.channels_out==2?(V+=A.loudness_sq[0][1],D+=A.loudness_sq[1][1]):(V+=V,D+=D),A.mode_gr==2&&(V=Math.max(V,D)),V*=.5,V*=A.ATH.aaSensitivityP,V>.03125)A.ATH.adjust>=1?A.ATH.adjust=1:A.ATH.adjust<A.ATH.adjustLimit&&(A.ATH.adjust=A.ATH.adjustLimit),A.ATH.adjustLimit=1;else{var O=31.98*V+625e-6;A.ATH.adjust>=O?(A.ATH.adjust*=O*.075+.925,A.ATH.adjust<O&&(A.ATH.adjust=O)):A.ATH.adjustLimit>=O?A.ATH.adjust=O:A.ATH.adjust<A.ATH.adjustLimit&&(A.ATH.adjust=A.ATH.adjustLimit),A.ATH.adjustLimit=O}}function x(A){var D,V;for(c(0<=A.bitrate_index&&A.bitrate_index<16),c(0<=A.mode_ext&&A.mode_ext<4),A.bitrate_stereoMode_Hist[A.bitrate_index][4]++,A.bitrate_stereoMode_Hist[15][4]++,A.channels_out==2&&(A.bitrate_stereoMode_Hist[A.bitrate_index][A.mode_ext]++,A.bitrate_stereoMode_Hist[15][A.mode_ext]++),D=0;D<A.mode_gr;++D)for(V=0;V<A.channels_out;++V){var O=A.l3_side.tt[D][V].block_type|0;A.l3_side.tt[D][V].mixed_block_flag!=0&&(O=4),A.bitrate_blockType_Hist[A.bitrate_index][O]++,A.bitrate_blockType_Hist[A.bitrate_index][5]++,A.bitrate_blockType_Hist[15][O]++,A.bitrate_blockType_Hist[15][5]++}}function f(A,D){var V=A.internal_flags,O,q;if(V.lame_encode_frame_init==0){var N,L,I=r(2014),h=r(286+1152+576);for(V.lame_encode_frame_init=1,N=0,L=0;N<286+576*(1+V.mode_gr);++N)N<576*V.mode_gr?(I[N]=0,V.channels_out==2&&(h[N]=0)):(I[N]=D[0][L],V.channels_out==2&&(h[N]=D[1][L]),++L);for(q=0;q<V.mode_gr;q++)for(O=0;O<V.channels_out;O++)V.l3_side.tt[q][O].block_type=a.SHORT_TYPE;T.mdct_sub48(V,I,h),c(576>=a.FFTOFFSET),c(V.mf_size>=a.BLKSIZE+A.framesize-a.FFTOFFSET),c(V.mf_size>=512+A.framesize-32)}}this.lame_encode_mp3_frame=function(A,D,V,O,q,N){var L,I=s([2,2]);I[0][0]=new d,I[0][1]=new d,I[1][0]=new d,I[1][1]=new d;var h=s([2,2]);h[0][0]=new d,h[0][1]=new d,h[1][0]=new d,h[1][1]=new d;var k,H=[null,null],R=A.internal_flags,F=i([2,4]),Y=[.5,.5],X=[[0,0],[0,0]],M=[[0,0],[0,0]],y,v,C;if(H[0]=D,H[1]=V,R.lame_encode_frame_init==0&&f(A,H),R.padding=0,(R.slot_lag-=R.frac_SpF)<0&&(R.slot_lag+=A.out_samplerate,R.padding=1),R.psymodel!=0){var $,Z=[null,null],se=0,j=l(2);for(C=0;C<R.mode_gr;C++){for(v=0;v<R.channels_out;v++)Z[v]=H[v],se=576+C*576-a.FFTOFFSET;if(A.VBR==n.vbr_mtrh||A.VBR==n.vbr_mt?$=b.L3psycho_anal_vbr(A,Z,se,C,I,h,X[C],M[C],F[C],j):$=b.L3psycho_anal_ns(A,Z,se,C,I,h,X[C],M[C],F[C],j),$!=0)return-4;for(A.mode==o.JOINT_STEREO&&(Y[C]=F[C][2]+F[C][3],Y[C]>0&&(Y[C]=F[C][3]/Y[C])),v=0;v<R.channels_out;v++){var ee=R.l3_side.tt[C][v];ee.block_type=j[v],ee.mixed_block_flag=0}}}else for(C=0;C<R.mode_gr;C++)for(v=0;v<R.channels_out;v++)R.l3_side.tt[C][v].block_type=a.NORM_TYPE,R.l3_side.tt[C][v].mixed_block_flag=0,M[C][v]=X[C][v]=700;if(m(R),T.mdct_sub48(R,H[0],H[1]),R.mode_ext=a.MPG_MD_LR_LR,A.force_ms)R.mode_ext=a.MPG_MD_MS_LR;else if(A.mode==o.JOINT_STEREO){var ce=0,me=0;for(C=0;C<R.mode_gr;C++)for(v=0;v<R.channels_out;v++)ce+=M[C][v],me+=X[C][v];if(ce<=1*me){var ye=R.l3_side.tt[0],Me=R.l3_side.tt[R.mode_gr-1];ye[0].block_type==ye[1].block_type&&Me[0].block_type==Me[1].block_type&&(R.mode_ext=a.MPG_MD_MS_LR)}}if(R.mode_ext==_?(k=h,y=M):(k=I,y=X),A.analysis&&R.pinfo!=null)for(C=0;C<R.mode_gr;C++)for(v=0;v<R.channels_out;v++)R.pinfo.ms_ratio[C]=R.ms_ratio[C],R.pinfo.ms_ener_ratio[C]=Y[C],R.pinfo.blocktype[C][v]=R.l3_side.tt[C][v].block_type,R.pinfo.pe[C][v]=y[C][v],e.arraycopy(R.l3_side.tt[C][v].xr,0,R.pinfo.xr[C][v],0,576),R.mode_ext==_&&(R.pinfo.ers[C][v]=R.pinfo.ers[C][v+2],e.arraycopy(R.pinfo.energy[C][v+2],0,R.pinfo.energy[C][v],0,R.pinfo.energy[C][v].length));if(A.VBR==n.vbr_off||A.VBR==n.vbr_abr){var Se,ie;for(Se=0;Se<18;Se++)R.nsPsy.pefirbuf[Se]=R.nsPsy.pefirbuf[Se+1];for(ie=0,C=0;C<R.mode_gr;C++)for(v=0;v<R.channels_out;v++)ie+=y[C][v];for(R.nsPsy.pefirbuf[18]=ie,ie=R.nsPsy.pefirbuf[9],Se=0;Se<9;Se++)ie+=(R.nsPsy.pefirbuf[Se]+R.nsPsy.pefirbuf[18-Se])*a.fircoef[Se];for(ie=670*5*R.mode_gr*R.channels_out/ie,C=0;C<R.mode_gr;C++)for(v=0;v<R.channels_out;v++)y[C][v]*=ie}if(R.iteration_loop.iteration_loop(A,y,Y,k),w.format_bitstream(A),L=w.copy_buffer(R,O,q,N,1),A.bWriteVbrTag&&g.addVbrFrame(A),A.analysis&&R.pinfo!=null){for(v=0;v<R.channels_out;v++){var ue;for(ue=0;ue<p;ue++)R.pinfo.pcmdata[v][ue]=R.pinfo.pcmdata[v][ue+A.framesize];for(ue=p;ue<1600;ue++)R.pinfo.pcmdata[v][ue]=H[v][ue-p]}E.set_frame_pinfo(A,k)}return x(R),L}}return ur=a,ur}var za=ft,Ii=za.Util,Ri=za.new_float,lt=vt();function Du(){var t=Ri(lt.BLKSIZE),e=Ri(lt.BLKSIZE_s/2),n=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475];function s(i,l,c){var a=0,u,d,o;c<<=1;var p=l+c;u=4;do{var _,w,b,g,E,T,m;m=u>>1,g=u,E=u<<1,T=E+g,u=E<<1,d=l,o=d+m;do{var x,f,A,D;f=i[d+0]-i[d+g],x=i[d+0]+i[d+g],D=i[d+E]-i[d+T],A=i[d+E]+i[d+T],i[d+E]=x-A,i[d+0]=x+A,i[d+T]=f-D,i[d+g]=f+D,f=i[o+0]-i[o+g],x=i[o+0]+i[o+g],D=Ii.SQRT2*i[o+T],A=Ii.SQRT2*i[o+E],i[o+E]=x-A,i[o+0]=x+A,i[o+T]=f-D,i[o+g]=f+D,o+=u,d+=u}while(d<p);for(w=n[a+0],_=n[a+1],b=1;b<m;b++){var V,O;V=1-2*_*_,O=2*_*w,d=l+b,o=l+g-b;do{var q,N,L,x,f,I,A,h,D,k;N=O*i[d+g]-V*i[o+g],q=V*i[d+g]+O*i[o+g],f=i[d+0]-q,x=i[d+0]+q,I=i[o+0]-N,L=i[o+0]+N,N=O*i[d+T]-V*i[o+T],q=V*i[d+T]+O*i[o+T],D=i[d+E]-q,A=i[d+E]+q,k=i[o+E]-N,h=i[o+E]+N,N=_*A-w*k,q=w*A+_*k,i[d+E]=x-q,i[d+0]=x+q,i[o+T]=I-N,i[o+g]=I+N,N=w*h-_*D,q=_*h+w*D,i[o+E]=L-q,i[o+0]=L+q,i[d+T]=f-N,i[d+g]=f+N,o+=u,d+=u}while(d<p);V=w,w=V*n[a+0]-_*n[a+1],_=V*n[a+1]+_*n[a+0]}a+=2}while(u<c)}var r=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(i,l,c,a,u){for(var d=0;d<3;d++){var o=lt.BLKSIZE_s/2,p=65535&576/3*(d+1),_=lt.BLKSIZE_s/8-1;do{var w,b,g,E,T,m=r[_<<2]&255;w=e[m]*a[c][u+m+p],T=e[127-m]*a[c][u+m+p+128],b=w-T,w=w+T,g=e[m+64]*a[c][u+m+p+64],T=e[63-m]*a[c][u+m+p+192],E=g-T,g=g+T,o-=4,l[d][o+0]=w+g,l[d][o+2]=w-g,l[d][o+1]=b+E,l[d][o+3]=b-E,w=e[m+1]*a[c][u+m+p+1],T=e[126-m]*a[c][u+m+p+129],b=w-T,w=w+T,g=e[m+65]*a[c][u+m+p+65],T=e[62-m]*a[c][u+m+p+193],E=g-T,g=g+T,l[d][o+lt.BLKSIZE_s/2+0]=w+g,l[d][o+lt.BLKSIZE_s/2+2]=w-g,l[d][o+lt.BLKSIZE_s/2+1]=b+E,l[d][o+lt.BLKSIZE_s/2+3]=b-E}while(--_>=0);s(l[d],o,lt.BLKSIZE_s/2)}},this.fft_long=function(i,l,c,a,u){var d=lt.BLKSIZE/8-1,o=lt.BLKSIZE/2;do{var p,_,w,b,g,E=r[d]&255;p=t[E]*a[c][u+E],g=t[E+512]*a[c][u+E+512],_=p-g,p=p+g,w=t[E+256]*a[c][u+E+256],g=t[E+768]*a[c][u+E+768],b=w-g,w=w+g,o-=4,l[o+0]=p+w,l[o+2]=p-w,l[o+1]=_+b,l[o+3]=_-b,p=t[E+1]*a[c][u+E+1],g=t[E+513]*a[c][u+E+513],_=p-g,p=p+g,w=t[E+257]*a[c][u+E+257],g=t[E+769]*a[c][u+E+769],b=w-g,w=w+g,l[o+lt.BLKSIZE/2+0]=p+w,l[o+lt.BLKSIZE/2+2]=p-w,l[o+lt.BLKSIZE/2+1]=_+b,l[o+lt.BLKSIZE/2+3]=_-b}while(--d>=0);s(l,o,lt.BLKSIZE/2)},this.init_fft=function(i){for(var l=0;l<lt.BLKSIZE;l++)t[l]=.42-.5*Math.cos(2*Math.PI*(l+.5)/lt.BLKSIZE)+.08*Math.cos(4*Math.PI*(l+.5)/lt.BLKSIZE);for(var l=0;l<lt.BLKSIZE_s/2;l++)e[l]=.5*(1-Math.cos(2*Math.PI*(l+.5)/lt.BLKSIZE_s))}}var Nu=Du,sn=ft,Bn=sn.VbrMode,Bi=sn.Float,Ln=sn.ShortBlock,Et=sn.Util,zu=sn.Arrays,ct=sn.new_float,Bt=sn.new_float_n,_n=sn.new_int,Xe=sn.assert,$u=Nu,le=vt();function Vu(){var t=Ns,e=new $u,n=2.302585092994046,s=2,r=16,i=2,l=16,c=.34,a=1/(14752*14752)/(le.BLKSIZE/2),u=.01,d=.8,o=.6,p=.3,_=3.5,w=21,b=.2302585093;function g(B,z){for(var G=0,U=0;U<le.BLKSIZE/2;++U)G+=B[U]*z.ATH.eql_w[U];return G*=a,G}function E(B,z,G,U,Q,W,J,te,K,oe,ne){var ae=B.internal_flags;if(K<2)e.fft_long(ae,U[Q],K,oe,ne),e.fft_short(ae,W[J],K,oe,ne);else if(K==2){for(var ve=le.BLKSIZE-1;ve>=0;--ve){var Ie=U[Q+0][ve],de=U[Q+1][ve];U[Q+0][ve]=(Ie+de)*Et.SQRT2*.5,U[Q+1][ve]=(Ie-de)*Et.SQRT2*.5}for(var Pe=2;Pe>=0;--Pe)for(var ve=le.BLKSIZE_s-1;ve>=0;--ve){var Ie=W[J+0][Pe][ve],de=W[J+1][Pe][ve];W[J+0][Pe][ve]=(Ie+de)*Et.SQRT2*.5,W[J+1][Pe][ve]=(Ie-de)*Et.SQRT2*.5}}z[0]=U[Q+0][0],z[0]*=z[0];for(var ve=le.BLKSIZE/2-1;ve>=0;--ve){var re=U[Q+0][le.BLKSIZE/2-ve],Te=U[Q+0][le.BLKSIZE/2+ve];z[le.BLKSIZE/2-ve]=(re*re+Te*Te)*.5}for(var Pe=2;Pe>=0;--Pe){G[Pe][0]=W[J+0][Pe][0],G[Pe][0]*=G[Pe][0];for(var ve=le.BLKSIZE_s/2-1;ve>=0;--ve){var re=W[J+0][Pe][le.BLKSIZE_s/2-ve],Te=W[J+0][Pe][le.BLKSIZE_s/2+ve];G[Pe][le.BLKSIZE_s/2-ve]=(re*re+Te*Te)*.5}}{for(var Fe=0,ve=11;ve<le.HBLKSIZE;ve++)Fe+=z[ve];ae.tot_ener[K]=Fe}if(B.analysis){for(var ve=0;ve<le.HBLKSIZE;ve++)ae.pinfo.energy[te][K][ve]=ae.pinfo.energy_save[K][ve],ae.pinfo.energy_save[K][ve]=z[ve];ae.pinfo.pe[te][K]=ae.pe[K]}B.athaa_loudapprox==2&&K<2&&(ae.loudness_sq[te][K]=ae.loudness_sq_save[K],ae.loudness_sq_save[K]=g(z,ae))}var T=8,m=23,x=15,f,A,D,V=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749];function O(){f=Math.pow(10,(T+1)/16),A=Math.pow(10,(m+1)/16),D=Math.pow(10,x/10)}var q=[3.3246*3.3246,3.23837*3.23837,3.15437*3.15437,3.00412*3.00412,2.86103*2.86103,2.65407*2.65407,2.46209*2.46209,2.284*2.284,2.11879*2.11879,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,1.56911*1.56911,1.46658*1.46658,1.37074*1.37074,1.31036*1.31036,1.25264*1.25264,1.20648*1.20648,1.16203*1.16203,1.12765*1.12765,1.09428*1.09428,1.0659*1.0659,1.03826*1.03826,1.01895*1.01895,1],N=[1.33352*1.33352,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.30382*1.30382,1.22321*1.22321,1.14758*1.14758,1],L=[2.35364*2.35364,2.29259*2.29259,2.23313*2.23313,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,1.49999*1.49999,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.11084*1.11084,1.03826*1.03826];function I(B,z,G,U,Q,W){var J;if(z>B)if(z<B*A)J=z/B;else return B+z;else{if(B>=z*A)return B+z;J=B/z}if(B+=z,U+3<=3+3){if(J>=f)return B;var te=0|Et.FAST_LOG10_X(J,16);return B*N[te]}var te=0|Et.FAST_LOG10_X(J,16);if(W!=0?z=Q.ATH.cb_s[G]*Q.ATH.adjust:z=Q.ATH.cb_l[G]*Q.ATH.adjust,B<D*z){if(B>z){var K,oe;return K=1,te<=13&&(K=L[te]),oe=Et.FAST_LOG10_X(B/z,10/15),B*((q[te]-K)*oe+K)}return te>13?B:B*L[te]}return B*q[te]}var h=[1.33352*1.33352,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.30382*1.30382,1.22321*1.22321,1.14758*1.14758,1];function k(B,z,G){var U;if(B<0&&(B=0),z<0&&(z=0),B<=0)return z;if(z<=0)return B;if(z>B?U=z/B:U=B/z,-2<=G&&G<=2){if(U>=f)return B+z;var Q=0|Et.FAST_LOG10_X(U,16);return(B+z)*h[Q]}return U<A?B+z:(B<z&&(B=z),B)}function H(B,z){var G=B.internal_flags;if(G.channels_out>1){for(var U=0;U<le.SBMAX_l;U++){var Q=G.thm[0].l[U],W=G.thm[1].l[U];G.thm[0].l[U]+=W*z,G.thm[1].l[U]+=Q*z}for(var U=0;U<le.SBMAX_s;U++)for(var J=0;J<3;J++){var Q=G.thm[0].s[U][J],W=G.thm[1].s[U][J];G.thm[0].s[U][J]+=W*z,G.thm[1].s[U][J]+=Q*z}}}function R(B){for(var z=0;z<le.SBMAX_l;z++)if(!(B.thm[0].l[z]>1.58*B.thm[1].l[z]||B.thm[1].l[z]>1.58*B.thm[0].l[z])){var G=B.mld_l[z]*B.en[3].l[z],U=Math.max(B.thm[2].l[z],Math.min(B.thm[3].l[z],G));G=B.mld_l[z]*B.en[2].l[z];var Q=Math.max(B.thm[3].l[z],Math.min(B.thm[2].l[z],G));B.thm[2].l[z]=U,B.thm[3].l[z]=Q}for(var z=0;z<le.SBMAX_s;z++)for(var W=0;W<3;W++)if(!(B.thm[0].s[z][W]>1.58*B.thm[1].s[z][W]||B.thm[1].s[z][W]>1.58*B.thm[0].s[z][W])){var G=B.mld_s[z]*B.en[3].s[z][W],U=Math.max(B.thm[2].s[z][W],Math.min(B.thm[3].s[z][W],G));G=B.mld_s[z]*B.en[2].s[z][W];var Q=Math.max(B.thm[3].s[z][W],Math.min(B.thm[2].s[z][W],G));B.thm[2].s[z][W]=U,B.thm[3].s[z][W]=Q}}function F(B,z,G){var U=z,Q=Math.pow(10,G);z*=2,U*=2;for(var W=0;W<le.SBMAX_l;W++){var J,te,K,oe;if(oe=B.ATH.cb_l[B.bm_l[W]]*Q,J=Math.min(Math.max(B.thm[0].l[W],oe),Math.max(B.thm[1].l[W],oe)),te=Math.max(B.thm[2].l[W],oe),K=Math.max(B.thm[3].l[W],oe),J*z<te+K){var ne=J*U/(te+K);te*=ne,K*=ne}B.thm[2].l[W]=Math.min(te,B.thm[2].l[W]),B.thm[3].l[W]=Math.min(K,B.thm[3].l[W])}Q*=le.BLKSIZE_s/le.BLKSIZE;for(var W=0;W<le.SBMAX_s;W++)for(var ae=0;ae<3;ae++){var J,te,K,oe;if(oe=B.ATH.cb_s[B.bm_s[W]]*Q,J=Math.min(Math.max(B.thm[0].s[W][ae],oe),Math.max(B.thm[1].s[W][ae],oe)),te=Math.max(B.thm[2].s[W][ae],oe),K=Math.max(B.thm[3].s[W][ae],oe),J*z<te+K){var ne=J*z/(te+K);te*=ne,K*=ne}B.thm[2].s[W][ae]=Math.min(B.thm[2].s[W][ae],te),B.thm[3].s[W][ae]=Math.min(B.thm[3].s[W][ae],K)}}function Y(B,z,G,U,Q){var W,J,te=0,K=0;for(W=J=0;W<le.SBMAX_s;++J,++W){for(var oe=B.bo_s[W],ne=B.npart_s,ae=oe<ne?oe:ne;J<ae;)Xe(z[J]>=0),Xe(G[J]>=0),te+=z[J],K+=G[J],J++;if(B.en[U].s[W][Q]=te,B.thm[U].s[W][Q]=K,J>=ne){++W;break}Xe(z[J]>=0),Xe(G[J]>=0);{var ve=B.PSY.bo_s_weight[W],Ie=1-ve;te=ve*z[J],K=ve*G[J],B.en[U].s[W][Q]+=te,B.thm[U].s[W][Q]+=K,te=Ie*z[J],K=Ie*G[J]}}for(;W<le.SBMAX_s;++W)B.en[U].s[W][Q]=0,B.thm[U].s[W][Q]=0}function X(B,z,G,U){var Q,W,J=0,te=0;for(Q=W=0;Q<le.SBMAX_l;++W,++Q){for(var K=B.bo_l[Q],oe=B.npart_l,ne=K<oe?K:oe;W<ne;)Xe(z[W]>=0),Xe(G[W]>=0),J+=z[W],te+=G[W],W++;if(B.en[U].l[Q]=J,B.thm[U].l[Q]=te,W>=oe){++Q;break}Xe(z[W]>=0),Xe(G[W]>=0);{var ae=B.PSY.bo_l_weight[Q],ve=1-ae;J=ae*z[W],te=ae*G[W],B.en[U].l[Q]+=J,B.thm[U].l[Q]+=te,J=ve*z[W],te=ve*G[W]}}for(;Q<le.SBMAX_l;++Q)B.en[U].l[Q]=0,B.thm[U].l[Q]=0}function M(B,z,G,U,Q,W){var J=B.internal_flags,te,K;for(K=te=0;K<J.npart_s;++K){for(var oe=0,ne=J.numlines_s[K],ae=0;ae<ne;++ae,++te){var ve=z[W][te];oe+=ve}G[K]=oe}for(Xe(K==J.npart_s),te=K=0;K<J.npart_s;K++){var Ie=J.s3ind_s[K][0],de=J.s3_ss[te++]*G[Ie];for(++Ie;Ie<=J.s3ind_s[K][1];)de+=J.s3_ss[te]*G[Ie],++te,++Ie;{var Pe=i*J.nb_s1[Q][K];U[K]=Math.min(de,Pe)}if(J.blocktype_old[Q&1]==le.SHORT_TYPE){var Pe=l*J.nb_s2[Q][K],re=U[K];U[K]=Math.min(Pe,re)}J.nb_s2[Q][K]=J.nb_s1[Q][K],J.nb_s1[Q][K]=de,Xe(U[K]>=0)}for(;K<=le.CBANDS;++K)G[K]=0,U[K]=0}function y(B,z,G,U){var Q=B.internal_flags;B.short_blocks==Ln.short_block_coupled&&!(z[0]!=0&&z[1]!=0)&&(z[0]=z[1]=0);for(var W=0;W<Q.channels_out;W++)U[W]=le.NORM_TYPE,B.short_blocks==Ln.short_block_dispensed&&(z[W]=1),B.short_blocks==Ln.short_block_forced&&(z[W]=0),z[W]!=0?(Xe(Q.blocktype_old[W]!=le.START_TYPE),Q.blocktype_old[W]==le.SHORT_TYPE&&(U[W]=le.STOP_TYPE)):(U[W]=le.SHORT_TYPE,Q.blocktype_old[W]==le.NORM_TYPE&&(Q.blocktype_old[W]=le.START_TYPE),Q.blocktype_old[W]==le.STOP_TYPE&&(Q.blocktype_old[W]=le.SHORT_TYPE)),G[W]=Q.blocktype_old[W],Q.blocktype_old[W]=U[W]}function v(B,z,G){return G>=1?B:G<=0?z:z>0?Math.pow(B/z,G)*z:0}var C=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130];function $(B,z){for(var G=309.07,U=0;U<le.SBMAX_s-1;U++)for(var Q=0;Q<3;Q++){var W=B.thm.s[U][Q];if(W>0){var J=W*z,te=B.en.s[U][Q];te>J&&(te>J*1e10?G+=C[U]*(10*n):G+=C[U]*Et.FAST_LOG10(te/J))}}return G}var Z=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1];function se(B,z){for(var G=281.0575,U=0;U<le.SBMAX_l-1;U++){var Q=B.thm.l[U];if(Q>0){var W=Q*z,J=B.en.l[U];J>W&&(J>W*1e10?G+=Z[U]*(10*n):G+=Z[U]*Et.FAST_LOG10(J/W))}}return G}function j(B,z,G,U,Q){var W,J;for(W=J=0;W<B.npart_l;++W){var te=0,K=0,oe;for(oe=0;oe<B.numlines_l[W];++oe,++J){var ne=z[J];te+=ne,K<ne&&(K=ne)}G[W]=te,U[W]=K,Q[W]=te*B.rnumlines_l[W],Xe(B.rnumlines_l[W]>=0),Xe(G[W]>=0),Xe(U[W]>=0),Xe(Q[W]>=0)}}function ee(B,z,G,U){var Q=V.length-1,W=0,J=G[W]+G[W+1];if(J>0){var te=z[W];te<z[W+1]&&(te=z[W+1]),Xe(B.numlines_l[W]+B.numlines_l[W+1]-1>0),J=20*(te*2-J)/(J*(B.numlines_l[W]+B.numlines_l[W+1]-1));var K=0|J;K>Q&&(K=Q),U[W]=K}else U[W]=0;for(W=1;W<B.npart_l-1;W++)if(J=G[W-1]+G[W]+G[W+1],J>0){var te=z[W-1];te<z[W]&&(te=z[W]),te<z[W+1]&&(te=z[W+1]),Xe(B.numlines_l[W-1]+B.numlines_l[W]+B.numlines_l[W+1]-1>0),J=20*(te*3-J)/(J*(B.numlines_l[W-1]+B.numlines_l[W]+B.numlines_l[W+1]-1));var K=0|J;K>Q&&(K=Q),U[W]=K}else U[W]=0;if(Xe(W==B.npart_l-1),J=G[W-1]+G[W],J>0){var te=z[W-1];te<z[W]&&(te=z[W]),Xe(B.numlines_l[W-1]+B.numlines_l[W]-1>0),J=20*(te*2-J)/(J*(B.numlines_l[W-1]+B.numlines_l[W]-1));var K=0|J;K>Q&&(K=Q),U[W]=K}else U[W]=0;Xe(W==B.npart_l-1)}var ce=[-865163e-23*2,-.00851586*2,-674764e-23*2,.0209036*2,-336639e-22*2,-.0438162*2,-154175e-22*2,.0931738*2,-552212e-22*2,-.313819*2];this.L3psycho_anal_ns=function(B,z,G,U,Q,W,J,te,K,oe){var ne=B.internal_flags,ae=Bt([2,le.BLKSIZE]),ve=Bt([2,3,le.BLKSIZE_s]),Ie=ct(le.CBANDS+1),de=ct(le.CBANDS+1),Pe=ct(le.CBANDS+2),re=_n(2),Te=_n(2),Fe,fe,Ae,pe,Re,Ke,we,$e,We=Bt([2,576]),et,xt=_n(le.CBANDS+2),ot=_n(le.CBANDS+2);for(zu.fill(ot,0),Fe=ne.channels_out,B.mode==t.JOINT_STEREO&&(Fe=4),B.VBR==Bn.vbr_off?et=ne.ResvMax==0?0:ne.ResvSize/ne.ResvMax*.5:B.VBR==Bn.vbr_rh||B.VBR==Bn.vbr_mtrh||B.VBR==Bn.vbr_mt?et=.6:et=1,fe=0;fe<ne.channels_out;fe++){var tt=z[fe],yt=G+576-350-w+192;for(pe=0;pe<576;pe++){var Ct,kt;for(Ct=tt[yt+pe+10],kt=0,Re=0;Re<(w-1)/2-1;Re+=2)Ct+=ce[Re]*(tt[yt+pe+Re]+tt[yt+pe+w-Re]),kt+=ce[Re+1]*(tt[yt+pe+Re+1]+tt[yt+pe+w-Re-1]);We[fe][pe]=Ct+kt}Q[U][fe].en.assign(ne.en[fe]),Q[U][fe].thm.assign(ne.thm[fe]),Fe>2&&(W[U][fe].en.assign(ne.en[fe+2]),W[U][fe].thm.assign(ne.thm[fe+2]))}for(fe=0;fe<Fe;fe++){var zt,$t,_t=ct(12),jt=[0,0,0,0],Rn=ct(12),js=1,fi,pi=ct(le.CBANDS),hi=ct(le.CBANDS),nt=[0,0,0,0],mi=ct(le.HBLKSIZE),_i=Bt([3,le.HBLKSIZE_s]);for(Xe(ne.npart_s<=le.CBANDS),Xe(ne.npart_l<=le.CBANDS),pe=0;pe<3;pe++)_t[pe]=ne.nsPsy.last_en_subshort[fe][pe+6],Xe(ne.nsPsy.last_en_subshort[fe][pe+4]>0),Rn[pe]=_t[pe]/ne.nsPsy.last_en_subshort[fe][pe+4],jt[0]+=_t[pe];if(fe==2)for(pe=0;pe<576;pe++){var qs,Us;qs=We[0][pe],Us=We[1][pe],We[0][pe]=qs+Us,We[1][pe]=qs-Us}{var gi=We[fe&1],Yn=0;for(pe=0;pe<9;pe++){for(var Zo=Yn+64,ut=1;Yn<Zo;Yn++)ut<Math.abs(gi[Yn])&&(ut=Math.abs(gi[Yn]));ne.nsPsy.last_en_subshort[fe][pe]=_t[pe+3]=ut,jt[1+pe/3]+=ut,ut>_t[pe+3-2]?(Xe(_t[pe+3-2]>0),ut=ut/_t[pe+3-2]):_t[pe+3-2]>ut*10?ut=_t[pe+3-2]/(ut*10):ut=0,Rn[pe+3]=ut}}if(B.analysis){var Zs=Rn[0];for(pe=1;pe<12;pe++)Zs<Rn[pe]&&(Zs=Rn[pe]);ne.pinfo.ers[U][fe]=ne.pinfo.ers_save[fe],ne.pinfo.ers_save[fe]=Zs}for(fi=fe==3?ne.nsPsy.attackthre_s:ne.nsPsy.attackthre,pe=0;pe<12;pe++)nt[pe/3]==0&&Rn[pe]>fi&&(nt[pe/3]=pe%3+1);for(pe=1;pe<4;pe++){var Qs;jt[pe-1]>jt[pe]?(Xe(jt[pe]>0),Qs=jt[pe-1]/jt[pe]):(Xe(jt[pe-1]>0),Qs=jt[pe]/jt[pe-1]),Qs<1.7&&(nt[pe]=0,pe==1&&(nt[0]=0))}for(nt[0]!=0&&ne.nsPsy.lastAttacks[fe]!=0&&(nt[0]=0),(ne.nsPsy.lastAttacks[fe]==3||nt[0]+nt[1]+nt[2]+nt[3]!=0)&&(js=0,nt[1]!=0&&nt[0]!=0&&(nt[1]=0),nt[2]!=0&&nt[1]!=0&&(nt[2]=0),nt[3]!=0&&nt[2]!=0&&(nt[3]=0)),fe<2?Te[fe]=js:js==0&&(Te[0]=Te[1]=0),K[fe]=ne.tot_ener[fe],$t=ve,zt=ae,E(B,mi,_i,zt,fe&1,$t,fe&1,U,fe,z,G),j(ne,mi,Ie,pi,hi),ee(ne,pi,hi,xt),$e=0;$e<3;$e++){var Ks,St;for(M(B,_i,de,Pe,fe,$e),Y(ne,de,Pe,fe,$e),we=0;we<le.SBMAX_s;we++){if(St=ne.thm[fe].s[we][$e],St*=d,nt[$e]>=2||nt[$e+1]==1){var Wn=$e!=0?$e-1:2,ut=v(ne.thm[fe].s[we][Wn],St,o*et);St=Math.min(St,ut)}if(nt[$e]==1){var Wn=$e!=0?$e-1:2,ut=v(ne.thm[fe].s[we][Wn],St,p*et);St=Math.min(St,ut)}else if($e!=0&&nt[$e-1]==3||$e==0&&ne.nsPsy.lastAttacks[fe]==3){var Wn=$e!=2?$e+1:0,ut=v(ne.thm[fe].s[we][Wn],St,p*et);St=Math.min(St,ut)}Ks=_t[$e*3+3]+_t[$e*3+4]+_t[$e*3+5],_t[$e*3+5]*6<Ks&&(St*=.5,_t[$e*3+4]*6<Ks&&(St*=.5)),ne.thm[fe].s[we][$e]=St}}for(ne.nsPsy.lastAttacks[fe]=nt[2],Ke=0,Ae=0;Ae<ne.npart_l;Ae++){for(var pn=ne.s3ind[Ae][0],Js=Ie[pn]*V[xt[pn]],hn=ne.s3_ll[Ke++]*Js;++pn<=ne.s3ind[Ae][1];)Js=Ie[pn]*V[xt[pn]],hn=I(hn,ne.s3_ll[Ke++]*Js,pn,pn-Ae,ne,0);hn*=.158489319246111,ne.blocktype_old[fe&1]==le.SHORT_TYPE?Pe[Ae]=hn:Pe[Ae]=v(Math.min(hn,Math.min(s*ne.nb_1[fe][Ae],r*ne.nb_2[fe][Ae])),hn,et),ne.nb_2[fe][Ae]=ne.nb_1[fe][Ae],ne.nb_1[fe][Ae]=hn}for(;Ae<=le.CBANDS;++Ae)Ie[Ae]=0,Pe[Ae]=0;X(ne,Ie,Pe,fe)}if((B.mode==t.STEREO||B.mode==t.JOINT_STEREO)&&B.interChRatio>0&&H(B,B.interChRatio),B.mode==t.JOINT_STEREO){var er;R(ne),er=B.msfix,Math.abs(er)>0&&F(ne,er,B.ATHlower*ne.ATH.adjust)}for(y(B,Te,oe,re),fe=0;fe<Fe;fe++){var Gn,jn=0,us,ds;fe>1?(Gn=te,jn=-2,us=le.NORM_TYPE,(oe[0]==le.SHORT_TYPE||oe[1]==le.SHORT_TYPE)&&(us=le.SHORT_TYPE),ds=W[U][fe-2]):(Gn=J,jn=0,us=oe[fe],ds=Q[U][fe]),us==le.SHORT_TYPE?Gn[jn+fe]=$(ds,ne.masking_lower):Gn[jn+fe]=se(ds,ne.masking_lower),B.analysis&&(ne.pinfo.pe[U][fe]=Gn[jn+fe])}return 0};function me(B,z,G,U,Q,W,J,te){var K=B.internal_flags;if(U<2)e.fft_long(K,J[te],U,z,G);else if(U==2)for(var oe=le.BLKSIZE-1;oe>=0;--oe){var ne=J[te+0][oe],ae=J[te+1][oe];J[te+0][oe]=(ne+ae)*Et.SQRT2*.5,J[te+1][oe]=(ne-ae)*Et.SQRT2*.5}W[0]=J[te+0][0],W[0]*=W[0];for(var oe=le.BLKSIZE/2-1;oe>=0;--oe){var ve=J[te+0][le.BLKSIZE/2-oe],Ie=J[te+0][le.BLKSIZE/2+oe];W[le.BLKSIZE/2-oe]=(ve*ve+Ie*Ie)*.5}{for(var de=0,oe=11;oe<le.HBLKSIZE;oe++)de+=W[oe];K.tot_ener[U]=de}if(B.analysis){for(var oe=0;oe<le.HBLKSIZE;oe++)K.pinfo.energy[Q][U][oe]=K.pinfo.energy_save[U][oe],K.pinfo.energy_save[U][oe]=W[oe];K.pinfo.pe[Q][U]=K.pe[U]}}function ye(B,z,G,U,Q,W,J,te){var K=B.internal_flags;if(Q==0&&U<2&&e.fft_short(K,J[te],U,z,G),U==2)for(var oe=le.BLKSIZE_s-1;oe>=0;--oe){var ne=J[te+0][Q][oe],ae=J[te+1][Q][oe];J[te+0][Q][oe]=(ne+ae)*Et.SQRT2*.5,J[te+1][Q][oe]=(ne-ae)*Et.SQRT2*.5}W[Q][0]=J[te+0][Q][0],W[Q][0]*=W[Q][0];for(var oe=le.BLKSIZE_s/2-1;oe>=0;--oe){var ve=J[te+0][Q][le.BLKSIZE_s/2-oe],Ie=J[te+0][Q][le.BLKSIZE_s/2+oe];W[Q][le.BLKSIZE_s/2-oe]=(ve*ve+Ie*Ie)*.5}}function Me(B,z,G,U){var Q=B.internal_flags;B.athaa_loudapprox==2&&G<2&&(Q.loudness_sq[z][G]=Q.loudness_sq_save[G],Q.loudness_sq_save[G]=g(U,Q))}var Se=[-865163e-23*2,-.00851586*2,-674764e-23*2,.0209036*2,-336639e-22*2,-.0438162*2,-154175e-22*2,.0931738*2,-552212e-22*2,-.313819*2];function ie(B,z,G,U,Q,W,J,te,K,oe){for(var ne=Bt([2,576]),ae=B.internal_flags,ve=ae.channels_out,Ie=B.mode==t.JOINT_STEREO?4:ve,de=0;de<ve;de++){firbuf=z[de];for(var Pe=G+576-350-w+192,re=0;re<576;re++){var Te,Fe;Te=firbuf[Pe+re+10],Fe=0;for(var fe=0;fe<(w-1)/2-1;fe+=2)Te+=Se[fe]*(firbuf[Pe+re+fe]+firbuf[Pe+re+w-fe]),Fe+=Se[fe+1]*(firbuf[Pe+re+fe+1]+firbuf[Pe+re+w-fe-1]);ne[de][re]=Te+Fe}Q[U][de].en.assign(ae.en[de]),Q[U][de].thm.assign(ae.thm[de]),Ie>2&&(W[U][de].en.assign(ae.en[de+2]),W[U][de].thm.assign(ae.thm[de+2]))}for(var de=0;de<Ie;de++){var Ae=ct(12),pe=ct(12),Re=[0,0,0,0],Ke=ne[de&1],we=0,$e=de==3?ae.nsPsy.attackthre_s:ae.nsPsy.attackthre,We=1;if(de==2)for(var re=0,fe=576;fe>0;++re,--fe){var et=ne[0][re],xt=ne[1][re];ne[0][re]=et+xt,ne[1][re]=et-xt}for(var re=0;re<3;re++)pe[re]=ae.nsPsy.last_en_subshort[de][re+6],Xe(ae.nsPsy.last_en_subshort[de][re+4]>0),Ae[re]=pe[re]/ae.nsPsy.last_en_subshort[de][re+4],Re[0]+=pe[re];for(var re=0;re<9;re++){for(var ot=we+64,tt=1;we<ot;we++)tt<Math.abs(Ke[we])&&(tt=Math.abs(Ke[we]));ae.nsPsy.last_en_subshort[de][re]=pe[re+3]=tt,Re[1+re/3]+=tt,tt>pe[re+3-2]?(Xe(pe[re+3-2]>0),tt=tt/pe[re+3-2]):pe[re+3-2]>tt*10?tt=pe[re+3-2]/(tt*10):tt=0,Ae[re+3]=tt}for(var re=0;re<3;++re){var yt=pe[re*3+3]+pe[re*3+4]+pe[re*3+5],Ct=1;pe[re*3+5]*6<yt&&(Ct*=.5,pe[re*3+4]*6<yt&&(Ct*=.5)),te[de][re]=Ct}if(B.analysis){for(var kt=Ae[0],re=1;re<12;re++)kt<Ae[re]&&(kt=Ae[re]);ae.pinfo.ers[U][de]=ae.pinfo.ers_save[de],ae.pinfo.ers_save[de]=kt}for(var re=0;re<12;re++)K[de][re/3]==0&&Ae[re]>$e&&(K[de][re/3]=re%3+1);for(var re=1;re<4;re++){var zt=Re[re-1],$t=Re[re],_t=Math.max(zt,$t);_t<4e4&&zt<1.7*$t&&$t<1.7*zt&&(re==1&&K[de][0]<=K[de][re]&&(K[de][0]=0),K[de][re]=0)}K[de][0]<=ae.nsPsy.lastAttacks[de]&&(K[de][0]=0),(ae.nsPsy.lastAttacks[de]==3||K[de][0]+K[de][1]+K[de][2]+K[de][3]!=0)&&(We=0,K[de][1]!=0&&K[de][0]!=0&&(K[de][1]=0),K[de][2]!=0&&K[de][1]!=0&&(K[de][2]=0),K[de][3]!=0&&K[de][2]!=0&&(K[de][3]=0)),de<2?oe[de]=We:We==0&&(oe[0]=oe[1]=0),J[de]=ae.tot_ener[de]}}function ue(B,z,G){if(G==0)for(var U=0;U<B.npart_s;U++)B.nb_s2[z][U]=B.nb_s1[z][U],B.nb_s1[z][U]=0}function ze(B,z){for(var G=0;G<B.npart_l;G++)B.nb_2[z][G]=B.nb_1[z][G],B.nb_1[z][G]=0}function qe(B,z,G,U){var Q=V.length-1,W=0,J=G[W]+G[W+1];if(J>0){var te=z[W];te<z[W+1]&&(te=z[W+1]),Xe(B.numlines_s[W]+B.numlines_s[W+1]-1>0),J=20*(te*2-J)/(J*(B.numlines_s[W]+B.numlines_s[W+1]-1));var K=0|J;K>Q&&(K=Q),U[W]=K}else U[W]=0;for(W=1;W<B.npart_s-1;W++)if(J=G[W-1]+G[W]+G[W+1],Xe(W+1<B.npart_s),J>0){var te=z[W-1];te<z[W]&&(te=z[W]),te<z[W+1]&&(te=z[W+1]),Xe(B.numlines_s[W-1]+B.numlines_s[W]+B.numlines_s[W+1]-1>0),J=20*(te*3-J)/(J*(B.numlines_s[W-1]+B.numlines_s[W]+B.numlines_s[W+1]-1));var K=0|J;K>Q&&(K=Q),U[W]=K}else U[W]=0;if(Xe(W==B.npart_s-1),J=G[W-1]+G[W],J>0){var te=z[W-1];te<z[W]&&(te=z[W]),Xe(B.numlines_s[W-1]+B.numlines_s[W]-1>0),J=20*(te*2-J)/(J*(B.numlines_s[W-1]+B.numlines_s[W]-1));var K=0|J;K>Q&&(K=Q),U[W]=K}else U[W]=0;Xe(W==B.npart_s-1)}function S(B,z,G,U,Q,W){var J=B.internal_flags,te=new float[le.CBANDS],K=ct(le.CBANDS),oe,ne,ae,ve=new int[le.CBANDS];for(ae=ne=0;ae<J.npart_s;++ae){var Ie=0,de=0,Pe=J.numlines_s[ae];for(oe=0;oe<Pe;++oe,++ne){var re=z[W][ne];Ie+=re,de<re&&(de=re)}G[ae]=Ie,te[ae]=de,K[ae]=Ie/Pe,Xe(K[ae]>=0)}for(Xe(ae==J.npart_s);ae<le.CBANDS;++ae)te[ae]=0,K[ae]=0;for(qe(J,te,K,ve),ne=ae=0;ae<J.npart_s;ae++){var Te=J.s3ind_s[ae][0],Fe=J.s3ind_s[ae][1],fe,Ae,pe,Re,Ke;for(fe=ve[Te],Ae=1,Re=J.s3_ss[ne]*G[Te]*V[ve[Te]],++ne,++Te;Te<=Fe;)fe+=ve[Te],Ae+=1,pe=J.s3_ss[ne]*G[Te]*V[ve[Te]],Re=k(Re,pe,Te-ae),++ne,++Te;fe=(1+2*fe)/(2*Ae),Ke=V[fe]*.5,Re*=Ke,U[ae]=Re,J.nb_s2[Q][ae]=J.nb_s1[Q][ae],J.nb_s1[Q][ae]=Re,pe=te[ae],pe*=J.minval_s[ae],pe*=Ke,U[ae]>pe&&(U[ae]=pe),J.masking_lower>1&&(U[ae]*=J.masking_lower),U[ae]>G[ae]&&(U[ae]=G[ae]),J.masking_lower<1&&(U[ae]*=J.masking_lower),Xe(U[ae]>=0)}for(;ae<le.CBANDS;++ae)G[ae]=0,U[ae]=0}function P(B,z,G,U,Q){var W=ct(le.CBANDS),J=ct(le.CBANDS),te=_n(le.CBANDS+2),K;j(B,z,G,W,J),ee(B,W,J,te);var oe=0;for(K=0;K<B.npart_l;K++){var ne,ae,ve,Ie,de=B.s3ind[K][0],Pe=B.s3ind[K][1],re=0,Te=0;for(re=te[de],Te+=1,ae=B.s3_ll[oe]*G[de]*V[te[de]],++oe,++de;de<=Pe;)re+=te[de],Te+=1,ne=B.s3_ll[oe]*G[de]*V[te[de]],Ie=k(ae,ne,de-K),ae=Ie,++oe,++de;if(re=(1+2*re)/(2*Te),ve=V[re]*.5,ae*=ve,B.blocktype_old[Q&1]==le.SHORT_TYPE){var Fe=s*B.nb_1[Q][K];Fe>0?U[K]=Math.min(ae,Fe):U[K]=Math.min(ae,G[K]*p)}else{var fe=r*B.nb_2[Q][K],Ae=s*B.nb_1[Q][K],Fe;fe<=0&&(fe=ae),Ae<=0&&(Ae=ae),B.blocktype_old[Q&1]==le.NORM_TYPE?Fe=Math.min(Ae,fe):Fe=Ae,U[K]=Math.min(ae,Fe)}B.nb_2[Q][K]=B.nb_1[Q][K],B.nb_1[Q][K]=ae,ne=W[K],ne*=B.minval_l[K],ne*=ve,U[K]>ne&&(U[K]=ne),B.masking_lower>1&&(U[K]*=B.masking_lower),U[K]>G[K]&&(U[K]=G[K]),B.masking_lower<1&&(U[K]*=B.masking_lower),Xe(U[K]>=0)}for(;K<le.CBANDS;++K)G[K]=0,U[K]=0}function ge(B,z){var G=B.internal_flags;B.short_blocks==Ln.short_block_coupled&&!(z[0]!=0&&z[1]!=0)&&(z[0]=z[1]=0);for(var U=0;U<G.channels_out;U++)B.short_blocks==Ln.short_block_dispensed&&(z[U]=1),B.short_blocks==Ln.short_block_forced&&(z[U]=0)}function Ee(B,z,G){for(var U=B.internal_flags,Q=0;Q<U.channels_out;Q++){var W=le.NORM_TYPE;z[Q]!=0?(Xe(U.blocktype_old[Q]!=le.START_TYPE),U.blocktype_old[Q]==le.SHORT_TYPE&&(W=le.STOP_TYPE)):(W=le.SHORT_TYPE,U.blocktype_old[Q]==le.NORM_TYPE&&(U.blocktype_old[Q]=le.START_TYPE),U.blocktype_old[Q]==le.STOP_TYPE&&(U.blocktype_old[Q]=le.SHORT_TYPE)),G[Q]=U.blocktype_old[Q],U.blocktype_old[Q]=W}}function Be(B,z,G,U,Q,W,J){for(var te=W*2,K=W>0?Math.pow(10,Q):1,oe,ne,ae=0;ae<J;++ae){var ve=B[2][ae],Ie=B[3][ae],de=z[0][ae],Pe=z[1][ae],re=z[2][ae],Te=z[3][ae];if(de<=1.58*Pe&&Pe<=1.58*de){var Fe=G[ae]*Ie,fe=G[ae]*ve;ne=Math.max(re,Math.min(Te,Fe)),oe=Math.max(Te,Math.min(re,fe))}else ne=re,oe=Te;if(W>0){var Ae,pe,Re=U[ae]*K;if(Ae=Math.min(Math.max(de,Re),Math.max(Pe,Re)),re=Math.max(ne,Re),Te=Math.max(oe,Re),pe=re+Te,pe>0&&Ae*te<pe){var Ke=Ae*te/pe;re*=Ke,Te*=Ke}ne=Math.min(re,ne),oe=Math.min(Te,oe)}ne>ve&&(ne=ve),oe>Ie&&(oe=Ie),z[2][ae]=ne,z[3][ae]=oe}}this.L3psycho_anal_vbr=function(B,z,G,U,Q,W,J,te,K,oe){var ne=B.internal_flags,ae,ve,Ie=ct(le.HBLKSIZE),de=Bt([3,le.HBLKSIZE_s]),Pe=Bt([2,le.BLKSIZE]),re=Bt([2,3,le.BLKSIZE_s]),Te=Bt([4,le.CBANDS]),Fe=Bt([4,le.CBANDS]),fe=Bt([4,3]),Ae=.6,pe=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]],Re=_n(2),Ke=B.mode==t.JOINT_STEREO?4:ne.channels_out;ie(B,z,G,U,Q,W,K,fe,pe,Re),ge(B,Re);{for(var we=0;we<Ke;we++){var $e=we&1;ae=Pe,me(B,z,G,we,U,Ie,ae,$e),Me(B,U,we,Ie),Re[$e]!=0?P(ne,Ie,Te[we],Fe[we],we):ze(ne,we)}Re[0]+Re[1]==2&&B.mode==t.JOINT_STEREO&&Be(Te,Fe,ne.mld_cb_l,ne.ATH.cb_l,B.ATHlower*ne.ATH.adjust,B.msfix,ne.npart_l);for(var we=0;we<Ke;we++){var $e=we&1;Re[$e]!=0&&X(ne,Te[we],Fe[we],we)}}{for(var We=0;We<3;We++){for(var we=0;we<Ke;++we){var $e=we&1;Re[$e]!=0?ue(ne,we,We):(ve=re,ye(B,z,G,we,We,de,ve,$e),S(B,de,Te[we],Fe[we],we,We))}Re[0]+Re[1]==0&&B.mode==t.JOINT_STEREO&&Be(Te,Fe,ne.mld_cb_s,ne.ATH.cb_s,B.ATHlower*ne.ATH.adjust,B.msfix,ne.npart_s);for(var we=0;we<Ke;++we){var $e=we&1;Re[$e]==0&&Y(ne,Te[we],Fe[we],we,We)}}for(var we=0;we<Ke;we++){var $e=we&1;if(Re[$e]==0)for(var et=0;et<le.SBMAX_s;et++){for(var xt=ct(3),We=0;We<3;We++){var ot=ne.thm[we].s[et][We];if(ot*=d,pe[we][We]>=2||pe[we][We+1]==1){var tt=We!=0?We-1:2,yt=v(ne.thm[we].s[et][tt],ot,o*Ae);ot=Math.min(ot,yt)}else if(pe[we][We]==1){var tt=We!=0?We-1:2,yt=v(ne.thm[we].s[et][tt],ot,p*Ae);ot=Math.min(ot,yt)}else if(We!=0&&pe[we][We-1]==3||We==0&&ne.nsPsy.lastAttacks[we]==3){var tt=We!=2?We+1:0,yt=v(ne.thm[we].s[et][tt],ot,p*Ae);ot=Math.min(ot,yt)}ot*=fe[we][We],xt[We]=ot}for(var We=0;We<3;We++)ne.thm[we].s[et][We]=xt[We]}}}for(var we=0;we<Ke;we++)ne.nsPsy.lastAttacks[we]=pe[we][2];Ee(B,Re,oe);for(var we=0;we<Ke;we++){var Ct,kt,zt,$t;we>1?(Ct=te,kt=-2,zt=le.NORM_TYPE,(oe[0]==le.SHORT_TYPE||oe[1]==le.SHORT_TYPE)&&(zt=le.SHORT_TYPE),$t=W[U][we-2]):(Ct=J,kt=0,zt=oe[we],$t=Q[U][we]),zt==le.SHORT_TYPE?Ct[kt+we]=$($t,ne.masking_lower):Ct[kt+we]=se($t,ne.masking_lower),B.analysis&&(ne.pinfo.pe[U][we]=Ct[kt+we])}return 0};function Ce(B,z){var G=B,U;return G>=0?U=-G*27:U=G*z,U<=-72?0:Math.exp(U*b)}function ke(B){var z=0,G=0;{var U=0,Q,W;for(U=0;Ce(U,B)>1e-20;U-=1);for(Q=U,W=0;Math.abs(W-Q)>1e-12;)U=(W+Q)/2,Ce(U,B)>0?W=U:Q=U;z=Q}{var U=0,Q,W;for(U=0;Ce(U,B)>1e-20;U+=1);for(Q=0,W=U;Math.abs(W-Q)>1e-12;)U=(W+Q)/2,Ce(U,B)>0?Q=U:W=U;G=W}{var J=0,te=1e3,K;for(K=0;K<=te;++K){var U=z+K*(G-z)/te,oe=Ce(U,B);J+=oe}{var ne=(te+1)/(J*(G-z));return ne}}}function xe(B){var z,G,U,Q;return z=B,z>=0?z*=3:z*=1.5,z>=.5&&z<=2.5?(Q=z-.5,G=8*(Q*Q-2*Q)):G=0,z+=.474,U=15.811389+7.5*z-17.5*Math.sqrt(1+z*z),U<=-60?0:(z=Math.exp((G+U)*b),z/=.6609193,z)}function He(B){return B<0&&(B=0),B=B*.001,13*Math.atan(.76*B)+3.5*Math.atan(B*B/(7.5*7.5))}function Le(B,z,G,U,Q,W,J,te,K,oe,ne,ae){var ve=ct(le.CBANDS+1),Ie=te/(ae>15?2*576:2*192),de=_n(le.HBLKSIZE),Pe;te/=K;var re=0,Te=0;for(Pe=0;Pe<le.CBANDS;Pe++){var Fe,fe;for(Fe=He(te*re),ve[Pe]=te*re,fe=re;He(te*fe)-Fe<c&&fe<=K/2;fe++);for(B[Pe]=fe-re,Te=Pe+1;re<fe;)Xe(re<le.HBLKSIZE),de[re++]=Pe;if(re>K/2){re=K/2,++Pe;break}}Xe(Pe<le.CBANDS),ve[Pe]=te*re;for(var Ae=0;Ae<ae;Ae++){var pe,Re,Ke,we,$e;Ke=oe[Ae],we=oe[Ae+1],pe=0|Math.floor(.5+ne*(Ke-.5)),pe<0&&(pe=0),Re=0|Math.floor(.5+ne*(we-.5)),Re>K/2&&(Re=K/2),G[Ae]=(de[pe]+de[Re])/2,z[Ae]=de[Re];var We=Ie*we;J[Ae]=(We-ve[z[Ae]])/(ve[z[Ae]+1]-ve[z[Ae]]),J[Ae]<0?J[Ae]=0:J[Ae]>1&&(J[Ae]=1),$e=He(te*oe[Ae]*ne),$e=Math.min($e,15.5)/15.5,W[Ae]=Math.pow(10,1.25*(1-Math.cos(Math.PI*$e))-2.5)}re=0;for(var et=0;et<Te;et++){var xt=B[et],Fe,ot;Fe=He(te*re),ot=He(te*(re+xt-1)),U[et]=.5*(Fe+ot),Fe=He(te*(re-.5)),ot=He(te*(re+xt-.5)),Q[et]=ot-Fe,re+=xt}return Te}function be(B,z,G,U,Q,W){var J=Bt([le.CBANDS,le.CBANDS]),te,K=0;if(W)for(var oe=0;oe<z;oe++)for(te=0;te<z;te++){var ne=xe(G[oe]-G[te])*U[te];J[oe][te]=ne*Q[oe]}else for(te=0;te<z;te++)for(var ae=15+Math.min(21/G[te],12),ve=ke(ae),oe=0;oe<z;oe++){var ne=ve*Ce(G[oe]-G[te],ae)*U[te];J[oe][te]=ne*Q[oe]}for(var oe=0;oe<z;oe++){for(te=0;te<z&&!(J[oe][te]>0);te++);for(B[oe][0]=te,te=z-1;te>0&&!(J[oe][te]>0);te--);B[oe][1]=te,K+=B[oe][1]-B[oe][0]+1}for(var Ie=ct(K),de=0,oe=0;oe<z;oe++)for(te=B[oe][0];te<=B[oe][1];te++)Ie[de++]=J[oe][te];return Ie}function Qe(B){var z=He(B);return z=Math.min(z,15.5)/15.5,Math.pow(10,1.25*(1-Math.cos(Math.PI*z))-2.5)}this.psymodel_init=function(B){var z=B.internal_flags,G,U=!0,Q=13,W=24,J=0,te=0,K=-8.25,oe=-4.5,ne=ct(le.CBANDS),ae=ct(le.CBANDS),ve=ct(le.CBANDS),Ie=B.out_samplerate;switch(B.experimentalZ){default:case 0:U=!0;break;case 1:U=!(B.VBR==Bn.vbr_mtrh||B.VBR==Bn.vbr_mt);break;case 2:U=!1;break;case 3:Q=8,J=-1.75,te=-.0125,K=-8.25,oe=-2.25;break}for(z.ms_ener_ratio_old=.25,z.blocktype_old[0]=z.blocktype_old[1]=le.NORM_TYPE,G=0;G<4;++G){for(var re=0;re<le.CBANDS;++re)z.nb_1[G][re]=1e20,z.nb_2[G][re]=1e20,z.nb_s1[G][re]=z.nb_s2[G][re]=1;for(var de=0;de<le.SBMAX_l;de++)z.en[G].l[de]=1e20,z.thm[G].l[de]=1e20;for(var re=0;re<3;++re){for(var de=0;de<le.SBMAX_s;de++)z.en[G].s[de][re]=1e20,z.thm[G].s[de][re]=1e20;z.nsPsy.lastAttacks[G]=0}for(var re=0;re<9;re++)z.nsPsy.last_en_subshort[G][re]=10}for(z.loudness_sq_save[0]=z.loudness_sq_save[1]=0,z.npart_l=Le(z.numlines_l,z.bo_l,z.bm_l,ne,ae,z.mld_l,z.PSY.bo_l_weight,Ie,le.BLKSIZE,z.scalefac_band.l,le.BLKSIZE/(2*576),le.SBMAX_l),Xe(z.npart_l<le.CBANDS),G=0;G<z.npart_l;G++){var Pe=J;ne[G]>=Q&&(Pe=te*(ne[G]-Q)/(W-Q)+J*(W-ne[G])/(W-Q)),ve[G]=Math.pow(10,Pe/10),z.numlines_l[G]>0?z.rnumlines_l[G]=1/z.numlines_l[G]:z.rnumlines_l[G]=0}z.s3_ll=be(z.s3ind,z.npart_l,ne,ae,ve,U);var re=0;for(G=0;G<z.npart_l;G++){var Te;Te=Bi.MAX_VALUE;for(var Fe=0;Fe<z.numlines_l[G];Fe++,re++){var fe=Ie*re/(1e3*le.BLKSIZE),Ae;Ae=this.ATHformula(fe*1e3,B)-20,Ae=Math.pow(10,.1*Ae),Ae*=z.numlines_l[G],Te>Ae&&(Te=Ae)}z.ATH.cb_l[G]=Te,Te=-20+ne[G]*20/10,Te>6&&(Te=100),Te<-15&&(Te=-15),Te-=8,z.minval_l[G]=Math.pow(10,Te/10)*z.numlines_l[G]}for(z.npart_s=Le(z.numlines_s,z.bo_s,z.bm_s,ne,ae,z.mld_s,z.PSY.bo_s_weight,Ie,le.BLKSIZE_s,z.scalefac_band.s,le.BLKSIZE_s/(2*192),le.SBMAX_s),Xe(z.npart_s<le.CBANDS),re=0,G=0;G<z.npart_s;G++){var Te,Pe=K;ne[G]>=Q&&(Pe=oe*(ne[G]-Q)/(W-Q)+K*(W-ne[G])/(W-Q)),ve[G]=Math.pow(10,Pe/10),Te=Bi.MAX_VALUE;for(var Fe=0;Fe<z.numlines_s[G];Fe++,re++){var fe=Ie*re/(1e3*le.BLKSIZE_s),Ae;Ae=this.ATHformula(fe*1e3,B)-20,Ae=Math.pow(10,.1*Ae),Ae*=z.numlines_s[G],Te>Ae&&(Te=Ae)}z.ATH.cb_s[G]=Te,Te=-7+ne[G]*7/12,ne[G]>12&&(Te*=1+Math.log(1+Te)*3.1),ne[G]<12&&(Te*=1+Math.log(1-Te)*2.3),Te<-15&&(Te=-15),Te-=8,z.minval_s[G]=Math.pow(10,Te/10)*z.numlines_s[G]}z.s3_ss=be(z.s3ind_s,z.npart_s,ne,ae,ve,U),O(),e.init_fft(z),z.decay=Math.exp(-1*n/(u*Ie/192));{var pe;pe=_,B.exp_nspsytune&2&&(pe=1),Math.abs(B.msfix)>0&&(pe=B.msfix),B.msfix=pe;for(var Re=0;Re<z.npart_l;Re++)z.s3ind[Re][1]>z.npart_l-1&&(z.s3ind[Re][1]=z.npart_l-1)}var Ke=576*z.mode_gr/Ie;if(z.ATH.decay=Math.pow(10,-12/10*Ke),z.ATH.adjust=.01,z.ATH.adjustLimit=1,Xe(z.bo_l[le.SBMAX_l-1]<=z.npart_l),Xe(z.bo_s[le.SBMAX_s-1]<=z.npart_s),B.ATHtype!=-1){var fe,we=B.out_samplerate/le.BLKSIZE,$e=0;for(fe=0,G=0;G<le.BLKSIZE/2;++G)fe+=we,z.ATH.eql_w[G]=1/Math.pow(10,this.ATHformula(fe,B)/10),$e+=z.ATH.eql_w[G];for($e=1/$e,G=le.BLKSIZE/2;--G>=0;)z.ATH.eql_w[G]*=$e}{for(var Re=re=0;Re<z.npart_s;++Re)for(G=0;G<z.numlines_s[Re];++G)++re;for(var Re=re=0;Re<z.npart_l;++Re)for(G=0;G<z.numlines_l[Re];++G)++re}for(re=0,G=0;G<z.npart_l;G++){var fe=Ie*(re+z.numlines_l[G]/2)/(1*le.BLKSIZE);z.mld_cb_l[G]=Qe(fe),re+=z.numlines_l[G]}for(;G<le.CBANDS;++G)z.mld_cb_l[G]=1;for(re=0,G=0;G<z.npart_s;G++){var fe=Ie*(re+z.numlines_s[G]/2)/(1*le.BLKSIZE_s);z.mld_cb_s[G]=Qe(fe),re+=z.numlines_s[G]}for(;G<le.CBANDS;++G)z.mld_cb_s[G]=1;return 0};function st(B,z){B<-.3&&(B=3410),B/=1e3,B=Math.max(.1,B);var G=3.64*Math.pow(B,-.8)-6.8*Math.exp(-.6*Math.pow(B-3.4,2))+6*Math.exp(-.15*Math.pow(B-8.7,2))+(.6+.04*z)*.001*Math.pow(B,4);return G}this.ATHformula=function(B,z){var G;switch(z.ATHtype){case 0:G=st(B,9);break;case 1:G=st(B,-1);break;case 2:G=st(B,0);break;case 3:G=st(B,1)+6;break;case 4:G=st(B,z.ATHcurve);break;default:G=st(B,0);break}return G}}var Hu=Vu,Xu=Ns;function Fu(){this.class_id=0,this.num_samples=0,this.num_channels=0,this.in_samplerate=0,this.out_samplerate=0,this.scale=0,this.scale_left=0,this.scale_right=0,this.analysis=!1,this.bWriteVbrTag=!1,this.decode_only=!1,this.quality=0,this.mode=Xu.STEREO,this.force_ms=!1,this.free_format=!1,this.findReplayGain=!1,this.decode_on_the_fly=!1,this.write_id3tag_automatic=!1,this.brate=0,this.compression_ratio=0,this.copyright=0,this.original=0,this.extension=0,this.emphasis=0,this.error_protection=0,this.strict_ISO=!1,this.disable_reservoir=!1,this.quant_comp=0,this.quant_comp_short=0,this.experimentalY=!1,this.experimentalZ=0,this.exp_nspsytune=0,this.preset=0,this.VBR=null,this.VBR_q_frac=0,this.VBR_q=0,this.VBR_mean_bitrate_kbps=0,this.VBR_min_bitrate_kbps=0,this.VBR_max_bitrate_kbps=0,this.VBR_hard_min=0,this.lowpassfreq=0,this.highpassfreq=0,this.lowpasswidth=0,this.highpasswidth=0,this.maskingadjust=0,this.maskingadjust_short=0,this.ATHonly=!1,this.ATHshort=!1,this.noATH=!1,this.ATHtype=0,this.ATHcurve=0,this.ATHlower=0,this.athaa_type=0,this.athaa_loudapprox=0,this.athaa_sensitivity=0,this.short_blocks=null,this.useTemporal=!1,this.interChRatio=0,this.msfix=0,this.tune=!1,this.tune_value_a=0,this.version=0,this.encoder_delay=0,this.encoder_padding=0,this.framesize=0,this.frameNum=0,this.lame_allocated_gfp=0,this.internal_flags=null}var Yu=Fu,Wu=vt(),$a={};$a.SFBMAX=Wu.SBMAX_s*3;var Xr=$a,Va=ft,Gu=Va.new_float,gn=Va.new_int,dr=Xr;function ju(){this.xr=Gu(576),this.l3_enc=gn(576),this.scalefac=gn(dr.SFBMAX),this.xrpow_max=0,this.part2_3_length=0,this.big_values=0,this.count1=0,this.global_gain=0,this.scalefac_compress=0,this.block_type=0,this.mixed_block_flag=0,this.table_select=gn(3),this.subblock_gain=gn(3+1),this.region0_count=0,this.region1_count=0,this.preflag=0,this.scalefac_scale=0,this.count1table_select=0,this.part2_length=0,this.sfb_lmax=0,this.sfb_smin=0,this.psy_lmax=0,this.sfbmax=0,this.psymax=0,this.sfbdivide=0,this.width=gn(dr.SFBMAX),this.window=gn(dr.SFBMAX),this.count1bits=0,this.sfb_partition_table=null,this.slen=gn(4),this.max_nonzero_coeff=0;var t=this;function e(s){return new Int32Array(s)}function n(s){return new Float32Array(s)}this.assign=function(s){t.xr=n(s.xr),t.l3_enc=e(s.l3_enc),t.scalefac=e(s.scalefac),t.xrpow_max=s.xrpow_max,t.part2_3_length=s.part2_3_length,t.big_values=s.big_values,t.count1=s.count1,t.global_gain=s.global_gain,t.scalefac_compress=s.scalefac_compress,t.block_type=s.block_type,t.mixed_block_flag=s.mixed_block_flag,t.table_select=e(s.table_select),t.subblock_gain=e(s.subblock_gain),t.region0_count=s.region0_count,t.region1_count=s.region1_count,t.preflag=s.preflag,t.scalefac_scale=s.scalefac_scale,t.count1table_select=s.count1table_select,t.part2_length=s.part2_length,t.sfb_lmax=s.sfb_lmax,t.sfb_smin=s.sfb_smin,t.psy_lmax=s.psy_lmax,t.sfbmax=s.sfbmax,t.psymax=s.psymax,t.sfbdivide=s.sfbdivide,t.width=e(s.width),t.window=e(s.window),t.count1bits=s.count1bits,t.sfb_partition_table=s.sfb_partition_table.slice(0),t.slen=e(s.slen),t.max_nonzero_coeff=s.max_nonzero_coeff}}var Ha=ju,qu=ft,Li=qu.new_int,Uu=Ha;function Zu(){this.tt=[[null,null],[null,null]],this.main_data_begin=0,this.private_bits=0,this.resvDrain_pre=0,this.resvDrain_post=0,this.scfsi=[Li(4),Li(4)];for(var t=0;t<2;t++)for(var e=0;e<2;e++)this.tt[t][e]=new Uu}var Qu=Zu,Xa=ft,fs=Xa.System,ps=Xa.new_int,hs=vt();function Ku(t,e,n,s){this.l=ps(1+hs.SBMAX_l),this.s=ps(1+hs.SBMAX_s),this.psfb21=ps(1+hs.PSFB21),this.psfb12=ps(1+hs.PSFB12);var r=this.l,i=this.s;arguments.length==4&&(this.arrL=arguments[0],this.arrS=arguments[1],this.arr21=arguments[2],this.arr12=arguments[3],fs.arraycopy(this.arrL,0,r,0,Math.min(this.arrL.length,this.l.length)),fs.arraycopy(this.arrS,0,i,0,Math.min(this.arrS.length,this.s.length)),fs.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),fs.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}var Fa=Ku,Fr=ft,fr=Fr.new_float,Ju=Fr.new_float_n,ed=Fr.new_int,Oi=vt();function td(){this.last_en_subshort=Ju([4,9]),this.lastAttacks=ed(4),this.pefirbuf=fr(19),this.longfact=fr(Oi.SBMAX_l),this.shortfact=fr(Oi.SBMAX_s),this.attackthre=0,this.attackthre_s=0}var nd=td;function sd(){this.sum=0,this.seen=0,this.want=0,this.pos=0,this.size=0,this.bag=null,this.nVbrNumFrames=0,this.nBytesWritten=0,this.TotalFrameSize=0}var rd=sd,Hn=ft,id=Hn.new_byte,ad=Hn.new_double,Lt=Hn.new_float,vn=Hn.new_float_n,Vt=Hn.new_int,ms=Hn.new_int_n,od=Qu,ld=Fa,cd=nd,ud=rd,Di=Na(),rt=vt(),dd=Xr;Kt.MFSIZE=3*1152+rt.ENCDELAY-rt.MDCTDELAY;Kt.MAX_HEADER_BUF=256;Kt.MAX_BITS_PER_CHANNEL=4095;Kt.MAX_BITS_PER_GRANULE=7680;Kt.BPC=320;function Kt(){var t=40;this.Class_ID=0,this.lame_encode_frame_init=0,this.iteration_init_init=0,this.fill_buffer_resample_init=0,this.mfbuf=vn([2,Kt.MFSIZE]),this.mode_gr=0,this.channels_in=0,this.channels_out=0,this.resample_ratio=0,this.mf_samples_to_encode=0,this.mf_size=0,this.VBR_min_bitrate=0,this.VBR_max_bitrate=0,this.bitrate_index=0,this.samplerate_index=0,this.mode_ext=0,this.lowpass1=0,this.lowpass2=0,this.highpass1=0,this.highpass2=0,this.noise_shaping=0,this.noise_shaping_amp=0,this.substep_shaping=0,this.psymodel=0,this.noise_shaping_stop=0,this.subblock_gain=0,this.use_best_huffman=0,this.full_outer_loop=0,this.l3_side=new od,this.ms_ratio=Lt(2),this.padding=0,this.frac_SpF=0,this.slot_lag=0,this.tag_spec=null,this.nMusicCRC=0,this.OldValue=Vt(2),this.CurrentStep=Vt(2),this.masking_lower=0,this.bv_scf=Vt(576),this.pseudohalf=Vt(dd.SFBMAX),this.sfb21_extra=!1,this.inbuf_old=new Array(2),this.blackfilt=new Array(2*Kt.BPC+1),this.itime=ad(2),this.sideinfo_len=0,this.sb_sample=vn([2,2,18,rt.SBLIMIT]),this.amp_filter=Lt(32);function e(){this.write_timing=0,this.ptr=0,this.buf=id(t)}this.header=new Array(Kt.MAX_HEADER_BUF),this.h_ptr=0,this.w_ptr=0,this.ancillary_flag=0,this.ResvSize=0,this.ResvMax=0,this.scalefac_band=new ld,this.minval_l=Lt(rt.CBANDS),this.minval_s=Lt(rt.CBANDS),this.nb_1=vn([4,rt.CBANDS]),this.nb_2=vn([4,rt.CBANDS]),this.nb_s1=vn([4,rt.CBANDS]),this.nb_s2=vn([4,rt.CBANDS]),this.s3_ss=null,this.s3_ll=null,this.decay=0,this.thm=new Array(4),this.en=new Array(4),this.tot_ener=Lt(4),this.loudness_sq=vn([2,2]),this.loudness_sq_save=Lt(2),this.mld_l=Lt(rt.SBMAX_l),this.mld_s=Lt(rt.SBMAX_s),this.bm_l=Vt(rt.SBMAX_l),this.bo_l=Vt(rt.SBMAX_l),this.bm_s=Vt(rt.SBMAX_s),this.bo_s=Vt(rt.SBMAX_s),this.npart_l=0,this.npart_s=0,this.s3ind=ms([rt.CBANDS,2]),this.s3ind_s=ms([rt.CBANDS,2]),this.numlines_s=Vt(rt.CBANDS),this.numlines_l=Vt(rt.CBANDS),this.rnumlines_l=Lt(rt.CBANDS),this.mld_cb_l=Lt(rt.CBANDS),this.mld_cb_s=Lt(rt.CBANDS),this.numlines_s_num1=0,this.numlines_l_num1=0,this.pe=Lt(4),this.ms_ratio_s_old=0,this.ms_ratio_l_old=0,this.ms_ener_ratio_old=0,this.blocktype_old=Vt(2),this.nsPsy=new cd,this.VBR_seek_table=new ud,this.ATH=null,this.PSY=null,this.nogap_total=0,this.nogap_current=0,this.decode_on_the_fly=!0,this.findReplayGain=!0,this.findPeakSample=!0,this.PeakSample=0,this.RadioGain=0,this.AudiophileGain=0,this.rgdata=null,this.noclipGainChange=0,this.noclipScale=0,this.bitrate_stereoMode_Hist=ms([16,4+1]),this.bitrate_blockType_Hist=ms([16,4+1+1]),this.pinfo=null,this.hip=null,this.in_buffer_nsamples=0,this.in_buffer_0=null,this.in_buffer_1=null,this.iteration_loop=null;for(var n=0;n<this.en.length;n++)this.en[n]=new Di;for(var n=0;n<this.thm.length;n++)this.thm[n]=new Di;for(var n=0;n<this.header.length;n++)this.header[n]=new e}var zs=Kt,fd=ft,bn=fd.new_float,wn=vt();function pd(){this.useAdjust=0,this.aaSensitivityP=0,this.adjust=0,this.adjustLimit=0,this.decay=0,this.floor=0,this.l=bn(wn.SBMAX_l),this.s=bn(wn.SBMAX_s),this.psfb21=bn(wn.PSFB21),this.psfb12=bn(wn.PSFB12),this.cb_l=bn(wn.CBANDS),this.cb_s=bn(wn.CBANDS),this.eql_w=bn(wn.BLKSIZE/2)}var hd=pd,Ya=ft,Tt=Ya.System,Ni=Ya.Arrays;at.STEPS_per_dB=100;at.MAX_dB=120;at.GAIN_NOT_ENOUGH_SAMPLES=-24601;at.GAIN_ANALYSIS_ERROR=0;at.GAIN_ANALYSIS_OK=1;at.INIT_GAIN_ANALYSIS_ERROR=0;at.INIT_GAIN_ANALYSIS_OK=1;at.YULE_ORDER=10;at.MAX_ORDER=at.YULE_ORDER;at.MAX_SAMP_FREQ=48e3;at.RMS_WINDOW_TIME_NUMERATOR=1;at.RMS_WINDOW_TIME_DENOMINATOR=20;at.MAX_SAMPLES_PER_WINDOW=at.MAX_SAMP_FREQ*at.RMS_WINDOW_TIME_NUMERATOR/at.RMS_WINDOW_TIME_DENOMINATOR+1;function at(){var t=64.82,e=.95,n=at.RMS_WINDOW_TIME_NUMERATOR,s=at.RMS_WINDOW_TIME_DENOMINATOR,r=[[.038575994352,-3.84664617118067,-.02160367184185,7.81501653005538,-.00123395316851,-11.34170355132042,-9291677959e-14,13.05504219327545,-.01655260341619,-12.28759895145294,.02161526843274,9.4829380631979,-.02074045215285,-5.87257861775999,.00594298065125,2.75465861874613,.00306428023191,-.86984376593551,.00012025322027,.13919314567432,.00288463683916],[.0541865640643,-3.47845948550071,-.02911007808948,6.36317777566148,-.00848709379851,-8.54751527471874,-.00851165645469,9.4769360780128,-.00834990904936,-8.81498681370155,.02245293253339,6.85401540936998,-.02596338512915,-4.39470996079559,.01624864962975,2.19611684890774,-.00240879051584,-.75104302451432,.00674613682247,.13149317958808,-.00187763777362],[.15457299681924,-2.37898834973084,-.09331049056315,2.84868151156327,-.06247880153653,-2.64577170229825,.02163541888798,2.23697657451713,-.05588393329856,-1.67148153367602,.04781476674921,1.00595954808547,.00222312597743,-.45953458054983,.03174092540049,.16378164858596,-.01390589421898,-.05032077717131,.00651420667831,.0234789740702,-.00881362733839],[.30296907319327,-1.61273165137247,-.22613988682123,1.0797749225997,-.08587323730772,-.2565625775407,.03282930172664,-.1627671912044,-.00915702933434,-.22638893773906,-.02364141202522,.39120800788284,-.00584456039913,-.22138138954925,.06276101321749,.04500235387352,-828086748e-14,.02005851806501,.00205861885564,.00302439095741,-.02950134983287],[.33642304856132,-1.49858979367799,-.2557224142557,.87350271418188,-.11828570177555,.12205022308084,.11921148675203,-.80774944671438,-.07834489609479,.47854794562326,-.0046997791438,-.12453458140019,-.0058950022444,-.04067510197014,.05724228140351,.08333755284107,.00832043980773,-.04237348025746,-.0163538138454,.02977207319925,-.0176017656815],[.4491525660845,-.62820619233671,-.14351757464547,.29661783706366,-.22784394429749,-.372563729424,-.01419140100551,.00213767857124,.04078262797139,-.42029820170918,-.12398163381748,.22199650564824,.04097565135648,.00613424350682,.10478503600251,.06747620744683,-.01863887810927,.05784820375801,-.03193428438915,.03222754072173,.00541907748707],[.56619470757641,-1.04800335126349,-.75464456939302,.29156311971249,.1624213774223,-.26806001042947,.16744243493672,.00819999645858,-.18901604199609,.45054734505008,.3093178284183,-.33032403314006,-.27562961986224,.0673936833311,.00647310677246,-.04784254229033,.08647503780351,.01639907836189,-.0378898455484,.01807364323573,-.00588215443421],[.58100494960553,-.51035327095184,-.53174909058578,-.31863563325245,-.14289799034253,-.20256413484477,.17520704835522,.1472815413433,.02377945217615,.38952639978999,.15558449135573,-.23313271880868,-.25344790059353,-.05246019024463,.01628462406333,-.02505961724053,.06920467763959,.02442357316099,-.03721611395801,.01818801111503,-.00749618797172],[.53648789255105,-.2504987195602,-.42163034350696,-.43193942311114,-.00275953611929,-.03424681017675,.04267842219415,-.04678328784242,-.10214864179676,.26408300200955,.14590772289388,.15113130533216,-.02459864859345,-.17556493366449,-.11202315195388,-.18823009262115,-.04060034127,.05477720428674,.0478866554818,.0470440968812,-.02217936801134]],i=[[.98621192462708,-1.97223372919527,-1.97242384925416,.97261396931306,.98621192462708],[.98500175787242,-1.96977855582618,-1.97000351574484,.9702284756635,.98500175787242],[.97938932735214,-1.95835380975398,-1.95877865470428,.95920349965459,.97938932735214],[.97531843204928,-1.95002759149878,-1.95063686409857,.95124613669835,.97531843204928],[.97316523498161,-1.94561023566527,-1.94633046996323,.94705070426118,.97316523498161],[.96454515552826,-1.92783286977036,-1.92909031105652,.93034775234268,.96454515552826],[.96009142950541,-1.91858953033784,-1.92018285901082,.92177618768381,.96009142950541],[.95856916599601,-1.9154210807478,-1.91713833199203,.91885558323625,.95856916599601],[.94597685600279,-1.88903307939452,-1.89195371200558,.89487434461664,.94597685600279]];function l(o,p,_,w,b,g){for(;b--!=0;)_[w]=1e-10+o[p+0]*g[0]-_[w-1]*g[1]+o[p-1]*g[2]-_[w-2]*g[3]+o[p-2]*g[4]-_[w-3]*g[5]+o[p-3]*g[6]-_[w-4]*g[7]+o[p-4]*g[8]-_[w-5]*g[9]+o[p-5]*g[10]-_[w-6]*g[11]+o[p-6]*g[12]-_[w-7]*g[13]+o[p-7]*g[14]-_[w-8]*g[15]+o[p-8]*g[16]-_[w-9]*g[17]+o[p-9]*g[18]-_[w-10]*g[19]+o[p-10]*g[20],++w,++p}function c(o,p,_,w,b,g){for(;b--!=0;)_[w]=o[p+0]*g[0]-_[w-1]*g[1]+o[p-1]*g[2]-_[w-2]*g[3]+o[p-2]*g[4],++w,++p}function a(o,p){for(var _=0;_<MAX_ORDER;_++)o.linprebuf[_]=o.lstepbuf[_]=o.loutbuf[_]=o.rinprebuf[_]=o.rstepbuf[_]=o.routbuf[_]=0;switch(0|p){case 48e3:o.reqindex=0;break;case 44100:o.reqindex=1;break;case 32e3:o.reqindex=2;break;case 24e3:o.reqindex=3;break;case 22050:o.reqindex=4;break;case 16e3:o.reqindex=5;break;case 12e3:o.reqindex=6;break;case 11025:o.reqindex=7;break;case 8e3:o.reqindex=8;break;default:return INIT_GAIN_ANALYSIS_ERROR}return o.sampleWindow=0|(p*n+s-1)/s,o.lsum=0,o.rsum=0,o.totsamp=0,Ni.ill(o.A,0),INIT_GAIN_ANALYSIS_OK}this.InitGainAnalysis=function(o,p){return a(o,p)!=INIT_GAIN_ANALYSIS_OK?INIT_GAIN_ANALYSIS_ERROR:(o.linpre=MAX_ORDER,o.rinpre=MAX_ORDER,o.lstep=MAX_ORDER,o.rstep=MAX_ORDER,o.lout=MAX_ORDER,o.rout=MAX_ORDER,Ni.fill(o.B,0),INIT_GAIN_ANALYSIS_OK)};function u(o){return o*o}this.AnalyzeSamples=function(o,p,_,w,b,g,E){var T,m,x,f,A,D,V;if(g==0)return GAIN_ANALYSIS_OK;switch(V=0,A=g,E){case 1:w=p,b=_;break;case 2:break;default:return GAIN_ANALYSIS_ERROR}for(g<MAX_ORDER?(Tt.arraycopy(p,_,o.linprebuf,MAX_ORDER,g),Tt.arraycopy(w,b,o.rinprebuf,MAX_ORDER,g)):(Tt.arraycopy(p,_,o.linprebuf,MAX_ORDER,MAX_ORDER),Tt.arraycopy(w,b,o.rinprebuf,MAX_ORDER,MAX_ORDER));A>0;){D=A>o.sampleWindow-o.totsamp?o.sampleWindow-o.totsamp:A,V<MAX_ORDER?(T=o.linpre+V,m=o.linprebuf,x=o.rinpre+V,f=o.rinprebuf,D>MAX_ORDER-V&&(D=MAX_ORDER-V)):(T=_+V,m=p,x=b+V,f=w),l(m,T,o.lstepbuf,o.lstep+o.totsamp,D,r[o.reqindex]),l(f,x,o.rstepbuf,o.rstep+o.totsamp,D,r[o.reqindex]),c(o.lstepbuf,o.lstep+o.totsamp,o.loutbuf,o.lout+o.totsamp,D,i[o.reqindex]),c(o.rstepbuf,o.rstep+o.totsamp,o.routbuf,o.rout+o.totsamp,D,i[o.reqindex]),T=o.lout+o.totsamp,m=o.loutbuf,x=o.rout+o.totsamp,f=o.routbuf;for(var O=D%8;O--!=0;)o.lsum+=u(m[T++]),o.rsum+=u(f[x++]);for(O=D/8;O--!=0;)o.lsum+=u(m[T+0])+u(m[T+1])+u(m[T+2])+u(m[T+3])+u(m[T+4])+u(m[T+5])+u(m[T+6])+u(m[T+7]),T+=8,o.rsum+=u(f[x+0])+u(f[x+1])+u(f[x+2])+u(f[x+3])+u(f[x+4])+u(f[x+5])+u(f[x+6])+u(f[x+7]),x+=8;if(A-=D,V+=D,o.totsamp+=D,o.totsamp==o.sampleWindow){var q=at.STEPS_per_dB*10*Math.log10((o.lsum+o.rsum)/o.totsamp*.5+1e-37),N=q<=0?0:0|q;N>=o.A.length&&(N=o.A.length-1),o.A[N]++,o.lsum=o.rsum=0,Tt.arraycopy(o.loutbuf,o.totsamp,o.loutbuf,0,MAX_ORDER),Tt.arraycopy(o.routbuf,o.totsamp,o.routbuf,0,MAX_ORDER),Tt.arraycopy(o.lstepbuf,o.totsamp,o.lstepbuf,0,MAX_ORDER),Tt.arraycopy(o.rstepbuf,o.totsamp,o.rstepbuf,0,MAX_ORDER),o.totsamp=0}if(o.totsamp>o.sampleWindow)return GAIN_ANALYSIS_ERROR}return g<MAX_ORDER?(Tt.arraycopy(o.linprebuf,g,o.linprebuf,0,MAX_ORDER-g),Tt.arraycopy(o.rinprebuf,g,o.rinprebuf,0,MAX_ORDER-g),Tt.arraycopy(p,_,o.linprebuf,MAX_ORDER-g,g),Tt.arraycopy(w,b,o.rinprebuf,MAX_ORDER-g,g)):(Tt.arraycopy(p,_+g-MAX_ORDER,o.linprebuf,0,MAX_ORDER),Tt.arraycopy(w,b+g-MAX_ORDER,o.rinprebuf,0,MAX_ORDER)),GAIN_ANALYSIS_OK};function d(o,p){var _,w=0;for(_=0;_<p;_++)w+=o[_];if(w==0)return GAIN_NOT_ENOUGH_SAMPLES;var b=0|Math.ceil(w*(1-e));for(_=p;_-- >0&&!((b-=o[_])<=0););return t-_/at.STEPS_per_dB}this.GetTitleGain=function(o){for(var p=d(o.A,o.A.length),_=0;_<o.A.length;_++)o.B[_]+=o.A[_],o.A[_]=0;for(var _=0;_<MAX_ORDER;_++)o.linprebuf[_]=o.lstepbuf[_]=o.loutbuf[_]=o.rinprebuf[_]=o.rstepbuf[_]=o.routbuf[_]=0;return o.totsamp=0,o.lsum=o.rsum=0,p}}var md=at,Wa=ft,On=Wa.new_float,zi=Wa.new_int,Mt=md;function _d(){this.linprebuf=On(Mt.MAX_ORDER*2),this.linpre=0,this.lstepbuf=On(Mt.MAX_SAMPLES_PER_WINDOW+Mt.MAX_ORDER),this.lstep=0,this.loutbuf=On(Mt.MAX_SAMPLES_PER_WINDOW+Mt.MAX_ORDER),this.lout=0,this.rinprebuf=On(Mt.MAX_ORDER*2),this.rinpre=0,this.rstepbuf=On(Mt.MAX_SAMPLES_PER_WINDOW+Mt.MAX_ORDER),this.rstep=0,this.routbuf=On(Mt.MAX_SAMPLES_PER_WINDOW+Mt.MAX_ORDER),this.rout=0,this.sampleWindow=0,this.totsamp=0,this.lsum=0,this.rsum=0,this.freqindex=0,this.first=0,this.A=zi(0|Mt.STEPS_per_dB*Mt.MAX_dB),this.B=zi(0|Mt.STEPS_per_dB*Mt.MAX_dB)}var gd=_d;function vd(t){this.bits=t}var Ga=vd,Yr=ft,$i=Yr.new_float,bd=Yr.new_int,Vi=Yr.assert,wd=Ga,Hi=vt(),yd=Xr,Sd=zs;function Ed(t){var e=t;this.quantize=e,this.iteration_loop=function(n,s,r,i){var l=n.internal_flags,c=$i(yd.SFBMAX),a=$i(576),u=bd(2),d=0,o,p=l.l3_side,_=new wd(d);this.quantize.rv.ResvFrameBegin(n,_),d=_.bits;for(var w=0;w<l.mode_gr;w++){o=this.quantize.qupvt.on_pe(n,s,u,d,w,w),l.mode_ext==Hi.MPG_MD_MS_LR&&(this.quantize.ms_convert(l.l3_side,w),this.quantize.qupvt.reduce_side(u,r[w],d,o));for(var b=0;b<l.channels_out;b++){var g,E,T=p.tt[w][b];T.block_type!=Hi.SHORT_TYPE?(g=0,E=l.PSY.mask_adjust-g):(g=0,E=l.PSY.mask_adjust_short-g),l.masking_lower=Math.pow(10,E*.1),this.quantize.init_outer_loop(l,T),this.quantize.init_xrpow(l,T,a)&&(this.quantize.qupvt.calc_xmin(n,i[w][b],T,c),this.quantize.outer_loop(n,T,c,a,b,u[b])),this.quantize.iteration_finish_one(l,w,b),Vi(T.part2_3_length<=Sd.MAX_BITS_PER_CHANNEL),Vi(T.part2_3_length<=u[b])}}this.quantize.rv.ResvFrameEnd(l,d)}}var Td=Ed;function Ze(t,e,n,s){this.xlen=t,this.linmax=e,this.table=n,this.hlen=s}var he={};he.t1HB=[1,1,1,0];he.t2HB=[1,2,1,3,1,1,3,2,0];he.t3HB=[3,2,1,1,1,1,3,2,0];he.t5HB=[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0];he.t6HB=[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0];he.t7HB=[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0];he.t8HB=[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0];he.t9HB=[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0];he.t10HB=[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0];he.t11HB=[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0];he.t12HB=[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0];he.t13HB=[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1];he.t15HB=[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0];he.t16HB=[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3];he.t24HB=[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3];he.t32HB=[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16];he.t33HB=[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0];he.t1l=[1,4,3,5];he.t2l=[1,4,7,4,5,7,6,7,8];he.t3l=[2,3,7,4,4,7,6,7,8];he.t5l=[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10];he.t6l=[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9];he.t7l=[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12];he.t8l=[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13];he.t9l=[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11];he.t10l=[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13];he.t11l=[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12];he.t12l=[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12];he.t13l=[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18];he.t15l=[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15];he.t16_5l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12];he.t16l=[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10];he.t24l=[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6];he.t32l=[1+0,4+1,4+1,5+2,4+1,6+2,5+2,6+3,4+1,5+2,5+2,6+3,5+2,6+3,6+3,6+4];he.t33l=[4+0,4+1,4+1,4+2,4+1,4+2,4+2,4+3,4+1,4+2,4+2,4+3,4+2,4+3,4+3,4+4];he.ht=[new Ze(0,0,null,null),new Ze(2,0,he.t1HB,he.t1l),new Ze(3,0,he.t2HB,he.t2l),new Ze(3,0,he.t3HB,he.t3l),new Ze(0,0,null,null),new Ze(4,0,he.t5HB,he.t5l),new Ze(4,0,he.t6HB,he.t6l),new Ze(6,0,he.t7HB,he.t7l),new Ze(6,0,he.t8HB,he.t8l),new Ze(6,0,he.t9HB,he.t9l),new Ze(8,0,he.t10HB,he.t10l),new Ze(8,0,he.t11HB,he.t11l),new Ze(8,0,he.t12HB,he.t12l),new Ze(16,0,he.t13HB,he.t13l),new Ze(0,0,null,he.t16_5l),new Ze(16,0,he.t15HB,he.t15l),new Ze(1,1,he.t16HB,he.t16l),new Ze(2,3,he.t16HB,he.t16l),new Ze(3,7,he.t16HB,he.t16l),new Ze(4,15,he.t16HB,he.t16l),new Ze(6,63,he.t16HB,he.t16l),new Ze(8,255,he.t16HB,he.t16l),new Ze(10,1023,he.t16HB,he.t16l),new Ze(13,8191,he.t16HB,he.t16l),new Ze(4,15,he.t24HB,he.t24l),new Ze(5,31,he.t24HB,he.t24l),new Ze(6,63,he.t24HB,he.t24l),new Ze(7,127,he.t24HB,he.t24l),new Ze(8,255,he.t24HB,he.t24l),new Ze(9,511,he.t24HB,he.t24l),new Ze(11,2047,he.t24HB,he.t24l),new Ze(13,8191,he.t24HB,he.t24l),new Ze(0,0,he.t32HB,he.t32l),new Ze(0,0,he.t33HB,he.t33l)];he.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366];he.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296];he.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369];he.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]];he.samplerate_table=[[22050,24e3,16e3,-1],[44100,48e3,32e3,-1],[11025,12e3,8e3,-1]];he.scfsi_band=[0,6,11,16,21];var Wr=he,pr,Xi;function ja(){if(Xi)return pr;Xi=1;var t=Fa,e=ft,n=e.VbrMode,s=e.Float,r=e.Util,i=e.new_float,l=e.new_int,c=e.assert,a=vt(),u=Ga,d=zs;o.Q_MAX=256+1,o.Q_MAX2=116,o.LARGE_BITS=1e5,o.IXMAX_VAL=8206;function o(){var p=Gr(),_=null,w=null,b=null;this.setModules=function(k,H,R){_=k,w=H,b=R};function g(k){return c(0<=k+o.Q_MAX2&&k<o.Q_MAX),V[k+o.Q_MAX2]}this.IPOW20=function(k){return c(0<=k&&k<o.Q_MAX),O[k]};var E=2220446049250313e-31,T=o.IXMAX_VAL,m=T+2,x=o.Q_MAX,f=o.Q_MAX2;o.LARGE_BITS;var A=100;this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var D=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=D,this.sfBandIndex=[new t([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new t([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new t([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new t([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new t([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new t([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new t([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0/3,12/3,24/3,36/3,54/3,78/3,108/3,144/3,186/3,240/3,312/3,402/3,522/3,576/3],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new t([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0/3,12/3,24/3,36/3,54/3,78/3,108/3,144/3,186/3,240/3,312/3,402/3,522/3,576/3],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new t([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0/3,24/3,48/3,72/3,108/3,156/3,216/3,288/3,372/3,480/3,486/3,492/3,498/3,576/3],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var V=i(x+f+1),O=i(x),q=i(m),N=i(m);this.adj43=N;function L(k,H){var R=b.ATHformula(H,k);return R-=A,R=Math.pow(10,R/10+k.ATHlower),R}function I(k){for(var H=k.internal_flags.ATH.l,R=k.internal_flags.ATH.psfb21,F=k.internal_flags.ATH.s,Y=k.internal_flags.ATH.psfb12,X=k.internal_flags,M=k.out_samplerate,y=0;y<a.SBMAX_l;y++){var v=X.scalefac_band.l[y],C=X.scalefac_band.l[y+1];H[y]=s.MAX_VALUE;for(var $=v;$<C;$++){var Z=$*M/1152,se=L(k,Z);H[y]=Math.min(H[y],se)}}for(var y=0;y<a.PSFB21;y++){var v=X.scalefac_band.psfb21[y],C=X.scalefac_band.psfb21[y+1];R[y]=s.MAX_VALUE;for(var $=v;$<C;$++){var Z=$*M/1152,se=L(k,Z);R[y]=Math.min(R[y],se)}}for(var y=0;y<a.SBMAX_s;y++){var v=X.scalefac_band.s[y],C=X.scalefac_band.s[y+1];F[y]=s.MAX_VALUE;for(var $=v;$<C;$++){var Z=$*M/384,se=L(k,Z);F[y]=Math.min(F[y],se)}F[y]*=X.scalefac_band.s[y+1]-X.scalefac_band.s[y]}for(var y=0;y<a.PSFB12;y++){var v=X.scalefac_band.psfb12[y],C=X.scalefac_band.psfb12[y+1];Y[y]=s.MAX_VALUE;for(var $=v;$<C;$++){var Z=$*M/384,se=L(k,Z);Y[y]=Math.min(Y[y],se)}Y[y]*=X.scalefac_band.s[13]-X.scalefac_band.s[12]}if(k.noATH){for(var y=0;y<a.SBMAX_l;y++)H[y]=1e-20;for(var y=0;y<a.PSFB21;y++)R[y]=1e-20;for(var y=0;y<a.SBMAX_s;y++)F[y]=1e-20;for(var y=0;y<a.PSFB12;y++)Y[y]=1e-20}X.ATH.floor=10*Math.log10(L(k,-1))}this.iteration_init=function(k){var H=k.internal_flags,R=H.l3_side,F;if(H.iteration_init_init==0){for(H.iteration_init_init=1,R.main_data_begin=0,I(k),q[0]=0,F=1;F<m;F++)q[F]=Math.pow(F,4/3);for(F=0;F<m-1;F++)N[F]=F+1-Math.pow(.5*(q[F]+q[F+1]),.75);for(N[F]=.5,F=0;F<x;F++)O[F]=Math.pow(2,(F-210)*-.1875);for(F=0;F<=x+f;F++)V[F]=Math.pow(2,(F-210-f)*.25);_.huffman_init(H);{var Y,X,M,y;for(F=k.exp_nspsytune>>2&63,F>=32&&(F-=64),Y=Math.pow(10,F/4/10),F=k.exp_nspsytune>>8&63,F>=32&&(F-=64),X=Math.pow(10,F/4/10),F=k.exp_nspsytune>>14&63,F>=32&&(F-=64),M=Math.pow(10,F/4/10),F=k.exp_nspsytune>>20&63,F>=32&&(F-=64),y=M*Math.pow(10,F/4/10),F=0;F<a.SBMAX_l;F++){var v;F<=6?v=Y:F<=13?v=X:F<=20?v=M:v=y,H.nsPsy.longfact[F]=v}for(F=0;F<a.SBMAX_s;F++){var v;F<=5?v=Y:F<=10?v=X:F<=11?v=M:v=y,H.nsPsy.shortfact[F]=v}}}},this.on_pe=function(k,H,R,F,Y,X){var M=k.internal_flags,y=0,v,C=l(2),$,Z=new u(y),se=w.ResvMaxBits(k,F,Z,X);y=Z.bits;var j=y+se;for(j>d.MAX_BITS_PER_GRANULE&&(j=d.MAX_BITS_PER_GRANULE),v=0,$=0;$<M.channels_out;++$)R[$]=Math.min(d.MAX_BITS_PER_CHANNEL,y/M.channels_out),C[$]=0|R[$]*H[Y][$]/700-R[$],C[$]>F*3/4&&(C[$]=F*3/4),C[$]<0&&(C[$]=0),C[$]+R[$]>d.MAX_BITS_PER_CHANNEL&&(C[$]=Math.max(0,d.MAX_BITS_PER_CHANNEL-R[$])),v+=C[$];if(v>se)for($=0;$<M.channels_out;++$)C[$]=se*C[$]/v;for($=0;$<M.channels_out;++$)R[$]+=C[$],se-=C[$];for(v=0,$=0;$<M.channels_out;++$)v+=R[$];if(v>d.MAX_BITS_PER_GRANULE){var ee=0;for($=0;$<M.channels_out;++$)R[$]*=d.MAX_BITS_PER_GRANULE,R[$]/=v,ee+=R[$]}return j},this.reduce_side=function(k,H,R,F){c(k[0]+k[1]<=d.MAX_BITS_PER_GRANULE);var Y=.33*(.5-H)/.5;Y<0&&(Y=0),Y>.5&&(Y=.5);var X=0|Y*.5*(k[0]+k[1]);X>d.MAX_BITS_PER_CHANNEL-k[0]&&(X=d.MAX_BITS_PER_CHANNEL-k[0]),X<0&&(X=0),k[1]>=125&&(k[1]-X>125?(k[0]<R&&(k[0]+=X),k[1]-=X):(k[0]+=k[1]-125,k[1]=125)),X=k[0]+k[1],X>F&&(k[0]=F*k[0]/X,k[1]=F*k[1]/X),c(k[0]<=d.MAX_BITS_PER_CHANNEL),c(k[1]<=d.MAX_BITS_PER_CHANNEL),c(k[0]+k[1]<=d.MAX_BITS_PER_GRANULE)},this.athAdjust=function(k,H,R){var F=90.30873362,Y=94.82444863,X=r.FAST_LOG10_X(H,10),M=k*k,y=0;return X-=R,M>1e-20&&(y=1+r.FAST_LOG10_X(M,10/F)),y<0&&(y=0),X*=y,X+=R+F-Y,Math.pow(10,.1*X)},this.calc_xmin=function(k,H,R,F){var Y=0,X=k.internal_flags,M,y=0,v=0,C=X.ATH,$=R.xr,Z=k.VBR==n.vbr_mtrh?1:0,se=X.masking_lower;for((k.VBR==n.vbr_mtrh||k.VBR==n.vbr_mt)&&(se=1),M=0;M<R.psy_lmax;M++){var j,ee,ce,me,ye,Me;k.VBR==n.vbr_rh||k.VBR==n.vbr_mtrh?ee=athAdjust(C.adjust,C.l[M],C.floor):ee=C.adjust*C.l[M],ye=R.width[M],ce=ee/ye,me=E,Me=ye>>1,j=0;do{var Se,ie;Se=$[y]*$[y],j+=Se,me+=Se<ce?Se:ce,y++,ie=$[y]*$[y],j+=ie,me+=ie<ce?ie:ce,y++}while(--Me>0);if(j>ee&&v++,M==a.SBPSY_l){var ue=ee*X.nsPsy.longfact[M];me<ue&&(me=ue)}if(Z!=0&&(ee=me),!k.ATHonly){var ze=H.en.l[M];if(ze>0){var ue;ue=j*H.thm.l[M]*se/ze,Z!=0&&(ue*=X.nsPsy.longfact[M]),ee<ue&&(ee=ue)}}Z!=0?F[Y++]=ee:F[Y++]=ee*X.nsPsy.longfact[M]}var qe=575;if(R.block_type!=a.SHORT_TYPE)for(var S=576;S--!=0&&p.EQ($[S],0);)qe=S;R.max_nonzero_coeff=qe;for(var P=R.sfb_smin;M<R.psymax;P++,M+=3){var ye,ge,Ee;for(k.VBR==n.vbr_rh||k.VBR==n.vbr_mtrh?Ee=athAdjust(C.adjust,C.s[P],C.floor):Ee=C.adjust*C.s[P],ye=R.width[M],ge=0;ge<3;ge++){var j=0,ee,ce,me,Me=ye>>1;ce=Ee/ye,me=E;do{var Se,ie;Se=$[y]*$[y],j+=Se,me+=Se<ce?Se:ce,y++,ie=$[y]*$[y],j+=ie,me+=ie<ce?ie:ce,y++}while(--Me>0);if(j>Ee&&v++,P==a.SBPSY_s){var ue=Ee*X.nsPsy.shortfact[P];me<ue&&(me=ue)}if(Z!=0?ee=me:ee=Ee,!k.ATHonly&&!k.ATHshort){var ze=H.en.s[P][ge];if(ze>0){var ue;ue=j*H.thm.s[P][ge]*se/ze,Z!=0&&(ue*=X.nsPsy.shortfact[P]),ee<ue&&(ee=ue)}}Z!=0?F[Y++]=ee:F[Y++]=ee*X.nsPsy.shortfact[P]}k.useTemporal&&(F[Y-3]>F[Y-3+1]&&(F[Y-3+1]+=(F[Y-3]-F[Y-3+1])*X.decay),F[Y-3+1]>F[Y-3+2]&&(F[Y-3+2]+=(F[Y-3+1]-F[Y-3+2])*X.decay))}return v};function h(k){this.s=k}this.calc_noise_core=function(k,H,R,F){var Y=0,X=H.s,M=k.l3_enc;if(X>k.count1)for(;R--!=0;){var y;y=k.xr[X],X++,Y+=y*y,y=k.xr[X],X++,Y+=y*y}else if(X>k.big_values){var v=i(2);for(v[0]=0,v[1]=F;R--!=0;){var y;y=Math.abs(k.xr[X])-v[M[X]],X++,Y+=y*y,y=Math.abs(k.xr[X])-v[M[X]],X++,Y+=y*y}}else for(;R--!=0;){var y;y=Math.abs(k.xr[X])-q[M[X]]*F,X++,Y+=y*y,y=Math.abs(k.xr[X])-q[M[X]]*F,X++,Y+=y*y}return H.s=X,Y},this.calc_noise=function(k,H,R,F,Y){var X=0,M=0,y,v,C=0,$=0,Z=0,se=-20,j=0,ee=k.scalefac,ce=0;for(F.over_SSD=0,y=0;y<k.psymax;y++){var me=k.global_gain-(ee[ce++]+(k.preflag!=0?D[y]:0)<<k.scalefac_scale+1)-k.subblock_gain[k.window[y]]*8,ye=0;if(Y!=null&&Y.step[y]==me)ye=Y.noise[y],j+=k.width[y],R[X++]=ye/H[M++],ye=Y.noise_log[y];else{var Me=g(me);if(v=k.width[y]>>1,j+k.width[y]>k.max_nonzero_coeff){var Se;Se=k.max_nonzero_coeff-j+1,Se>0?v=Se>>1:v=0}var ie=new h(j);ye=this.calc_noise_core(k,ie,v,Me),j=ie.s,Y!=null&&(Y.step[y]=me,Y.noise[y]=ye),ye=R[X++]=ye/H[M++],ye=r.FAST_LOG10(Math.max(ye,1e-20)),Y!=null&&(Y.noise_log[y]=ye)}if(Y!=null&&(Y.global_gain=k.global_gain),Z+=ye,ye>0){var ue;ue=Math.max(0|ye*10+.5,1),F.over_SSD+=ue*ue,C++,$+=ye}se=Math.max(se,ye)}return F.over_count=C,F.tot_noise=Z,F.over_noise=$,F.max_noise=se,C},this.set_pinfo=function(k,H,R,F,Y){var X=k.internal_flags,M,y,v,C,$,Z=H.scalefac_scale==0?.5:1,se=H.scalefac,j=i(L3Side.SFBMAX),ee=i(L3Side.SFBMAX),ce=new CalcNoiseResult;calc_xmin(k,R,H,j),calc_noise(H,j,ee,ce,null);var me=0;for(y=H.sfb_lmax,H.block_type!=a.SHORT_TYPE&&H.mixed_block_flag==0&&(y=22),M=0;M<y;M++){var ye=X.scalefac_band.l[M],Me=X.scalefac_band.l[M+1],Se=Me-ye;for(C=0;me<Me;me++)C+=H.xr[me]*H.xr[me];C/=Se,$=1e15,X.pinfo.en[F][Y][M]=$*C,X.pinfo.xfsf[F][Y][M]=$*j[M]*ee[M]/Se,R.en.l[M]>0&&!k.ATHonly?C=C/R.en.l[M]:C=0,X.pinfo.thr[F][Y][M]=$*Math.max(C*R.thm.l[M],X.ATH.l[M]),X.pinfo.LAMEsfb[F][Y][M]=0,H.preflag!=0&&M>=11&&(X.pinfo.LAMEsfb[F][Y][M]=-Z*D[M]),M<a.SBPSY_l&&(c(se[M]>=0),X.pinfo.LAMEsfb[F][Y][M]-=Z*se[M])}if(H.block_type==a.SHORT_TYPE)for(y=M,M=H.sfb_smin;M<a.SBMAX_s;M++)for(var ye=X.scalefac_band.s[M],Me=X.scalefac_band.s[M+1],Se=Me-ye,ie=0;ie<3;ie++){for(C=0,v=ye;v<Me;v++)C+=H.xr[me]*H.xr[me],me++;C=Math.max(C/Se,1e-20),$=1e15,X.pinfo.en_s[F][Y][3*M+ie]=$*C,X.pinfo.xfsf_s[F][Y][3*M+ie]=$*j[y]*ee[y]/Se,R.en.s[M][ie]>0?C=C/R.en.s[M][ie]:C=0,(k.ATHonly||k.ATHshort)&&(C=0),X.pinfo.thr_s[F][Y][3*M+ie]=$*Math.max(C*R.thm.s[M][ie],X.ATH.s[M]),X.pinfo.LAMEsfb_s[F][Y][3*M+ie]=-2*H.subblock_gain[ie],M<a.SBPSY_s&&(X.pinfo.LAMEsfb_s[F][Y][3*M+ie]-=Z*se[y]),y++}X.pinfo.LAMEqss[F][Y]=H.global_gain,X.pinfo.LAMEmainbits[F][Y]=H.part2_3_length+H.part2_length,X.pinfo.LAMEsfbits[F][Y]=H.part2_length,X.pinfo.over[F][Y]=ce.over_count,X.pinfo.max_noise[F][Y]=ce.max_noise*10,X.pinfo.over_noise[F][Y]=ce.over_noise*10,X.pinfo.tot_noise[F][Y]=ce.tot_noise*10,X.pinfo.over_SSD[F][Y]=ce.over_SSD}}return pr=o,pr}var hr,Fi;function qa(){if(Fi)return hr;Fi=1;var t=ft,e=t.System,n=t.Arrays,s=t.new_int,r=t.assert,i=vt(),l=Wr,c=Ha,a=ja();function u(){var d=null;this.qupvt=null,this.setModules=function(X){this.qupvt=X,d=X};function o(X){this.bits=0|X}var p=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]];function _(X,M,y,v,C,$){var Z=.5946/M;for(X=X>>1;X--!=0;)C[$++]=Z>y[v++]?0:1,C[$++]=Z>y[v++]?0:1}function w(X,M,y,v,C,$){X=X>>1;var Z=X%2;for(X=X>>1;X--!=0;){var se,j,ee,ce,me,ye,Me,Se;se=y[v++]*M,j=y[v++]*M,me=0|se,ee=y[v++]*M,ye=0|j,ce=y[v++]*M,Me=0|ee,se+=d.adj43[me],Se=0|ce,j+=d.adj43[ye],C[$++]=0|se,ee+=d.adj43[Me],C[$++]=0|j,ce+=d.adj43[Se],C[$++]=0|ee,C[$++]=0|ce}if(Z!=0){var se,j,me,ye;se=y[v++]*M,j=y[v++]*M,me=0|se,ye=0|j,se+=d.adj43[me],j+=d.adj43[ye],C[$++]=0|se,C[$++]=0|j}}function b(X,M,y,v,C){var $,Z,se=0,j,ee=0,ce=0,me=0,ye=M,Me=0,Se=ye,ie=0,ue=X,ze=0;for(j=C!=null&&v.global_gain==C.global_gain,v.block_type==i.SHORT_TYPE?Z=38:Z=21,$=0;$<=Z;$++){var qe=-1;if((j||v.block_type==i.NORM_TYPE)&&(qe=v.global_gain-(v.scalefac[$]+(v.preflag!=0?d.pretab[$]:0)<<v.scalefac_scale+1)-v.subblock_gain[v.window[$]]*8),r(v.width[$]>=0),j&&C.step[$]==qe)ee!=0&&(w(ee,y,ue,ze,Se,ie),ee=0),ce!=0&&(_(ce,y,ue,ze,Se,ie),ce=0);else{var S=v.width[$];if(se+v.width[$]>v.max_nonzero_coeff){var P;P=v.max_nonzero_coeff-se+1,n.fill(M,v.max_nonzero_coeff,576,0),S=P,S<0&&(S=0),$=Z+1}if(ee==0&&ce==0&&(Se=ye,ie=Me,ue=X,ze=me),C!=null&&C.sfb_count1>0&&$>=C.sfb_count1&&C.step[$]>0&&qe>=C.step[$]?(ee!=0&&(w(ee,y,ue,ze,Se,ie),ee=0,Se=ye,ie=Me,ue=X,ze=me),ce+=S):(ce!=0&&(_(ce,y,ue,ze,Se,ie),ce=0,Se=ye,ie=Me,ue=X,ze=me),ee+=S),S<=0){ce!=0&&(_(ce,y,ue,ze,Se,ie),ce=0),ee!=0&&(w(ee,y,ue,ze,Se,ie),ee=0);break}}$<=Z&&(Me+=v.width[$],me+=v.width[$],se+=v.width[$])}ee!=0&&(w(ee,y,ue,ze,Se,ie),ee=0),ce!=0&&(_(ce,y,ue,ze,Se,ie),ce=0)}function g(X,M,y){var v=0,C=0;do{var $=X[M++],Z=X[M++];v<$&&(v=$),C<Z&&(C=Z)}while(M<y);return v<C&&(v=C),v}function E(X,M,y,v,C,$){var Z=l.ht[v].xlen*65536+l.ht[C].xlen,se=0,j;do{var ee=X[M++],ce=X[M++];ee!=0&&(ee>14&&(ee=15,se+=Z),ee*=16),ce!=0&&(ce>14&&(ce=15,se+=Z),ee+=ce),se+=l.largetbl[ee]}while(M<y);return j=se&65535,se>>=16,se>j&&(se=j,v=C),$.bits+=se,v}function T(X,M,y,v){var C=0,$=l.ht[1].hlen;do{var Z=X[M+0]*2+X[M+1];M+=2,C+=$[Z]}while(M<y);return v.bits+=C,1}function m(X,M,y,v,C){var $=0,Z,se=l.ht[v].xlen,j;v==2?j=l.table23:j=l.table56;do{var ee=X[M+0]*se+X[M+1];M+=2,$+=j[ee]}while(M<y);return Z=$&65535,$>>=16,$>Z&&($=Z,v++),C.bits+=$,v}function x(X,M,y,v,C){var $=0,Z=0,se=0,j=l.ht[v].xlen,ee=l.ht[v].hlen,ce=l.ht[v+1].hlen,me=l.ht[v+2].hlen;do{var ye=X[M+0]*j+X[M+1];M+=2,$+=ee[ye],Z+=ce[ye],se+=me[ye]}while(M<y);var Me=v;return $>Z&&($=Z,Me++),$>se&&($=se,Me=v+2),C.bits+=$,Me}var f=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];function A(X,M,y,v){var C=g(X,M,y);switch(C){case 0:return C;case 1:return T(X,M,y,v);case 2:case 3:return m(X,M,y,f[C-1],v);case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:return x(X,M,y,f[C-1],v);default:if(C>a.IXMAX_VAL)return v.bits=a.LARGE_BITS,-1;C-=15;var $;for($=24;$<32&&!(l.ht[$].linmax>=C);$++);var Z;for(Z=$-8;Z<24&&!(l.ht[Z].linmax>=C);Z++);return E(X,M,y,Z,$,v)}}this.noquant_count_bits=function(X,M,y){var v=M.l3_enc,C=Math.min(576,M.max_nonzero_coeff+2>>1<<1);for(y!=null&&(y.sfb_count1=0);C>1&&!(v[C-1]|v[C-2]);C-=2);M.count1=C;for(var $=0,Z=0;C>3;C-=4){var se;if(((v[C-1]|v[C-2]|v[C-3]|v[C-4])&2147483647)>1)break;se=((v[C-4]*2+v[C-3])*2+v[C-2])*2+v[C-1],$+=l.t32l[se],Z+=l.t33l[se]}var j=$;if(M.count1table_select=0,$>Z&&(j=Z,M.count1table_select=1),M.count1bits=j,M.big_values=C,C==0)return j;if(M.block_type==i.SHORT_TYPE)$=3*X.scalefac_band.s[3],$>M.big_values&&($=M.big_values),Z=M.big_values;else if(M.block_type==i.NORM_TYPE){if($=M.region0_count=X.bv_scf[C-2],Z=M.region1_count=X.bv_scf[C-1],r($+Z+2<i.SBPSY_l),Z=X.scalefac_band.l[$+Z+2],$=X.scalefac_band.l[$+1],Z<C){var ee=new o(j);M.table_select[2]=A(v,Z,C,ee),j=ee.bits}}else M.region0_count=7,M.region1_count=i.SBMAX_l-1-7-1,$=X.scalefac_band.l[7+1],Z=C,$>Z&&($=Z);if($=Math.min($,C),Z=Math.min(Z,C),0<$){var ee=new o(j);M.table_select[0]=A(v,0,$,ee),j=ee.bits}if($<Z){var ee=new o(j);M.table_select[1]=A(v,$,Z,ee),j=ee.bits}if(X.use_best_huffman==2&&(M.part2_3_length=j,best_huffman_divide(X,M),j=M.part2_3_length),y!=null&&M.block_type==i.NORM_TYPE){for(var ce=0;X.scalefac_band.l[ce]<M.big_values;)ce++;y.sfb_count1=ce}return j},this.count_bits=function(X,M,y,v){var C=y.l3_enc,$=a.IXMAX_VAL/d.IPOW20(y.global_gain);if(y.xrpow_max>$)return a.LARGE_BITS;if(b(M,C,d.IPOW20(y.global_gain),y,v),X.substep_shaping&2)for(var Z=0,se=y.global_gain+y.scalefac_scale,j=.634521682242439/d.IPOW20(se),ee=0;ee<y.sfbmax;ee++){var ce=y.width[ee];if(X.pseudohalf[ee]==0)Z+=ce;else{var me;for(me=Z,Z+=ce;me<Z;++me)C[me]=M[me]>=j?C[me]:0}}return this.noquant_count_bits(X,y,v)};function D(X,M,y,v,C,$,Z){for(var se=M.big_values,j=0;j<=7+15;j++)v[j]=a.LARGE_BITS;for(var j=0;j<16;j++){var ee=X.scalefac_band.l[j+1];if(ee>=se)break;var ce=0,me=new o(ce),ye=A(y,0,ee,me);ce=me.bits;for(var Me=0;Me<8;Me++){var Se=X.scalefac_band.l[j+Me+2];if(Se>=se)break;var ie=ce;me=new o(ie);var ue=A(y,ee,Se,me);ie=me.bits,v[j+Me]>ie&&(v[j+Me]=ie,C[j+Me]=j,$[j+Me]=ye,Z[j+Me]=ue)}}}function V(X,M,y,v,C,$,Z,se){for(var j=M.big_values,ee=2;ee<i.SBMAX_l+1;ee++){var ce=X.scalefac_band.l[ee];if(ce>=j)break;var me=C[ee-2]+M.count1bits;if(y.part2_3_length<=me)break;var ye=new o(me),Me=A(v,ce,j,ye);me=ye.bits,!(y.part2_3_length<=me)&&(y.assign(M),y.part2_3_length=me,y.region0_count=$[ee-2],y.region1_count=ee-2-$[ee-2],y.table_select[0]=Z[ee-2],y.table_select[1]=se[ee-2],y.table_select[2]=Me)}}this.best_huffman_divide=function(X,M){var y=new c,v=M.l3_enc,C=s(7+15+1),$=s(7+15+1),Z=s(7+15+1),se=s(7+15+1);if(!(M.block_type==i.SHORT_TYPE&&X.mode_gr==1)){y.assign(M),M.block_type==i.NORM_TYPE&&(D(X,M,v,C,$,Z,se),V(X,y,M,v,C,$,Z,se));var j=y.big_values;if(!(j==0||(v[j-2]|v[j-1])>1)&&(j=M.count1+2,!(j>576))){y.assign(M),y.count1=j;for(var ee=0,ce=0;j>y.big_values;j-=4){var me=((v[j-4]*2+v[j-3])*2+v[j-2])*2+v[j-1];ee+=l.t32l[me],ce+=l.t33l[me]}if(y.big_values=j,y.count1table_select=0,ee>ce&&(ee=ce,y.count1table_select=1),y.count1bits=ee,y.block_type==i.NORM_TYPE)V(X,y,M,v,C,$,Z,se);else{if(y.part2_3_length=ee,ee=X.scalefac_band.l[7+1],ee>j&&(ee=j),ee>0){var ye=new o(y.part2_3_length);y.table_select[0]=A(v,0,ee,ye),y.part2_3_length=ye.bits}if(j>ee){var ye=new o(y.part2_3_length);y.table_select[1]=A(v,ee,j,ye),y.part2_3_length=ye.bits}M.part2_3_length>y.part2_3_length&&M.assign(y)}}}};var O=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],q=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],N=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],L=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];u.slen1_tab=N,u.slen2_tab=L;function I(X,M){for(var y,v=M.tt[1][X],C=M.tt[0][X],$=0;$<l.scfsi_band.length-1;$++){for(y=l.scfsi_band[$];y<l.scfsi_band[$+1]&&!(C.scalefac[y]!=v.scalefac[y]&&v.scalefac[y]>=0);y++);if(y==l.scfsi_band[$+1]){for(y=l.scfsi_band[$];y<l.scfsi_band[$+1];y++)v.scalefac[y]=-1;M.scfsi[X][$]=1}}var Z=0,se=0;for(y=0;y<11;y++)v.scalefac[y]!=-1&&(se++,Z<v.scalefac[y]&&(Z=v.scalefac[y]));for(var j=0,ee=0;y<i.SBPSY_l;y++)v.scalefac[y]!=-1&&(ee++,j<v.scalefac[y]&&(j=v.scalefac[y]));for(var $=0;$<16;$++)if(Z<O[$]&&j<q[$]){var ce=N[$]*se+L[$]*ee;v.part2_length>ce&&(v.part2_length=ce,v.scalefac_compress=$)}}this.best_scalefac_store=function(X,M,y,v){var C=v.tt[M][y],$,Z,se,j,ee=0;for(se=0,$=0;$<C.sfbmax;$++){var ce=C.width[$];for(se+=ce,j=-ce;j<0&&C.l3_enc[j+se]==0;j++);j==0&&(C.scalefac[$]=ee=-2)}if(C.scalefac_scale==0&&C.preflag==0){var me=0;for($=0;$<C.sfbmax;$++)C.scalefac[$]>0&&(me|=C.scalefac[$]);if(!(me&1)&&me!=0){for($=0;$<C.sfbmax;$++)C.scalefac[$]>0&&(C.scalefac[$]>>=1);C.scalefac_scale=ee=1}}if(C.preflag==0&&C.block_type!=i.SHORT_TYPE&&X.mode_gr==2){for($=11;$<i.SBPSY_l&&!(C.scalefac[$]<d.pretab[$]&&C.scalefac[$]!=-2);$++);if($==i.SBPSY_l){for($=11;$<i.SBPSY_l;$++)C.scalefac[$]>0&&(C.scalefac[$]-=d.pretab[$]);C.preflag=ee=1}}for(Z=0;Z<4;Z++)v.scfsi[y][Z]=0;for(X.mode_gr==2&&M==1&&v.tt[0][y].block_type!=i.SHORT_TYPE&&v.tt[1][y].block_type!=i.SHORT_TYPE&&(I(y,v),ee=0),$=0;$<C.sfbmax;$++)C.scalefac[$]==-2&&(C.scalefac[$]=0);ee!=0&&(X.mode_gr==2?this.scale_bitcount(C):this.scale_bitcount_lsf(X,C))};function h(X,M){for(var y=0;y<M;++y)if(X[y]<0)return!1;return!0}var k=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],H=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],R=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(X){var M,y,v=0,C=0,$,Z=X.scalefac;if(r(h(Z,X.sfbmax)),X.block_type==i.SHORT_TYPE)$=k,X.mixed_block_flag!=0&&($=H);else if($=R,X.preflag==0){for(y=11;y<i.SBPSY_l&&!(Z[y]<d.pretab[y]);y++);if(y==i.SBPSY_l)for(X.preflag=1,y=11;y<i.SBPSY_l;y++)Z[y]-=d.pretab[y]}for(y=0;y<X.sfbdivide;y++)v<Z[y]&&(v=Z[y]);for(;y<X.sfbmax;y++)C<Z[y]&&(C=Z[y]);for(X.part2_length=a.LARGE_BITS,M=0;M<16;M++)v<O[M]&&C<q[M]&&X.part2_length>$[M]&&(X.part2_length=$[M],X.scalefac_compress=M);return X.part2_length==a.LARGE_BITS};var F=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(X,M){var y,v,C,$,Z,se,j,ee,ce=s(4),me=M.scalefac;for(M.preflag!=0?y=2:y=0,j=0;j<4;j++)ce[j]=0;if(M.block_type==i.SHORT_TYPE){v=1;var ye=d.nr_of_sfb_block[y][v];for(ee=0,C=0;C<4;C++)for($=ye[C]/3,j=0;j<$;j++,ee++)for(Z=0;Z<3;Z++)me[ee*3+Z]>ce[C]&&(ce[C]=me[ee*3+Z])}else{v=0;var ye=d.nr_of_sfb_block[y][v];for(ee=0,C=0;C<4;C++)for($=ye[C],j=0;j<$;j++,ee++)me[ee]>ce[C]&&(ce[C]=me[ee])}for(se=!1,C=0;C<4;C++)ce[C]>F[y][C]&&(se=!0);if(!se){var Me,Se,ie,ue;for(M.sfb_partition_table=d.nr_of_sfb_block[y][v],C=0;C<4;C++)M.slen[C]=Y[ce[C]];switch(Me=M.slen[0],Se=M.slen[1],ie=M.slen[2],ue=M.slen[3],y){case 0:M.scalefac_compress=(Me*5+Se<<4)+(ie<<2)+ue;break;case 1:M.scalefac_compress=400+(Me*5+Se<<2)+ie;break;case 2:M.scalefac_compress=500+Me*3+Se;break;default:e.err.printf(`intensity stereo not implemented yet
`);break}}if(!se)for(r(M.sfb_partition_table!=null),M.part2_length=0,C=0;C<4;C++)M.part2_length+=M.slen[C]*M.sfb_partition_table[C];return se};var Y=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(X){for(var M=2;M<=576;M+=2){for(var y=0,v;X.scalefac_band.l[++y]<M;);for(v=p[y][0];X.scalefac_band.l[v+1]>M;)v--;for(v<0&&(v=p[y][0]),X.bv_scf[M-2]=v,v=p[y][1];X.scalefac_band.l[v+X.bv_scf[M-2]+2]>M;)v--;v<0&&(v=p[y][1]),X.bv_scf[M-1]=v}}}return hr=u,hr}var mr,Yi;function Gr(){if(Yi)return mr;Yi=1;var t=ft,e=t.System,n=t.Arrays,s=t.new_byte,r=t.new_float_n,i=t.new_int,l=t.assert,c=qa(),a=Wr,u=vt(),d=zs;o.EQ=function(p,_){return Math.abs(p)>Math.abs(_)?Math.abs(p-_)<=Math.abs(p)*1e-6:Math.abs(p-_)<=Math.abs(_)*1e-6},o.NEQ=function(p,_){return!o.EQ(p,_)};function o(){var p=Ua(),_=this,w=32773,b=null,g=null,E=null,T=null;this.setModules=function(M,y,v,C){b=M,g=y,E=v,T=C};var m=null,x=0,f=0,A=0;this.getframebits=function(M){var y=M.internal_flags,v;y.bitrate_index!=0?v=a.bitrate_table[M.version][y.bitrate_index]:v=M.brate;var C=0|(M.version+1)*72e3*v/M.out_samplerate+y.padding;return 8*C};function D(M){e.arraycopy(M.header[M.w_ptr].buf,0,m,f,M.sideinfo_len),f+=M.sideinfo_len,x+=M.sideinfo_len*8,M.w_ptr=M.w_ptr+1&d.MAX_HEADER_BUF-1}function V(M,y,v){for(;v>0;){var C;A==0&&(A=8,f++,l(f<p.LAME_MAXMP3BUFFER),l(M.header[M.w_ptr].write_timing>=x),M.header[M.w_ptr].write_timing==x&&D(M),m[f]=0),C=Math.min(v,A),v-=C,A-=C,m[f]|=y>>v<<A,x+=C}}function O(M,y,v){for(;v>0;){var C;A==0&&(A=8,f++,l(f<p.LAME_MAXMP3BUFFER),m[f]=0),C=Math.min(v,A),v-=C,A-=C,m[f]|=y>>v<<A,x+=C}}function q(M,y){var v=M.internal_flags,C;if(y>=8&&(V(v,76,8),y-=8),y>=8&&(V(v,65,8),y-=8),y>=8&&(V(v,77,8),y-=8),y>=8&&(V(v,69,8),y-=8),y>=32){var $=E.getLameShortVersion();if(y>=32)for(C=0;C<$.length&&y>=8;++C)y-=8,V(v,$.charAt(C),8)}for(;y>=1;y-=1)V(v,v.ancillary_flag,1),v.ancillary_flag^=M.disable_reservoir?0:1}function N(M,y,v){for(var C=M.header[M.h_ptr].ptr;v>0;){var $=Math.min(v,8-(C&7));v-=$,M.header[M.h_ptr].buf[C>>3]|=y>>v<<8-(C&7)-$,C+=$}M.header[M.h_ptr].ptr=C}function L(M,y){M<<=8;for(var v=0;v<8;v++)M<<=1,y<<=1,(y^M)&65536&&(y^=w);return y}this.CRC_writeheader=function(M,y){var v=65535;v=L(y[2]&255,v),v=L(y[3]&255,v);for(var C=6;C<M.sideinfo_len;C++)v=L(y[C]&255,v);y[4]=byte(v>>8),y[5]=byte(v&255)};function I(M,y){var v=M.internal_flags,C,$,Z;if(C=v.l3_side,v.header[v.h_ptr].ptr=0,n.fill(v.header[v.h_ptr].buf,0,v.sideinfo_len,0),M.out_samplerate<16e3?N(v,4094,12):N(v,4095,12),N(v,M.version,1),N(v,4-3,2),N(v,M.error_protection?0:1,1),N(v,v.bitrate_index,4),N(v,v.samplerate_index,2),N(v,v.padding,1),N(v,M.extension,1),N(v,M.mode.ordinal(),2),N(v,v.mode_ext,2),N(v,M.copyright,1),N(v,M.original,1),N(v,M.emphasis,2),M.error_protection&&N(v,0,16),M.version==1){for(l(C.main_data_begin>=0),N(v,C.main_data_begin,9),v.channels_out==2?N(v,C.private_bits,3):N(v,C.private_bits,5),Z=0;Z<v.channels_out;Z++){var se;for(se=0;se<4;se++)N(v,C.scfsi[Z][se],1)}for($=0;$<2;$++)for(Z=0;Z<v.channels_out;Z++){var j=C.tt[$][Z];N(v,j.part2_3_length+j.part2_length,12),N(v,j.big_values/2,9),N(v,j.global_gain,8),N(v,j.scalefac_compress,4),j.block_type!=u.NORM_TYPE?(N(v,1,1),N(v,j.block_type,2),N(v,j.mixed_block_flag,1),j.table_select[0]==14&&(j.table_select[0]=16),N(v,j.table_select[0],5),j.table_select[1]==14&&(j.table_select[1]=16),N(v,j.table_select[1],5),N(v,j.subblock_gain[0],3),N(v,j.subblock_gain[1],3),N(v,j.subblock_gain[2],3)):(N(v,0,1),j.table_select[0]==14&&(j.table_select[0]=16),N(v,j.table_select[0],5),j.table_select[1]==14&&(j.table_select[1]=16),N(v,j.table_select[1],5),j.table_select[2]==14&&(j.table_select[2]=16),N(v,j.table_select[2],5),l(0<=j.region0_count&&j.region0_count<16),l(0<=j.region1_count&&j.region1_count<8),N(v,j.region0_count,4),N(v,j.region1_count,3)),N(v,j.preflag,1),N(v,j.scalefac_scale,1),N(v,j.count1table_select,1)}}else for(l(C.main_data_begin>=0),N(v,C.main_data_begin,8),N(v,C.private_bits,v.channels_out),$=0,Z=0;Z<v.channels_out;Z++){var j=C.tt[$][Z];N(v,j.part2_3_length+j.part2_length,12),N(v,j.big_values/2,9),N(v,j.global_gain,8),N(v,j.scalefac_compress,9),j.block_type!=u.NORM_TYPE?(N(v,1,1),N(v,j.block_type,2),N(v,j.mixed_block_flag,1),j.table_select[0]==14&&(j.table_select[0]=16),N(v,j.table_select[0],5),j.table_select[1]==14&&(j.table_select[1]=16),N(v,j.table_select[1],5),N(v,j.subblock_gain[0],3),N(v,j.subblock_gain[1],3),N(v,j.subblock_gain[2],3)):(N(v,0,1),j.table_select[0]==14&&(j.table_select[0]=16),N(v,j.table_select[0],5),j.table_select[1]==14&&(j.table_select[1]=16),N(v,j.table_select[1],5),j.table_select[2]==14&&(j.table_select[2]=16),N(v,j.table_select[2],5),l(0<=j.region0_count&&j.region0_count<16),l(0<=j.region1_count&&j.region1_count<8),N(v,j.region0_count,4),N(v,j.region1_count,3)),N(v,j.scalefac_scale,1),N(v,j.count1table_select,1)}M.error_protection&&CRC_writeheader(v,v.header[v.h_ptr].buf);{var ee=v.h_ptr;l(v.header[ee].ptr==v.sideinfo_len*8),v.h_ptr=ee+1&d.MAX_HEADER_BUF-1,v.header[v.h_ptr].write_timing=v.header[ee].write_timing+y,v.h_ptr==v.w_ptr&&e.err.println(`Error: MAX_HEADER_BUF too small in bitstream.c 
`)}}function h(M,y){var v=a.ht[y.count1table_select+32],C,$=0,Z=y.big_values,se=y.big_values;for(l(y.count1table_select<2),C=(y.count1-y.big_values)/4;C>0;--C){var j=0,ee=0,ce;ce=y.l3_enc[Z+0],ce!=0&&(ee+=8,y.xr[se+0]<0&&j++),ce=y.l3_enc[Z+1],ce!=0&&(ee+=4,j*=2,y.xr[se+1]<0&&j++),ce=y.l3_enc[Z+2],ce!=0&&(ee+=2,j*=2,y.xr[se+2]<0&&j++),ce=y.l3_enc[Z+3],ce!=0&&(ee++,j*=2,y.xr[se+3]<0&&j++),Z+=4,se+=4,V(M,j+v.table[ee],v.hlen[ee]),$+=v.hlen[ee]}return $}function k(M,y,v,C,$){var Z=a.ht[y],se=0;if(y==0)return se;for(var j=v;j<C;j+=2){var ee=0,ce=0,me=Z.xlen,ye=Z.xlen,Me=0,Se=$.l3_enc[j],ie=$.l3_enc[j+1];if(Se!=0&&($.xr[j]<0&&Me++,ee--),y>15){if(Se>14){var ue=Se-15;l(ue<=Z.linmax),Me|=ue<<1,ce=me,Se=15}if(ie>14){var ze=ie-15;l(ze<=Z.linmax),Me<<=me,Me|=ze,ce+=me,ie=15}ye=16}ie!=0&&(Me<<=1,$.xr[j+1]<0&&Me++,ee--),Se=Se*ye+ie,ce-=ee,ee+=Z.hlen[Se],V(M,Z.table[Se],ee),V(M,Me,ce),se+=ee+ce}return se}function H(M,y){var v=3*M.scalefac_band.s[3];v>y.big_values&&(v=y.big_values);var C=k(M,y.table_select[0],0,v,y);return C+=k(M,y.table_select[1],v,y.big_values,y),C}function R(M,y){var v,C,$,Z;v=y.big_values;var se=y.region0_count+1;return l(se<M.scalefac_band.l.length),$=M.scalefac_band.l[se],se+=y.region1_count+1,l(se<M.scalefac_band.l.length),Z=M.scalefac_band.l[se],$>v&&($=v),Z>v&&(Z=v),C=k(M,y.table_select[0],0,$,y),C+=k(M,y.table_select[1],$,Z,y),C+=k(M,y.table_select[2],Z,v,y),C}function F(M){var y,v,C,$,Z=0,se=M.internal_flags,j=se.l3_side;if(M.version==1)for(y=0;y<2;y++)for(v=0;v<se.channels_out;v++){var ee=j.tt[y][v],ce=c.slen1_tab[ee.scalefac_compress],me=c.slen2_tab[ee.scalefac_compress];for($=0,C=0;C<ee.sfbdivide;C++)ee.scalefac[C]!=-1&&(V(se,ee.scalefac[C],ce),$+=ce);for(;C<ee.sfbmax;C++)ee.scalefac[C]!=-1&&(V(se,ee.scalefac[C],me),$+=me);l($==ee.part2_length),ee.block_type==u.SHORT_TYPE?$+=H(se,ee):$+=R(se,ee),$+=h(se,ee),l($==ee.part2_3_length+ee.part2_length),Z+=$}else for(y=0,v=0;v<se.channels_out;v++){var ee=j.tt[y][v],ye,Me,Se=0;if(l(ee.sfb_partition_table!=null),$=0,C=0,Me=0,ee.block_type==u.SHORT_TYPE){for(;Me<4;Me++){var ie=ee.sfb_partition_table[Me]/3,ue=ee.slen[Me];for(ye=0;ye<ie;ye++,C++)V(se,Math.max(ee.scalefac[C*3+0],0),ue),V(se,Math.max(ee.scalefac[C*3+1],0),ue),V(se,Math.max(ee.scalefac[C*3+2],0),ue),Se+=3*ue}$+=H(se,ee)}else{for(;Me<4;Me++){var ie=ee.sfb_partition_table[Me],ue=ee.slen[Me];for(ye=0;ye<ie;ye++,C++)V(se,Math.max(ee.scalefac[C],0),ue),Se+=ue}$+=R(se,ee)}$+=h(se,ee),l($==ee.part2_3_length),l(Se==ee.part2_length),Z+=Se+$}return Z}function Y(){this.total=0}function X(M,y){var v=M.internal_flags,C,$,Z,se,j;return j=v.w_ptr,se=v.h_ptr-1,se==-1&&(se=d.MAX_HEADER_BUF-1),C=v.header[se].write_timing-x,y.total=C,C>=0&&($=1+se-j,se<j&&($=1+se-j+d.MAX_HEADER_BUF),C-=$*8*v.sideinfo_len),Z=_.getframebits(M),C+=Z,y.total+=Z,y.total%8!=0?y.total=1+y.total/8:y.total=y.total/8,y.total+=f+1,C<0&&e.err.println(`strange error flushing buffer ... 
`),C}this.flush_bitstream=function(M){var y=M.internal_flags,v,C,$=y.h_ptr-1;if($==-1&&($=d.MAX_HEADER_BUF-1),v=y.l3_side,!((C=X(M,new Y))<0)){if(q(M,C),l(y.header[$].write_timing+this.getframebits(M)==x),y.ResvSize=0,v.main_data_begin=0,y.findReplayGain){var Z=b.GetTitleGain(y.rgdata);l(NEQ(Z,GainAnalysis.GAIN_NOT_ENOUGH_SAMPLES)),y.RadioGain=Math.floor(Z*10+.5)|0}y.findPeakSample&&(y.noclipGainChange=Math.ceil(Math.log10(y.PeakSample/32767)*20*10)|0,y.noclipGainChange>0&&(EQ(M.scale,1)||EQ(M.scale,0))?y.noclipScale=Math.floor(32767/y.PeakSample*100)/100:y.noclipScale=-1)}},this.add_dummy_byte=function(M,y,v){for(var C=M.internal_flags,$;v-- >0;)for(O(C,y,8),$=0;$<d.MAX_HEADER_BUF;++$)C.header[$].write_timing+=8},this.format_bitstream=function(M){var y=M.internal_flags,v;v=y.l3_side;var C=this.getframebits(M);q(M,v.resvDrain_pre),I(M,C);var $=8*y.sideinfo_len;if($+=F(M),q(M,v.resvDrain_post),$+=v.resvDrain_post,v.main_data_begin+=(C-$)/8,X(M,new Y)!=y.ResvSize&&e.err.println("Internal buffer inconsistency. flushbits <> ResvSize"),v.main_data_begin*8!=y.ResvSize&&(e.err.printf(`bit reservoir error: 
l3_side.main_data_begin: %d 
Resvoir size:             %d 
resv drain (post)         %d 
resv drain (pre)          %d 
header and sideinfo:      %d 
data bits:                %d 
total bits:               %d (remainder: %d) 
bitsperframe:             %d 
`,8*v.main_data_begin,y.ResvSize,v.resvDrain_post,v.resvDrain_pre,8*y.sideinfo_len,$-v.resvDrain_post-8*y.sideinfo_len,$,$%8,C),e.err.println("This is a fatal error.  It has several possible causes:"),e.err.println("90%%  LAME compiled with buggy version of gcc using advanced optimizations"),e.err.println(" 9%%  Your system is overclocked"),e.err.println(" 1%%  bug in LAME encoding library"),y.ResvSize=v.main_data_begin*8),x>1e9){var Z;for(Z=0;Z<d.MAX_HEADER_BUF;++Z)y.header[Z].write_timing-=x;x=0}return 0},this.copy_buffer=function(M,y,v,C,$){var Z=f+1;if(Z<=0)return 0;if(C!=0&&Z>C)return-1;if(e.arraycopy(m,0,y,v,Z),f=-1,A=0,$!=0){var se=i(1);if(se[0]=M.nMusicCRC,T.updateMusicCRC(se,y,v,Z),M.nMusicCRC=se[0],Z>0&&(M.VBR_seek_table.nBytesWritten+=Z),M.decode_on_the_fly){for(var j=r([2,1152]),ee=Z,ce=-1,me;ce!=0;)if(ce=g.hip_decode1_unclipped(M.hip,y,v,ee,j[0],j[1]),ee=0,ce==-1&&(ce=0),ce>0){if(M.findPeakSample){for(me=0;me<ce;me++)j[0][me]>M.PeakSample?M.PeakSample=j[0][me]:-j[0][me]>M.PeakSample&&(M.PeakSample=-j[0][me]);if(M.channels_out>1)for(me=0;me<ce;me++)j[1][me]>M.PeakSample?M.PeakSample=j[1][me]:-j[1][me]>M.PeakSample&&(M.PeakSample=-j[1][me])}if(M.findReplayGain&&b.AnalyzeSamples(M.rgdata,j[0],0,j[1],0,ce,M.channels_out)==GainAnalysis.GAIN_ANALYSIS_ERROR)return-6}}}return Z},this.init_bit_stream_w=function(M){m=s(p.LAME_MAXMP3BUFFER),M.h_ptr=M.w_ptr=0,M.header[M.h_ptr].write_timing=0,f=-1,A=0,x=0}}return mr=o,mr}var _r,Wi;function Ua(){if(Wi)return _r;Wi=1;var t=ft,e=t.System,n=t.VbrMode,s=t.ShortBlock,r=t.new_float,i=t.new_int_n,l=t.new_short_n,c=t.assert,a=Hu,u=Yu,d=zs,o=hd,p=gd,_=Td,w=Gr(),b=Wr,g=vt();function E(){var T=Ns,m=this,x=128*1024;E.V9=410,E.V8=420,E.V7=430,E.V6=440,E.V5=450,E.V4=460,E.V3=470,E.V2=480,E.V1=490,E.V0=500,E.R3MIX=1e3,E.STANDARD=1001,E.EXTREME=1002,E.INSANE=1003,E.STANDARD_FAST=1004,E.EXTREME_FAST=1005,E.MEDIUM=1006,E.MEDIUM_FAST=1007;var f=16384+x;E.LAME_MAXMP3BUFFER=f;var A,D,V,O,q,N=new a,L,I,h;this.enc=new g,this.setModules=function(S,P,ge,Ee,Be,Ce,ke,xe,He){A=S,D=P,V=ge,O=Ee,q=Be,L=Ce,I=xe,h=He,this.enc.setModules(D,N,O,L)};function k(){this.mask_adjust=0,this.mask_adjust_short=0,this.bo_l_weight=r(g.SBMAX_l),this.bo_s_weight=r(g.SBMAX_s)}function H(){this.lowerlimit=0}function R(S,P){this.lowpass=P}var F=4294479419;function Y(S){var P;return S.class_id=F,P=S.internal_flags=new d,S.mode=T.NOT_SET,S.original=1,S.in_samplerate=44100,S.num_channels=2,S.num_samples=-1,S.bWriteVbrTag=!0,S.quality=-1,S.short_blocks=null,P.subblock_gain=-1,S.lowpassfreq=0,S.highpassfreq=0,S.lowpasswidth=-1,S.highpasswidth=-1,S.VBR=n.vbr_off,S.VBR_q=4,S.ATHcurve=-1,S.VBR_mean_bitrate_kbps=128,S.VBR_min_bitrate_kbps=0,S.VBR_max_bitrate_kbps=0,S.VBR_hard_min=0,P.VBR_min_bitrate=1,P.VBR_max_bitrate=13,S.quant_comp=-1,S.quant_comp_short=-1,S.msfix=-1,P.resample_ratio=1,P.OldValue[0]=180,P.OldValue[1]=180,P.CurrentStep[0]=4,P.CurrentStep[1]=4,P.masking_lower=1,P.nsPsy.attackthre=-1,P.nsPsy.attackthre_s=-1,S.scale=-1,S.athaa_type=-1,S.ATHtype=-1,S.athaa_loudapprox=-1,S.athaa_sensitivity=0,S.useTemporal=null,S.interChRatio=-1,P.mf_samples_to_encode=g.ENCDELAY+g.POSTDELAY,S.encoder_padding=0,P.mf_size=g.ENCDELAY-g.MDCTDELAY,S.findReplayGain=!1,S.decode_on_the_fly=!1,P.decode_on_the_fly=!1,P.findReplayGain=!1,P.findPeakSample=!1,P.RadioGain=0,P.AudiophileGain=0,P.noclipGainChange=0,P.noclipScale=-1,S.preset=0,S.write_id3tag_automatic=!0,0}this.lame_init=function(){var S=new u;return Y(S),S.lame_allocated_gfp=1,S};function X(S){return S>1?0:S<=0?1:Math.cos(Math.PI/2*S)}this.nearestBitrateFullIndex=function(S){var P=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],ge=0,Ee=0,Be=0,Ce=0;Ce=P[16],Be=16,Ee=P[16],ge=16;for(var ke=0;ke<16;ke++)if(Math.max(S,P[ke+1])!=S){Ce=P[ke+1],Be=ke+1,Ee=P[ke],ge=ke;break}return Ce-S>S-Ee?ge:Be};function M(S,P){var ge=44100;return P>=48e3?ge=48e3:P>=44100?ge=44100:P>=32e3?ge=32e3:P>=24e3?ge=24e3:P>=22050?ge=22050:P>=16e3?ge=16e3:P>=12e3?ge=12e3:P>=11025?ge=11025:P>=8e3&&(ge=8e3),S==-1?ge:(S<=15960&&(ge=44100),S<=15250&&(ge=32e3),S<=11220&&(ge=24e3),S<=9970&&(ge=22050),S<=7230&&(ge=16e3),S<=5420&&(ge=12e3),S<=4510&&(ge=11025),S<=3970&&(ge=8e3),P<ge?P>44100?48e3:P>32e3?44100:P>24e3?32e3:P>22050?24e3:P>16e3?22050:P>12e3?16e3:P>11025?12e3:P>8e3?11025:8e3:ge)}function y(S,P){switch(S){case 44100:return P.version=1,0;case 48e3:return P.version=1,1;case 32e3:return P.version=1,2;case 22050:return P.version=0,0;case 24e3:return P.version=0,1;case 16e3:return P.version=0,2;case 11025:return P.version=0,0;case 12e3:return P.version=0,1;case 8e3:return P.version=0,2;default:return P.version=0,-1}}function v(S,P,ge){ge<16e3&&(P=2);for(var Ee=b.bitrate_table[P][1],Be=2;Be<=14;Be++)b.bitrate_table[P][Be]>0&&Math.abs(b.bitrate_table[P][Be]-S)<Math.abs(Ee-S)&&(Ee=b.bitrate_table[P][Be]);return Ee}function C(S,P,ge){ge<16e3&&(P=2);for(var Ee=0;Ee<=14;Ee++)if(b.bitrate_table[P][Ee]>0&&b.bitrate_table[P][Ee]==S)return Ee;return-1}function $(S,P){var ge=[new R(8,2e3),new R(16,3700),new R(24,3900),new R(32,5500),new R(40,7e3),new R(48,7500),new R(56,1e4),new R(64,11e3),new R(80,13500),new R(96,15100),new R(112,15600),new R(128,17e3),new R(160,17500),new R(192,18600),new R(224,19400),new R(256,19700),new R(320,20500)],Ee=m.nearestBitrateFullIndex(P);S.lowerlimit=ge[Ee].lowpass}function Z(S){var P=S.internal_flags,ge=32,Ee=-1;if(P.lowpass1>0){for(var Be=999,Ce=0;Ce<=31;Ce++){var ke=Ce/31;ke>=P.lowpass2&&(ge=Math.min(ge,Ce)),P.lowpass1<ke&&ke<P.lowpass2&&(Be=Math.min(Be,Ce))}Be==999?P.lowpass1=(ge-.75)/31:P.lowpass1=(Be-.75)/31,P.lowpass2=ge/31}if(P.highpass2>0&&P.highpass2<.9*(.75/31)&&(P.highpass1=0,P.highpass2=0,e.err.println(`Warning: highpass filter disabled.  highpass frequency too small
`)),P.highpass2>0){for(var xe=-1,Ce=0;Ce<=31;Ce++){var ke=Ce/31;ke<=P.highpass1&&(Ee=Math.max(Ee,Ce)),P.highpass1<ke&&ke<P.highpass2&&(xe=Math.max(xe,Ce))}P.highpass1=Ee/31,xe==-1?P.highpass2=(Ee+.75)/31:P.highpass2=(xe+.75)/31}for(var Ce=0;Ce<32;Ce++){var He,Le,ke=Ce/31;P.highpass2>P.highpass1?He=X((P.highpass2-ke)/(P.highpass2-P.highpass1+1e-20)):He=1,P.lowpass2>P.lowpass1?Le=X((ke-P.lowpass1)/(P.lowpass2-P.lowpass1+1e-20)):Le=1,P.amp_filter[Ce]=He*Le}}function se(S){var P=S.internal_flags;switch(S.quality){default:case 9:P.psymodel=0,P.noise_shaping=0,P.noise_shaping_amp=0,P.noise_shaping_stop=0,P.use_best_huffman=0,P.full_outer_loop=0;break;case 8:S.quality=7;case 7:P.psymodel=1,P.noise_shaping=0,P.noise_shaping_amp=0,P.noise_shaping_stop=0,P.use_best_huffman=0,P.full_outer_loop=0;break;case 6:P.psymodel=1,P.noise_shaping==0&&(P.noise_shaping=1),P.noise_shaping_amp=0,P.noise_shaping_stop=0,P.subblock_gain==-1&&(P.subblock_gain=1),P.use_best_huffman=0,P.full_outer_loop=0;break;case 5:P.psymodel=1,P.noise_shaping==0&&(P.noise_shaping=1),P.noise_shaping_amp=0,P.noise_shaping_stop=0,P.subblock_gain==-1&&(P.subblock_gain=1),P.use_best_huffman=0,P.full_outer_loop=0;break;case 4:P.psymodel=1,P.noise_shaping==0&&(P.noise_shaping=1),P.noise_shaping_amp=0,P.noise_shaping_stop=0,P.subblock_gain==-1&&(P.subblock_gain=1),P.use_best_huffman=1,P.full_outer_loop=0;break;case 3:P.psymodel=1,P.noise_shaping==0&&(P.noise_shaping=1),P.noise_shaping_amp=1,P.noise_shaping_stop=1,P.subblock_gain==-1&&(P.subblock_gain=1),P.use_best_huffman=1,P.full_outer_loop=0;break;case 2:P.psymodel=1,P.noise_shaping==0&&(P.noise_shaping=1),P.substep_shaping==0&&(P.substep_shaping=2),P.noise_shaping_amp=1,P.noise_shaping_stop=1,P.subblock_gain==-1&&(P.subblock_gain=1),P.use_best_huffman=1,P.full_outer_loop=0;break;case 1:P.psymodel=1,P.noise_shaping==0&&(P.noise_shaping=1),P.substep_shaping==0&&(P.substep_shaping=2),P.noise_shaping_amp=2,P.noise_shaping_stop=1,P.subblock_gain==-1&&(P.subblock_gain=1),P.use_best_huffman=1,P.full_outer_loop=0;break;case 0:P.psymodel=1,P.noise_shaping==0&&(P.noise_shaping=1),P.substep_shaping==0&&(P.substep_shaping=2),P.noise_shaping_amp=2,P.noise_shaping_stop=1,P.subblock_gain==-1&&(P.subblock_gain=1),P.use_best_huffman=1,P.full_outer_loop=0;break}}function j(S){var P=S.internal_flags;S.frameNum=0,S.write_id3tag_automatic&&I.id3tag_write_v2(S),P.bitrate_stereoMode_Hist=i([16,4+1]),P.bitrate_blockType_Hist=i([16,4+1+1]),P.PeakSample=0,S.bWriteVbrTag&&L.InitVbrTag(S)}this.lame_init_params=function(S){var P=S.internal_flags;if(P.Class_ID=0,P.ATH==null&&(P.ATH=new o),P.PSY==null&&(P.PSY=new k),P.rgdata==null&&(P.rgdata=new p),P.channels_in=S.num_channels,P.channels_in==1&&(S.mode=T.MONO),P.channels_out=S.mode==T.MONO?1:2,P.mode_ext=g.MPG_MD_MS_LR,S.mode==T.MONO&&(S.force_ms=!1),S.VBR==n.vbr_off&&S.VBR_mean_bitrate_kbps!=128&&S.brate==0&&(S.brate=S.VBR_mean_bitrate_kbps),S.VBR==n.vbr_off||S.VBR==n.vbr_mtrh||S.VBR==n.vbr_mt||(S.free_format=!1),S.VBR==n.vbr_off&&S.brate==0&&w.EQ(S.compression_ratio,0)&&(S.compression_ratio=11.025),S.VBR==n.vbr_off&&S.compression_ratio>0&&(S.out_samplerate==0&&(S.out_samplerate=map2MP3Frequency(int(.97*S.in_samplerate))),S.brate=0|S.out_samplerate*16*P.channels_out/(1e3*S.compression_ratio),P.samplerate_index=y(S.out_samplerate,S),S.free_format||(S.brate=v(S.brate,S.version,S.out_samplerate))),S.out_samplerate!=0&&(S.out_samplerate<16e3?(S.VBR_mean_bitrate_kbps=Math.max(S.VBR_mean_bitrate_kbps,8),S.VBR_mean_bitrate_kbps=Math.min(S.VBR_mean_bitrate_kbps,64)):S.out_samplerate<32e3?(S.VBR_mean_bitrate_kbps=Math.max(S.VBR_mean_bitrate_kbps,8),S.VBR_mean_bitrate_kbps=Math.min(S.VBR_mean_bitrate_kbps,160)):(S.VBR_mean_bitrate_kbps=Math.max(S.VBR_mean_bitrate_kbps,32),S.VBR_mean_bitrate_kbps=Math.min(S.VBR_mean_bitrate_kbps,320))),S.lowpassfreq==0){var ge=16e3;switch(S.VBR){case n.vbr_off:{var Ee=new H;$(Ee,S.brate),ge=Ee.lowerlimit;break}case n.vbr_abr:{var Ee=new H;$(Ee,S.VBR_mean_bitrate_kbps),ge=Ee.lowerlimit;break}case n.vbr_rh:{var Be=[19500,19e3,18600,18e3,17500,16e3,15600,14900,12500,1e4,3950];if(0<=S.VBR_q&&S.VBR_q<=9){var Ce=Be[S.VBR_q],ke=Be[S.VBR_q+1],xe=S.VBR_q_frac;ge=linear_int(Ce,ke,xe)}else ge=19500;break}default:{var Be=[19500,19e3,18500,18e3,17500,16500,15500,14500,12500,9500,3950];if(0<=S.VBR_q&&S.VBR_q<=9){var Ce=Be[S.VBR_q],ke=Be[S.VBR_q+1],xe=S.VBR_q_frac;ge=linear_int(Ce,ke,xe)}else ge=19500}}S.mode==T.MONO&&(S.VBR==n.vbr_off||S.VBR==n.vbr_abr)&&(ge*=1.5),S.lowpassfreq=ge|0}if(S.out_samplerate==0&&(2*S.lowpassfreq>S.in_samplerate&&(S.lowpassfreq=S.in_samplerate/2),S.out_samplerate=M(S.lowpassfreq|0,S.in_samplerate)),S.lowpassfreq=Math.min(20500,S.lowpassfreq),S.lowpassfreq=Math.min(S.out_samplerate/2,S.lowpassfreq),S.VBR==n.vbr_off&&(S.compression_ratio=S.out_samplerate*16*P.channels_out/(1e3*S.brate)),S.VBR==n.vbr_abr&&(S.compression_ratio=S.out_samplerate*16*P.channels_out/(1e3*S.VBR_mean_bitrate_kbps)),S.bWriteVbrTag||(S.findReplayGain=!1,S.decode_on_the_fly=!1,P.findPeakSample=!1),P.findReplayGain=S.findReplayGain,P.decode_on_the_fly=S.decode_on_the_fly,P.decode_on_the_fly&&(P.findPeakSample=!0),P.findReplayGain&&A.InitGainAnalysis(P.rgdata,S.out_samplerate)==GainAnalysis.INIT_GAIN_ANALYSIS_ERROR)return S.internal_flags=null,-6;switch(P.decode_on_the_fly&&!S.decode_only&&(P.hip!=null&&h.hip_decode_exit(P.hip),P.hip=h.hip_decode_init()),P.mode_gr=S.out_samplerate<=24e3?1:2,S.framesize=576*P.mode_gr,S.encoder_delay=g.ENCDELAY,P.resample_ratio=S.in_samplerate/S.out_samplerate,S.VBR){case n.vbr_mt:case n.vbr_rh:case n.vbr_mtrh:{var He=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5];S.compression_ratio=He[S.VBR_q]}break;case n.vbr_abr:S.compression_ratio=S.out_samplerate*16*P.channels_out/(1e3*S.VBR_mean_bitrate_kbps);break;default:S.compression_ratio=S.out_samplerate*16*P.channels_out/(1e3*S.brate);break}if(S.mode==T.NOT_SET&&(S.mode=T.JOINT_STEREO),S.highpassfreq>0?(P.highpass1=2*S.highpassfreq,S.highpasswidth>=0?P.highpass2=2*(S.highpassfreq+S.highpasswidth):P.highpass2=(1+0)*2*S.highpassfreq,P.highpass1/=S.out_samplerate,P.highpass2/=S.out_samplerate):(P.highpass1=0,P.highpass2=0),S.lowpassfreq>0?(P.lowpass2=2*S.lowpassfreq,S.lowpasswidth>=0?(P.lowpass1=2*(S.lowpassfreq-S.lowpasswidth),P.lowpass1<0&&(P.lowpass1=0)):P.lowpass1=(1-0)*2*S.lowpassfreq,P.lowpass1/=S.out_samplerate,P.lowpass2/=S.out_samplerate):(P.lowpass1=0,P.lowpass2=0),Z(S),P.samplerate_index=y(S.out_samplerate,S),P.samplerate_index<0)return S.internal_flags=null,-1;if(S.VBR==n.vbr_off){if(S.free_format)P.bitrate_index=0;else if(S.brate=v(S.brate,S.version,S.out_samplerate),P.bitrate_index=C(S.brate,S.version,S.out_samplerate),P.bitrate_index<=0)return S.internal_flags=null,-1}else P.bitrate_index=1;S.analysis&&(S.bWriteVbrTag=!1),P.pinfo!=null&&(S.bWriteVbrTag=!1),D.init_bit_stream_w(P);for(var Le=P.samplerate_index+3*S.version+6*(S.out_samplerate<16e3?1:0),be=0;be<g.SBMAX_l+1;be++)P.scalefac_band.l[be]=O.sfBandIndex[Le].l[be];for(var be=0;be<g.PSFB21+1;be++){var Qe=(P.scalefac_band.l[22]-P.scalefac_band.l[21])/g.PSFB21,st=P.scalefac_band.l[21]+be*Qe;P.scalefac_band.psfb21[be]=st}P.scalefac_band.psfb21[g.PSFB21]=576;for(var be=0;be<g.SBMAX_s+1;be++)P.scalefac_band.s[be]=O.sfBandIndex[Le].s[be];for(var be=0;be<g.PSFB12+1;be++){var Qe=(P.scalefac_band.s[13]-P.scalefac_band.s[12])/g.PSFB12,st=P.scalefac_band.s[12]+be*Qe;P.scalefac_band.psfb12[be]=st}P.scalefac_band.psfb12[g.PSFB12]=192,S.version==1?P.sideinfo_len=P.channels_out==1?4+17:4+32:P.sideinfo_len=P.channels_out==1?4+9:4+17,S.error_protection&&(P.sideinfo_len+=2),j(S),P.Class_ID=F;{var B;for(B=0;B<19;B++)P.nsPsy.pefirbuf[B]=700*P.mode_gr*P.channels_out;S.ATHtype==-1&&(S.ATHtype=4)}switch(c(S.VBR_q<=9),c(S.VBR_q>=0),S.VBR){case n.vbr_mt:S.VBR=n.vbr_mtrh;case n.vbr_mtrh:{S.useTemporal==null&&(S.useTemporal=!1),V.apply_preset(S,500-S.VBR_q*10,0),S.quality<0&&(S.quality=LAME_DEFAULT_QUALITY),S.quality<5&&(S.quality=0),S.quality>5&&(S.quality=5),P.PSY.mask_adjust=S.maskingadjust,P.PSY.mask_adjust_short=S.maskingadjust_short,S.experimentalY?P.sfb21_extra=!1:P.sfb21_extra=S.out_samplerate>44e3,P.iteration_loop=new VBRNewIterationLoop(q);break}case n.vbr_rh:{V.apply_preset(S,500-S.VBR_q*10,0),P.PSY.mask_adjust=S.maskingadjust,P.PSY.mask_adjust_short=S.maskingadjust_short,S.experimentalY?P.sfb21_extra=!1:P.sfb21_extra=S.out_samplerate>44e3,S.quality>6&&(S.quality=6),S.quality<0&&(S.quality=LAME_DEFAULT_QUALITY),P.iteration_loop=new VBROldIterationLoop(q);break}default:{var z;P.sfb21_extra=!1,S.quality<0&&(S.quality=LAME_DEFAULT_QUALITY),z=S.VBR,z==n.vbr_off&&(S.VBR_mean_bitrate_kbps=S.brate),V.apply_preset(S,S.VBR_mean_bitrate_kbps,0),S.VBR=z,P.PSY.mask_adjust=S.maskingadjust,P.PSY.mask_adjust_short=S.maskingadjust_short,z==n.vbr_off?P.iteration_loop=new _(q):P.iteration_loop=new ABRIterationLoop(q);break}}if(c(S.scale>=0),S.VBR!=n.vbr_off){if(P.VBR_min_bitrate=1,P.VBR_max_bitrate=14,S.out_samplerate<16e3&&(P.VBR_max_bitrate=8),S.VBR_min_bitrate_kbps!=0&&(S.VBR_min_bitrate_kbps=v(S.VBR_min_bitrate_kbps,S.version,S.out_samplerate),P.VBR_min_bitrate=C(S.VBR_min_bitrate_kbps,S.version,S.out_samplerate),P.VBR_min_bitrate<0)||S.VBR_max_bitrate_kbps!=0&&(S.VBR_max_bitrate_kbps=v(S.VBR_max_bitrate_kbps,S.version,S.out_samplerate),P.VBR_max_bitrate=C(S.VBR_max_bitrate_kbps,S.version,S.out_samplerate),P.VBR_max_bitrate<0))return-1;S.VBR_min_bitrate_kbps=b.bitrate_table[S.version][P.VBR_min_bitrate],S.VBR_max_bitrate_kbps=b.bitrate_table[S.version][P.VBR_max_bitrate],S.VBR_mean_bitrate_kbps=Math.min(b.bitrate_table[S.version][P.VBR_max_bitrate],S.VBR_mean_bitrate_kbps),S.VBR_mean_bitrate_kbps=Math.max(b.bitrate_table[S.version][P.VBR_min_bitrate],S.VBR_mean_bitrate_kbps)}return S.tune&&(P.PSY.mask_adjust+=S.tune_value_a,P.PSY.mask_adjust_short+=S.tune_value_a),se(S),c(S.scale>=0),S.athaa_type<0?P.ATH.useAdjust=3:P.ATH.useAdjust=S.athaa_type,P.ATH.aaSensitivityP=Math.pow(10,S.athaa_sensitivity/-10),S.short_blocks==null&&(S.short_blocks=s.short_block_allowed),S.short_blocks==s.short_block_allowed&&(S.mode==T.JOINT_STEREO||S.mode==T.STEREO)&&(S.short_blocks=s.short_block_coupled),S.quant_comp<0&&(S.quant_comp=1),S.quant_comp_short<0&&(S.quant_comp_short=0),S.msfix<0&&(S.msfix=0),S.exp_nspsytune=S.exp_nspsytune|1,S.internal_flags.nsPsy.attackthre<0&&(S.internal_flags.nsPsy.attackthre=a.NSATTACKTHRE),S.internal_flags.nsPsy.attackthre_s<0&&(S.internal_flags.nsPsy.attackthre_s=a.NSATTACKTHRE_S),c(S.scale>=0),S.scale<0&&(S.scale=1),S.ATHtype<0&&(S.ATHtype=4),S.ATHcurve<0&&(S.ATHcurve=4),S.athaa_loudapprox<0&&(S.athaa_loudapprox=2),S.interChRatio<0&&(S.interChRatio=0),S.useTemporal==null&&(S.useTemporal=!0),P.slot_lag=P.frac_SpF=0,S.VBR==n.vbr_off&&(P.slot_lag=P.frac_SpF=(S.version+1)*72e3*S.brate%S.out_samplerate|0),O.iteration_init(S),N.psymodel_init(S),c(S.scale>=0),0};function ee(S,P){(S.in_buffer_0==null||S.in_buffer_nsamples<P)&&(S.in_buffer_0=r(P),S.in_buffer_1=r(P),S.in_buffer_nsamples=P)}this.lame_encode_flush=function(S,P,ge,Ee){var Be=S.internal_flags,Ce=l([2,1152]),ke=0,xe,He,Le,be,Qe=Be.mf_samples_to_encode-g.POSTDELAY,st=ce(S);if(Be.mf_samples_to_encode<1)return 0;for(xe=0,S.in_samplerate!=S.out_samplerate&&(Qe+=16*S.out_samplerate/S.in_samplerate),Le=S.framesize-Qe%S.framesize,Le<576&&(Le+=S.framesize),S.encoder_padding=Le,be=(Qe+Le)/S.framesize;be>0&&ke>=0;){var B=st-Be.mf_size,z=S.frameNum;B*=S.in_samplerate,B/=S.out_samplerate,B>1152&&(B=1152),B<1&&(B=1),He=Ee-xe,Ee==0&&(He=0),ke=this.lame_encode_buffer(S,Ce[0],Ce[1],B,P,ge,He),ge+=ke,xe+=ke,be-=z!=S.frameNum?1:0}if(Be.mf_samples_to_encode=0,ke<0||(He=Ee-xe,Ee==0&&(He=0),D.flush_bitstream(S),ke=D.copy_buffer(Be,P,ge,He,1),ke<0))return ke;if(ge+=ke,xe+=ke,He=Ee-xe,Ee==0&&(He=0),S.write_id3tag_automatic){if(I.id3tag_write_v1(S),ke=D.copy_buffer(Be,P,ge,He,0),ke<0)return ke;xe+=ke}return xe},this.lame_encode_buffer=function(S,P,ge,Ee,Be,Ce,ke){var xe=S.internal_flags,He=[null,null];if(xe.Class_ID!=F)return-3;if(Ee==0)return 0;ee(xe,Ee),He[0]=xe.in_buffer_0,He[1]=xe.in_buffer_1;for(var Le=0;Le<Ee;Le++)He[0][Le]=P[Le],xe.channels_in>1&&(He[1][Le]=ge[Le]);return me(S,He[0],He[1],Ee,Be,Ce,ke)};function ce(S){var P=g.BLKSIZE+S.framesize-g.FFTOFFSET;return P=Math.max(P,512+S.framesize-32),P}function me(S,P,ge,Ee,Be,Ce,ke){var xe=S.internal_flags,He=0,Le,be,Qe,st,B,z=[null,null],G=[null,null];if(xe.Class_ID!=F)return-3;if(Ee==0)return 0;if(B=D.copy_buffer(xe,Be,Ce,ke,0),B<0)return B;if(Ce+=B,He+=B,G[0]=P,G[1]=ge,w.NEQ(S.scale,0)&&w.NEQ(S.scale,1))for(be=0;be<Ee;++be)G[0][be]*=S.scale,xe.channels_out==2&&(G[1][be]*=S.scale);if(w.NEQ(S.scale_left,0)&&w.NEQ(S.scale_left,1))for(be=0;be<Ee;++be)G[0][be]*=S.scale_left;if(w.NEQ(S.scale_right,0)&&w.NEQ(S.scale_right,1))for(be=0;be<Ee;++be)G[1][be]*=S.scale_right;if(S.num_channels==2&&xe.channels_out==1)for(be=0;be<Ee;++be)G[0][be]=.5*(G[0][be]+G[1][be]),G[1][be]=0;st=ce(S),z[0]=xe.mfbuf[0],z[1]=xe.mfbuf[1];for(var U=0;Ee>0;){var Q=[null,null],W=0,J=0;Q[0]=G[0],Q[1]=G[1];var te=new Me;if(qe(S,z,Q,U,Ee,te),W=te.n_in,J=te.n_out,xe.findReplayGain&&!xe.decode_on_the_fly&&A.AnalyzeSamples(xe.rgdata,z[0],xe.mf_size,z[1],xe.mf_size,J,xe.channels_out)==GainAnalysis.GAIN_ANALYSIS_ERROR)return-6;if(Ee-=W,U+=W,xe.channels_out==2,xe.mf_size+=J,c(xe.mf_size<=d.MFSIZE),xe.mf_samples_to_encode<1&&(xe.mf_samples_to_encode=g.ENCDELAY+g.POSTDELAY),xe.mf_samples_to_encode+=J,xe.mf_size>=st){var K=ke-He;if(ke==0&&(K=0),Le=ye(S,z[0],z[1],Be,Ce,K),Le<0)return Le;for(Ce+=Le,He+=Le,xe.mf_size-=S.framesize,xe.mf_samples_to_encode-=S.framesize,Qe=0;Qe<xe.channels_out;Qe++)for(be=0;be<xe.mf_size;be++)z[Qe][be]=z[Qe][be+S.framesize]}}return He}function ye(S,P,ge,Ee,Be,Ce){var ke=m.enc.lame_encode_mp3_frame(S,P,ge,Ee,Be,Ce);return S.frameNum++,ke}function Me(){this.n_in=0,this.n_out=0}function Se(){this.num_used=0}function ie(S,P){return P!=0?ie(P,S%P):S}function ue(S,P,ge){var Ee=Math.PI*P;S/=ge,S<0&&(S=0),S>1&&(S=1);var Be=S-.5,Ce=.42-.5*Math.cos(2*S*Math.PI)+.08*Math.cos(4*S*Math.PI);return Math.abs(Be)<1e-9?Ee/Math.PI:Ce*Math.sin(ge*Ee*Be)/(Math.PI*ge*Be)}function ze(S,P,ge,Ee,Be,Ce,ke,xe,He){var Le=S.internal_flags,be,Qe=0,st,B=S.out_samplerate/ie(S.out_samplerate,S.in_samplerate);B>d.BPC&&(B=d.BPC);var z=Math.abs(Le.resample_ratio-Math.floor(.5+Le.resample_ratio))<1e-4?1:0,G=1/Le.resample_ratio;G>1&&(G=1);var U=31;U%2==0&&--U,U+=z;var Q=U+1;if(Le.fill_buffer_resample_init==0){for(Le.inbuf_old[0]=r(Q),Le.inbuf_old[1]=r(Q),be=0;be<=2*B;++be)Le.blackfilt[be]=r(Q);for(Le.itime[0]=0,Le.itime[1]=0,Qe=0;Qe<=2*B;Qe++){var W=0,J=(Qe-B)/(2*B);for(be=0;be<=U;be++)W+=Le.blackfilt[Qe][be]=ue(be-J,G,U);for(be=0;be<=U;be++)Le.blackfilt[Qe][be]/=W}Le.fill_buffer_resample_init=1}var te=Le.inbuf_old[He];for(st=0;st<Ee;st++){var K,oe;if(K=st*Le.resample_ratio,Qe=0|Math.floor(K-Le.itime[He]),U+Qe-U/2>=ke)break;var J=K-Le.itime[He]-(Qe+.5*(U%2));oe=0|Math.floor(J*2*B+B+.5);var ne=0;for(be=0;be<=U;++be){var ae=0|be+Qe-U/2,ve;ve=ae<0?te[Q+ae]:Be[Ce+ae],ne+=ve*Le.blackfilt[oe][be]}P[ge+st]=ne}if(xe.num_used=Math.min(ke,U+Qe-U/2),Le.itime[He]+=xe.num_used-st*Le.resample_ratio,xe.num_used>=Q)for(be=0;be<Q;be++)te[be]=Be[Ce+xe.num_used+be-Q];else{var Ie=Q-xe.num_used;for(be=0;be<Ie;++be)te[be]=te[be+xe.num_used];for(Qe=0;be<Q;++be,++Qe)te[be]=Be[Ce+Qe];c(Qe==xe.num_used)}return st}function qe(S,P,ge,Ee,Be,Ce){var ke=S.internal_flags;if(ke.resample_ratio<.9999||ke.resample_ratio>1.0001)for(var xe=0;xe<ke.channels_out;xe++){var He=new Se;Ce.n_out=ze(S,P[xe],ke.mf_size,S.framesize,ge[xe],Ee,Be,He,xe),Ce.n_in=He.num_used}else{Ce.n_out=Math.min(S.framesize,Be),Ce.n_in=Ce.n_out;for(var Le=0;Le<Ce.n_out;++Le)P[0][ke.mf_size+Le]=ge[0][Ee+Le],ke.channels_out==2&&(P[1][ke.mf_size+Le]=ge[1][Ee+Le])}}}return _r=E,_r}vt();Ua();ja();qa();Gr();vt();function qt(){this.dataOffset=0,this.dataLen=0,this.channels=0,this.sampleRate=0}function $s(t){return t.charCodeAt(0)<<24|t.charCodeAt(1)<<16|t.charCodeAt(2)<<8|t.charCodeAt(3)}qt.RIFF=$s("RIFF");qt.WAVE=$s("WAVE");qt.fmt_=$s("fmt ");qt.data=$s("data");qt.readHeader=function(t){var e=new qt,n=t.getUint32(0,!1);if(qt.RIFF==n&&(t.getUint32(4,!0),qt.WAVE==t.getUint32(8,!1)&&qt.fmt_==t.getUint32(12,!1))){var s=t.getUint32(16,!0),r=16+4;switch(s){case 16:case 18:e.channels=t.getUint16(r+2,!0),e.sampleRate=t.getUint32(r+4,!0);break;default:throw"extended fmt chunk not implemented"}r+=s;for(var i=qt.data,l=0;i!=n&&(n=t.getUint32(r,!1),l=t.getUint32(r+4,!0),i!=n);)r+=l+8;return e.dataLen=l,e.dataOffset=r+8,e}};const Md=t=>e=>{const n=t(e);return e.add(n),n},Ad=t=>(e,n)=>(t.set(e,n),n),Gi=Number.MAX_SAFE_INTEGER===void 0?9007199254740991:Number.MAX_SAFE_INTEGER,Za=536870912,ji=Za*2,xd=(t,e)=>n=>{const s=e.get(n);let r=s===void 0?n.size:s<ji?s+1:0;if(!n.has(r))return t(n,r);if(n.size<Za){for(;n.has(r);)r=Math.floor(Math.random()*ji);return t(n,r)}if(n.size>Gi)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;n.has(r);)r=Math.floor(Math.random()*Gi);return t(n,r)},Qa=new WeakMap,Cd=Ad(Qa),jr=xd(Cd,Qa),qi=Md(jr),kd=t=>typeof t.start=="function",Ui=new WeakMap,Pd=t=>({...t,connect:({call:e})=>async()=>{const{port1:n,port2:s}=new MessageChannel,r=await e("connect",{port:n},[n]);return Ui.set(s,r),s},disconnect:({call:e})=>async n=>{const s=Ui.get(n);if(s===void 0)throw new Error("The given port is not connected.");await e("disconnect",{portId:s})},isSupported:({call:e})=>()=>e("isSupported")}),gr=new WeakMap,Id=t=>{if(gr.has(t))return gr.get(t);const e=new Map;return gr.set(t,e),e},Ka=t=>{const e=Pd(t);return n=>{const s=Id(n);n.addEventListener("message",({data:c})=>{const{id:a}=c;if(a!==null&&s.has(a)){const{reject:u,resolve:d}=s.get(a);s.delete(a),c.error===void 0?d(c.result):u(new Error(c.error.message))}}),kd(n)&&n.start();const r=(c,a=null,u=[])=>new Promise((d,o)=>{const p=jr(s);s.set(p,{reject:o,resolve:d}),a===null?n.postMessage({id:p,method:c},u):n.postMessage({id:p,method:c,params:a},u)}),i=(c,a,u=[])=>{n.postMessage({id:null,method:c,params:a},u)};let l={};for(const[c,a]of Object.entries(e))l={...l,[c]:a({call:r,notify:i})};return{...l}}},vr=new Set,Zi=new Set,qn=new WeakMap,Rd=Ka({deregister:({call:t})=>async e=>{const n=qn.get(e);if(n===void 0)throw new Error("There is no encoder registered with the given port.");const s=await t("deregister",{encoderId:n});return vr.delete(n),qn.delete(e),s},encode:({call:t})=>async(e,n)=>{const s=await t("encode",{encoderInstanceId:e,timeslice:n});return Zi.delete(e),s},instantiate:({call:t})=>async(e,n)=>{const s=qi(Zi),r=await t("instantiate",{encoderInstanceId:s,mimeType:e,sampleRate:n});return{encoderInstanceId:s,port:r}},register:({call:t})=>async e=>{if(qn.has(e))throw new Error("");const n=qi(vr);qn.set(e,n);try{return await t("register",{encoderId:n,port:e},[e])}catch(s){throw vr.delete(n),qn.delete(e),s}}}),Bd=t=>{const e=new Worker(t);return Rd(e)},Ld=`(()=>{var e={455:function(e,t){!function(e){"use strict";var t=function(e){return function(t){var r=e(t);return t.add(r),r}},r=function(e){return function(t,r){return e.set(t,r),r}},n=void 0===Number.MAX_SAFE_INTEGER?9007199254740991:Number.MAX_SAFE_INTEGER,o=536870912,s=2*o,a=function(e,t){return function(r){var a=t.get(r),c=void 0===a?r.size:a<s?a+1:0;if(!r.has(c))return e(r,c);if(r.size<o){for(;r.has(c);)c=Math.floor(Math.random()*s);return e(r,c)}if(r.size>n)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;r.has(c);)c=Math.floor(Math.random()*n);return e(r,c)}},c=new WeakMap,i=r(c),l=a(i,c),d=t(l);e.addUniqueNumber=d,e.generateUniqueNumber=l}(t)}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n].call(s.exports,s,s.exports,r),s.exports}(()=>{"use strict";var e=r(455);const t=new WeakMap,n=new WeakMap,o=(r=>{const o=(s=r,{...s,connect:e=>{let{call:r}=e;return async()=>{const{port1:e,port2:n}=new MessageChannel,o=await r("connect",{port:e},[e]);return t.set(n,o),n}},disconnect:e=>{let{call:r}=e;return async e=>{const n=t.get(e);if(void 0===n)throw new Error("The given port is not connected.");await r("disconnect",{portId:n})}},isSupported:e=>{let{call:t}=e;return()=>t("isSupported")}});var s;return t=>{const r=(e=>{if(n.has(e))return n.get(e);const t=new Map;return n.set(e,t),t})(t);t.addEventListener("message",(e=>{let{data:t}=e;const{id:n}=t;if(null!==n&&r.has(n)){const{reject:e,resolve:o}=r.get(n);r.delete(n),void 0===t.error?o(t.result):e(new Error(t.error.message))}})),(e=>"function"==typeof e.start)(t)&&t.start();const s=function(n){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return new Promise(((a,c)=>{const i=(0,e.generateUniqueNumber)(r);r.set(i,{reject:c,resolve:a}),null===o?t.postMessage({id:i,method:n},s):t.postMessage({id:i,method:n,params:o},s)}))},a=function(e,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];t.postMessage({id:null,method:e,params:r},n)};let c={};for(const[e,t]of Object.entries(o))c={...c,[e]:t({call:s,notify:a})};return{...c}}})({characterize:e=>{let{call:t}=e;return()=>t("characterize")},encode:e=>{let{call:t}=e;return(e,r)=>t("encode",{recordingId:e,timeslice:r})},record:e=>{let{call:t}=e;return async(e,r,n)=>{await t("record",{recordingId:e,sampleRate:r,typedArrays:n},n.map((e=>{let{buffer:t}=e;return t})))}}}),s=-32603,a=-32602,c=-32601,i=(e,t)=>Object.assign(new Error(e),{status:t}),l=e=>i('The handler of the method called "'.concat(e,'" returned an unexpected result.'),s),d=(e,t)=>async r=>{let{data:{id:n,method:o,params:a}}=r;const d=t[o];try{if(void 0===d)throw(e=>i('The requested method called "'.concat(e,'" is not supported.'),c))(o);const t=void 0===a?d():d(a);if(void 0===t)throw(e=>i('The handler of the method called "'.concat(e,'" returned no required result.'),s))(o);const r=t instanceof Promise?await t:t;if(null===n){if(void 0!==r.result)throw l(o)}else{if(void 0===r.result)throw l(o);const{result:t,transferables:s=[]}=r;e.postMessage({id:n,result:t},s)}}catch(t){const{message:r,status:o=-32603}=t;e.postMessage({error:{code:o,message:r},id:n})}},u=new Map,h=(t,r,n)=>({...r,connect:n=>{let{port:o}=n;o.start();const s=t(o,r),a=(0,e.generateUniqueNumber)(u);return u.set(a,(()=>{s(),o.close(),u.delete(a)})),{result:a}},disconnect:e=>{let{portId:t}=e;const r=u.get(t);if(void 0===r)throw(e=>i('The specified parameter called "portId" with the given value "'.concat(e,'" does not identify a port connected to this worker.'),a))(t);return r(),{result:null}},isSupported:async()=>{if(await new Promise((e=>{const t=new ArrayBuffer(0),{port1:r,port2:n}=new MessageChannel;r.onmessage=t=>{let{data:r}=t;return e(null!==r)},n.postMessage(t,[t])}))){const e=n();return{result:e instanceof Promise?await e:e}}return{result:!1}}}),f=function(e,t){const r=h(f,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>!0),n=d(e,r);return e.addEventListener("message",n),()=>e.removeEventListener("message",n)},w=e=>{e.onmessage=null,e.close()},p=new Map,g=new Map,m=((e,t)=>r=>{const n=t.get(r);if(void 0===n)throw new Error("There was no encoder stored with the given id.");e.delete(n),t.delete(r)})(p,g),v=new Map,y=(e=>t=>{const r=e.get(t);if(void 0===r)throw new Error("There was no instance of an encoder stored with the given id.");return r})(v),M=((e,t)=>r=>{const n=t(r);return e.delete(r),n})(v,y),E=((e,t)=>r=>{const[n,o,s,a]=t(r);return s?new Promise((t=>{o.onmessage=s=>{let{data:c}=s;0===c.length?(e(o),t(n.encode(r,null))):n.record(r,a,c)}})):n.encode(r,null)})(w,M),b=(e=>t=>{for(const[r,n]of Array.from(e.values()))if(r.test(t))return n;throw new Error("There is no encoder registered which could handle the given mimeType.")})(p),T=((e,t,r)=>(n,o,s)=>{if(t.has(n))throw new Error('There is already an encoder instance registered with an id called "'.concat(n,'".'));const a=r(o),{port1:c,port2:i}=new MessageChannel,l=[a,c,!0,s];return t.set(n,l),c.onmessage=t=>{let{data:r}=t;0===r.length?(e(c),l[2]=!1):a.record(n,s,r.map((e=>"number"==typeof e?new Float32Array(e):e)))},i})(w,v,b),I=((e,t,r)=>async(n,o)=>{const s=r(o),a=await s.characterize(),c=a.toString();if(e.has(c))throw new Error("There is already an encoder stored which handles exactly the same mime types.");if(t.has(n))throw new Error('There is already an encoder registered with an id called "'.concat(n,'".'));return e.set(c,[a,s]),t.set(n,c),a})(p,g,o),A=(e=>(t,r)=>{const[n]=e(t);return n.encode(t,r)})(y);f(self,{deregister:async e=>{let{encoderId:t}=e;return m(t),{result:null}},encode:async e=>{let{encoderInstanceId:t,timeslice:r}=e;const n=null===r?await E(t):await A(t,r);return{result:n,transferables:n}},instantiate:e=>{let{encoderInstanceId:t,mimeType:r,sampleRate:n}=e;const o=T(t,r,n);return{result:o,transferables:[o]}},register:async e=>{let{encoderId:t,port:r}=e;return{result:await I(t,r)}}})})()})();`,Od=new Blob([Ld],{type:"application/javascript; charset=utf-8"}),Ja=URL.createObjectURL(Od),qr=Bd(Ja),Ts=qr.encode,eo=qr.instantiate,Dd=qr.register;URL.revokeObjectURL(Ja);const Nd=t=>(e,n)=>{if(t===null)throw new Error("A native BlobEvent could not be created.");return new t(e,n)},zd=(t,e)=>(n,s,r)=>{const i=[];let l=s,c=0;for(;c<n.byteLength;)if(l===null){const a=e(n,c);if(a===null)break;const{length:u,type:d}=a;l=d,c+=u}else{const a=t(n,c,l,r);if(a===null)break;const{content:u,length:d}=a;l=null,c+=d,u!==null&&i.push(u)}return{contents:i,currentElementType:l,offset:c}},$d=(t,e)=>class{constructor(s=null){this._listeners=new WeakMap,this._nativeEventTarget=s===null?t():s}addEventListener(s,r,i){if(r!==null){let l=this._listeners.get(r);l===void 0&&(l=e(this,r),typeof r=="function"&&this._listeners.set(r,l)),this._nativeEventTarget.addEventListener(s,l,i)}}dispatchEvent(s){return this._nativeEventTarget.dispatchEvent(s)}removeEventListener(s,r,i){const l=r===null?void 0:this._listeners.get(r);this._nativeEventTarget.removeEventListener(s,l===void 0?null:l,i)}},Vd=t=>()=>{if(t===null)throw new Error("A native EventTarget could not be created.");return t.document.createElement("p")},Hd=(t="")=>{try{return new DOMException(t,"InvalidModificationError")}catch(e){return e.code=13,e.message=t,e.name="InvalidModificationError",e}},Xd=()=>{try{return new DOMException("","InvalidStateError")}catch(t){return t.code=11,t.name="InvalidStateError",t}},Fd=(t,e,n,s,r,i,l)=>class extends i{constructor(a,u={}){const{mimeType:d}=u;if(l!==null&&(d===void 0||l.isTypeSupported!==void 0&&l.isTypeSupported(d))){const o=t(l,a,u);super(o),this._internalMediaRecorder=o}else if(d!==void 0&&r.some(o=>o.test(d)))super(),l!==null&&l.isTypeSupported!==void 0&&l.isTypeSupported("audio/webm;codecs=pcm")?this._internalMediaRecorder=s(this,l,a,d):this._internalMediaRecorder=n(this,a,d);else throw l!==null&&t(l,a,u),e();this._ondataavailable=null,this._onerror=null,this._onpause=null,this._onresume=null,this._onstart=null,this._onstop=null}get mimeType(){return this._internalMediaRecorder.mimeType}get ondataavailable(){return this._ondataavailable===null?this._ondataavailable:this._ondataavailable[0]}set ondataavailable(a){if(this._ondataavailable!==null&&this.removeEventListener("dataavailable",this._ondataavailable[1]),typeof a=="function"){const u=a.bind(this);this.addEventListener("dataavailable",u),this._ondataavailable=[a,u]}else this._ondataavailable=null}get onerror(){return this._onerror===null?this._onerror:this._onerror[0]}set onerror(a){if(this._onerror!==null&&this.removeEventListener("error",this._onerror[1]),typeof a=="function"){const u=a.bind(this);this.addEventListener("error",u),this._onerror=[a,u]}else this._onerror=null}get onpause(){return this._onpause===null?this._onpause:this._onpause[0]}set onpause(a){if(this._onpause!==null&&this.removeEventListener("pause",this._onpause[1]),typeof a=="function"){const u=a.bind(this);this.addEventListener("pause",u),this._onpause=[a,u]}else this._onpause=null}get onresume(){return this._onresume===null?this._onresume:this._onresume[0]}set onresume(a){if(this._onresume!==null&&this.removeEventListener("resume",this._onresume[1]),typeof a=="function"){const u=a.bind(this);this.addEventListener("resume",u),this._onresume=[a,u]}else this._onresume=null}get onstart(){return this._onstart===null?this._onstart:this._onstart[0]}set onstart(a){if(this._onstart!==null&&this.removeEventListener("start",this._onstart[1]),typeof a=="function"){const u=a.bind(this);this.addEventListener("start",u),this._onstart=[a,u]}else this._onstart=null}get onstop(){return this._onstop===null?this._onstop:this._onstop[0]}set onstop(a){if(this._onstop!==null&&this.removeEventListener("stop",this._onstop[1]),typeof a=="function"){const u=a.bind(this);this.addEventListener("stop",u),this._onstop=[a,u]}else this._onstop=null}get state(){return this._internalMediaRecorder.state}pause(){return this._internalMediaRecorder.pause()}resume(){return this._internalMediaRecorder.resume()}start(a){return this._internalMediaRecorder.start(a)}stop(){return this._internalMediaRecorder.stop()}static isTypeSupported(a){return l!==null&&l.isTypeSupported!==void 0&&l.isTypeSupported(a)||r.some(u=>u.test(a))}},Yd=t=>t!==null&&t.BlobEvent!==void 0?t.BlobEvent:null,Wd=t=>t===null||t.MediaRecorder===void 0?null:t.MediaRecorder,Gd=t=>(e,n,s)=>{const r=new Map,i=new WeakMap,l=new WeakMap,c=[],a=new e(n,s),u=new WeakMap;return a.addEventListener("stop",({isTrusted:d})=>{d&&setTimeout(()=>c.shift())}),a.addEventListener=(d=>(o,p,_)=>{let w=p;if(typeof p=="function")if(o==="dataavailable"){const b=[];w=g=>{const[[E,T]=[!1,!1]]=c;E&&!T?b.push(g):p.call(a,g)},r.set(p,b),i.set(p,w)}else o==="error"?(w=b=>{b instanceof ErrorEvent?p.call(a,b):p.call(a,new ErrorEvent("error",{error:b.error}))},l.set(p,w)):o==="stop"&&(w=b=>{for(const[g,E]of r.entries())if(E.length>0){const[T]=E;E.length>1&&Object.defineProperty(T,"data",{value:new Blob(E.map(({data:m})=>m),{type:T.data.type})}),E.length=0,g.call(a,T)}p.call(a,b)},u.set(p,w));return d.call(a,o,w,_)})(a.addEventListener),a.removeEventListener=(d=>(o,p,_)=>{let w=p;if(typeof p=="function"){if(o==="dataavailable"){r.delete(p);const b=i.get(p);b!==void 0&&(w=b)}else if(o==="error"){const b=l.get(p);b!==void 0&&(w=b)}else if(o==="stop"){const b=u.get(p);b!==void 0&&(w=b)}}return d.call(a,o,w,_)})(a.removeEventListener),a.start=(d=>o=>{if(s.mimeType!==void 0&&s.mimeType.startsWith("audio/")&&n.getVideoTracks().length>0)throw t();return a.state==="inactive"&&c.push([o!==void 0,!0]),o===void 0?d.call(a):d.call(a,o)})(a.start),a.stop=(d=>()=>{a.state!=="inactive"&&(c[0][1]=!1),d.call(a)})(a.stop),a},Cr=()=>{try{return new DOMException("","NotSupportedError")}catch(t){return t.code=9,t.name="NotSupportedError",t}},jd=t=>(e,n,s,r=2)=>{const i=t(e,n);if(i===null)return i;const{length:l,value:c}=i;if(s==="master")return{content:null,length:l};if(n+l+c>e.byteLength)return null;if(s==="binary"){const a=(c/Float32Array.BYTES_PER_ELEMENT-1)/r,u=Array.from({length:r},()=>new Float32Array(a));for(let d=0;d<a;d+=1){const o=d*r+1;for(let p=0;p<r;p+=1)u[p][d]=e.getFloat32(n+l+(o+p)*Float32Array.BYTES_PER_ELEMENT,!0)}return{content:u,length:l+c}}return{content:null,length:l+c}},qd=t=>(e,n)=>{const s=t(e,n);if(s===null)return s;const{length:r,value:i}=s;return i===35?{length:r,type:"binary"}:i===46||i===97||i===88713574||i===106212971||i===139690087||i===172351395||i===256095861?{length:r,type:"master"}:{length:r,type:"unknown"}},Ud=t=>(e,n)=>{const s=t(e,n);if(s===null)return s;const r=n+Math.floor((s-1)/8);if(r+s>e.byteLength)return null;let l=e.getUint8(r)&(1<<8-s%8)-1;for(let c=1;c<s;c+=1)l=(l<<8)+e.getUint8(r+c);return{length:s,value:l}},Qi=Symbol.observable||"@@observable";function Zd(t){return Symbol.observable||(typeof t=="function"&&t.prototype&&t.prototype[Symbol.observable]?(t.prototype[Qi]=t.prototype[Symbol.observable],delete t.prototype[Symbol.observable]):(t[Qi]=t[Symbol.observable],delete t[Symbol.observable])),t}const _s=()=>{},Ki=t=>{throw t};function Qd(t){return t?t.next&&t.error&&t.complete?t:{complete:(t.complete??_s).bind(t),error:(t.error??Ki).bind(t),next:(t.next??_s).bind(t)}:{complete:_s,error:Ki,next:_s}}const Kd=t=>(e,n,s)=>t(r=>{const i=l=>r.next(l);return e.addEventListener(n,i,s),()=>e.removeEventListener(n,i,s)}),Jd=(t,e)=>{const n=()=>{},s=r=>typeof r[0]=="function";return r=>{const i=(...l)=>{const c=r(s(l)?e({next:l[0]}):e(...l));return c!==void 0?c:n};return i[Symbol.observable]=()=>({subscribe:(...l)=>({unsubscribe:i(...l)})}),t(i)}},ef=Jd(Zd,Qd),to=Kd(ef),tf=(t,e,n)=>async s=>{const r=new t([n],{type:"application/javascript; charset=utf-8"}),i=e.createObjectURL(r);try{await s(i)}finally{e.revokeObjectURL(i)}},nf=t=>({data:e})=>{const{id:n}=e;if(n!==null){const s=t.get(n);if(s!==void 0){const{reject:r,resolve:i}=s;t.delete(n),e.error===void 0?i(e.result):r(new Error(e.error.message))}}},sf=t=>(e,n)=>(s,r=[])=>new Promise((i,l)=>{const c=t(e);e.set(c,{reject:l,resolve:i}),n.postMessage({id:c,...s},r)}),rf=(t,e,n,s)=>(r,i,l={})=>{const c=new r(i,"recorder-audio-worklet-processor",{...l,channelCountMode:"explicit",numberOfInputs:1,numberOfOutputs:0}),a=new Map,u=e(a,c.port),d=n(c.port,"message")(t(a));c.port.start();let o="inactive";return Object.defineProperties(c,{pause:{get(){return async()=>(s(["recording"],o),o="paused",u({method:"pause"}))}},port:{get(){throw new Error("The port of a RecorderAudioWorkletNode can't be accessed.")}},record:{get(){return async p=>(s(["inactive"],o),o="recording",u({method:"record",params:{encoderPort:p}},[p]))}},resume:{get(){return async()=>(s(["paused"],o),o="recording",u({method:"resume"}))}},stop:{get(){return async()=>{s(["paused","recording"],o),o="stopped";try{await u({method:"stop"})}finally{d()}}}}}),c},af=(t,e)=>{if(!t.includes(e))throw new Error(`Expected the state to be ${t.map(n=>`"${n}"`).join(" or ")} but it was "${e}".`)},of='(()=>{"use strict";class e extends AudioWorkletProcessor{constructor(){super(),this._encoderPort=null,this._numberOfChannels=0,this._state="inactive",this.port.onmessage=e=>{let{data:t}=e;"pause"===t.method?"active"===this._state||"recording"===this._state?(this._state="paused",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"record"===t.method?"inactive"===this._state?(this._encoderPort=t.params.encoderPort,this._state="active",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"resume"===t.method?"paused"===this._state?(this._state="active",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"stop"===t.method?"active"!==this._state&&"paused"!==this._state&&"recording"!==this._state||null===this._encoderPort?this._sendUnexpectedStateError(t.id):(this._stop(this._encoderPort),this._sendAcknowledgement(t.id)):"number"==typeof t.id&&this.port.postMessage({error:{code:-32601,message:"The requested method is not supported."},id:t.id})}}process(e){let[t]=e;if("inactive"===this._state||"paused"===this._state)return!0;if("active"===this._state){if(void 0===t)throw new Error("No channelData was received for the first input.");if(0===t.length)return!0;this._state="recording"}if("recording"===this._state&&null!==this._encoderPort){if(void 0===t)throw new Error("No channelData was received for the first input.");return 0===t.length?this._encoderPort.postMessage(Array.from({length:this._numberOfChannels},(()=>128))):(this._encoderPort.postMessage(t,t.map((e=>{let{buffer:t}=e;return t}))),this._numberOfChannels=t.length),!0}return!1}_sendAcknowledgement(e){this.port.postMessage({id:e,result:null})}_sendUnexpectedStateError(e){this.port.postMessage({error:{code:-32603,message:"The internal state does not allow to process the given message."},id:e})}_stop(e){e.postMessage([]),e.close(),this._encoderPort=null,this._state="stopped"}}e.parameterDescriptors=[],registerProcessor("recorder-audio-worklet-processor",e)})();',lf=tf(Blob,URL,of),cf=rf(nf,sf(jr),to,af),Ji=(t,e,n)=>({endTime:e,insertTime:n,type:"exponentialRampToValue",value:t}),ea=(t,e,n)=>({endTime:e,insertTime:n,type:"linearRampToValue",value:t}),kr=(t,e)=>({startTime:e,type:"setValue",value:t}),no=(t,e,n)=>({duration:n,startTime:e,type:"setValueCurve",values:t}),so=(t,e,{startTime:n,target:s,timeConstant:r})=>s+(e-s)*Math.exp((n-t)/r),Dn=t=>t.type==="exponentialRampToValue",Ms=t=>t.type==="linearRampToValue",an=t=>Dn(t)||Ms(t),Ur=t=>t.type==="setValue",Qt=t=>t.type==="setValueCurve",As=(t,e,n,s)=>{const r=t[e];return r===void 0?s:an(r)||Ur(r)?r.value:Qt(r)?r.values[r.values.length-1]:so(n,As(t,e-1,r.startTime,s),r)},ta=(t,e,n,s,r)=>n===void 0?[s.insertTime,r]:an(n)?[n.endTime,n.value]:Ur(n)?[n.startTime,n.value]:Qt(n)?[n.startTime+n.duration,n.values[n.values.length-1]]:[n.startTime,As(t,e-1,n.startTime,r)],Pr=t=>t.type==="cancelAndHold",Ir=t=>t.type==="cancelScheduledValues",rn=t=>Pr(t)||Ir(t)?t.cancelTime:Dn(t)||Ms(t)?t.endTime:t.startTime,na=(t,e,n,{endTime:s,value:r})=>n===r?r:0<n&&0<r||n<0&&r<0?n*(r/n)**((t-e)/(s-e)):0,sa=(t,e,n,{endTime:s,value:r})=>n+(t-e)/(s-e)*(r-n),uf=(t,e)=>{const n=Math.floor(e),s=Math.ceil(e);return n===s?t[n]:(1-(e-n))*t[n]+(1-(s-e))*t[s]},df=(t,{duration:e,startTime:n,values:s})=>{const r=(t-n)/e*(s.length-1);return uf(s,r)},gs=t=>t.type==="setTarget";class ff{constructor(e){this._automationEvents=[],this._currenTime=0,this._defaultValue=e}[Symbol.iterator](){return this._automationEvents[Symbol.iterator]()}add(e){const n=rn(e);if(Pr(e)||Ir(e)){const s=this._automationEvents.findIndex(i=>Ir(e)&&Qt(i)?i.startTime+i.duration>=n:rn(i)>=n),r=this._automationEvents[s];if(s!==-1&&(this._automationEvents=this._automationEvents.slice(0,s)),Pr(e)){const i=this._automationEvents[this._automationEvents.length-1];if(r!==void 0&&an(r)){if(i!==void 0&&gs(i))throw new Error("The internal list is malformed.");const l=i===void 0?r.insertTime:Qt(i)?i.startTime+i.duration:rn(i),c=i===void 0?this._defaultValue:Qt(i)?i.values[i.values.length-1]:i.value,a=Dn(r)?na(n,l,c,r):sa(n,l,c,r),u=Dn(r)?Ji(a,n,this._currenTime):ea(a,n,this._currenTime);this._automationEvents.push(u)}if(i!==void 0&&gs(i)&&this._automationEvents.push(kr(this.getValue(n),n)),i!==void 0&&Qt(i)&&i.startTime+i.duration>n){const l=n-i.startTime,c=(i.values.length-1)/i.duration,a=Math.max(2,1+Math.ceil(l*c)),u=l/(a-1)*c,d=i.values.slice(0,a);if(u<1)for(let o=1;o<a;o+=1){const p=u*o%1;d[o]=i.values[o-1]*(1-p)+i.values[o]*p}this._automationEvents[this._automationEvents.length-1]=no(d,i.startTime,l)}}}else{const s=this._automationEvents.findIndex(l=>rn(l)>n),r=s===-1?this._automationEvents[this._automationEvents.length-1]:this._automationEvents[s-1];if(r!==void 0&&Qt(r)&&rn(r)+r.duration>n)return!1;const i=Dn(e)?Ji(e.value,e.endTime,this._currenTime):Ms(e)?ea(e.value,n,this._currenTime):e;if(s===-1)this._automationEvents.push(i);else{if(Qt(e)&&n+e.duration>rn(this._automationEvents[s]))return!1;this._automationEvents.splice(s,0,i)}}return!0}flush(e){const n=this._automationEvents.findIndex(s=>rn(s)>e);if(n>1){const s=this._automationEvents.slice(n-1),r=s[0];gs(r)&&s.unshift(kr(As(this._automationEvents,n-2,r.startTime,this._defaultValue),r.startTime)),this._automationEvents=s}}getValue(e){if(this._automationEvents.length===0)return this._defaultValue;const n=this._automationEvents.findIndex(l=>rn(l)>e),s=this._automationEvents[n],r=(n===-1?this._automationEvents.length:n)-1,i=this._automationEvents[r];if(i!==void 0&&gs(i)&&(s===void 0||!an(s)||s.insertTime>e))return so(e,As(this._automationEvents,r-1,i.startTime,this._defaultValue),i);if(i!==void 0&&Ur(i)&&(s===void 0||!an(s)))return i.value;if(i!==void 0&&Qt(i)&&(s===void 0||!an(s)||i.startTime+i.duration>e))return e<i.startTime+i.duration?df(e,i):i.values[i.values.length-1];if(i!==void 0&&an(i)&&(s===void 0||!an(s)))return i.value;if(s!==void 0&&Dn(s)){const[l,c]=ta(this._automationEvents,r,i,s,this._defaultValue);return na(e,l,c,s)}if(s!==void 0&&Ms(s)){const[l,c]=ta(this._automationEvents,r,i,s,this._defaultValue);return sa(e,l,c,s)}return this._defaultValue}}const pf=t=>({cancelTime:t,type:"cancelAndHold"}),hf=t=>({cancelTime:t,type:"cancelScheduledValues"}),mf=(t,e)=>({endTime:e,type:"exponentialRampToValue",value:t}),_f=(t,e)=>({endTime:e,type:"linearRampToValue",value:t}),gf=(t,e,n)=>({startTime:e,target:t,timeConstant:n,type:"setTarget"}),vf=()=>new DOMException("","AbortError"),bf=t=>(e,n,[s,r,i],l)=>{t(e[r],[n,s,i],c=>c[0]===n&&c[1]===s,l)},wf=t=>(e,n,s)=>{const r=[];for(let i=0;i<s.numberOfInputs;i+=1)r.push(new Set);t.set(e,{activeInputs:r,outputs:new Set,passiveInputs:new WeakMap,renderer:n})},yf=t=>(e,n)=>{t.set(e,{activeInputs:new Set,passiveInputs:new WeakMap,renderer:n})},$n=new WeakSet,ro=new WeakMap,io=new WeakMap,ao=new WeakMap,oo=new WeakMap,lo=new WeakMap,co=new WeakMap,Rr=new WeakMap,Br=new WeakMap,Lr=new WeakMap,uo={construct(){return uo}},Sf=t=>{try{const e=new Proxy(t,uo);new e}catch{return!1}return!0},ra=/^import(?:(?:[\s]+[\w]+|(?:[\s]+[\w]+[\s]*,)?[\s]*\{[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?(?:[\s]*,[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?)*[\s]*}|(?:[\s]+[\w]+[\s]*,)?[\s]*\*[\s]+as[\s]+[\w]+)[\s]+from)?(?:[\s]*)("([^"\\]|\\.)+"|'([^'\\]|\\.)+')(?:[\s]*);?/,ia=(t,e)=>{const n=[];let s=t.replace(/^[\s]+/,""),r=s.match(ra);for(;r!==null;){const i=r[1].slice(1,-1),l=r[0].replace(/([\s]+)?;?$/,"").replace(i,new URL(i,e).toString());n.push(l),s=s.slice(r[0].length).replace(/^[\s]+/,""),r=s.match(ra)}return[n.join(";"),s]},aa=t=>{if(t!==void 0&&!Array.isArray(t))throw new TypeError("The parameterDescriptors property of given value for processorCtor is not an array.")},oa=t=>{if(!Sf(t))throw new TypeError("The given value for processorCtor should be a constructor.");if(t.prototype===null||typeof t.prototype!="object")throw new TypeError("The given value for processorCtor should have a prototype.")},Ef=(t,e,n,s,r,i,l,c,a,u,d,o,p)=>{let _=0;return(w,b,g={credentials:"omit"})=>{const E=d.get(w);if(E!==void 0&&E.has(b))return Promise.resolve();const T=u.get(w);if(T!==void 0){const f=T.get(b);if(f!==void 0)return f}const m=i(w),x=m.audioWorklet===void 0?r(b).then(([f,A])=>{const[D,V]=ia(f,A),O=`${D};((a,b)=>{(a[b]=a[b]||[]).push((AudioWorkletProcessor,global,registerProcessor,sampleRate,self,window)=>{${V}
})})(window,'_AWGS')`;return n(O)}).then(()=>{const f=p._AWGS.pop();if(f===void 0)throw new SyntaxError;s(m.currentTime,m.sampleRate,()=>f(class{},void 0,(A,D)=>{if(A.trim()==="")throw e();const V=Br.get(m);if(V!==void 0){if(V.has(A))throw e();oa(D),aa(D.parameterDescriptors),V.set(A,D)}else oa(D),aa(D.parameterDescriptors),Br.set(m,new Map([[A,D]]))},m.sampleRate,void 0,void 0))}):Promise.all([r(b),Promise.resolve(t(o,o))]).then(([[f,A],D])=>{const V=_+1;_=V;const[O,q]=ia(f,A),h=`${O};((AudioWorkletProcessor,registerProcessor)=>{${q}
})(${D?"AudioWorkletProcessor":"class extends AudioWorkletProcessor {__b=new WeakSet();constructor(){super();(p=>p.postMessage=(q=>(m,t)=>q.call(p,m,t?t.filter(u=>!this.__b.has(u)):t))(p.postMessage))(this.port)}}"},(n,p)=>registerProcessor(n,class extends p{${D?"":"__c = (a) => a.forEach(e=>this.__b.add(e.buffer));"}process(i,o,p){${D?"":"i.forEach(this.__c);o.forEach(this.__c);this.__c(Object.values(p));"}return super.process(i.map(j=>j.some(k=>k.length===0)?[]:j),o,p)}}));registerProcessor('__sac${V}',class extends AudioWorkletProcessor{process(){return !1}})`,k=new Blob([h],{type:"application/javascript; charset=utf-8"}),H=URL.createObjectURL(k);return m.audioWorklet.addModule(H,g).then(()=>{if(c(m))return m;const R=l(m);return R.audioWorklet.addModule(H,g).then(()=>R)}).then(R=>{if(a===null)throw new SyntaxError;try{new a(R,`__sac${V}`)}catch{throw new SyntaxError}}).finally(()=>URL.revokeObjectURL(H))});return T===void 0?u.set(w,new Map([[b,x]])):T.set(b,x),x.then(()=>{const f=d.get(w);f===void 0?d.set(w,new Set([b])):f.add(b)}).finally(()=>{const f=u.get(w);f!==void 0&&f.delete(b)}),x}},Gt=(t,e)=>{const n=t.get(e);if(n===void 0)throw new Error("A value with the given key could not be found.");return n},Vs=(t,e)=>{const n=Array.from(t).filter(e);if(n.length>1)throw Error("More than one element was found.");if(n.length===0)throw Error("No element was found.");const[s]=n;return t.delete(s),s},fo=(t,e,n,s)=>{const r=Gt(t,e),i=Vs(r,l=>l[0]===n&&l[1]===s);return r.size===0&&t.delete(e),i},as=t=>Gt(co,t),xs=t=>{if($n.has(t))throw new Error("The AudioNode is already stored.");$n.add(t),as(t).forEach(e=>e(!0))},po=t=>"port"in t,Zr=t=>{if(!$n.has(t))throw new Error("The AudioNode is not stored.");$n.delete(t),as(t).forEach(e=>e(!1))},Or=(t,e)=>{!po(t)&&e.every(n=>n.size===0)&&Zr(t)},Tf=(t,e,n,s,r,i,l,c,a,u,d,o,p)=>{const _=new WeakMap;return(w,b,g,E,T)=>{const{activeInputs:m,passiveInputs:x}=i(b),{outputs:f}=i(w),A=c(w),D=V=>{const O=a(b),q=a(w);if(V){const N=fo(x,w,g,E);t(m,w,N,!1),!T&&!o(w)&&n(q,O,g,E),p(b)&&xs(b)}else{const N=s(m,w,g,E);e(x,E,N,!1),!T&&!o(w)&&r(q,O,g,E);const L=l(b);if(L===0)d(b)&&Or(b,m);else{const I=_.get(b);I!==void 0&&clearTimeout(I),_.set(b,setTimeout(()=>{d(b)&&Or(b,m)},L*1e3))}}};return u(f,[b,g,E],V=>V[0]===b&&V[1]===g&&V[2]===E,!0)?(A.add(D),d(w)?t(m,w,[g,E,D],!0):e(x,E,[w,g,D],!0),!0):!1}},Mf=t=>(e,n,[s,r,i],l)=>{const c=e.get(s);c===void 0?e.set(s,new Set([[r,n,i]])):t(c,[r,n,i],a=>a[0]===r&&a[1]===n,l)},Af=t=>(e,n)=>{const s=t(e,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});n.connect(s).connect(e.destination);const r=()=>{n.removeEventListener("ended",r),n.disconnect(s),s.disconnect()};n.addEventListener("ended",r)},xf=t=>(e,n)=>{t(e).add(n)},ho=(t,e)=>t.context===e,la=t=>{try{t.copyToChannel(new Float32Array(1),0,-1)}catch{return!1}return!0},kn=()=>new DOMException("","IndexSizeError"),Cf=t=>{t.getChannelData=(e=>n=>{try{return e.call(t,n)}catch(s){throw s.code===12?kn():s}})(t.getChannelData)},kf={numberOfChannels:1},Pf=(t,e,n,s,r,i,l,c)=>{let a=null;return class mo{constructor(d){if(r===null)throw new Error("Missing the native OfflineAudioContext constructor.");const{length:o,numberOfChannels:p,sampleRate:_}={...kf,...d};a===null&&(a=new r(1,1,44100));const w=s!==null&&e(i,i)?new s({length:o,numberOfChannels:p,sampleRate:_}):a.createBuffer(p,o,_);if(w.numberOfChannels===0)throw n();return typeof w.copyFromChannel!="function"?(l(w),Cf(w)):e(la,()=>la(w))||c(w),t.add(w),w}static[Symbol.hasInstance](d){return d!==null&&typeof d=="object"&&Object.getPrototypeOf(d)===mo.prototype||t.has(d)}}},Hs=-34028234663852886e22,Qr=-Hs,Tn=t=>$n.has(t),If={buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1},Rf=(t,e,n,s,r,i,l,c)=>class extends t{constructor(u,d){const o=i(u),p={...If,...d},_=r(o,p),w=l(o),b=w?e():null;super(u,!1,_,b),this._audioBufferSourceNodeRenderer=b,this._isBufferNullified=!1,this._isBufferSet=p.buffer!==null,this._nativeAudioBufferSourceNode=_,this._onended=null,this._playbackRate=n(this,w,_.playbackRate,Qr,Hs)}get buffer(){return this._isBufferNullified?null:this._nativeAudioBufferSourceNode.buffer}set buffer(u){if(this._nativeAudioBufferSourceNode.buffer=u,u!==null){if(this._isBufferSet)throw s();this._isBufferSet=!0}}get loop(){return this._nativeAudioBufferSourceNode.loop}set loop(u){this._nativeAudioBufferSourceNode.loop=u}get loopEnd(){return this._nativeAudioBufferSourceNode.loopEnd}set loopEnd(u){this._nativeAudioBufferSourceNode.loopEnd=u}get loopStart(){return this._nativeAudioBufferSourceNode.loopStart}set loopStart(u){this._nativeAudioBufferSourceNode.loopStart=u}get onended(){return this._onended}set onended(u){const d=typeof u=="function"?c(this,u):null;this._nativeAudioBufferSourceNode.onended=d;const o=this._nativeAudioBufferSourceNode.onended;this._onended=o!==null&&o===d?u:o}get playbackRate(){return this._playbackRate}start(u=0,d=0,o){if(this._nativeAudioBufferSourceNode.start(u,d,o),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.start=o===void 0?[u,d]:[u,d,o]),this.context.state!=="closed"){xs(this);const p=()=>{this._nativeAudioBufferSourceNode.removeEventListener("ended",p),Tn(this)&&Zr(this)};this._nativeAudioBufferSourceNode.addEventListener("ended",p)}}stop(u=0){this._nativeAudioBufferSourceNode.stop(u),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.stop=u)}},Bf=(t,e,n,s,r)=>()=>{const i=new WeakMap;let l=null,c=null;const a=async(u,d)=>{let o=n(u);const p=ho(o,d);if(!p){const _={buffer:o.buffer,channelCount:o.channelCount,channelCountMode:o.channelCountMode,channelInterpretation:o.channelInterpretation,loop:o.loop,loopEnd:o.loopEnd,loopStart:o.loopStart,playbackRate:o.playbackRate.value};o=e(d,_),l!==null&&o.start(...l),c!==null&&o.stop(c)}return i.set(d,o),p?await t(d,u.playbackRate,o.playbackRate):await s(d,u.playbackRate,o.playbackRate),await r(u,d,o),o};return{set start(u){l=u},set stop(u){c=u},render(u,d){const o=i.get(d);return o!==void 0?Promise.resolve(o):a(u,d)}}},Lf=t=>"playbackRate"in t,Of=t=>"frequency"in t&&"gain"in t,Df=t=>"offset"in t,Nf=t=>!("frequency"in t)&&"gain"in t,zf=t=>"detune"in t&&"frequency"in t&&!("gain"in t),$f=t=>"pan"in t,wt=t=>Gt(ro,t),os=t=>Gt(ao,t),Dr=(t,e)=>{const{activeInputs:n}=wt(t);n.forEach(r=>r.forEach(([i])=>{e.includes(t)||Dr(i,[...e,t])}));const s=Lf(t)?[t.playbackRate]:po(t)?Array.from(t.parameters.values()):Of(t)?[t.Q,t.detune,t.frequency,t.gain]:Df(t)?[t.offset]:Nf(t)?[t.gain]:zf(t)?[t.detune,t.frequency]:$f(t)?[t.pan]:[];for(const r of s){const i=os(r);i!==void 0&&i.activeInputs.forEach(([l])=>Dr(l,e))}Tn(t)&&Zr(t)},Vf=t=>{Dr(t.destination,[])},Hf=t=>t===void 0||typeof t=="number"||typeof t=="string"&&(t==="balanced"||t==="interactive"||t==="playback"),Xf=(t,e,n,s,r,i,l,c)=>class extends t{constructor(u,d){const o=i(u),p=l(o),_=r(o,d,p),w=p?e(c):null;super(u,!1,_,w),this._isNodeOfNativeOfflineAudioContext=p,this._nativeAudioDestinationNode=_}get channelCount(){return this._nativeAudioDestinationNode.channelCount}set channelCount(u){if(this._isNodeOfNativeOfflineAudioContext)throw s();if(u>this._nativeAudioDestinationNode.maxChannelCount)throw n();this._nativeAudioDestinationNode.channelCount=u}get channelCountMode(){return this._nativeAudioDestinationNode.channelCountMode}set channelCountMode(u){if(this._isNodeOfNativeOfflineAudioContext)throw s();this._nativeAudioDestinationNode.channelCountMode=u}get maxChannelCount(){return this._nativeAudioDestinationNode.maxChannelCount}},Ff=t=>{const e=new WeakMap,n=async(s,r)=>{const i=r.destination;return e.set(r,i),await t(s,r,i),i};return{render(s,r){const i=e.get(r);return i!==void 0?Promise.resolve(i):n(s,r)}}},Yf=(t,e,n,s,r,i,l,c)=>(a,u)=>{const d=u.listener,o=()=>{const f=new Float32Array(1),A=e(u,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:9}),D=l(u);let V=!1,O=[0,0,-1,0,1,0],q=[0,0,0];const N=()=>{if(V)return;V=!0;const k=s(u,256,9,0);k.onaudioprocess=({inputBuffer:H})=>{const R=[i(H,f,0),i(H,f,1),i(H,f,2),i(H,f,3),i(H,f,4),i(H,f,5)];R.some((Y,X)=>Y!==O[X])&&(d.setOrientation(...R),O=R);const F=[i(H,f,6),i(H,f,7),i(H,f,8)];F.some((Y,X)=>Y!==q[X])&&(d.setPosition(...F),q=F)},A.connect(k)},L=k=>H=>{H!==O[k]&&(O[k]=H,d.setOrientation(...O))},I=k=>H=>{H!==q[k]&&(q[k]=H,d.setPosition(...q))},h=(k,H,R)=>{const F=n(u,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:H});F.connect(A,0,k),F.start(),Object.defineProperty(F.offset,"defaultValue",{get(){return H}});const Y=t({context:a},D,F.offset,Qr,Hs);return c(Y,"value",X=>()=>X.call(Y),X=>M=>{try{X.call(Y,M)}catch(y){if(y.code!==9)throw y}N(),D&&R(M)}),Y.cancelAndHoldAtTime=(X=>D?()=>{throw r()}:(...M)=>{const y=X.apply(Y,M);return N(),y})(Y.cancelAndHoldAtTime),Y.cancelScheduledValues=(X=>D?()=>{throw r()}:(...M)=>{const y=X.apply(Y,M);return N(),y})(Y.cancelScheduledValues),Y.exponentialRampToValueAtTime=(X=>D?()=>{throw r()}:(...M)=>{const y=X.apply(Y,M);return N(),y})(Y.exponentialRampToValueAtTime),Y.linearRampToValueAtTime=(X=>D?()=>{throw r()}:(...M)=>{const y=X.apply(Y,M);return N(),y})(Y.linearRampToValueAtTime),Y.setTargetAtTime=(X=>D?()=>{throw r()}:(...M)=>{const y=X.apply(Y,M);return N(),y})(Y.setTargetAtTime),Y.setValueAtTime=(X=>D?()=>{throw r()}:(...M)=>{const y=X.apply(Y,M);return N(),y})(Y.setValueAtTime),Y.setValueCurveAtTime=(X=>D?()=>{throw r()}:(...M)=>{const y=X.apply(Y,M);return N(),y})(Y.setValueCurveAtTime),Y};return{forwardX:h(0,0,L(0)),forwardY:h(1,0,L(1)),forwardZ:h(2,-1,L(2)),positionX:h(6,0,I(0)),positionY:h(7,0,I(1)),positionZ:h(8,0,I(2)),upX:h(3,0,L(3)),upY:h(4,1,L(4)),upZ:h(5,0,L(5))}},{forwardX:p,forwardY:_,forwardZ:w,positionX:b,positionY:g,positionZ:E,upX:T,upY:m,upZ:x}=d.forwardX===void 0?o():d;return{get forwardX(){return p},get forwardY(){return _},get forwardZ(){return w},get positionX(){return b},get positionY(){return g},get positionZ(){return E},get upX(){return T},get upY(){return m},get upZ(){return x}}},Cs=t=>"context"in t,ls=t=>Cs(t[0]),Pn=(t,e,n,s)=>{for(const r of t)if(n(r)){if(s)return!1;throw Error("The set contains at least one similar element.")}return t.add(e),!0},ca=(t,e,[n,s],r)=>{Pn(t,[e,n,s],i=>i[0]===e&&i[1]===n,r)},ua=(t,[e,n,s],r)=>{const i=t.get(e);i===void 0?t.set(e,new Set([[n,s]])):Pn(i,[n,s],l=>l[0]===n,r)},_o=t=>"inputs"in t,Nr=(t,e,n,s)=>{if(_o(e)){const r=e.inputs[s];return t.connect(r,n,0),[r,n,0]}return t.connect(e,n,s),[e,n,s]},go=(t,e,n)=>{for(const s of t)if(s[0]===e&&s[1]===n)return t.delete(s),s;return null},Wf=(t,e,n)=>Vs(t,s=>s[0]===e&&s[1]===n),vo=(t,e)=>{if(!as(t).delete(e))throw new Error("Missing the expected event listener.")},bo=(t,e,n)=>{const s=Gt(t,e),r=Vs(s,i=>i[0]===n);return s.size===0&&t.delete(e),r},zr=(t,e,n,s)=>{_o(e)?t.disconnect(e.inputs[s],n,0):t.disconnect(e,n,s)},Ft=t=>Gt(io,t),es=t=>Gt(oo,t),An=t=>Rr.has(t),ys=t=>!$n.has(t),da=(t,e)=>new Promise(n=>{if(e!==null)n(!0);else{const s=t.createScriptProcessor(256,1,1),r=t.createGain(),i=t.createBuffer(1,2,44100),l=i.getChannelData(0);l[0]=1,l[1]=1;const c=t.createBufferSource();c.buffer=i,c.loop=!0,c.connect(s).connect(t.destination),c.connect(r),c.disconnect(r),s.onaudioprocess=a=>{const u=a.inputBuffer.getChannelData(0);Array.prototype.some.call(u,d=>d===1)?n(!0):n(!1),c.stop(),s.onaudioprocess=null,c.disconnect(s),s.disconnect(t.destination)},c.start()}}),br=(t,e)=>{const n=new Map;for(const s of t)for(const r of s){const i=n.get(r);n.set(r,i===void 0?1:i+1)}n.forEach((s,r)=>e(r,s))},ks=t=>"context"in t,Gf=t=>{const e=new Map;t.connect=(n=>(s,r=0,i=0)=>{const l=ks(s)?n(s,r,i):n(s,r),c=e.get(s);return c===void 0?e.set(s,[{input:i,output:r}]):c.every(a=>a.input!==i||a.output!==r)&&c.push({input:i,output:r}),l})(t.connect.bind(t)),t.disconnect=(n=>(s,r,i)=>{if(n.apply(t),s===void 0)e.clear();else if(typeof s=="number")for(const[l,c]of e){const a=c.filter(u=>u.output!==s);a.length===0?e.delete(l):e.set(l,a)}else if(e.has(s))if(r===void 0)e.delete(s);else{const l=e.get(s);if(l!==void 0){const c=l.filter(a=>a.output!==r&&(a.input!==i||i===void 0));c.length===0?e.delete(s):e.set(s,c)}}for(const[l,c]of e)c.forEach(a=>{ks(l)?t.connect(l,a.output,a.input):t.connect(l,a.output)})})(t.disconnect)},jf=(t,e,n,s)=>{const{activeInputs:r,passiveInputs:i}=os(e),{outputs:l}=wt(t),c=as(t),a=u=>{const d=Ft(t),o=es(e);if(u){const p=bo(i,t,n);ca(r,t,p,!1),!s&&!An(t)&&d.connect(o,n)}else{const p=Wf(r,t,n);ua(i,p,!1),!s&&!An(t)&&d.disconnect(o,n)}};return Pn(l,[e,n],u=>u[0]===e&&u[1]===n,!0)?(c.add(a),Tn(t)?ca(r,t,[n,a],!0):ua(i,[t,n,a],!0),!0):!1},qf=(t,e,n,s)=>{const{activeInputs:r,passiveInputs:i}=wt(e),l=go(r[s],t,n);return l===null?[fo(i,t,n,s)[2],!1]:[l[2],!0]},Uf=(t,e,n)=>{const{activeInputs:s,passiveInputs:r}=os(e),i=go(s,t,n);return i===null?[bo(r,t,n)[1],!1]:[i[2],!0]},Kr=(t,e,n,s,r)=>{const[i,l]=qf(t,n,s,r);if(i!==null&&(vo(t,i),l&&!e&&!An(t)&&zr(Ft(t),Ft(n),s,r)),Tn(n)){const{activeInputs:c}=wt(n);Or(n,c)}},Jr=(t,e,n,s)=>{const[r,i]=Uf(t,n,s);r!==null&&(vo(t,r),i&&!e&&!An(t)&&Ft(t).disconnect(es(n),s))},Zf=(t,e)=>{const n=wt(t),s=[];for(const r of n.outputs)ls(r)?Kr(t,e,...r):Jr(t,e,...r),s.push(r[0]);return n.outputs.clear(),s},Qf=(t,e,n)=>{const s=wt(t),r=[];for(const i of s.outputs)i[1]===n&&(ls(i)?Kr(t,e,...i):Jr(t,e,...i),r.push(i[0]),s.outputs.delete(i));return r},Kf=(t,e,n,s,r)=>{const i=wt(t);return Array.from(i.outputs).filter(l=>l[0]===n&&(s===void 0||l[1]===s)&&(r===void 0||l[2]===r)).map(l=>(ls(l)?Kr(t,e,...l):Jr(t,e,...l),i.outputs.delete(l),l[0]))},Jf=(t,e,n,s,r,i,l,c,a,u,d,o,p,_,w,b)=>class extends u{constructor(E,T,m,x){super(m),this._context=E,this._nativeAudioNode=m;const f=d(E);o(f)&&n(da,()=>da(f,b))!==!0&&Gf(m),io.set(this,m),co.set(this,new Set),E.state!=="closed"&&T&&xs(this),t(this,x,m)}get channelCount(){return this._nativeAudioNode.channelCount}set channelCount(E){this._nativeAudioNode.channelCount=E}get channelCountMode(){return this._nativeAudioNode.channelCountMode}set channelCountMode(E){this._nativeAudioNode.channelCountMode=E}get channelInterpretation(){return this._nativeAudioNode.channelInterpretation}set channelInterpretation(E){this._nativeAudioNode.channelInterpretation=E}get context(){return this._context}get numberOfInputs(){return this._nativeAudioNode.numberOfInputs}get numberOfOutputs(){return this._nativeAudioNode.numberOfOutputs}connect(E,T=0,m=0){if(T<0||T>=this._nativeAudioNode.numberOfOutputs)throw r();const x=d(this._context),f=w(x);if(p(E)||_(E))throw i();if(Cs(E)){const V=Ft(E);try{const q=Nr(this._nativeAudioNode,V,T,m),N=ys(this);(f||N)&&this._nativeAudioNode.disconnect(...q),this.context.state!=="closed"&&!N&&ys(E)&&xs(E)}catch(q){throw q.code===12?i():q}if(e(this,E,T,m,f)){const q=a([this],E);br(q,s(f))}return E}const A=es(E);if(A.name==="playbackRate"&&A.maxValue===1024)throw l();try{this._nativeAudioNode.connect(A,T),(f||ys(this))&&this._nativeAudioNode.disconnect(A,T)}catch(V){throw V.code===12?i():V}if(jf(this,E,T,f)){const V=a([this],E);br(V,s(f))}}disconnect(E,T,m){let x;const f=d(this._context),A=w(f);if(E===void 0)x=Zf(this,A);else if(typeof E=="number"){if(E<0||E>=this.numberOfOutputs)throw r();x=Qf(this,A,E)}else{if(T!==void 0&&(T<0||T>=this.numberOfOutputs)||Cs(E)&&m!==void 0&&(m<0||m>=E.numberOfInputs))throw r();if(x=Kf(this,A,E,T,m),x.length===0)throw i()}for(const D of x){const V=a([this],D);br(V,c)}}},e1=(t,e,n,s,r,i,l,c,a,u,d,o,p)=>(_,w,b,g=null,E=null)=>{const T=b.value,m=new ff(T),x=w?s(m):null,f={get defaultValue(){return T},get maxValue(){return g===null?b.maxValue:g},get minValue(){return E===null?b.minValue:E},get value(){return b.value},set value(A){b.value=A,f.setValueAtTime(A,_.context.currentTime)},cancelAndHoldAtTime(A){if(typeof b.cancelAndHoldAtTime=="function")x===null&&m.flush(_.context.currentTime),m.add(r(A)),b.cancelAndHoldAtTime(A);else{const D=Array.from(m).pop();x===null&&m.flush(_.context.currentTime),m.add(r(A));const V=Array.from(m).pop();b.cancelScheduledValues(A),D!==V&&V!==void 0&&(V.type==="exponentialRampToValue"?b.exponentialRampToValueAtTime(V.value,V.endTime):V.type==="linearRampToValue"?b.linearRampToValueAtTime(V.value,V.endTime):V.type==="setValue"?b.setValueAtTime(V.value,V.startTime):V.type==="setValueCurve"&&b.setValueCurveAtTime(V.values,V.startTime,V.duration))}return f},cancelScheduledValues(A){return x===null&&m.flush(_.context.currentTime),m.add(i(A)),b.cancelScheduledValues(A),f},exponentialRampToValueAtTime(A,D){if(A===0)throw new RangeError;if(!Number.isFinite(D)||D<0)throw new RangeError;const V=_.context.currentTime;return x===null&&m.flush(V),Array.from(m).length===0&&(m.add(u(T,V)),b.setValueAtTime(T,V)),m.add(l(A,D)),b.exponentialRampToValueAtTime(A,D),f},linearRampToValueAtTime(A,D){const V=_.context.currentTime;return x===null&&m.flush(V),Array.from(m).length===0&&(m.add(u(T,V)),b.setValueAtTime(T,V)),m.add(c(A,D)),b.linearRampToValueAtTime(A,D),f},setTargetAtTime(A,D,V){return x===null&&m.flush(_.context.currentTime),m.add(a(A,D,V)),b.setTargetAtTime(A,D,V),f},setValueAtTime(A,D){return x===null&&m.flush(_.context.currentTime),m.add(u(A,D)),b.setValueAtTime(A,D),f},setValueCurveAtTime(A,D,V){const O=A instanceof Float32Array?A:new Float32Array(A);if(o!==null&&o.name==="webkitAudioContext"){const q=D+V,N=_.context.sampleRate,L=Math.ceil(D*N),I=Math.floor(q*N),h=I-L,k=new Float32Array(h);for(let R=0;R<h;R+=1){const F=(O.length-1)/V*((L+R)/N-D),Y=Math.floor(F),X=Math.ceil(F);k[R]=Y===X?O[Y]:(1-(F-Y))*O[Y]+(1-(X-F))*O[X]}x===null&&m.flush(_.context.currentTime),m.add(d(k,D,V)),b.setValueCurveAtTime(k,D,V);const H=I/N;H<q&&p(f,k[k.length-1],H),p(f,O[O.length-1],q)}else x===null&&m.flush(_.context.currentTime),m.add(d(O,D,V)),b.setValueCurveAtTime(O,D,V);return f}};return n.set(f,b),e.set(f,_),t(f,x),f},t1=t=>({replay(e){for(const n of t)if(n.type==="exponentialRampToValue"){const{endTime:s,value:r}=n;e.exponentialRampToValueAtTime(r,s)}else if(n.type==="linearRampToValue"){const{endTime:s,value:r}=n;e.linearRampToValueAtTime(r,s)}else if(n.type==="setTarget"){const{startTime:s,target:r,timeConstant:i}=n;e.setTargetAtTime(r,s,i)}else if(n.type==="setValue"){const{startTime:s,value:r}=n;e.setValueAtTime(r,s)}else if(n.type==="setValueCurve"){const{duration:s,startTime:r,values:i}=n;e.setValueCurveAtTime(i,r,s)}else throw new Error("Can't apply an unknown automation.")}});class wo{constructor(e){this._map=new Map(e)}get size(){return this._map.size}entries(){return this._map.entries()}forEach(e,n=null){return this._map.forEach((s,r)=>e.call(n,s,r,this))}get(e){return this._map.get(e)}has(e){return this._map.has(e)}keys(){return this._map.keys()}values(){return this._map.values()}}const n1={channelCount:2,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:1,numberOfOutputs:1,parameterData:{},processorOptions:{}},s1=(t,e,n,s,r,i,l,c,a,u,d,o,p,_)=>class extends e{constructor(b,g,E){var T;const m=c(b),x=a(m),f=d({...n1,...E});p(f);const A=Br.get(m),D=A==null?void 0:A.get(g),V=x||m.state!=="closed"?m:(T=l(m))!==null&&T!==void 0?T:m,O=r(V,x?null:b.baseLatency,u,g,D,f),q=x?s(g,f,D):null;super(b,!0,O,q);const N=[];O.parameters.forEach((I,h)=>{const k=n(this,x,I);N.push([h,k])}),this._nativeAudioWorkletNode=O,this._onprocessorerror=null,this._parameters=new wo(N),x&&t(m,this);const{activeInputs:L}=i(this);o(O,L)}get onprocessorerror(){return this._onprocessorerror}set onprocessorerror(b){const g=typeof b=="function"?_(this,b):null;this._nativeAudioWorkletNode.onprocessorerror=g;const E=this._nativeAudioWorkletNode.onprocessorerror;this._onprocessorerror=E!==null&&E===g?b:E}get parameters(){return this._parameters===null?this._nativeAudioWorkletNode.parameters:this._parameters}get port(){return this._nativeAudioWorkletNode.port}};function Ps(t,e,n,s,r){if(typeof t.copyFromChannel=="function")e[n].byteLength===0&&(e[n]=new Float32Array(128)),t.copyFromChannel(e[n],s,r);else{const i=t.getChannelData(s);if(e[n].byteLength===0)e[n]=i.slice(r,r+128);else{const l=new Float32Array(i.buffer,r*Float32Array.BYTES_PER_ELEMENT,128);e[n].set(l)}}}const yo=(t,e,n,s,r)=>{typeof t.copyToChannel=="function"?e[n].byteLength!==0&&t.copyToChannel(e[n],s,r):e[n].byteLength!==0&&t.getChannelData(s).set(e[n],r)},Is=(t,e)=>{const n=[];for(let s=0;s<t;s+=1){const r=[],i=typeof e=="number"?e:e[s];for(let l=0;l<i;l+=1)r.push(new Float32Array(128));n.push(r)}return n},r1=(t,e)=>{const n=Gt(Lr,t),s=Ft(e);return Gt(n,s)},i1=async(t,e,n,s,r,i,l)=>{const c=e===null?Math.ceil(t.context.length/128)*128:e.length,a=s.channelCount*s.numberOfInputs,u=r.reduce((g,E)=>g+E,0),d=u===0?null:n.createBuffer(u,c,n.sampleRate);if(i===void 0)throw new Error("Missing the processor constructor.");const o=wt(t),p=await r1(n,t),_=Is(s.numberOfInputs,s.channelCount),w=Is(s.numberOfOutputs,r),b=Array.from(t.parameters.keys()).reduce((g,E)=>({...g,[E]:new Float32Array(128)}),{});for(let g=0;g<c;g+=128){if(s.numberOfInputs>0&&e!==null)for(let E=0;E<s.numberOfInputs;E+=1)for(let T=0;T<s.channelCount;T+=1)Ps(e,_[E],T,T,g);i.parameterDescriptors!==void 0&&e!==null&&i.parameterDescriptors.forEach(({name:E},T)=>{Ps(e,b,E,a+T,g)});for(let E=0;E<s.numberOfInputs;E+=1)for(let T=0;T<r[E];T+=1)w[E][T].byteLength===0&&(w[E][T]=new Float32Array(128));try{const E=_.map((m,x)=>o.activeInputs[x].size===0?[]:m),T=l(g/n.sampleRate,n.sampleRate,()=>p.process(E,w,b));if(d!==null)for(let m=0,x=0;m<s.numberOfOutputs;m+=1){for(let f=0;f<r[m];f+=1)yo(d,w[m],f,x+f,g);x+=r[m]}if(!T)break}catch(E){t.dispatchEvent(new ErrorEvent("processorerror",{colno:E.colno,filename:E.filename,lineno:E.lineno,message:E.message}));break}}return d},a1=(t,e,n,s,r,i,l,c,a,u,d,o,p,_,w,b)=>(g,E,T)=>{const m=new WeakMap;let x=null;const f=async(A,D)=>{let V=d(A),O=null;const q=ho(V,D),N=Array.isArray(E.outputChannelCount)?E.outputChannelCount:Array.from(E.outputChannelCount);if(o===null){const L=N.reduce((H,R)=>H+R,0),I=r(D,{channelCount:Math.max(1,L),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,L)}),h=[];for(let H=0;H<A.numberOfOutputs;H+=1)h.push(s(D,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:N[H]}));const k=l(D,{channelCount:E.channelCount,channelCountMode:E.channelCountMode,channelInterpretation:E.channelInterpretation,gain:1});k.connect=e.bind(null,h),k.disconnect=a.bind(null,h),O=[I,h,k]}else q||(V=new o(D,g));if(m.set(D,O===null?V:O[2]),O!==null){if(x===null){if(T===void 0)throw new Error("Missing the processor constructor.");if(p===null)throw new Error("Missing the native OfflineAudioContext constructor.");const R=A.channelCount*A.numberOfInputs,F=T.parameterDescriptors===void 0?0:T.parameterDescriptors.length,Y=R+F;x=i1(A,Y===0?null:await(async()=>{const M=new p(Y,Math.ceil(A.context.length/128)*128,D.sampleRate),y=[],v=[];for(let Z=0;Z<E.numberOfInputs;Z+=1)y.push(l(M,{channelCount:E.channelCount,channelCountMode:E.channelCountMode,channelInterpretation:E.channelInterpretation,gain:1})),v.push(r(M,{channelCount:E.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:E.channelCount}));const C=await Promise.all(Array.from(A.parameters.values()).map(async Z=>{const se=i(M,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:Z.value});return await _(M,Z,se.offset),se})),$=s(M,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,R+F)});for(let Z=0;Z<E.numberOfInputs;Z+=1){y[Z].connect(v[Z]);for(let se=0;se<E.channelCount;se+=1)v[Z].connect($,se,Z*E.channelCount+se)}for(const[Z,se]of C.entries())se.connect($,0,R+Z),se.start(0);return $.connect(M.destination),await Promise.all(y.map(Z=>w(A,M,Z))),b(M)})(),D,E,N,T,u)}const L=await x,I=n(D,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),[h,k,H]=O;L!==null&&(I.buffer=L,I.start(0)),I.connect(h);for(let R=0,F=0;R<A.numberOfOutputs;R+=1){const Y=k[R];for(let X=0;X<N[R];X+=1)h.connect(Y,F+X,X);F+=N[R]}return H}if(q)for(const[L,I]of A.parameters.entries())await t(D,I,V.parameters.get(L));else for(const[L,I]of A.parameters.entries())await _(D,I,V.parameters.get(L));return await w(A,D,V),V};return{render(A,D){c(D,A);const V=m.get(D);return V!==void 0?Promise.resolve(V):f(A,D)}}},o1=(t,e)=>(n,s)=>{const r=e.get(n);if(r!==void 0)return r;const i=t.get(n);if(i!==void 0)return i;try{const l=s();return l instanceof Promise?(t.set(n,l),l.catch(()=>!1).then(c=>(t.delete(n),e.set(n,c),c))):(e.set(n,l),l)}catch{return e.set(n,!1),!1}},l1=t=>(e,n,s)=>t(n,e,s),c1=t=>(e,n,s=0,r=0)=>{const i=e[s];if(i===void 0)throw t();return ks(n)?i.connect(n,0,r):i.connect(n,0)},u1=t=>e=>(t[0]=e,t[0]),d1=(t,e,n,s,r,i,l,c)=>(a,u)=>{const d=e.get(a);if(d===void 0)throw new Error("Missing the expected cycle count.");const o=i(a.context),p=c(o);if(d===u){if(e.delete(a),!p&&l(a)){const _=s(a),{outputs:w}=n(a);for(const b of w)if(ls(b)){const g=s(b[0]);t(_,g,b[1],b[2])}else{const g=r(b[0]);_.connect(g,b[1])}}}else e.set(a,d-u)},f1=t=>(e,n,s,r)=>t(e[r],i=>i[0]===n&&i[1]===s),p1=t=>(e,n)=>{t(e).delete(n)},h1=t=>"delayTime"in t,m1=(t,e,n)=>function s(r,i){const l=Cs(i)?i:n(t,i);if(h1(l))return[];if(r[0]===l)return[r];if(r.includes(l))return[];const{outputs:c}=e(l);return Array.from(c).map(a=>s([...r,l],a[0])).reduce((a,u)=>a.concat(u),[])},vs=(t,e,n)=>{const s=e[n];if(s===void 0)throw t();return s},_1=t=>(e,n=void 0,s=void 0,r=0)=>n===void 0?e.forEach(i=>i.disconnect()):typeof n=="number"?vs(t,e,n).disconnect():ks(n)?s===void 0?e.forEach(i=>i.disconnect(n)):r===void 0?vs(t,e,s).disconnect(n,0):vs(t,e,s).disconnect(n,0,r):s===void 0?e.forEach(i=>i.disconnect(n)):vs(t,e,s).disconnect(n,0),g1=t=>e=>new Promise((n,s)=>{if(t===null){s(new SyntaxError);return}const r=t.document.head;if(r===null)s(new SyntaxError);else{const i=t.document.createElement("script"),l=new Blob([e],{type:"application/javascript"}),c=URL.createObjectURL(l),a=t.onerror,u=()=>{t.onerror=a,URL.revokeObjectURL(c)};t.onerror=(d,o,p,_,w)=>{if(o===c||o===t.location.href&&p===1&&_===1)return u(),s(w),!1;if(a!==null)return a(d,o,p,_,w)},i.onerror=()=>{u(),s(new SyntaxError)},i.onload=()=>{u(),n()},i.src=c,i.type="module",r.appendChild(i)}}),v1=t=>class{constructor(n){this._nativeEventTarget=n,this._listeners=new WeakMap}addEventListener(n,s,r){if(s!==null){let i=this._listeners.get(s);i===void 0&&(i=t(this,s),typeof s=="function"&&this._listeners.set(s,i)),this._nativeEventTarget.addEventListener(n,i,r)}}dispatchEvent(n){return this._nativeEventTarget.dispatchEvent(n)}removeEventListener(n,s,r){const i=s===null?void 0:this._listeners.get(s);this._nativeEventTarget.removeEventListener(n,i===void 0?null:i,r)}},b1=t=>(e,n,s)=>{Object.defineProperties(t,{currentFrame:{configurable:!0,get(){return Math.round(e*n)}},currentTime:{configurable:!0,get(){return e}}});try{return s()}finally{t!==null&&(delete t.currentFrame,delete t.currentTime)}},w1=t=>async e=>{try{const n=await fetch(e);if(n.ok)return[await n.text(),n.url]}catch{}throw t()},y1=(t,e)=>n=>e(t,n),S1=t=>e=>{const n=t(e);if(n.renderer===null)throw new Error("Missing the renderer of the given AudioNode in the audio graph.");return n.renderer},E1=t=>e=>{var n;return(n=t.get(e))!==null&&n!==void 0?n:0},T1=t=>e=>{const n=t(e);if(n.renderer===null)throw new Error("Missing the renderer of the given AudioParam in the audio graph.");return n.renderer},M1=t=>e=>t.get(e),Yt=()=>new DOMException("","InvalidStateError"),A1=t=>e=>{const n=t.get(e);if(n===void 0)throw Yt();return n},x1=(t,e)=>n=>{let s=t.get(n);if(s!==void 0)return s;if(e===null)throw new Error("Missing the native OfflineAudioContext constructor.");return s=new e(1,1,44100),t.set(n,s),s},C1=t=>e=>{const n=t.get(e);if(n===void 0)throw new Error("The context has no set of AudioWorkletNodes.");return n},k1=()=>new DOMException("","InvalidAccessError"),P1=(t,e,n,s,r,i)=>l=>(c,a)=>{const u=t.get(c);if(u===void 0){if(!l&&i(c)){const d=s(c),{outputs:o}=n(c);for(const p of o)if(ls(p)){const _=s(p[0]);e(d,_,p[1],p[2])}else{const _=r(p[0]);d.disconnect(_,p[1])}}t.set(c,a)}else t.set(c,u+a)},I1=t=>e=>t!==null&&e instanceof t,R1=t=>e=>t!==null&&typeof t.AudioNode=="function"&&e instanceof t.AudioNode,B1=t=>e=>t!==null&&typeof t.AudioParam=="function"&&e instanceof t.AudioParam,L1=t=>e=>t!==null&&e instanceof t,O1=t=>t!==null&&t.isSecureContext,D1=(t,e,n,s)=>class extends t{constructor(i,l){const c=n(i),a=e(c,l);if(s(c))throw new TypeError;super(i,!0,a,null),this._nativeMediaStreamAudioSourceNode=a}get mediaStream(){return this._nativeMediaStreamAudioSourceNode.mediaStream}},N1=(t,e,n,s,r)=>class extends s{constructor(l={}){if(r===null)throw new Error("Missing the native AudioContext constructor.");let c;try{c=new r(l)}catch(d){throw d.code===12&&d.message==="sampleRate is not in range"?e():d}if(c===null)throw n();if(!Hf(l.latencyHint))throw new TypeError(`The provided value '${l.latencyHint}' is not a valid enum value of type AudioContextLatencyCategory.`);if(l.sampleRate!==void 0&&c.sampleRate!==l.sampleRate)throw e();super(c,2);const{latencyHint:a}=l,{sampleRate:u}=c;if(this._baseLatency=typeof c.baseLatency=="number"?c.baseLatency:a==="balanced"?512/u:a==="interactive"||a===void 0?256/u:a==="playback"?1024/u:Math.max(2,Math.min(128,Math.round(a*u/128)))*128/u,this._nativeAudioContext=c,r.name==="webkitAudioContext"?(this._nativeGainNode=c.createGain(),this._nativeOscillatorNode=c.createOscillator(),this._nativeGainNode.gain.value=1e-37,this._nativeOscillatorNode.connect(this._nativeGainNode).connect(c.destination),this._nativeOscillatorNode.start()):(this._nativeGainNode=null,this._nativeOscillatorNode=null),this._state=null,c.state==="running"){this._state="suspended";const d=()=>{this._state==="suspended"&&(this._state=null),c.removeEventListener("statechange",d)};c.addEventListener("statechange",d)}}get baseLatency(){return this._baseLatency}get state(){return this._state!==null?this._state:this._nativeAudioContext.state}close(){return this.state==="closed"?this._nativeAudioContext.close().then(()=>{throw t()}):(this._state==="suspended"&&(this._state=null),this._nativeAudioContext.close().then(()=>{this._nativeGainNode!==null&&this._nativeOscillatorNode!==null&&(this._nativeOscillatorNode.stop(),this._nativeGainNode.disconnect(),this._nativeOscillatorNode.disconnect()),Vf(this)}))}resume(){return this._state==="suspended"?new Promise((l,c)=>{const a=()=>{this._nativeAudioContext.removeEventListener("statechange",a),this._nativeAudioContext.state==="running"?l():this.resume().then(l,c)};this._nativeAudioContext.addEventListener("statechange",a)}):this._nativeAudioContext.resume().catch(l=>{throw l===void 0||l.code===15?t():l})}suspend(){return this._nativeAudioContext.suspend().catch(l=>{throw l===void 0?t():l})}},z1=(t,e,n,s,r,i)=>class extends n{constructor(c,a){super(c),this._nativeContext=c,lo.set(this,c),s(c)&&r.set(c,new Set),this._destination=new t(this,a),this._listener=e(this,c),this._onstatechange=null}get currentTime(){return this._nativeContext.currentTime}get destination(){return this._destination}get listener(){return this._listener}get onstatechange(){return this._onstatechange}set onstatechange(c){const a=typeof c=="function"?i(this,c):null;this._nativeContext.onstatechange=a;const u=this._nativeContext.onstatechange;this._onstatechange=u!==null&&u===a?c:u}get sampleRate(){return this._nativeContext.sampleRate}get state(){return this._nativeContext.state}},fa=t=>{const e=new Uint32Array([1179011410,40,1163280727,544501094,16,131073,44100,176400,1048580,1635017060,4,0]);try{const n=t.decodeAudioData(e.buffer,()=>{});return n===void 0?!1:(n.catch(()=>{}),!0)}catch{}return!1},$1=(t,e)=>(n,s,r)=>{const i=new Set;return n.connect=(l=>(c,a=0,u=0)=>{const d=i.size===0;if(e(c))return l.call(n,c,a,u),t(i,[c,a,u],o=>o[0]===c&&o[1]===a&&o[2]===u,!0),d&&s(),c;l.call(n,c,a),t(i,[c,a],o=>o[0]===c&&o[1]===a,!0),d&&s()})(n.connect),n.disconnect=(l=>(c,a,u)=>{const d=i.size>0;if(c===void 0)l.apply(n),i.clear();else if(typeof c=="number"){l.call(n,c);for(const p of i)p[1]===c&&i.delete(p)}else{e(c)?l.call(n,c,a,u):l.call(n,c,a);for(const p of i)p[0]===c&&(a===void 0||p[1]===a)&&(u===void 0||p[2]===u)&&i.delete(p)}const o=i.size===0;d&&o&&r()})(n.disconnect),n},yn=(t,e,n)=>{const s=e[n];s!==void 0&&s!==t[n]&&(t[n]=s)},cs=(t,e)=>{yn(t,e,"channelCount"),yn(t,e,"channelCountMode"),yn(t,e,"channelInterpretation")},V1=t=>t===null?null:t.hasOwnProperty("AudioBuffer")?t.AudioBuffer:null,ei=(t,e,n)=>{const s=e[n];s!==void 0&&s!==t[n].value&&(t[n].value=s)},H1=t=>{t.start=(e=>{let n=!1;return(s=0,r=0,i)=>{if(n)throw Yt();e.call(t,s,r,i),n=!0}})(t.start)},So=t=>{t.start=(e=>(n=0,s=0,r)=>{if(typeof r=="number"&&r<0||s<0||n<0)throw new RangeError("The parameters can't be negative.");e.call(t,n,s,r)})(t.start)},Eo=t=>{t.stop=(e=>(n=0)=>{if(n<0)throw new RangeError("The parameter can't be negative.");e.call(t,n)})(t.stop)},X1=(t,e,n,s,r,i,l,c,a,u,d)=>(o,p)=>{const _=o.createBufferSource();return cs(_,p),ei(_,p,"playbackRate"),yn(_,p,"buffer"),yn(_,p,"loop"),yn(_,p,"loopEnd"),yn(_,p,"loopStart"),e(n,()=>n(o))||H1(_),e(s,()=>s(o))||a(_),e(r,()=>r(o))||u(_,o),e(i,()=>i(o))||So(_),e(l,()=>l(o))||d(_,o),e(c,()=>c(o))||Eo(_),t(o,_),_},F1=t=>t===null?null:t.hasOwnProperty("AudioContext")?t.AudioContext:t.hasOwnProperty("webkitAudioContext")?t.webkitAudioContext:null,Y1=(t,e)=>(n,s,r)=>{const i=n.destination;if(i.channelCount!==s)try{i.channelCount=s}catch{}r&&i.channelCountMode!=="explicit"&&(i.channelCountMode="explicit"),i.maxChannelCount===0&&Object.defineProperty(i,"maxChannelCount",{value:s});const l=t(n,{channelCount:s,channelCountMode:i.channelCountMode,channelInterpretation:i.channelInterpretation,gain:1});return e(l,"channelCount",c=>()=>c.call(l),c=>a=>{c.call(l,a);try{i.channelCount=a}catch(u){if(a>i.maxChannelCount)throw u}}),e(l,"channelCountMode",c=>()=>c.call(l),c=>a=>{c.call(l,a),i.channelCountMode=a}),e(l,"channelInterpretation",c=>()=>c.call(l),c=>a=>{c.call(l,a),i.channelInterpretation=a}),Object.defineProperty(l,"maxChannelCount",{get:()=>i.maxChannelCount}),l.connect(i),l},W1=t=>t===null?null:t.hasOwnProperty("AudioWorkletNode")?t.AudioWorkletNode:null,G1=t=>{const{port1:e}=new MessageChannel;try{e.postMessage(t)}finally{e.close()}},j1=(t,e,n,s,r)=>(i,l,c,a,u,d)=>{if(c!==null)try{const o=new c(i,a,d),p=new Map;let _=null;if(Object.defineProperties(o,{channelCount:{get:()=>d.channelCount,set:()=>{throw t()}},channelCountMode:{get:()=>"explicit",set:()=>{throw t()}},onprocessorerror:{get:()=>_,set:w=>{typeof _=="function"&&o.removeEventListener("processorerror",_),_=typeof w=="function"?w:null,typeof _=="function"&&o.addEventListener("processorerror",_)}}}),o.addEventListener=(w=>(...b)=>{if(b[0]==="processorerror"){const g=typeof b[1]=="function"?b[1]:typeof b[1]=="object"&&b[1]!==null&&typeof b[1].handleEvent=="function"?b[1].handleEvent:null;if(g!==null){const E=p.get(b[1]);E!==void 0?b[1]=E:(b[1]=T=>{T.type==="error"?(Object.defineProperties(T,{type:{value:"processorerror"}}),g(T)):g(new ErrorEvent(b[0],{...T}))},p.set(g,b[1]))}}return w.call(o,"error",b[1],b[2]),w.call(o,...b)})(o.addEventListener),o.removeEventListener=(w=>(...b)=>{if(b[0]==="processorerror"){const g=p.get(b[1]);g!==void 0&&(p.delete(b[1]),b[1]=g)}return w.call(o,"error",b[1],b[2]),w.call(o,b[0],b[1],b[2])})(o.removeEventListener),d.numberOfOutputs!==0){const w=n(i,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return o.connect(w).connect(i.destination),r(o,()=>w.disconnect(),()=>w.connect(i.destination))}return o}catch(o){throw o.code===11?s():o}if(u===void 0)throw s();return G1(d),e(i,l,u,d)},q1=(t,e)=>t===null?512:Math.max(512,Math.min(16384,Math.pow(2,Math.round(Math.log2(t*e))))),U1=t=>new Promise((e,n)=>{const{port1:s,port2:r}=new MessageChannel;s.onmessage=({data:i})=>{s.close(),r.close(),e(i)},s.onmessageerror=({data:i})=>{s.close(),r.close(),n(i)},r.postMessage(t)}),Z1=async(t,e)=>{const n=await U1(e);return new t(n)},Q1=(t,e,n,s)=>{let r=Lr.get(t);r===void 0&&(r=new WeakMap,Lr.set(t,r));const i=Z1(n,s);return r.set(e,i),i},K1=(t,e,n,s,r,i,l,c,a,u,d,o,p)=>(_,w,b,g)=>{if(g.numberOfInputs===0&&g.numberOfOutputs===0)throw a();const E=Array.isArray(g.outputChannelCount)?g.outputChannelCount:Array.from(g.outputChannelCount);if(E.some(ie=>ie<1))throw a();if(E.length!==g.numberOfOutputs)throw e();if(g.channelCountMode!=="explicit")throw a();const T=g.channelCount*g.numberOfInputs,m=E.reduce((ie,ue)=>ie+ue,0),x=b.parameterDescriptors===void 0?0:b.parameterDescriptors.length;if(T+x>6||m>6)throw a();const f=new MessageChannel,A=[],D=[];for(let ie=0;ie<g.numberOfInputs;ie+=1)A.push(l(_,{channelCount:g.channelCount,channelCountMode:g.channelCountMode,channelInterpretation:g.channelInterpretation,gain:1})),D.push(r(_,{channelCount:g.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:g.channelCount}));const V=[];if(b.parameterDescriptors!==void 0)for(const{defaultValue:ie,maxValue:ue,minValue:ze,name:qe}of b.parameterDescriptors){const S=i(_,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:g.parameterData[qe]!==void 0?g.parameterData[qe]:ie===void 0?0:ie});Object.defineProperties(S.offset,{defaultValue:{get:()=>ie===void 0?0:ie},maxValue:{get:()=>ue===void 0?Qr:ue},minValue:{get:()=>ze===void 0?Hs:ze}}),V.push(S)}const O=s(_,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,T+x)}),q=q1(w,_.sampleRate),N=c(_,q,T+x,Math.max(1,m)),L=r(_,{channelCount:Math.max(1,m),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,m)}),I=[];for(let ie=0;ie<g.numberOfOutputs;ie+=1)I.push(s(_,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:E[ie]}));for(let ie=0;ie<g.numberOfInputs;ie+=1){A[ie].connect(D[ie]);for(let ue=0;ue<g.channelCount;ue+=1)D[ie].connect(O,ue,ie*g.channelCount+ue)}const h=new wo(b.parameterDescriptors===void 0?[]:b.parameterDescriptors.map(({name:ie},ue)=>{const ze=V[ue];return ze.connect(O,0,T+ue),ze.start(0),[ie,ze.offset]}));O.connect(N);let k=g.channelInterpretation,H=null;const R=g.numberOfOutputs===0?[N]:I,F={get bufferSize(){return q},get channelCount(){return g.channelCount},set channelCount(ie){throw n()},get channelCountMode(){return g.channelCountMode},set channelCountMode(ie){throw n()},get channelInterpretation(){return k},set channelInterpretation(ie){for(const ue of A)ue.channelInterpretation=ie;k=ie},get context(){return N.context},get inputs(){return A},get numberOfInputs(){return g.numberOfInputs},get numberOfOutputs(){return g.numberOfOutputs},get onprocessorerror(){return H},set onprocessorerror(ie){typeof H=="function"&&F.removeEventListener("processorerror",H),H=typeof ie=="function"?ie:null,typeof H=="function"&&F.addEventListener("processorerror",H)},get parameters(){return h},get port(){return f.port2},addEventListener(...ie){return N.addEventListener(ie[0],ie[1],ie[2])},connect:t.bind(null,R),disconnect:u.bind(null,R),dispatchEvent(...ie){return N.dispatchEvent(ie[0])},removeEventListener(...ie){return N.removeEventListener(ie[0],ie[1],ie[2])}},Y=new Map;f.port1.addEventListener=(ie=>(...ue)=>{if(ue[0]==="message"){const ze=typeof ue[1]=="function"?ue[1]:typeof ue[1]=="object"&&ue[1]!==null&&typeof ue[1].handleEvent=="function"?ue[1].handleEvent:null;if(ze!==null){const qe=Y.get(ue[1]);qe!==void 0?ue[1]=qe:(ue[1]=S=>{d(_.currentTime,_.sampleRate,()=>ze(S))},Y.set(ze,ue[1]))}}return ie.call(f.port1,ue[0],ue[1],ue[2])})(f.port1.addEventListener),f.port1.removeEventListener=(ie=>(...ue)=>{if(ue[0]==="message"){const ze=Y.get(ue[1]);ze!==void 0&&(Y.delete(ue[1]),ue[1]=ze)}return ie.call(f.port1,ue[0],ue[1],ue[2])})(f.port1.removeEventListener);let X=null;Object.defineProperty(f.port1,"onmessage",{get:()=>X,set:ie=>{typeof X=="function"&&f.port1.removeEventListener("message",X),X=typeof ie=="function"?ie:null,typeof X=="function"&&(f.port1.addEventListener("message",X),f.port1.start())}}),b.prototype.port=f.port1;let M=null;Q1(_,F,b,g).then(ie=>M=ie);const v=Is(g.numberOfInputs,g.channelCount),C=Is(g.numberOfOutputs,E),$=b.parameterDescriptors===void 0?[]:b.parameterDescriptors.reduce((ie,{name:ue})=>({...ie,[ue]:new Float32Array(128)}),{});let Z=!0;const se=()=>{g.numberOfOutputs>0&&N.disconnect(L);for(let ie=0,ue=0;ie<g.numberOfOutputs;ie+=1){const ze=I[ie];for(let qe=0;qe<E[ie];qe+=1)L.disconnect(ze,ue+qe,qe);ue+=E[ie]}},j=new Map;N.onaudioprocess=({inputBuffer:ie,outputBuffer:ue})=>{if(M!==null){const ze=o(F);for(let qe=0;qe<q;qe+=128){for(let S=0;S<g.numberOfInputs;S+=1)for(let P=0;P<g.channelCount;P+=1)Ps(ie,v[S],P,P,qe);b.parameterDescriptors!==void 0&&b.parameterDescriptors.forEach(({name:S},P)=>{Ps(ie,$,S,T+P,qe)});for(let S=0;S<g.numberOfInputs;S+=1)for(let P=0;P<E[S];P+=1)C[S][P].byteLength===0&&(C[S][P]=new Float32Array(128));try{const S=v.map((ge,Ee)=>{if(ze[Ee].size>0)return j.set(Ee,q/128),ge;const Ce=j.get(Ee);return Ce===void 0?[]:(ge.every(ke=>ke.every(xe=>xe===0))&&(Ce===1?j.delete(Ee):j.set(Ee,Ce-1)),ge)});Z=d(_.currentTime+qe/_.sampleRate,_.sampleRate,()=>M.process(S,C,$));for(let ge=0,Ee=0;ge<g.numberOfOutputs;ge+=1){for(let Be=0;Be<E[ge];Be+=1)yo(ue,C[ge],Be,Ee+Be,qe);Ee+=E[ge]}}catch(S){Z=!1,F.dispatchEvent(new ErrorEvent("processorerror",{colno:S.colno,filename:S.filename,lineno:S.lineno,message:S.message}))}if(!Z){for(let S=0;S<g.numberOfInputs;S+=1){A[S].disconnect(D[S]);for(let P=0;P<g.channelCount;P+=1)D[qe].disconnect(O,P,S*g.channelCount+P)}if(b.parameterDescriptors!==void 0){const S=b.parameterDescriptors.length;for(let P=0;P<S;P+=1){const ge=V[P];ge.disconnect(O,0,T+P),ge.stop()}}O.disconnect(N),N.onaudioprocess=null,ee?se():ye();break}}}};let ee=!1;const ce=l(_,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0}),me=()=>N.connect(ce).connect(_.destination),ye=()=>{N.disconnect(ce),ce.disconnect()},Me=()=>{if(Z){ye(),g.numberOfOutputs>0&&N.connect(L);for(let ie=0,ue=0;ie<g.numberOfOutputs;ie+=1){const ze=I[ie];for(let qe=0;qe<E[ie];qe+=1)L.connect(ze,ue+qe,qe);ue+=E[ie]}}ee=!0},Se=()=>{Z&&(me(),se()),ee=!1};return me(),p(F,Me,Se)},J1=(t,e)=>(n,s)=>{const r=n.createChannelMerger(s.numberOfInputs);return t!==null&&t.name==="webkitAudioContext"&&e(n,r),cs(r,s),r},ep=t=>{const e=t.numberOfOutputs;Object.defineProperty(t,"channelCount",{get:()=>e,set:n=>{if(n!==e)throw Yt()}}),Object.defineProperty(t,"channelCountMode",{get:()=>"explicit",set:n=>{if(n!=="explicit")throw Yt()}}),Object.defineProperty(t,"channelInterpretation",{get:()=>"discrete",set:n=>{if(n!=="discrete")throw Yt()}})},To=(t,e)=>{const n=t.createChannelSplitter(e.numberOfOutputs);return cs(n,e),ep(n),n},tp=(t,e,n,s,r)=>(i,l)=>{if(i.createConstantSource===void 0)return n(i,l);const c=i.createConstantSource();return cs(c,l),ei(c,l,"offset"),e(s,()=>s(i))||So(c),e(r,()=>r(i))||Eo(c),t(i,c),c},Mo=(t,e)=>(t.connect=e.connect.bind(e),t.disconnect=e.disconnect.bind(e),t),np=(t,e,n,s)=>(r,{offset:i,...l})=>{const c=r.createBuffer(1,2,44100),a=e(r,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),u=n(r,{...l,gain:i}),d=c.getChannelData(0);d[0]=1,d[1]=1,a.buffer=c,a.loop=!0;const o={get bufferSize(){},get channelCount(){return u.channelCount},set channelCount(w){u.channelCount=w},get channelCountMode(){return u.channelCountMode},set channelCountMode(w){u.channelCountMode=w},get channelInterpretation(){return u.channelInterpretation},set channelInterpretation(w){u.channelInterpretation=w},get context(){return u.context},get inputs(){return[]},get numberOfInputs(){return a.numberOfInputs},get numberOfOutputs(){return u.numberOfOutputs},get offset(){return u.gain},get onended(){return a.onended},set onended(w){a.onended=w},addEventListener(...w){return a.addEventListener(w[0],w[1],w[2])},dispatchEvent(...w){return a.dispatchEvent(w[0])},removeEventListener(...w){return a.removeEventListener(w[0],w[1],w[2])},start(w=0){a.start.call(a,w)},stop(w=0){a.stop.call(a,w)}},p=()=>a.connect(u),_=()=>a.disconnect(u);return t(r,a),s(Mo(o,u),p,_)},dn=(t,e)=>{const n=t.createGain();return cs(n,e),ei(n,e,"gain"),n},sp=(t,{mediaStream:e})=>{const n=e.getAudioTracks();n.sort((i,l)=>i.id<l.id?-1:i.id>l.id?1:0);const s=n.slice(0,1),r=t.createMediaStreamSource(new MediaStream(s));return Object.defineProperty(r,"mediaStream",{value:e}),r},rp=t=>t===null?null:t.hasOwnProperty("OfflineAudioContext")?t.OfflineAudioContext:t.hasOwnProperty("webkitOfflineAudioContext")?t.webkitOfflineAudioContext:null,ti=(t,e,n,s)=>t.createScriptProcessor(e,n,s),In=()=>new DOMException("","NotSupportedError"),ip=(t,e)=>(n,s,r)=>(t(s).replay(r),e(s,n,r)),ap=(t,e,n)=>async(s,r,i)=>{const l=t(s);await Promise.all(l.activeInputs.map((c,a)=>Array.from(c).map(async([u,d])=>{const p=await e(u).render(u,r),_=s.context.destination;!n(u)&&(s!==_||!n(s))&&p.connect(i,d,a)})).reduce((c,a)=>[...c,...a],[]))},op=(t,e,n)=>async(s,r,i)=>{const l=e(s);await Promise.all(Array.from(l.activeInputs).map(async([c,a])=>{const d=await t(c).render(c,r);n(c)||d.connect(i,a)}))},lp=(t,e,n,s)=>r=>t(fa,()=>fa(r))?Promise.resolve(t(s,s)).then(i=>{if(!i){const l=n(r,512,0,1);r.oncomplete=()=>{l.onaudioprocess=null,l.disconnect()},l.onaudioprocess=()=>r.currentTime,l.connect(r.destination)}return r.startRendering()}):new Promise(i=>{const l=e(r,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});r.oncomplete=c=>{l.disconnect(),i(c.renderedBuffer)},l.connect(r.destination),r.startRendering()}),cp=t=>(e,n)=>{t.set(e,n)},up=t=>()=>{if(t===null)return!1;try{new t({length:1,sampleRate:44100})}catch{return!1}return!0},dp=(t,e)=>async()=>{if(t===null)return!0;if(e===null)return!1;const n=new Blob(['class A extends AudioWorkletProcessor{process(i){this.port.postMessage(i,[i[0][0].buffer])}}registerProcessor("a",A)'],{type:"application/javascript; charset=utf-8"}),s=new e(1,128,44100),r=URL.createObjectURL(n);let i=!1,l=!1;try{await s.audioWorklet.addModule(r);const c=new t(s,"a",{numberOfOutputs:0}),a=s.createOscillator();c.port.onmessage=()=>i=!0,c.onprocessorerror=()=>l=!0,a.connect(c),a.start(0),await s.startRendering(),await new Promise(u=>setTimeout(u))}catch{}finally{URL.revokeObjectURL(r)}return i&&!l},fp=(t,e)=>()=>{if(e===null)return Promise.resolve(!1);const n=new e(1,1,44100),s=t(n,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return new Promise(r=>{n.oncomplete=()=>{s.disconnect(),r(n.currentTime!==0)},n.startRendering()})},pp=()=>new DOMException("","UnknownError"),hp=()=>typeof window>"u"?null:window,mp=(t,e)=>n=>{n.copyFromChannel=(s,r,i=0)=>{const l=t(i),c=t(r);if(c>=n.numberOfChannels)throw e();const a=n.length,u=n.getChannelData(c),d=s.length;for(let o=l<0?-l:0;o+l<a&&o<d;o+=1)s[o]=u[o+l]},n.copyToChannel=(s,r,i=0)=>{const l=t(i),c=t(r);if(c>=n.numberOfChannels)throw e();const a=n.length,u=n.getChannelData(c),d=s.length;for(let o=l<0?-l:0;o+l<a&&o<d;o+=1)u[o+l]=s[o]}},_p=t=>e=>{e.copyFromChannel=(n=>(s,r,i=0)=>{const l=t(i),c=t(r);if(l<e.length)return n.call(e,s,c,l)})(e.copyFromChannel),e.copyToChannel=(n=>(s,r,i=0)=>{const l=t(i),c=t(r);if(l<e.length)return n.call(e,s,c,l)})(e.copyToChannel)},gp=t=>(e,n)=>{const s=n.createBuffer(1,1,44100);e.buffer===null&&(e.buffer=s),t(e,"buffer",r=>()=>{const i=r.call(e);return i===s?null:i},r=>i=>r.call(e,i===null?s:i))},vp=(t,e)=>(n,s)=>{s.channelCount=1,s.channelCountMode="explicit",Object.defineProperty(s,"channelCount",{get:()=>1,set:()=>{throw t()}}),Object.defineProperty(s,"channelCountMode",{get:()=>"explicit",set:()=>{throw t()}});const r=n.createBufferSource();e(s,()=>{const c=s.numberOfInputs;for(let a=0;a<c;a+=1)r.connect(s,0,a)},()=>r.disconnect(s))},bp=(t,e,n)=>t.copyFromChannel===void 0?t.getChannelData(n)[0]:(t.copyFromChannel(e,n),e[0]),ni=(t,e,n,s)=>{let r=t;for(;!r.hasOwnProperty(e);)r=Object.getPrototypeOf(r);const{get:i,set:l}=Object.getOwnPropertyDescriptor(r,e);Object.defineProperty(t,e,{get:n(i),set:s(l)})},wp=t=>({...t,outputChannelCount:t.outputChannelCount!==void 0?t.outputChannelCount:t.numberOfInputs===1&&t.numberOfOutputs===1?[t.channelCount]:Array.from({length:t.numberOfOutputs},()=>1)}),Ao=(t,e,n)=>{try{t.setValueAtTime(e,n)}catch(s){if(s.code!==9)throw s;Ao(t,e,n+1e-7)}},yp=t=>{const e=t.createBufferSource();e.start();try{e.start()}catch{return!0}return!1},Sp=t=>{const e=t.createBufferSource(),n=t.createBuffer(1,1,44100);e.buffer=n;try{e.start(0,1)}catch{return!1}return!0},Ep=t=>{const e=t.createBufferSource();e.start();try{e.stop()}catch{return!1}return!0},xo=t=>{const e=t.createOscillator();try{e.start(-1)}catch(n){return n instanceof RangeError}return!1},Tp=t=>{const e=t.createBuffer(1,1,44100),n=t.createBufferSource();n.buffer=e,n.start(),n.stop();try{return n.stop(),!0}catch{return!1}},Co=t=>{const e=t.createOscillator();try{e.stop(-1)}catch(n){return n instanceof RangeError}return!1},Mp=t=>{const{port1:e,port2:n}=new MessageChannel;try{e.postMessage(t)}finally{e.close(),n.close()}},Ap=t=>{t.start=(e=>(n=0,s=0,r)=>{const i=t.buffer,l=i===null?s:Math.min(i.duration,s);i!==null&&l>i.duration-.5/t.context.sampleRate?e.call(t,n,0,0):e.call(t,n,l,r)})(t.start)},xp=(t,e)=>{const n=e.createGain();t.connect(n);const s=(r=>()=>{r.call(t,n),t.removeEventListener("ended",s)})(t.disconnect);t.addEventListener("ended",s),Mo(t,n),t.stop=(r=>{let i=!1;return(l=0)=>{if(i)try{r.call(t,l)}catch{n.gain.setValueAtTime(0,l)}else r.call(t,l),i=!0}})(t.stop)},Xs=(t,e)=>n=>{const s={value:t};return Object.defineProperties(n,{currentTarget:s,target:s}),typeof e=="function"?e.call(t,n):e.handleEvent.call(t,n)},Cp=bf(Pn),kp=Mf(Pn),Pp=f1(Vs),Ip=new WeakMap,Rp=E1(Ip),Xn=o1(new Map,new WeakMap),Zt=hp(),ko=S1(wt),si=ap(wt,ko,An),xn=A1(lo),Fn=rp(Zt),tn=L1(Fn),Po=new WeakMap,Io=v1(Xs),Fs=F1(Zt),Bp=I1(Fs),Ro=R1(Zt),Lp=B1(Zt),ts=W1(Zt),Ys=Jf(wf(ro),Tf(Cp,kp,Nr,Pp,zr,wt,Rp,as,Ft,Pn,Tn,An,ys),Xn,P1(Rr,zr,wt,Ft,es,Tn),kn,k1,In,d1(Nr,Rr,wt,Ft,es,xn,Tn,tn),m1(Po,wt,Gt),Io,xn,Bp,Ro,Lp,tn,ts),Op=new WeakSet,pa=V1(Zt),Bo=u1(new Uint32Array(1)),Dp=mp(Bo,kn),Np=_p(Bo),zp=Pf(Op,Xn,In,pa,Fn,up(pa),Dp,Np),ri=Af(dn),Lo=op(ko,os,An),Oo=l1(Lo),Ws=X1(ri,Xn,yp,Sp,Ep,xo,Tp,Co,Ap,gp(ni),xp),Do=ip(T1(os),Lo),$p=Bf(Oo,Ws,Ft,Do,si),ii=e1(yf(ao),Po,oo,t1,pf,hf,mf,_f,gf,kr,no,Fs,Ao),Vp=Rf(Ys,$p,ii,Yt,Ws,xn,tn,Xs),Hp=Xf(Ys,Ff,kn,Yt,Y1(dn,ni),xn,tn,si),Gs=$1(Pn,Ro),Xp=vp(Yt,Gs),ai=J1(Fs,Xp),Fp=np(ri,Ws,dn,Gs),oi=tp(ri,Xn,Fp,xo,Co),Yp=lp(Xn,dn,ti,fp(dn,Fn)),Wp=Yf(ii,ai,oi,ti,In,bp,tn,ni),No=new WeakMap,Gp=z1(Hp,Wp,Io,tn,No,Xs),zo=O1(Zt),li=b1(Zt),$o=new WeakMap,jp=x1($o,Fn),ha=zo?Ef(Xn,In,g1(Zt),li,w1(vf),xn,jp,tn,ts,new WeakMap,new WeakMap,dp(ts,Fn),Zt):void 0,qp=D1(Ys,sp,xn,tn),Vo=C1(No),Up=xf(Vo),Ho=c1(kn),Zp=p1(Vo),Xo=_1(kn),Fo=new WeakMap,Qp=y1(Fo,Gt),Kp=K1(Ho,kn,Yt,ai,To,oi,dn,ti,In,Xo,li,Qp,Gs),Jp=j1(Yt,Kp,dn,In,Gs),e0=a1(Oo,Ho,Ws,ai,To,oi,dn,Zp,Xo,li,Ft,ts,Fn,Do,si,Yp),t0=M1($o),n0=cp(Fo),ma=zo?s1(Up,Ys,ii,e0,Jp,wt,t0,xn,tn,ts,wp,n0,Mp,Xs):void 0,s0=N1(Yt,In,pp,Gp,Fs),Yo="Missing AudioWorklet support. Maybe this is not running in a secure context.",r0=async(t,e,n,s,r)=>{const{encoderInstanceId:i,port:l}=await eo(r,e.sampleRate);if(ma===void 0)throw new Error(Yo);const c=new Vp(e,{buffer:t}),a=new qp(e,{mediaStream:s}),u=cf(ma,e,{channelCount:n});return{audioBufferSourceNode:c,encoderInstanceId:i,mediaStreamAudioSourceNode:a,port:l,recorderAudioWorkletNode:u}},i0=(t,e,n,s)=>(r,i,l)=>{var c;const a=(c=i.getAudioTracks()[0])===null||c===void 0?void 0:c.getSettings().sampleRate,u=new s0({latencyHint:"playback",sampleRate:a}),d=Math.max(1024,Math.ceil(u.baseLatency*u.sampleRate)),o=new zp({length:d,sampleRate:u.sampleRate}),p=[],_=lf(V=>{if(ha===void 0)throw new Error(Yo);return ha(u,V)});let w=null,b=null,g=null,E=null,T=!0;const m=V=>{r.dispatchEvent(t("dataavailable",{data:new Blob(V,{type:l})}))},x=async(V,O)=>{const q=await Ts(V,O);g===null?p.push(...q):(m(q),E=x(V,O))},f=()=>(T=!0,u.resume()),A=()=>{g!==null&&(w!==null&&(i.removeEventListener("addtrack",w),i.removeEventListener("removetrack",w)),b!==null&&clearTimeout(b),g.then(async({encoderInstanceId:V,mediaStreamAudioSourceNode:O,recorderAudioWorkletNode:q})=>{E!==null&&(E.catch(()=>{}),E=null),await q.stop(),O.disconnect(q);const N=await Ts(V,null);g===null&&await D(),m([...p,...N]),p.length=0,r.dispatchEvent(new Event("stop"))}),g=null)},D=()=>(T=!1,u.suspend());return D(),{get mimeType(){return l},get state(){return g===null?"inactive":T?"recording":"paused"},pause(){if(g===null)throw n();T&&(D(),r.dispatchEvent(new Event("pause")))},resume(){if(g===null)throw n();T||(f(),r.dispatchEvent(new Event("resume")))},start(V){var O;if(g!==null)throw n();if(i.getVideoTracks().length>0)throw s();r.dispatchEvent(new Event("start"));const q=i.getAudioTracks(),N=q.length===0?2:(O=q[0].getSettings().channelCount)!==null&&O!==void 0?O:2;g=Promise.all([f(),_.then(()=>r0(o,u,N,i,l))]).then(async([,{audioBufferSourceNode:I,encoderInstanceId:h,mediaStreamAudioSourceNode:k,port:H,recorderAudioWorkletNode:R}])=>(k.connect(R),await new Promise(F=>{I.onended=F,I.connect(R),I.start(u.currentTime+d/u.sampleRate)}),I.disconnect(R),await R.record(H),V!==void 0&&(E=x(h,V)),{encoderInstanceId:h,mediaStreamAudioSourceNode:k,recorderAudioWorkletNode:R}));const L=i.getTracks();w=()=>{A(),r.dispatchEvent(new ErrorEvent("error",{error:e()}))},i.addEventListener("addtrack",w),i.addEventListener("removetrack",w),b=setInterval(()=>{const I=i.getTracks();(I.length!==L.length||I.some((h,k)=>h!==L[k]))&&w!==null&&w()},1e3)},stop:A}};class wr{constructor(e,n=0,s){if(n<0||s!==void 0&&s<0)throw new RangeError;const r=e.reduce((d,o)=>d+o.byteLength,0);if(n>r||s!==void 0&&n+s>r)throw new RangeError;const i=[],l=s===void 0?r-n:s,c=[];let a=0,u=n;for(const d of e)if(c.length===0)if(d.byteLength>u){a=d.byteLength-u;const o=a>l?l:a;i.push(new DataView(d,u,o)),c.push(d)}else u-=d.byteLength;else if(a<l){a+=d.byteLength;const o=a>l?d.byteLength-a+l:d.byteLength;i.push(new DataView(d,0,o)),c.push(d)}this._buffers=c,this._byteLength=l,this._byteOffset=u,this._dataViews=i,this._internalBuffer=new DataView(new ArrayBuffer(8))}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get byteOffset(){return this._byteOffset}getFloat32(e,n){return this._internalBuffer.setUint8(0,this.getUint8(e+0)),this._internalBuffer.setUint8(1,this.getUint8(e+1)),this._internalBuffer.setUint8(2,this.getUint8(e+2)),this._internalBuffer.setUint8(3,this.getUint8(e+3)),this._internalBuffer.getFloat32(0,n)}getFloat64(e,n){return this._internalBuffer.setUint8(0,this.getUint8(e+0)),this._internalBuffer.setUint8(1,this.getUint8(e+1)),this._internalBuffer.setUint8(2,this.getUint8(e+2)),this._internalBuffer.setUint8(3,this.getUint8(e+3)),this._internalBuffer.setUint8(4,this.getUint8(e+4)),this._internalBuffer.setUint8(5,this.getUint8(e+5)),this._internalBuffer.setUint8(6,this.getUint8(e+6)),this._internalBuffer.setUint8(7,this.getUint8(e+7)),this._internalBuffer.getFloat64(0,n)}getInt16(e,n){return this._internalBuffer.setUint8(0,this.getUint8(e+0)),this._internalBuffer.setUint8(1,this.getUint8(e+1)),this._internalBuffer.getInt16(0,n)}getInt32(e,n){return this._internalBuffer.setUint8(0,this.getUint8(e+0)),this._internalBuffer.setUint8(1,this.getUint8(e+1)),this._internalBuffer.setUint8(2,this.getUint8(e+2)),this._internalBuffer.setUint8(3,this.getUint8(e+3)),this._internalBuffer.getInt32(0,n)}getInt8(e){const[n,s]=this._findDataViewWithOffset(e);return n.getInt8(e-s)}getUint16(e,n){return this._internalBuffer.setUint8(0,this.getUint8(e+0)),this._internalBuffer.setUint8(1,this.getUint8(e+1)),this._internalBuffer.getUint16(0,n)}getUint32(e,n){return this._internalBuffer.setUint8(0,this.getUint8(e+0)),this._internalBuffer.setUint8(1,this.getUint8(e+1)),this._internalBuffer.setUint8(2,this.getUint8(e+2)),this._internalBuffer.setUint8(3,this.getUint8(e+3)),this._internalBuffer.getUint32(0,n)}getUint8(e){const[n,s]=this._findDataViewWithOffset(e);return n.getUint8(e-s)}setFloat32(e,n,s){this._internalBuffer.setFloat32(0,n,s),this.setUint8(e,this._internalBuffer.getUint8(0)),this.setUint8(e+1,this._internalBuffer.getUint8(1)),this.setUint8(e+2,this._internalBuffer.getUint8(2)),this.setUint8(e+3,this._internalBuffer.getUint8(3))}setFloat64(e,n,s){this._internalBuffer.setFloat64(0,n,s),this.setUint8(e,this._internalBuffer.getUint8(0)),this.setUint8(e+1,this._internalBuffer.getUint8(1)),this.setUint8(e+2,this._internalBuffer.getUint8(2)),this.setUint8(e+3,this._internalBuffer.getUint8(3)),this.setUint8(e+4,this._internalBuffer.getUint8(4)),this.setUint8(e+5,this._internalBuffer.getUint8(5)),this.setUint8(e+6,this._internalBuffer.getUint8(6)),this.setUint8(e+7,this._internalBuffer.getUint8(7))}setInt16(e,n,s){this._internalBuffer.setInt16(0,n,s),this.setUint8(e,this._internalBuffer.getUint8(0)),this.setUint8(e+1,this._internalBuffer.getUint8(1))}setInt32(e,n,s){this._internalBuffer.setInt32(0,n,s),this.setUint8(e,this._internalBuffer.getUint8(0)),this.setUint8(e+1,this._internalBuffer.getUint8(1)),this.setUint8(e+2,this._internalBuffer.getUint8(2)),this.setUint8(e+3,this._internalBuffer.getUint8(3))}setInt8(e,n){const[s,r]=this._findDataViewWithOffset(e);s.setInt8(e-r,n)}setUint16(e,n,s){this._internalBuffer.setUint16(0,n,s),this.setUint8(e,this._internalBuffer.getUint8(0)),this.setUint8(e+1,this._internalBuffer.getUint8(1))}setUint32(e,n,s){this._internalBuffer.setUint32(0,n,s),this.setUint8(e,this._internalBuffer.getUint8(0)),this.setUint8(e+1,this._internalBuffer.getUint8(1)),this.setUint8(e+2,this._internalBuffer.getUint8(2)),this.setUint8(e+3,this._internalBuffer.getUint8(3))}setUint8(e,n){const[s,r]=this._findDataViewWithOffset(e);s.setUint8(e-r,n)}_findDataViewWithOffset(e){let n=0;for(const s of this._dataViews){const r=n+s.byteLength;if(e>=n&&e<r)return[s,n];n=r}throw new RangeError}}const a0=(t,e,n)=>(s,r,i,l)=>{const c=[],a=new r(i,{mimeType:"audio/webm;codecs=pcm"});let u=null,d=()=>{};const o=w=>{s.dispatchEvent(t("dataavailable",{data:new Blob(w,{type:l})}))},p=async(w,b)=>{const g=await Ts(w,b);a.state==="inactive"?c.push(...g):(o(g),u=p(w,b))},_=()=>{a.state!=="inactive"&&(u!==null&&(u.catch(()=>{}),u=null),d(),d=()=>{},a.stop())};return a.addEventListener("error",w=>{_(),s.dispatchEvent(new ErrorEvent("error",{error:w.error}))}),a.addEventListener("pause",()=>s.dispatchEvent(new Event("pause"))),a.addEventListener("resume",()=>s.dispatchEvent(new Event("resume"))),a.addEventListener("start",()=>s.dispatchEvent(new Event("start"))),{get mimeType(){return l},get state(){return a.state},pause(){return a.pause()},resume(){return a.resume()},start(w){const[b]=i.getAudioTracks();if(b!==void 0&&a.state==="inactive"){const{channelCount:g,sampleRate:E}=b.getSettings();if(g===void 0)throw new Error("The channelCount is not defined.");if(E===void 0)throw new Error("The sampleRate is not defined.");let T=!1,m=!1,x=0,f=eo(l,E);d=()=>{m=!0};const A=to(a,"dataavailable")(({data:D})=>{x+=1;const V=D.arrayBuffer();f=f.then(async({dataView:O=null,elementType:q=null,encoderInstanceId:N,port:L})=>{const I=await V;x-=1;const h=O===null?new wr([I]):new wr([...O.buffers,I],O.byteOffset);if(!T&&a.state==="recording"&&!m){const Y=n(h,0);if(Y===null)return{dataView:h,elementType:q,encoderInstanceId:N,port:L};const{value:X}=Y;if(X!==172351395)return{dataView:O,elementType:q,encoderInstanceId:N,port:L};T=!0}const{currentElementType:k,offset:H,contents:R}=e(h,q,g),F=H<h.byteLength?new wr(h.buffers,h.byteOffset+H):null;return R.forEach(Y=>L.postMessage(Y,Y.map(({buffer:X})=>X))),x===0&&(a.state==="inactive"||m)&&(Ts(N,null).then(Y=>{o([...c,...Y]),c.length=0,s.dispatchEvent(new Event("stop"))}),L.postMessage([]),L.close(),A()),{dataView:F,elementType:k,encoderInstanceId:N,port:L}})});w!==void 0&&f.then(({encoderInstanceId:D})=>{m||(u=p(D,w))})}a.start(100)},stop:_}},o0=()=>typeof window>"u"?null:window,Wo=(t,e)=>{if(e>=t.byteLength)return null;const n=t.getUint8(e);if(n>127)return 1;if(n>63)return 2;if(n>31)return 3;if(n>15)return 4;if(n>7)return 5;if(n>3)return 6;if(n>1)return 7;if(n>0)return 8;const s=Wo(t,e+1);return s===null?null:s+8},l0=(t,e)=>n=>{const s={value:t};return Object.defineProperties(n,{currentTarget:s,target:s}),typeof e=="function"?e.call(t,n):e.handleEvent.call(t,n)},Go=[],ci=o0(),c0=Yd(ci),jo=Nd(c0),u0=i0(jo,Hd,Xd,Cr),ui=Ud(Wo),d0=jd(ui),f0=qd(ui),p0=zd(d0,f0),h0=a0(jo,p0,ui),m0=Vd(ci),_0=Wd(ci),yr=Fd(Gd(Cr),Cr,u0,h0,Go,$d(m0,l0),_0),g0=new WeakMap,v0=async t=>{const e=await Dd(t);Go.push(e),g0.set(t,e)},b0=Ka({characterize:({call:t})=>()=>t("characterize"),encode:({call:t})=>(e,n)=>t("encode",{recordingId:e,timeslice:n}),record:({call:t})=>async(e,n,s)=>{await t("record",{recordingId:e,sampleRate:n,typedArrays:s},s.map(({buffer:r})=>r))}}),w0=t=>{const e=new Worker(t);return b0(e)},y0=`(()=>{var e={455:function(e,t){!function(e){"use strict";var t=function(e){return function(t){var r=e(t);return t.add(r),r}},r=function(e){return function(t,r){return e.set(t,r),r}},n=void 0===Number.MAX_SAFE_INTEGER?9007199254740991:Number.MAX_SAFE_INTEGER,s=536870912,a=2*s,o=function(e,t){return function(r){var o=t.get(r),i=void 0===o?r.size:o<a?o+1:0;if(!r.has(i))return e(r,i);if(r.size<s){for(;r.has(i);)i=Math.floor(Math.random()*a);return e(r,i)}if(r.size>n)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;r.has(i);)i=Math.floor(Math.random()*n);return e(r,i)}},i=new WeakMap,l=r(i),c=o(l,i),u=t(c);e.addUniqueNumber=u,e.generateUniqueNumber=c}(t)}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var a=t[n]={exports:{}};return e[n].call(a.exports,a,a.exports,r),a.exports}(()=>{"use strict";const e=-32603,t=-32602,n=-32601,s=(e,t)=>Object.assign(new Error(e),{status:t}),a=t=>s('The handler of the method called "'.concat(t,'" returned an unexpected result.'),e),o=(t,r)=>async o=>{let{data:{id:i,method:l,params:c}}=o;const u=r[l];try{if(void 0===u)throw(e=>s('The requested method called "'.concat(e,'" is not supported.'),n))(l);const r=void 0===c?u():u(c);if(void 0===r)throw(t=>s('The handler of the method called "'.concat(t,'" returned no required result.'),e))(l);const o=r instanceof Promise?await r:r;if(null===i){if(void 0!==o.result)throw a(l)}else{if(void 0===o.result)throw a(l);const{result:e,transferables:r=[]}=o;t.postMessage({id:i,result:e},r)}}catch(e){const{message:r,status:n=-32603}=e;t.postMessage({error:{code:n,message:r},id:i})}};var i=r(455);const l=new Map,c=(e,r,n)=>({...r,connect:t=>{let{port:n}=t;n.start();const s=e(n,r),a=(0,i.generateUniqueNumber)(l);return l.set(a,(()=>{s(),n.close(),l.delete(a)})),{result:a}},disconnect:e=>{let{portId:r}=e;const n=l.get(r);if(void 0===n)throw(e=>s('The specified parameter called "portId" with the given value "'.concat(e,'" does not identify a port connected to this worker.'),t))(r);return n(),{result:null}},isSupported:async()=>{if(await new Promise((e=>{const t=new ArrayBuffer(0),{port1:r,port2:n}=new MessageChannel;r.onmessage=t=>{let{data:r}=t;return e(null!==r)},n.postMessage(t,[t])}))){const e=n();return{result:e instanceof Promise?await e:e}}return{result:!1}}}),u=function(e,t){const r=c(u,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>!0),n=o(e,r);return e.addEventListener("message",n),()=>e.removeEventListener("message",n)},d=e=>e.reduce(((e,t)=>e+t.length),0),h=(e,t)=>{const r=[];let n=0;e:for(;n<t;){const t=e.length;for(let s=0;s<t;s+=1){const t=e[s];void 0===r[s]&&(r[s]=[]);const a=t.shift();if(void 0===a)break e;r[s].push(a),0===s&&(n+=a.length)}}if(n>t){const s=n-t;r.forEach(((t,r)=>{const n=t.pop(),a=n.length-s;t.push(n.subarray(0,a)),e[r].unshift(n.subarray(a))}))}return r},f=new Map,m=(e=>(t,r,n)=>{const s=e.get(t);if(void 0===s){const s={channelDataArrays:n.map((e=>[e])),isComplete:!0,sampleRate:r};return e.set(t,s),s}return s.channelDataArrays.forEach(((e,t)=>e.push(n[t]))),s})(f),p=((e,t)=>(r,n,s,a)=>{const o=s>>3,i="subsequent"===n?0:44,l=r.length,c=e(r[0]),u=new ArrayBuffer(c*l*o+i),d=new DataView(u);return"subsequent"!==n&&t(d,s,l,"complete"===n?c:Number.POSITIVE_INFINITY,a),r.forEach(((e,t)=>{let r=i+t*o;e.forEach((e=>{const t=e.length;for(let n=0;n<t;n+=1){const t=e[n];d.setInt16(r,t<0?32768*Math.max(-1,t):32767*Math.min(1,t),!0),r+=l*o}}))})),[u]})(d,((e,t,r,n,s)=>{const a=t>>3,o=Math.min(n*r*a,4294967251);e.setUint32(0,1380533830),e.setUint32(4,o+36,!0),e.setUint32(8,1463899717),e.setUint32(12,1718449184),e.setUint32(16,16,!0),e.setUint16(20,1,!0),e.setUint16(22,r,!0),e.setUint32(24,s,!0),e.setUint32(28,s*r*a,!0),e.setUint16(32,r*a,!0),e.setUint16(34,t,!0),e.setUint32(36,1684108385),e.setUint32(40,o,!0)})),v=new Map;u(self,{characterize:()=>({result:/^audio\\/wav$/}),encode:e=>{let{recordingId:t,timeslice:r}=e;const n=v.get(t);void 0!==n&&(v.delete(t),n.reject(new Error("Another request was made to initiate an encoding.")));const s=f.get(t);if(null!==r){if(void 0===s||d(s.channelDataArrays[0])*(1e3/s.sampleRate)<r)return new Promise(((e,n)=>{v.set(t,{reject:n,resolve:e,timeslice:r})}));const e=h(s.channelDataArrays,Math.ceil(r*(s.sampleRate/1e3))),n=p(e,s.isComplete?"initial":"subsequent",16,s.sampleRate);return s.isComplete=!1,{result:n,transferables:n}}if(void 0!==s){const e=p(s.channelDataArrays,s.isComplete?"complete":"subsequent",16,s.sampleRate);return f.delete(t),{result:e,transferables:e}}return{result:[],transferables:[]}},record:e=>{let{recordingId:t,sampleRate:r,typedArrays:n}=e;const s=m(t,r,n),a=v.get(t);if(void 0!==a&&d(s.channelDataArrays[0])*(1e3/r)>=a.timeslice){const e=h(s.channelDataArrays,Math.ceil(a.timeslice*(r/1e3))),n=p(e,s.isComplete?"initial":"subsequent",16,r);s.isComplete=!1,v.delete(t),a.resolve({result:n,transferables:n})}return{result:null}}})})()})();`,S0=new Blob([y0],{type:"application/javascript; charset=utf-8"}),qo=URL.createObjectURL(S0),E0=w0(qo),T0=E0.connect;URL.revokeObjectURL(qo);const M0={key:0,class:"overlay"},A0={key:0},x0={key:1},C0={key:2,class:"my-2"},k0={class:"recorded_buttons"},P0=!1,I0={__name:"VoiceTemplate",props:{file:{type:String,required:!0},id:{type:Number,required:!0}},emits:["fileChanged","delete"],async setup(t,{emit:e}){let n,s;const r=t,i=e,l=Ue(""),c=Ue(""),a=Ue(null),u=Ue(!1),d=Ue(null),o=Ue(null),p=Ue([]),_=Ue(!1);nn(()=>{if(r.file!=null){const m=r.file.split("/"),x=m[m.length-1];x!="loading"&&(c.value=x)}}),Nt(()=>r.file,m=>{if(m!=null){const x=m.split("/"),f=x[x.length-1];f!="loading"?(c.value=f,_.value=!1):_.value=!0}});const w=async m=>{const x=m.target.files[0];if(console.log(x.type),x.size>26214400){l.value="حجم صدا حداکثر 25 مگابایت میباشد";return}i("fileChanged",x)};function b(){const m=document.createElement("a");m.href=r.file,m.target="_blank";const x=r.file.split("/"),f=x[x.length-1];m.download=f,document.body.appendChild(m),m.click(),document.body.removeChild(m)}const g=async()=>{i("delete")};try{[n,s]=vi(async()=>v0(([n,s]=vi(()=>T0()),n=await n,s(),n))),await n,s()}catch{}const E=async()=>{try{const m=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,volume:1},video:!1});let x={},f={},A="";yr.isTypeSupported("audio/wav")?(x={mimeType:"audio/wav"},f={type:"audio/wav"},A="recordedaudio.wav"):yr.isTypeSupported("audio/mp4;codecs=pcm")&&(x={audioBitsPerSecond:32e3,mimeType:"audio/mp4;codecs=pcm"},f={type:"audio/mp4;codecs=pcm"},A="recordedaudio.mp4"),d.value=new yr(m,x),p.value=[],d.value.ondataavailable=D=>{p.value.push(D.data)},d.value.onstop=async()=>{const D=d.mimeType,V=new Blob(p.value,{type:D});m&&m.getTracks().forEach(O=>O.stop()),i("fileChanged",Qo(V,A))},d.value.start(),u.value=!0}catch(m){u.value=!1,console.error("Error in starting recording: ",m),l.value="لطفا دسترسی به میکروفون را باز کنید"}},T=()=>{d.value&&d.value.state==="recording"&&(d.value.stop(),u.value=!1)};return(m,x)=>{const f=ss("IconBtn");return _e(),Oe(Xt,null,{default:De(()=>[je(f,{class:"close-btn",size:"22"},{default:De(()=>[je(dt,{icon:"tabler-x",size:"20",onClick:g})]),_:1}),je(bt,null,{default:De(()=>[Ne("div",{ref_key:"voiceDiv",ref:a,class:Ut(["voice-input",{recording:Ve(u)}])},[P0?(_e(),Ge("div",M0)):Ye("",!0),Ve(_)?(_e(),Oe(fn,{key:2,indeterminate:""})):(_e(),Ge(mt,{key:1},[Ve(u)?(_e(),Oe(on,{key:1,class:"mt-1 cursor-pointer",block:"",onClick:T},{default:De(()=>[cn(" توقف ضبط ")]),_:1})):(_e(),Ge(mt,{key:0},[t.file?Ye("",!0):(_e(),Ge("input",{key:0,class:"d-none",ref_key:"fileInput",ref:o,type:"file",accept:"audio/webm,audio/ogg,audio/aac,audio/m4a,audio/wav,audio/mp4",onInput:w},null,544)),t.file?Ye("",!0):(_e(),Oe(on,{key:1,block:"",class:"mt-4 mb-1 cursor-pointer",onClick:x[0]||(x[0]=A=>m.$refs.fileInput.click())},{default:De(()=>[Ve(c)==""?(_e(),Ge("span",A0," آپلود صدا ")):(_e(),Ge("span",x0,Dt(Ve(c)),1))]),_:1})),t.file?Ye("",!0):(_e(),Ge("span",C0,"یا")),t.file?Ye("",!0):(_e(),Oe(on,{key:3,block:"",class:"mt-1 cursor-pointer",onClick:E},{default:De(()=>[cn(" ضبط صدا ")]),_:1})),Ne("div",k0,[t.file?(_e(),Oe(on,{key:0,class:"mt-3 cursor-pointer",onClick:x[1]||(x[1]=A=>m.$emit("fileChanged",""))},{default:De(()=>[cn(" حذف صدا ")]),_:1})):Ye("",!0),t.file&&r.id!==-1?(_e(),Oe(on,{key:1,class:"mt-3 cursor-pointer",onClick:b},{default:De(()=>[je(dt,{icon:"tabler-download",size:"20"})]),_:1})):Ye("",!0)])],64))],64))],2),Ve(l)?(_e(),Oe($r,{key:0,class:"mt-4",type:"error",onClick:x[2]||(x[2]=A=>l.value="")},{default:De(()=>[cn(Dt(Ve(l)),1)]),_:1})):Ye("",!0)]),_:1})]),_:1})}}},R0=At(I0,[["__scopeId","data-v-b887d728"]]);const B0={key:0,class:"overlay"},_a=!1,L0={__name:"VideoTemplate",props:{file:{type:String,required:!0},id:{type:Number,default:-1}},emits:["fileChanged","delete"],setup(t,{emit:e}){const n=t,s=e,r=Ue(""),i=Ue(""),l=Ue(null),c=Ue(!1);nn(()=>{if(n.file!=null){const d=n.file.split("/"),o=d[d.length-1];o!="loading"&&(i.value=o)}}),Nt(()=>n.file,d=>{if(d!=null){const o=d.split("/"),p=o[o.length-1];p!="loading"?(i.value=p,c.value=!1):c.value=!0}});const a=async d=>{d.target.value=""},u=async d=>{const o=d.target.files[0];if(o.size>26214400){r.value="حجم فیلم حداکثر 25 مگابایت میباشد";return}o&&s("fileChanged",o)};return(d,o)=>{const p=ss("IconBtn");return _e(),Oe(Xt,null,{default:De(()=>[je(p,{size:"22"},{default:De(()=>[je(dt,{icon:"tabler-x",size:"20",onClick:o[0]||(o[0]=_=>d.$emit("delete"))})]),_:1}),Ne("input",{ref:"fileInput",type:"file",class:"d-none",accept:".mp4,.mov,video/webm,.ogg,video/avi",onClick:a,onChange:u},null,544),je(bt,null,{default:De(()=>[Ne("div",{ref_key:"videoDiv",ref:l,class:"video-input",onClick:o[1]||(o[1]=_=>_a?!0:d.$refs.fileInput.click())},[_a?(_e(),Ge("div",B0)):Ye("",!0),Ve(c)?(_e(),Oe(fn,{key:2,indeterminate:""})):(_e(),Ge(mt,{key:1},[je(dt,{icon:"tabler-video",size:"40"}),Ve(i)==""?(_e(),Oe(on,{key:0,block:"",class:"mt-2 text-center"},{default:De(()=>[cn(" آپلود فیلم ")]),_:1})):(_e(),Oe(on,{key:1,block:"",class:"mt-4 text-center"},{default:De(()=>[cn(Dt(Ve(i)),1)]),_:1}))],64))],512),Ve(r)?(_e(),Oe($r,{key:0,class:"mt-4",type:"error",onClick:o[2]||(o[2]=_=>r.value="")},{default:De(()=>[cn(Dt(Ve(r)),1)]),_:1})):Ye("",!0)]),_:1})]),_:1})}}},O0=At(L0,[["__scopeId","data-v-41ceaebf"]]);const D0=t=>(Rs("data-v-34e36a3e"),t=t(),Bs(),t),N0={key:0,class:"overlay"},z0=D0(()=>Ne("span",null," آپلود عکس ",-1)),$0=!1,V0={__name:"ImageTemplate",props:{file:{type:String,required:!0},id:{type:Number,required:!0},hasClose:{type:Boolean,default:!0},isUpload:{type:Boolean,default:!0}},emits:["fileChanged","delete"],setup(t,{emit:e}){const n=t,s=e,r=Ue(""),i=Ue(null),l=Ue(!1),c=Ue(null);Nt(()=>n.file,o=>{const p=i.value.$el||i.value;if(o=="")l.value=!1;else if(p){if(n.isUpload){const _=o.split("/");if(_[_.length-1]=="loading"){p.style.background="none",l.value=!0;return}}p.style.setProperty("background",`url(${n.file})`,"important"),p.style.setProperty("background-position","center center","important"),p.style.setProperty("background-repeat","no-repeat","important"),p.style.setProperty("background-size","contain","important"),l.value=!1}}),nn(()=>{const o=i.value.$el||i.value;o&&n.file!=""&&(o.style.setProperty("background",`url(${n.file})`,"important"),o.style.setProperty("background-position","center center","important"),o.style.setProperty("background-repeat","no-repeat","important"),o.style.setProperty("background-size","contain","important"))});const a=async o=>{const p=o.target.files[0];if(p.size>26214400){r.value="حجم عکس حداکثر 25 مگابایت میباشد";return}if(n.isUpload)s("fileChanged",p);else{const _=new FileReader;_.onload=w=>{s("fileChanged",{file:w.target.result,address:p})},_.readAsDataURL(p)}},u=()=>{l.value||c.value.click()},d=()=>{s("delete")};return(o,p)=>{const _=ss("IconBtn");return _e(),Oe(Xt,null,{default:De(()=>[t.hasClose?(_e(),Oe(_,{key:0,class:"close-btn",size:"22"},{default:De(()=>[je(dt,{icon:"tabler-x",size:"20",onClick:d})]),_:1})):Ye("",!0),Ne("input",{ref_key:"fileInput",ref:c,type:"file",class:"d-none",accept:"image/jpeg,image/jpg,image/png,image/gif",onInput:a},null,544),je(bt,null,{default:De(()=>[Ne("div",{ref_key:"imageDiv",ref:i,class:"image-input",onClick:u},[$0?(_e(),Ge("div",N0)):Ye("",!0),Ve(l)?(_e(),Oe(fn,{key:2,indeterminate:""})):(_e(),Ge(mt,{key:1},[t.file==""?(_e(),Oe(dt,{key:0,icon:"tabler-photo",size:"40"})):Ye("",!0),t.file==""?(_e(),Oe(on,{key:1,block:"",class:"mt-2"},{default:De(()=>[z0]),_:1})):Ye("",!0)],64))],512),Ve(r)?(_e(),Oe($r,{key:0,class:"mt-4",type:"error",onClick:p[0]||(p[0]=w=>r.value="")},{default:De(()=>[cn(Dt(Ve(r)),1)]),_:1})):Ye("",!0)]),_:1})]),_:1})}}},H0=At(V0,[["__scopeId","data-v-34e36a3e"]]);const Uo=t=>(Rs("data-v-e47981c8"),t=t(),Bs(),t),X0={class:"component-container"},F0={key:0,class:"overlay"},Y0={key:0,class:"loader"},W0=["value"],G0=["value","onInput"],j0=Uo(()=>Ne("span",null,"+",-1)),q0=Uo(()=>Ne("span",null,"اضافه کردن دکمه",-1)),U0=[j0,q0],Z0=!1,Q0={__name:"TextTemplate",props:{buttons:{type:[Array,Object],required:!0},id:{type:Number,required:!0},text:{type:String,required:!0},cantAddButtonToText:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},buttonLimit:{type:Number,default:12},hasClose:{type:Boolean,default:!0}},emits:["textChanged","addButton","removeButton","buttonChanged","delete"],setup(t,{emit:e}){const n=t,s=e,r=Ue(null),i=()=>{s("delete")};return nn(()=>{const l=r.value;l.style.height="auto"}),(l,c)=>{const a=ss("IconBtn");return _e(),Oe(Xt,null,{default:De(()=>[t.hasClose?(_e(),Oe(a,{key:0,class:"close-btn",size:"22"},{default:De(()=>[je(dt,{icon:"tabler-x",size:"20",onClick:i})]),_:1})):Ye("",!0),je(bt,null,{default:De(()=>[Ne("div",X0,[Z0?(_e(),Ge("div",F0)):Ye("",!0),Ne("div",{class:Ut(["textarea",{round_bottom:t.cantAddButtonToText}])},[n.loading?(_e(),Ge("div",Y0)):Ye("",!0),Ne("textarea",{ref_key:"textareaRef",ref:r,value:n.text,maxlength:"1000",placeholder:"متن خود را اینجا وارد کنید ...",onInput:c[0]||(c[0]=u=>l.$emit("textChanged",u.target.value))},null,40,W0)],2),je(Xt,null,{default:De(()=>[je(bt,{cols:"12"},{default:De(()=>[t.cantAddButtonToText?Ye("",!0):(_e(!0),Ge(mt,{key:0},Jt(n.buttons,(u,d)=>(_e(),Ge("div",{key:d,class:Ut(["button_declaration",{round_bottom:d==t.buttonLimit}])},[je(Xt,null,{default:De(()=>[je(bt,{cols:"10",class:"button_name pe-0"},{default:De(()=>[Ne("input",{maxlength:"30",type:"text",value:u,onInput:o=>l.$emit("buttonChanged",{value:o.target.value,index:d})},null,40,G0)]),_:2},1024),je(bt,{class:"align-center justify-center d-flex pe-0 ps-0",style:{"margin-inline-end":"12px"},onClick:c[1]||(c[1]=o=>l.$emit("removeButton"))},{default:De(()=>[je(dt,{icon:"tabler-x"})]),_:1})]),_:2},1024)],2))),128)),t.cantAddButtonToText?Ye("",!0):(_e(),Ge("div",{key:1,class:Ut(["add_button",{"d-none":n.buttons.length>t.buttonLimit}]),onClick:c[2]||(c[2]=u=>l.$emit("addButton"))},U0,2))]),_:1})]),_:1})])]),_:1})]),_:1})}}},K0=At(Q0,[["__scopeId","data-v-e47981c8"]]);const J0={class:"text-inputs"},eh={class:"title"},th={class:"description"},nh={class:"button_name"},sh=["href"],rh={__name:"CardTemplateSwiperSlidePreview",props:{image:{type:String,allowNull:!0},description:{type:String,allowNull:!0},title:{type:String,allowNull:!0},card_item_keys:{type:[Array,Object,null]},template_card_id:Number,id:Number},setup(t){const e=t,n=Ue(null),s=Ue(!1);return nn(()=>{const r=n.value.$el||n.value;if(r&&e.image!=""){const i=Sn(e.image,"images");r.style.background=`url(${i})`,r.style.backgroundPosition="center center",r.style.backgroundRepeat="no-repeat",r.style.backgroundSize="contain"}}),Nt(()=>e.image,()=>{const r=n.value.$el||n.value;if(e.image=="")s.value=!1;else if(r){const i=Sn(e.image,"images"),l=i.split("/");if(l[l.length-1]=="loading"){r.style.background="none",s.value=!0;return}s.value=!1,r.style.background=`url(${i})`,r.style.backgroundPosition="center center",r.style.backgroundRepeat="no-repeat",r.style.backgroundSize="contain"}}),(r,i)=>{var l;return _e(),Ge(mt,null,[Ne("div",{class:Ut(["card",{bben:((l=e.card_item_keys)==null?void 0:l.length)>0}])},[Ne("div",{ref_key:"imageDiv",ref:n,class:"upload-area"},[(t.image==""||t.image==null)&&!Ve(s)?(_e(),Oe(dt,{key:0,icon:"tabler-photo",size:"40"})):Ye("",!0),Ve(s)?(_e(),Oe(fn,{key:1,indeterminate:""})):Ye("",!0)],512),Ne("div",J0,[Ne("p",eh,Dt(e.title),1),Ne("p",th,Dt(e.description),1)])],2),(_e(!0),Ge(mt,null,Jt(e.card_item_keys,(c,a)=>(_e(),Ge("div",{key:a,class:Ut(["button_declaration",{round_bottom:a==e.card_item_keys.length-1}])},[Ne("div",nh,[Ne("a",{href:("ensureCompleteUrl"in r?r.ensureCompleteUrl:Ve(Ko))(c[1]),target:"blank"},Dt(c[0]),9,sh)])],2))),128))],64)}}},ih=At(rh,[["__scopeId","data-v-ec0e13bf"]]);const ah=["slides-per-view"],oh={__name:"CardTemplatePreview",props:{slides:{type:Array,required:!0}},setup(t){const e=t;Aa();const n=Ue(null);return nn(()=>{e.slides.forEach(s=>{try{s.card_item_keys=JSON.parse(s.card_item_keys)}catch(r){console.log("some error happend in json parsing card keys"),console.log("error : ",r),console.log("card_item_keys : ",s.card_item_keys)}})}),Nt(()=>e.slides.length,()=>{n.value&&Er(()=>{n.value.swiper.update(),n.value.swiper.slideNext()})}),Nt(()=>e.slides,()=>{n.value&&Er(()=>{n.value.swiper.update()})}),(s,r)=>{const i=ih;return _e(),Oe(Xt,{class:"card-template-preview"},{default:De(()=>[je(bt,null,{default:De(()=>[Ne("swiper-container",{ref_key:"swiperEl",ref:n,"events-prefix":"swiper-","slides-per-view":e.slides.length>1?1.4:1,navigation:"true"},[(_e(!0),Ge(mt,null,Jt(e.slides,(l,c)=>(_e(),Ge("swiper-slide",{key:c},[je(i,{image:l.image,description:l.description,title:l.title,card_item_keys:l.card_item_keys,template_card_id:l.template_card_id},null,8,["image","description","title","card_item_keys","template_card_id"])]))),128))],8,ah)]),_:1})]),_:1})}}},lh=At(oh,[["__scopeId","data-v-eb0b0827"]]);const ch={class:"audio-player"},uh={key:0},dh={key:1,class:"progress-bar"},fh=["src"],ph={__name:"VoiceTemplatePreview",props:{file:{type:String,required:!0}},setup(t){const e=t,n=Ue(!1),s=Ue(!1),r=Ue(null),i=Ue(0),l=Ue(!1),c=Ue(0);let a=null;Nt(()=>e.file,g=>{if(g!=null){const E=g.split("/");E[E.length-1]!="loading"?l.value=!1:l.value=!0}});const u=()=>{r.value&&e.file!=""&&(r.value.play(),n.value=!0,a=setInterval(w,1e3))},d=()=>{r.value&&(r.value.pause(),n.value=!1,clearInterval(a))};Jo(()=>{r.value&&r.value.pause()});const o=()=>{const g=parseInt((i.value-c.value).toFixed(0)),E=parseInt(g/60),T=parseInt(g%60);let m=`${E}`,x=`${T}`;return E<10&&(m=`0${m}`),T<10&&(x=`0${T}`),`${m}:${x}`},p=Kn(()=>c.value/i.value*100+"%");Nt(c,g=>{const E=document.getElementById("progress");E&&(E.style.width=p.value)});const _=()=>{r.value&&(i.value=r.value.duration,(!s.value&&r.value.duration===1/0||isNaN(Number(r.value.duration)))&&(r.value.currentTime=1e101,s.value=!0))},w=()=>{r.value&&(s.value&&(r.value.currentTime=0,s.value=!1,i.value=r.value.duration),c.value=r.value.currentTime)},b=()=>{n.value=!1,clearInterval(a),c.value=0};return(g,E)=>(_e(),Ge("div",ch,[Ve(l)?(_e(),Oe(fn,{key:1,indeterminate:""})):(_e(),Ge(mt,{key:0},[e.file!=""?(_e(),Ge("div",uh,Dt(o()),1)):Ye("",!0),e.file!=""?(_e(),Ge("div",dh,[Ne("div",{id:"progress",class:"progress",style:el({width:Ve(p)})},null,4)])):Ye("",!0),Ve(n)?(_e(),Ge("button",{key:3,onClick:d},[je(dt,{icon:"tabler-pause"})])):(_e(),Ge("button",{key:2,onClick:u},[je(dt,{icon:"tabler-play"})])),Ne("audio",{ref_key:"audioRef",ref:r,src:e.file,onLoadedmetadata:_,onTimeupdate:w,onEnded:b},null,40,fh)],64))]))}},hh=At(ph,[["__scopeId","data-v-d822ca57"]]);const mh={class:"video"},_h=["src"],gh={__name:"VideoTemplatePreview",props:{file:{type:String,required:!0}},setup(t){const e=t,n=Ue(null),s=Ue(!1),r=Ue(!1);Nt(()=>e.file,l=>{if(l!=null){const c=l.split("/");c[c.length-1]!="loading"?r.value=!1:r.value=!0}});const i=()=>{console.log(n.value.paused),n.value&&(n.value.paused?(n.value.play(),s.value=!0):(n.value.pause(),s.value=!1))};return(l,c)=>(_e(),Ge("div",mh,[Ve(r)?(_e(),Oe(fn,{key:1,indeterminate:""})):(_e(),Ge(mt,{key:0},[Ne("video",{ref_key:"videoPlayer",ref:n,src:e.file,onClick:i},null,8,_h),Ve(s)?Ye("",!0):(_e(),Oe(dt,{key:0,icon:"tabler-play",color:"white",class:"play_icon",onClick:i}))],64))]))}},vh=At(gh,[["__scopeId","data-v-f12161fe"]]);const bh={__name:"ImageTemplatePreview",props:{file:{type:String,required:!0}},setup(t){const e=t,n=Ue(null),s=Ue(!1);return nn(()=>{const r=n.value.$el||n.value;if(e.file=="")s.value=!1;else if(r){const i=e.file.split("/");if(i[i.length-1]=="loading"){r.style.background="none",s.value=!0;return}r.style.background=`url(${e.file})`,r.style.backgroundPosition="center center",r.style.backgroundRepeat="no-repeat",r.style.backgroundSize="contain",s.value=!1}}),tl(()=>{const r=n.value.$el||n.value;if(r&&e.file!=""){const i=e.file.split("/");if(i[i.length-1]=="loading"){r.style.background="none",s.value=!0;return}r.style.background=`url(${e.file})`,r.style.backgroundPosition="center center",r.style.backgroundRepeat="no-repeat",r.style.backgroundSize="contain",s.value=!1}}),(r,i)=>(_e(),Ge("div",{ref_key:"imageDiv",ref:n,class:"image"},[Ve(s)?(_e(),Oe(fn,{key:0,indeterminate:""})):Ye("",!0)],512))}},wh=At(bh,[["__scopeId","data-v-5b1ad66a"]]);const yh={__name:"TextTemplatePreview",props:{text:{type:String,required:!0},buttons:{type:[Array,Object],required:!0},reverse:Boolean,cantAddButtonToText:{type:Boolean,default:!1}},setup(t){const e=t;return(n,s)=>(_e(),Oe(Xt,{class:Ut({reverse:t.reverse})},{default:De(()=>[Ne("div",{class:Ut(["textarea",{round_bottom:t.cantAddButtonToText}])},Dt(e.text==""?"...":e.text),3),t.cantAddButtonToText?Ye("",!0):(_e(!0),Ge(mt,{key:0},Jt(e.buttons,(r,i)=>(_e(),Oe(bt,{key:i,class:"button_name",cols:"12"},{default:De(()=>[Ne("span",null,Dt(r),1)]),_:2},1024))),128))]),_:1},8,["class"]))}},Sh=At(yh,[["__scopeId","data-v-18db067b"]]),Eh="/assets/phone_things-a82da5cb.svg";const di=t=>(Rs("data-v-fc980c4d"),t=t(),Bs(),t),Th={class:"d-flex justify-center"},Mh={class:"mt-1 me-auto"},Ah=["src"],xh=di(()=>Ne("div",{class:"middle_top"},null,-1)),Ch=di(()=>Ne("span",{class:"text-left mt-1",style:{"flex-grow":"0.1"}},"4:17",-1)),kh={class:"insta_header"},Ph=di(()=>Ne("span",{style:{"font-size":"15px"}},"پیش نمایش",-1)),Ih=["src"],Rh={__name:"MessagePreview",props:{templates:{type:[Array,Object],required:!0},cantAddButtonToText:{type:Boolean,default:!1}},setup(t){return(e,n)=>{const s=Sh,r=wh,i=vh,l=hh,c=lh;return _e(),Oe(bt,{cols:"12",md:"5"},{default:De(()=>[je(ns,{class:"layer1"},{default:De(()=>[Ne("div",Th,[Ne("div",Mh,[Ne("img",{src:Ve(Eh),style:{"flex-grow":"0.05","object-fit":"contain"}},null,8,Ah)]),xh,Ch]),Ne("div",kh,[Ph,Ne("img",{src:Ve(ll),alt:""},null,8,Ih)]),(_e(!0),Ge(mt,null,Jt(t.templates,(a,u)=>(_e(),Oe(bt,{key:u,cols:"12"},{default:De(()=>[a.type=="text"||a.type=="comment"?(_e(),Oe(s,{key:0,"cant-add-button-to-text":t.cantAddButtonToText,text:a.text??"",buttons:a.buttons??[]},null,8,["cant-add-button-to-text","text","buttons"])):Ye("",!0),a.type=="image"?(_e(),Oe(r,{key:1,file:("getUploadedPath"in e?e.getUploadedPath:Ve(Sn))(a.file,"images")},null,8,["file"])):Ye("",!0),a.type=="video"?(_e(),Oe(i,{key:2,file:("getUploadedPath"in e?e.getUploadedPath:Ve(Sn))(a.file,"images")},null,8,["file"])):Ye("",!0),a.type=="audio"?(_e(),Oe(l,{key:3,file:("getUploadedPath"in e?e.getUploadedPath:Ve(Sn))(a.file,"voices")},null,8,["file"])):Ye("",!0),a.type=="card"?(_e(),Oe(c,{key:4,slides:a.slides},null,8,["slides"])):Ye("",!0)]),_:2},1024))),128))]),_:1})]),_:1})}}},Bh=At(Rh,[["__scopeId","data-v-fc980c4d"]]);const Lh={__name:"PreviewFloatingButton",props:{templates:{type:[Array,Object],required:!0}},setup(t){const e=Ue(!1);return(n,s)=>{const r=cl,i=Bh;return _e(),Oe(sl,{modelValue:Ve(e),"onUpdate:modelValue":s[1]||(s[1]=l=>nl(e)?e.value=l:null),width:"500",scrollable:"","content-class":"scrollable-dialog"},{activator:De(({props:l})=>[]),default:De(()=>[je(r,{onClick:s[0]||(s[0]=l=>e.value=!Ve(e))}),je(ns,null,{default:De(()=>[je(i,{templates:t.templates},null,8,["templates"])]),_:1})]),_:1},8,["modelValue"])}}},Oh={key:0},Sr="https://prest.manymessage.com",Jh={__name:"MessageBox",props:{product_id:{type:Number,default:-1},templates:{type:Array,required:!0},cantAddButtonToText:{type:Boolean,default:!0},templateButtonsList:{type:Array,required:!0},hasCommandTrigger:Boolean,hasClose:{type:Boolean,default:!0}},emits:["templatesChanged","delete"],setup(t,{expose:e,emit:n}){const s=t,r=n,i=Kn(()=>{let L=-1;for(let I=0;I<s.templates.length;I++)s.templates[I].type=="text"&&(L=I);return L});rl();function l(L,I=1500){let h;return(...k)=>{clearTimeout(h),h=setTimeout(()=>{L.apply(this,k)},I)}}const c=l(async(L,I)=>{const h={title:"عملیات تغییر متن با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"},k=u.value[I].id;if(console.log(u.value),console.log("media value"),console.log(L),u.value[I].value=a(L),console.log(u.value[I].value),console.log(I),console.log(u.value[I]),s.product_id!==-1){u.value[I].loading=!0;try{if(k===-1){const H=await pt.post(mn.productsMedia,{type:"text",product_id:s.product_id,text:u.value[I].value});u.value[I].id=H.data.id,u.value[I].value=H.data.value}else{const H=await pt.post(mn.productsMedia+"/"+k,{type:"text",product_id:s.product_id,text:u.value[I].value});u.value[I].value=H.data.value}}catch(H){Je(h),console.error(H)}u.value[I].loading=!1}}),a=(L,I)=>L;Nt(()=>s.templates,()=>{u.value=s.templates});const u=Ue(s.templates);e({saveAllMedia:async L=>{console.log(u.value);for(const I of u.value){let h,k;if(I.type==="text")I.loading=!0,await pt.post(mn.productsMedia,{type:"text",product_id:L,text:I.value}),I.loading=!1;else{if(I.value=="")continue;try{const H=new FormData;H.append("type",I.type),H.append("product_id",L),H.append("file",I.value),h=I.value,(I.type==="image"||I.type==="video")&&(k=I.preview,I.preview="loading"),I.value="loading";let R;R=await pt.post(mn.productsMedia,H,{headers:{"Content-Type":"multipart/form-data"}}),(I.type==="image"||I.type==="video")&&(I.preview=Sr+R.data.value),I.value=Sr+R.data.value}catch{I.value=h,(I.type==="image"||I.type==="video")&&(I.preview=k)}}}}});const o=async L=>{if(u.value[L].buttons==null||u.value[L].buttons.length<13){const I={title:"عملیات اضافه کردن دکمه با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"},h=u.value[L].id;u.value[L].buttons==null?u.value[L].buttons=["دکمه جدید 1"]:u.value[L].buttons.push(`دکمه جدید ${u.value[L].buttons.length+1}`);try{(await pt(`/templateText/${h}`,{method:"PATCH",body:{text_keys:u.value[L].buttons,command_id:s.product_id}})).success||(Je(I),u.value[L].buttons.pop())}catch(k){Je(I),u.value[L].buttons.pop(),console.error(k)}}},p=async(L,I)=>{const h=u.value[I].slides[L].card_item_keys,k={title:"عملیات اضافه کردن دکمه کارت  با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"},H=u.value[I].slides[L].id;if(h==null||h.length<3){h==null?u.value[I].slides[L].card_item_keys=[["دکمه جدید 1",""]]:u.value[I].slides[L].card_item_keys.push([`دکمه جدید ${h.length+1}`,""]);try{(await pt(`/templateCardItems/${H}`,{method:"PATCH",body:{card_item_keys:u.value[I].slides[L].card_item_keys}})).success||(Je(k),u.value[I].slides[L].card_item_keys.pop())}catch(R){Je(k),u.value[I].slides[L].card_item_keys.pop(),console.error(R)}}},_=async(L,I)=>{const h={title:"عملیات حذف دکمه با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"},k=u.value[I].id,H=u.value[I].buttons;console.log(u.value[I].buttons),u.value[I].buttons.splice(L,1);try{(await pt(`/templateText/${k}`,{method:"PATCH",body:{text_keys:u.value[I].buttons,command_id:s.product_id}})).success||(Je(h),u.value[I].buttons=H)}catch(R){Je(h),u.value[I].buttons=H,console.error(R)}},w=async(L,I)=>{const h={title:"عملیات حذف دکمه ویترین با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"},k=u.value[I].slides[L.slideIndex].id,H=u.value[I].slides[L.slideIndex].card_item_keys;u.value[I].slides[L.slideIndex].card_item_keys.splice(L.buttonIndex,1);try{(await pt(`/templateCardItems/${k}`,{method:"PATCH",body:{card_item_keys:u.value[I].slides[L.slideIndex].card_item_keys}})).success||(Je(h),u.value[I].slides[L.slideIndex].card_item_keys=H)}catch(R){Je(h),u.value[I].slides[L.slideIndex].card_item_keys=H,console.error(R)}},b=l(async(L,I)=>{const h={title:"عملیات تغییر متن دکمه با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"},k=u.value[I].id,H=u.value[I].buttons[L.index];u.value[I].buttons[L.index]=al(L.value);try{(await pt(`/templateText/${k}`,{method:"PATCH",body:{text_keys:u.value[I].buttons,command_id:s.product_id}})).success||(Je(h),u.value[I].buttons[L.index]=H)}catch(R){Je(h),u.value[I].buttons[L.index]=H,console.error(R)}}),g=async(L,I)=>{const h=u.value[L].id;u.value[L].buttons=I;try{await pt(`/templateText/${h}`,{method:"PATCH",body:{text_keys:I,command_id:s.product_id}})}catch(k){console.error(k)}},E=l(async(L,I)=>{const h={title:"عملیات تغییر متن دکمه کارت با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"};console.log(L);const k=u.value[I].slides[L.slideIndex].card_item_keys[L.buttonIndex];if(L.value[1]!=k[1]&&!ol(L.value[1])){h.title="فرمت لینک صحیح نمیباشد",Je(h);return}const H=u.value[I].slides[L.slideIndex].id,R=u.value[I].slides[L.slideIndex].card_item_keys[L.index];u.value[I].slides[L.slideIndex].card_item_keys[L.buttonIndex]=L.value;try{(await pt(`/templateCardItems/${H}`,{method:"PATCH",body:{card_item_keys:u.value[I].slides[L.slideIndex].card_item_keys}})).success||(Je(h),u.value[I].slides[L.slideIndex].card_item_keys[L.index]=R)}catch(F){Je(h),u.value[I].slides[L.slideIndex].card_item_keys[L.index]=R,console.error(F)}}),T=async L=>{const I=u.value.filter((h,k)=>k===L)[0];if(console.log(I),I.id!==-1)try{const h=await pt.delete(mn.productsMedia+"/"+I.id);u.value=u.value.filter((k,H)=>H!==L)}catch{Je({title:"عملیات حذف با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"})}else u.value=u.value.filter((h,k)=>k!==L);r("delete",L)},m=async(L,I)=>{if(u.value[I].slides.length>1){const k=u.value[I].slides[L].id,H={title:"عملیات حذف اسلاید کارت با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"},R=u.value[I].slides;u.value[I].slides.splice(L,1);try{(await pt(`/templateCardItems/${k}`,{method:"DELETE"})).success||(Je(H),u.value[I].slides=R)}catch(F){Je(H),u.value[I].slides=R,console.error(F)}return}if(u.value[I].id==-1){Je({title:"لطفا قبل از حذف کردن کارت جدید کمی صبر کنید",icon:"error",confirmButtonText:"باشه"});return}r("delete",I)},x=async(L,I)=>{var R;if(L==""){u.value[I].value="";return}const h=u.value[I].type,k=u.value[I].id;let H;if(s.product_id===-1){u.value[I].value=L,h==="image"&&(u.value[I].preview=URL.createObjectURL(L)),h==="video"&&(u.value[I].preview=L.name);return}try{const F=new FormData;F.append("type",h),F.append("product_id",s.product_id),F.append("file",L),H=u.value[I].value,u.value[I].value="loading";let Y;k==-1?Y=await pt.post(mn.productsMedia,F,{headers:{"Content-Type":"multipart/form-data"}}):Y=await pt.post(mn.productsMedia+"/"+k,F,{headers:{"Content-Type":"multipart/form-data"}}),console.log(Y),u.value[I].value=Y.data.value,u.value[I].id=Y.data.id}catch(F){const Y={title:((R=F==null?void 0:F.data)==null?void 0:R.message)||"عملیات آپلود با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"};Je(Y),u.value[I].value=H,console.log(F)}},f=async L=>{const I=u.value[L].slides[0].template_card_id;if(console.log(I),I==-1){Je({title:"لطفا قبل از اضافه کردن اسلاید جدید کمی صبر کنید",icon:"error",confirmButtonText:"باشه"});return}u.value[L].slides.push({image:"",template_card_id:I,description:"",id:-1,title:"",card_item_keys:[]});const h=u.value[L].slides.length-1,k={title:"عملیات اضافه کردن اسلاید با خطا مواجه شد",icon:"error",confirmButtonText:"باشه"};try{const H=await pt("/templateCardItems",{method:"POST",body:{template_card_id:I}});H.success?u.value[L].slides[h].id=H.data.id:(Je(k),u.value[L].slides.pop())}catch(H){Je(k),u.value[L].slides.pop(),console.error(H)}},A=async(L,I)=>{let h="";I.where=="image"?h="عکس":I.where=="title"?h="تیتر":h="متن";const k={title:`عملیات تغییر ${h} کارت با خطا مواجه شد`,icon:"error",confirmButtonText:"باشه"},R=[...u.value][L].slides[I.index];console.log(R);const F=R[I.where],Y=R.id;I.where!="image"?(u.value[L].slides[I.index][I.where]=I.value,D(L,Y,I,F,k)):(u.value[L].slides[I.index][I.where]="loading",console.log("index is : ",L),console.log("slideId is : ",Y),console.log("args.where is : ",I),console.log("temp is : ",R),V(L,Y,I,F,k))},D=l(async(L,I,h,k,H)=>{await V(L,I,h,k,H)}),V=async(L,I,h,k,H)=>{try{const R=new FormData;R.append("_method","PATCH"),R.append(h.where,h.value);const F=await pt(`/templateCardItems/${I}`,{method:"POST",body:R});F.success?h.where=="image"&&(u.value[L].slides[h.index][h.where]=F.data[h.where]):(Je(H),u.value[L].slides[h.index][h.where]=k)}catch(R){Je(H),u.value[L].slides[h.index][h.where]=k,console.error(R)}},O=Kn(()=>s.hasCommandTrigger&&s.product_id=="new"),q=async L=>{if(!s.hasCommandTrigger||s.product_id=="new"){Je({title:"برای ساخت  پاسخ باید حداقل یه فعال کننده داشته باشید",icon:"error",confirmButtonText:"باشه"});return}if(L=="addText"){let I=[];u.value.push({text:"",id:-1,buttons:I,type:"text"}),il(I)||g(u.value.length-1,I)}L=="addImage"&&u.value.push({id:-1,file:"",type:"image"}),L=="addVideo"&&u.value.push({file:"",id:-1,value:"",type:"video"}),L=="addVoice"&&u.value.push({file:"",value:"",id:-1,type:"audio"}),L=="addCard"&&u.value.push({slides:[{image:"",template_card_id:-1,description:"",id:-1,title:"",card_item_keys:[]}],id:-1,type:"card"}),r("templatesChanged",u.value)},N=L=>L===void 0?"":L===""||L==="loading"?L:Sr+L;return(L,I)=>{const h=Lh,k=K0,H=H0,R=O0,F=R0,Y=Pu,X=fu;return _e(),Oe(bt,{cols:"12",md:"5"},{default:De(()=>[L.$vuetify.display.width<960?(_e(),Oe(h,{key:0,templates:Ve(u),class:"mb-4"},null,8,["templates"])):Ye("",!0),je(ns,{flat:"",border:"",class:"message_box"},{default:De(()=>[je(ga,null,{default:De(()=>[je(Xt,{class:"message_box__place_holder mb-4 justify-center align-center"},{default:De(()=>[Ve(u).length==0?(_e(),Ge("span",Oh," از دکمه های زیر برای ساختن پاسخ خود استفاده کنید ")):Ye("",!0),(_e(!0),Ge(mt,null,Jt(Ve(u),(M,y)=>(_e(),Oe(bt,{key:y,cols:"12"},{default:De(()=>[M.type=="text"||M.type=="comment"?(_e(),Oe(k,{key:0,id:M.id,"cant-add-button-to-text":t.cantAddButtonToText||Ve(i)!=y,text:M.value??"","has-close":t.hasClose,loading:M.loading??!1,buttons:M.buttons??[],onTextChanged:v=>Ve(c)(v,y),onAddButton:v=>o(y),onRemoveButton:v=>_(v,y),onButtonChanged:v=>Ve(b)(v,y),onDelete:v=>T(y)},null,8,["id","cant-add-button-to-text","text","has-close","loading","buttons","onTextChanged","onAddButton","onRemoveButton","onButtonChanged","onDelete"])):Ye("",!0),M.type=="image"?(_e(),Oe(H,{key:1,id:M.id,file:s.product_id===-1&&M.preview?M.preview:N(M.value),onFileChanged:v=>x(v,y),onDelete:v=>T(y)},null,8,["id","file","onFileChanged","onDelete"])):Ye("",!0),M.type=="video"?(_e(),Oe(R,{key:2,id:M.id,file:s.product_id===-1&&M.preview?M.preview:N(M.value),onFileChanged:v=>x(v,y),onDelete:v=>T(y)},null,8,["id","file","onFileChanged","onDelete"])):Ye("",!0),M.type=="audio"?(_e(),Oe(F,{key:3,id:M.id,file:N(M.value),onFileChanged:v=>x(v,y),onDelete:v=>T(y)},null,8,["id","file","onFileChanged","onDelete"])):Ye("",!0),M.type=="card"?(_e(),Oe(Y,{key:4,id:M.id,slides:M.slides,onDelete:v=>m(v,y),onAddSlide:v=>f(y),onSlideChanged:v=>A(y,v),onAddButton:v=>p(v,y),onRemoveButton:v=>w(v,y),onButtonChanged:v=>Ve(E)(v,y)},null,8,["id","slides","onDelete","onAddSlide","onSlideChanged","onAddButton","onRemoveButton","onButtonChanged"])):Ye("",!0)]),_:2},1024))),128))]),_:1}),je(Xt,{class:"mt-4"},{default:De(()=>[(_e(!0),Ge(mt,null,Jt(t.templateButtonsList,(M,y)=>(_e(),Oe(X,{key:M.name,disable:Ve(O),"emit-name":M.emitName,icon:M.icon,name:M.name,"is-last":y==t.templateButtonsList.length-1&&t.templateButtonsList.length%2==1,onAddTemplate:q},null,8,["disable","emit-name","icon","name","is-last"]))),128))]),_:1})]),_:1})]),_:1})]),_:1})}}};export{Jh as _,Aa as r};
