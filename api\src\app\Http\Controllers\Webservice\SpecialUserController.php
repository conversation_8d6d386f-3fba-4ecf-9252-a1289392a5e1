<?php

namespace App\Http\Controllers\Webservice;

use App\Http\Controllers\Controller;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use App\Services\Directam\PackageCheckerService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SpecialUserController extends Controller
{
    protected SpecialUserRepoInterface $specialUserRepository;
    protected PackageCheckerService $directamPackageService;

    public function __construct(SpecialUserRepoInterface $specialUserRepository, PackageCheckerService $packageChecker)
    {
        $this->specialUserRepository = $specialUserRepository;
        $this->directamPackageService = $packageChecker;
    }

    /**
     * @OA\Post(
     *     path="/api/webservice",
     *     summary="Create a special user",
     *     description="Creates a new special user if the authenticated user is eligible and connected to Meta via Instagram. Returns the API token only once upon creation.",
     *     operationId="storeSpecialUser",
     *     tags={"Special User"},
     *     security={{"sanctum":{}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Special user created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Special user created successfully"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="api_token", type="string", example="tk7b85sbbre9gf8v"),
     *                 @OA\Property(property="webhook_endpoints", type="array", @OA\Items(type="string"), example={"https://webhook1.example.com", "https://webhook2.example.com"}),
     *                 @OA\Property(property="valid_endpoints", type="array", @OA\Items(type="string"), example={"***********", "***********"})
     *             )
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=400,
     *         description="User is not connected to Meta or Instagram",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="User is not connected to Meta")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="User is not eligible for the web service",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="User is not eligible for web service")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized - Authentication required",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated")
     *         )
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$this->isValidForHavingWebservice($user->phone)) {
            return response()->json([
                'success' => false,
                'message' => 'User is not eligible for web service'
            ], 403);
        }

        $pages = $user->pages;
        if (!$pages || $pages->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'User has not connected to Meta yet'
            ], 400);
        }

        $page = $pages->first();
        if (!$page->access_token || !$page->page_user_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not connected to Meta'
            ], 400);
        }

        if ($page->fb_user_id !== 'loggedInWithInstagram') {
            return response()->json([
                'success' => false,
                'message' => 'User is not logged in with Instagram'
            ], 400);
        }

        $data = [
            'user_id'      => $user->id,
            'access_token' => $page->getRawOriginal('access_token'),
            'instagram_id' => $page->page_user_id,
        ];

        $specialUser = $this->specialUserRepository->create($data);
        $apiToken = $this->specialUserRepository->generateApiToken($specialUser->id);

        return response()->json([
            'success' => true,
            'message' => 'Special user created successfully',
            'data'    => [
                'api_token'         => $apiToken,
                'webhook_endpoints' => $specialUser->webhook_endpoints,
                'valid_endpoints'   => json_decode($specialUser->valid_endpoints, true),
            ]
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/webservice",
     *     summary="Get special user data",
     *     description="Retrieves the webhook endpoints and valid IPs of the authenticated special user. If no special user exists, returns an error.",
     *     operationId="getSpecialUser",
     *     tags={"Special User"},
     *     security={{"sanctum":{}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Special user data retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="webhook_endpoints", type="array", @OA\Items(type="string"), example={"https://webhook1.example.com", "https://webhook2.example.com"}),
     *                 @OA\Property(property="valid_endpoints", type="array", @OA\Items(type="string"), example={"***********", "***********"})
     *             )
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="User does not have an active special user account",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Webservice is not active")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized - Authentication required",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated")
     *         )
     *     )
     * )
     */
    public function show(): JsonResponse
    {
        $userId = auth()->user()->id;
        $specialUser = $this->specialUserRepository->findByUserId($userId);
        if (!$specialUser) {
            return response()->json([
                'success' => false,
                'message' => 'Webservice is not active'
            ], 403);
        }
        return response()->json([
            'success' => true,
            'data'    => [
                'webhook_endpoints' => $specialUser->webhook_endpoints,
                'valid_endpoints'   => json_decode($specialUser->valid_endpoints, true),
            ]
        ], 200);
    }

    private function isValidForHavingWebservice(string $phone): bool
    {
        return $this->directamPackageService->isAnnuallySubscribed(phone: $phone);
    }
}
