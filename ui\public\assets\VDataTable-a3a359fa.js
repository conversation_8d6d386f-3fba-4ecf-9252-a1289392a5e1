import{c9 as lt,ap as k,aq as nt,bl as rt,ar as ot,ax as st,as as I,b1 as ut,bm as it,at as O,b as f,aW as V,b6 as F,a8 as P,cx as xe,P as _,bE as Pe,w as Se,V as A,cq as Y,aB as ae,s as D,cy as dt,r as C,bC as ct,cz as ft,bS as ke,b_ as Z,a1 as T,cA as q,cB as L,au as ce,c5 as gt,c6 as vt,az as mt,c8 as ht,F as H,a4 as bt,aj as we,v as fe,ad as ge,x as yt,bG as ee,bR as M,bb as pt}from"./index-169996dc.js";import{V as ve}from"./VPagination-8a53e08f.js";import{V as xt}from"./VSelect-d6fde9f4.js";import{V as le}from"./VCheckboxBtn-be9663e7.js";import{m as Pt,u as St}from"./filter-09e553ed.js";import{V as kt}from"./VDivider-12bfa926.js";function me(e,l,a){return Object.keys(e).filter(t=>lt(t)&&t.endsWith(l)).reduce((t,n)=>(t[n.slice(0,-l.length)]=r=>e[n](r,a(r)),t),{})}const Te=k({fixedHeader:Boolean,fixedFooter:Boolean,height:[Number,String],hover:Boolean,...nt(),...rt(),...ot(),...st()},"VTable"),he=I()({name:"VTable",props:Te(),setup(e,l){let{slots:a,emit:t}=l;const{themeClasses:n}=ut(e),{densityClasses:r}=it(e);return O(()=>f(e.tag,{class:["v-table",{"v-table--fixed-height":!!e.height,"v-table--fixed-header":e.fixedHeader,"v-table--fixed-footer":e.fixedFooter,"v-table--has-top":!!a.top,"v-table--has-bottom":!!a.bottom,"v-table--hover":e.hover},n.value,r.value,e.class],style:e.style},{default:()=>{var u,o,s;return[(u=a.top)==null?void 0:u.call(a),a.default?f("div",{class:"v-table__wrapper",style:{height:V(e.height)}},[f("table",null,[a.default()])]):(o=a.wrapper)==null?void 0:o.call(a),(s=a.bottom)==null?void 0:s.call(a)]}})),{}}});const wt=k({page:{type:[Number,String],default:1},itemsPerPage:{type:[Number,String],default:10}},"DataTable-paginate"),De=Symbol.for("vuetify:data-table-pagination");function Tt(e){const l=F(e,"page",void 0,t=>+(t??1)),a=F(e,"itemsPerPage",void 0,t=>+(t??10));return{page:l,itemsPerPage:a}}function Dt(e){const{page:l,itemsPerPage:a,itemsLength:t}=e,n=P(()=>a.value===-1?0:a.value*(l.value-1)),r=P(()=>a.value===-1?t.value:Math.min(t.value,n.value+a.value)),u=P(()=>a.value===-1||t.value===0?1:Math.ceil(t.value/a.value));xe(()=>{l.value>u.value&&(l.value=u.value)});function o(d){a.value=d,l.value=1}function s(){l.value=Y(l.value+1,1,u.value)}function i(){l.value=Y(l.value-1,1,u.value)}function c(d){l.value=Y(d,1,u.value)}const m={page:l,itemsPerPage:a,startIndex:n,stopIndex:r,pageCount:u,itemsLength:t,nextPage:s,prevPage:i,setPage:c,setItemsPerPage:o};return _(De,m),m}function Vt(){const e=A(De);if(!e)throw new Error("Missing pagination!");return e}function It(e){const l=Pe("usePaginatedItems"),{items:a,startIndex:t,stopIndex:n,itemsPerPage:r}=e,u=P(()=>r.value<=0?a.value:a.value.slice(t.value,n.value));return Se(u,o=>{l.emit("update:currentItems",o)}),{paginatedItems:u}}const Ve=k({prevIcon:{type:String,default:"$prev"},nextIcon:{type:String,default:"$next"},firstIcon:{type:String,default:"$first"},lastIcon:{type:String,default:"$last"},itemsPerPageText:{type:String,default:"$vuetify.dataFooter.itemsPerPageText"},pageText:{type:String,default:"$vuetify.dataFooter.pageText"},firstPageLabel:{type:String,default:"$vuetify.dataFooter.firstPage"},prevPageLabel:{type:String,default:"$vuetify.dataFooter.prevPage"},nextPageLabel:{type:String,default:"$vuetify.dataFooter.nextPage"},lastPageLabel:{type:String,default:"$vuetify.dataFooter.lastPage"},itemsPerPageOptions:{type:Array,default:()=>[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"},{value:-1,title:"$vuetify.dataFooter.itemsPerPageAll"}]},showCurrentPage:Boolean},"VDataTableFooter"),be=I()({name:"VDataTableFooter",props:Ve(),setup(e,l){let{slots:a}=l;const{t}=ae(),{page:n,pageCount:r,startIndex:u,stopIndex:o,itemsLength:s,itemsPerPage:i,setItemsPerPage:c}=Vt(),m=P(()=>e.itemsPerPageOptions.map(d=>typeof d=="number"?{value:d,title:d===-1?t("$vuetify.dataFooter.itemsPerPageAll"):String(d)}:{...d,title:t(d.title)}));return O(()=>{var v;const d=ve.filterProps(e);return f("div",{class:"v-data-table-footer"},[(v=a.prepend)==null?void 0:v.call(a),f("div",{class:"v-data-table-footer__items-per-page"},[f("span",null,[t(e.itemsPerPageText)]),f(xt,{items:m.value,modelValue:i.value,"onUpdate:modelValue":y=>c(Number(y)),density:"compact",variant:"outlined","hide-details":!0},null)]),f("div",{class:"v-data-table-footer__info"},[f("div",null,[t(e.pageText,s.value?u.value+1:0,o.value,s.value)])]),f("div",{class:"v-data-table-footer__pagination"},[f(ve,D({modelValue:n.value,"onUpdate:modelValue":y=>n.value=y,density:"comfortable","first-aria-label":e.firstPageLabel,"last-aria-label":e.lastPageLabel,length:r.value,"next-aria-label":e.nextPageLabel,"previous-aria-label":e.prevPageLabel,rounded:!0,"show-first-last-page":!0,"total-visible":e.showCurrentPage?1:0,variant:"plain"},d),null)])])}),{}}}),ne=dt({align:{type:String,default:"start"},fixed:Boolean,fixedOffset:[Number,String],height:[Number,String],lastFixed:Boolean,noPadding:Boolean,tag:String,width:[Number,String]},(e,l)=>{let{slots:a}=l;const t=e.tag??"td";return f(t,{class:["v-data-table__td",{"v-data-table-column--fixed":e.fixed,"v-data-table-column--last-fixed":e.lastFixed,"v-data-table-column--no-padding":e.noPadding},`v-data-table-column--align-${e.align}`],style:{height:V(e.height),width:V(e.width),left:V(e.fixedOffset||null)}},{default:()=>{var n;return[(n=a.default)==null?void 0:n.call(a)]}})}),Bt=k({headers:Array},"DataTable-header"),Ie=Symbol.for("vuetify:data-table-headers"),Be={title:"",sortable:!1},Ct={...Be,width:48};function Ft(){const l=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).map(a=>({element:a,priority:0}));return{enqueue:(a,t)=>{let n=!1;for(let r=0;r<l.length;r++)if(l[r].priority>t){l.splice(r,0,{element:a,priority:t}),n=!0;break}n||l.push({element:a,priority:t})},size:()=>l.length,count:()=>{let a=0;if(!l.length)return 0;const t=Math.floor(l[0].priority);for(let n=0;n<l.length;n++)Math.floor(l[n].priority)===t&&(a+=1);return a},dequeue:()=>l.shift()}}function te(e){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];if(!e.children)l.push(e);else for(const a of e.children)te(a,l);return l}function Ce(e){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:new Set;for(const a of e)a.key&&l.add(a.key),a.children&&Ce(a.children,l);return l}function Ot(e){if(e.key){if(e.key==="data-table-group")return Be;if(["data-table-expand","data-table-select"].includes(e.key))return Ct}}function re(e){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.children?Math.max(l,...e.children.map(a=>re(a,l+1))):l}function _t(e){let l=!1;function a(r){let u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(r)if(u&&(r.fixed=!0),r.fixed)if(r.children)for(let o=r.children.length-1;o>=0;o--)a(r.children[o],!0);else l?isNaN(+r.width)&&ft(`Multiple fixed columns should have a static width (key: ${r.key})`):r.lastFixed=!0,l=!0;else if(r.children)for(let o=r.children.length-1;o>=0;o--)a(r.children[o]);else l=!1}for(let r=e.length-1;r>=0;r--)a(e[r]);function t(r){let u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(!r)return u;if(r.children){r.fixedOffset=u;for(const o of r.children)u=t(o,u)}else r.fixed&&(r.fixedOffset=u,u+=parseFloat(r.width||"0")||0);return u}let n=0;for(const r of e)n=t(r,n)}function At(e,l){const a=[];let t=0;const n=Ft(e);for(;n.size()>0;){let u=n.count();const o=[];let s=1;for(;u>0;){const{element:i,priority:c}=n.dequeue(),m=l-t-re(i);if(o.push({...i,rowspan:m??1,colspan:i.children?te(i).length:1}),i.children)for(const d of i.children){const v=c%1+s/Math.pow(10,t+2);n.enqueue(d,t+m+v)}s+=1,u-=1}t+=1,a.push(o)}return{columns:e.map(u=>te(u)).flat(),headers:a}}function Fe(e){const l=[];for(const a of e){const t={...Ot(a),...a},n=t.key??(typeof t.value=="string"?t.value:null),r=t.value??n??null,u={...t,key:n,value:r,sortable:t.sortable??(t.key!=null||!!t.sort),children:t.children?Fe(t.children):void 0};l.push(u)}return l}function $t(e,l){const a=C([]),t=C([]),n=C({}),r=C({}),u=C({});xe(()=>{var b,g,x;const i=(e.headers||Object.keys(e.items[0]??{}).map(p=>({key:p,title:ct(p)}))).slice(),c=Ce(i);(b=l==null?void 0:l.groupBy)!=null&&b.value.length&&!c.has("data-table-group")&&i.unshift({key:"data-table-group",title:"Group"}),(g=l==null?void 0:l.showSelect)!=null&&g.value&&!c.has("data-table-select")&&i.unshift({key:"data-table-select"}),(x=l==null?void 0:l.showExpand)!=null&&x.value&&!c.has("data-table-expand")&&i.push({key:"data-table-expand"});const m=Fe(i);_t(m);const d=Math.max(...m.map(p=>re(p)))+1,v=At(m,d);a.value=v.headers,t.value=v.columns;const y=v.headers.flat(1);for(const p of y)p.key&&(p.sortable&&(p.sort&&(n.value[p.key]=p.sort),p.sortRaw&&(r.value[p.key]=p.sortRaw)),p.filter&&(u.value[p.key]=p.filter))});const o={headers:a,columns:t,sortFunctions:n,sortRawFunctions:r,filterFunctions:u};return _(Ie,o),o}function z(){const e=A(Ie);if(!e)throw new Error("Missing headers!");return e}const Et={showSelectAll:!1,allSelected:()=>[],select:e=>{var t;let{items:l,value:a}=e;return new Set(a?[(t=l[0])==null?void 0:t.value]:[])},selectAll:e=>{let{selected:l}=e;return l}},Oe={showSelectAll:!0,allSelected:e=>{let{currentPage:l}=e;return l},select:e=>{let{items:l,value:a,selected:t}=e;for(const n of l)a?t.add(n.value):t.delete(n.value);return t},selectAll:e=>{let{value:l,currentPage:a,selected:t}=e;return Oe.select({items:a,value:l,selected:t})}},_e={showSelectAll:!0,allSelected:e=>{let{allItems:l}=e;return l},select:e=>{let{items:l,value:a,selected:t}=e;for(const n of l)a?t.add(n.value):t.delete(n.value);return t},selectAll:e=>{let{value:l,allItems:a,selected:t}=e;return _e.select({items:a,value:l,selected:t})}},Nt=k({showSelect:Boolean,selectStrategy:{type:[String,Object],default:"page"},modelValue:{type:Array,default:()=>[]},valueComparator:{type:Function,default:ke}},"DataTable-select"),Ae=Symbol.for("vuetify:data-table-selection");function Gt(e,l){let{allItems:a,currentPage:t}=l;const n=F(e,"modelValue",e.modelValue,g=>new Set(Z(g).map(x=>{var p;return((p=a.value.find(S=>e.valueComparator(x,S.value)))==null?void 0:p.value)??x})),g=>[...g.values()]),r=P(()=>a.value.filter(g=>g.selectable)),u=P(()=>t.value.filter(g=>g.selectable)),o=P(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;switch(e.selectStrategy){case"single":return Et;case"all":return _e;case"page":default:return Oe}});function s(g){return Z(g).every(x=>n.value.has(x.value))}function i(g){return Z(g).some(x=>n.value.has(x.value))}function c(g,x){const p=o.value.select({items:g,value:x,selected:new Set(n.value)});n.value=p}function m(g){c([g],!s([g]))}function d(g){const x=o.value.selectAll({value:g,allItems:r.value,currentPage:u.value,selected:new Set(n.value)});n.value=x}const v=P(()=>n.value.size>0),y=P(()=>{const g=o.value.allSelected({allItems:r.value,currentPage:u.value});return!!g.length&&s(g)}),b={toggleSelect:m,select:c,selectAll:d,isSelected:s,isSomeSelected:i,someSelected:v,allSelected:y,showSelectAll:o.value.showSelectAll};return _(Ae,b),b}function W(){const e=A(Ae);if(!e)throw new Error("Missing selection!");return e}const Ht=k({sortBy:{type:Array,default:()=>[]},customKeySort:Object,multiSort:Boolean,mustSort:Boolean},"DataTable-sort"),$e=Symbol.for("vuetify:data-table-sort");function Rt(e){const l=F(e,"sortBy"),a=T(e,"mustSort"),t=T(e,"multiSort");return{sortBy:l,mustSort:a,multiSort:t}}function jt(e){const{sortBy:l,mustSort:a,multiSort:t,page:n}=e,r=s=>{if(s.key==null)return;let i=l.value.map(m=>({...m}))??[];const c=i.find(m=>m.key===s.key);c?c.order==="desc"?a.value?c.order="asc":i=i.filter(m=>m.key!==s.key):c.order="desc":t.value?i=[...i,{key:s.key,order:"asc"}]:i=[{key:s.key,order:"asc"}],l.value=i,n&&(n.value=1)};function u(s){return!!l.value.find(i=>i.key===s.key)}const o={sortBy:l,toggleSort:r,isSorted:u};return _($e,o),o}function Lt(){const e=A($e);if(!e)throw new Error("Missing sort!");return e}function Mt(e,l,a,t,n){const r=ae();return{sortedItems:P(()=>a.value.length?qt(l.value,a.value,r.current.value,{...e.customKeySort,...t==null?void 0:t.value},n==null?void 0:n.value):l.value)}}function qt(e,l,a,t,n){const r=new Intl.Collator(a,{sensitivity:"accent",usage:"sort"});return[...e].sort((u,o)=>{for(let s=0;s<l.length;s++){let i=!1;const c=l[s].key,m=l[s].order??"asc";if(m===!1)continue;let d=q(u.raw,c),v=q(o.raw,c),y=u.raw,b=o.raw;if(m==="desc"&&([d,v]=[v,d],[y,b]=[b,y]),n!=null&&n[c]){const g=n[c](y,b);if(g==null)continue;if(i=!0,g)return g}if(t!=null&&t[c]){const g=t[c](d,v);if(g==null)continue;if(i=!0,g)return g}if(!i){if(d instanceof Date&&v instanceof Date)return d.getTime()-v.getTime();if([d,v]=[d,v].map(g=>g!=null?g.toString().toLocaleLowerCase():g),d!==v)return L(d)&&L(v)?0:L(d)?-1:L(v)?1:!isNaN(d)&&!isNaN(v)?Number(d)-Number(v):r.compare(d,v)}}return 0})}const Ee=k({color:String,sticky:Boolean,multiSort:Boolean,sortAscIcon:{type:ce,default:"$sortAsc"},sortDescIcon:{type:ce,default:"$sortDesc"},headerProps:{type:Object},...gt()},"VDataTableHeaders"),ye=I()({name:"VDataTableHeaders",props:Ee(),setup(e,l){let{slots:a}=l;const{toggleSort:t,sortBy:n,isSorted:r}=Lt(),{someSelected:u,allSelected:o,selectAll:s,showSelectAll:i}=W(),{columns:c,headers:m}=z(),{loaderClasses:d}=vt(e);function v(S,h){if(!(!e.sticky&&!S.fixed))return{position:"sticky",left:S.fixed?V(S.fixedOffset):void 0,top:e.sticky?`calc(var(--v-table-header-height) * ${h})`:void 0}}function y(S){const h=n.value.find(B=>B.key===S.key);return h?h.order==="asc"?e.sortAscIcon:e.sortDescIcon:e.sortAscIcon}const{backgroundColorClasses:b,backgroundColorStyles:g}=mt(e,"color"),x=P(()=>({headers:m.value,columns:c.value,toggleSort:t,isSorted:r,sortBy:n.value,someSelected:u.value,allSelected:o.value,selectAll:s,getSortIcon:y})),p=S=>{let{column:h,x:B,y:$}=S;const K=h.key==="data-table-select"||h.key==="data-table-expand",U=D(e.headerProps??{},h.headerProps??{});return f(ne,D({tag:"th",align:h.align,class:["v-data-table__th",{"v-data-table__th--sortable":h.sortable,"v-data-table__th--sorted":r(h),"v-data-table__th--fixed":h.fixed,"v-data-table__th--sticky":e.sticky},d.value],style:{width:V(h.width),minWidth:V(h.minWidth),...v(h,$)},colspan:h.colspan,rowspan:h.rowspan,onClick:h.sortable?()=>t(h):void 0,fixed:h.fixed,lastFixed:h.lastFixed,noPadding:K},U),{default:()=>{var j;const E=`header.${h.key}`,R={column:h,selectAll:s,isSorted:r,toggleSort:t,sortBy:n.value,someSelected:u.value,allSelected:o.value,getSortIcon:y};return a[E]?a[E](R):h.key==="data-table-select"?((j=a["header.data-table-select"])==null?void 0:j.call(a,R))??(i&&f(le,{modelValue:o.value,indeterminate:u.value&&!o.value,"onUpdate:modelValue":s},null)):f("div",{class:"v-data-table-header__content"},[f("span",null,[h.title]),h.sortable&&f(bt,{key:"icon",class:"v-data-table-header__sort-icon",icon:y(h)},null),e.multiSort&&r(h)&&f("div",{key:"badge",class:["v-data-table-header__sort-badge",...b.value],style:g.value},[n.value.findIndex(Q=>Q.key===h.key)+1])])}})};O(()=>f(H,null,[a.headers?a.headers(x.value):m.value.map((S,h)=>f("tr",null,[S.map((B,$)=>f(p,{column:B,x:$,y:h},null))])),e.loading&&f("tr",{class:"v-data-table-progress"},[f("th",{colspan:c.value.length},[f(ht,{name:"v-data-table-progress",absolute:!0,active:!0,color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0},{default:a.loader})])])]))}}),zt=k({groupBy:{type:Array,default:()=>[]}},"DataTable-group"),Ne=Symbol.for("vuetify:data-table-group");function Wt(e){return{groupBy:F(e,"groupBy")}}function Kt(e){const{groupBy:l,sortBy:a}=e,t=C(new Set),n=P(()=>l.value.map(i=>({...i,order:i.order??!1})).concat(a.value));function r(i){return t.value.has(i.id)}function u(i){const c=new Set(t.value);r(i)?c.delete(i.id):c.add(i.id),t.value=c}function o(i){function c(m){const d=[];for(const v of m.items)"type"in v&&v.type==="group"?d.push(...c(v)):d.push(v);return d}return c({type:"group",items:i,id:"dummy",key:"dummy",value:"dummy",depth:0})}const s={sortByWithGroups:n,toggleGroup:u,opened:t,groupBy:l,extractRows:o,isGroupOpen:r};return _(Ne,s),s}function Ge(){const e=A(Ne);if(!e)throw new Error("Missing group!");return e}function Ut(e,l){if(!e.length)return[];const a=new Map;for(const t of e){const n=q(t.raw,l);a.has(n)||a.set(n,[]),a.get(n).push(t)}return a}function He(e,l){let a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,t=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"root";if(!l.length)return[];const n=Ut(e,l[0]),r=[],u=l.slice(1);return n.forEach((o,s)=>{const i=l[0],c=`${t}_${i}_${s}`;r.push({depth:a,id:c,key:i,value:s,items:u.length?He(o,u,a+1,c):o,type:"group"})}),r}function Re(e,l){const a=[];for(const t of e)"type"in t&&t.type==="group"?(t.value!=null&&a.push(t),(l.has(t.id)||t.value==null)&&a.push(...Re(t.items,l))):a.push(t);return a}function Qt(e,l,a){return{flatItems:P(()=>{if(!l.value.length)return e.value;const n=He(e.value,l.value.map(r=>r.key));return Re(n,a.value)})}}const Jt=k({item:{type:Object,required:!0}},"VDataTableGroupHeaderRow"),Xt=I()({name:"VDataTableGroupHeaderRow",props:Jt(),setup(e,l){let{slots:a}=l;const{isGroupOpen:t,toggleGroup:n,extractRows:r}=Ge(),{isSelected:u,isSomeSelected:o,select:s}=W(),{columns:i}=z(),c=P(()=>r([e.item]));return()=>f("tr",{class:"v-data-table-group-header-row",style:{"--v-data-table-group-header-row-depth":e.item.depth}},[i.value.map(m=>{var d,v;if(m.key==="data-table-group"){const y=t(e.item)?"$expand":"$next",b=()=>n(e.item);return((d=a["data-table-group"])==null?void 0:d.call(a,{item:e.item,count:c.value.length,props:{icon:y,onClick:b}}))??f(ne,{class:"v-data-table-group-header-row__column"},{default:()=>[f(we,{size:"small",variant:"text",icon:y,onClick:b},null),f("span",null,[e.item.value]),f("span",null,[fe("("),c.value.length,fe(")")])]})}if(m.key==="data-table-select"){const y=u(c.value),b=o(c.value)&&!y,g=x=>s(c.value,x);return((v=a["data-table-select"])==null?void 0:v.call(a,{props:{modelValue:y,indeterminate:b,"onUpdate:modelValue":g}}))??f("td",null,[f(le,{modelValue:y,indeterminate:b,"onUpdate:modelValue":g},null)])}return f("td",null,null)})])}}),Yt=k({expandOnClick:Boolean,showExpand:Boolean,expanded:{type:Array,default:()=>[]}},"DataTable-expand"),je=Symbol.for("vuetify:datatable:expanded");function Zt(e){const l=T(e,"expandOnClick"),a=F(e,"expanded",e.expanded,o=>new Set(o),o=>[...o.values()]);function t(o,s){const i=new Set(a.value);s?i.add(o.value):i.delete(o.value),a.value=i}function n(o){return a.value.has(o.value)}function r(o){t(o,!n(o))}const u={expand:t,expanded:a,expandOnClick:l,isExpanded:n,toggleExpand:r};return _(je,u),u}function Le(){const e=A(je);if(!e)throw new Error("foo");return e}const ea=k({index:Number,item:Object,cellProps:[Object,Function],onClick:ee(),onContextmenu:ee(),onDblclick:ee()},"VDataTableRow"),ta=I()({name:"VDataTableRow",props:ea(),setup(e,l){let{slots:a}=l;const{isSelected:t,toggleSelect:n}=W(),{isExpanded:r,toggleExpand:u}=Le(),{columns:o}=z();O(()=>f("tr",{class:["v-data-table__tr",{"v-data-table__tr--clickable":!!(e.onClick||e.onContextmenu||e.onDblclick)}],onClick:e.onClick,onContextmenu:e.onContextmenu,onDblclick:e.onDblclick},[e.item&&o.value.map((s,i)=>{const c=e.item,m=`item.${s.key}`,d={index:e.index,item:c.raw,internalItem:c,value:q(c.columns,s.key),column:s,isSelected:t,toggleSelect:n,isExpanded:r,toggleExpand:u},v=typeof e.cellProps=="function"?e.cellProps({index:d.index,item:d.item,internalItem:d.internalItem,value:d.value,column:s}):e.cellProps,y=typeof s.cellProps=="function"?s.cellProps({index:d.index,item:d.item,internalItem:d.internalItem,value:d.value}):s.cellProps;return f(ne,D({align:s.align,fixed:s.fixed,fixedOffset:s.fixedOffset,lastFixed:s.lastFixed,noPadding:s.key==="data-table-select"||s.key==="data-table-expand",width:s.width},v,y),{default:()=>{var b,g;return a[m]?a[m](d):s.key==="data-table-select"?((b=a["item.data-table-select"])==null?void 0:b.call(a,d))??f(le,{disabled:!c.selectable,modelValue:t([c]),onClick:ge(()=>n(c),["stop"])},null):s.key==="data-table-expand"?((g=a["item.data-table-expand"])==null?void 0:g.call(a,d))??f(we,{icon:r(c)?"$collapse":"$expand",size:"small",variant:"text",onClick:ge(()=>u(c),["stop"])},null):yt(d.value)}})})]))}}),Me=k({loading:[Boolean,String],loadingText:{type:String,default:"$vuetify.dataIterator.loadingText"},hideNoData:Boolean,items:{type:Array,default:()=>[]},noDataText:{type:String,default:"$vuetify.noDataText"},rowProps:[Object,Function],cellProps:[Object,Function]},"VDataTableRows"),pe=I()({name:"VDataTableRows",inheritAttrs:!1,props:Me(),setup(e,l){let{attrs:a,slots:t}=l;const{columns:n}=z(),{expandOnClick:r,toggleExpand:u,isExpanded:o}=Le(),{isSelected:s,toggleSelect:i}=W(),{toggleGroup:c,isGroupOpen:m}=Ge(),{t:d}=ae();return O(()=>{var v,y;return e.loading&&(!e.items.length||t.loading)?f("tr",{class:"v-data-table-rows-loading",key:"loading"},[f("td",{colspan:n.value.length},[((v=t.loading)==null?void 0:v.call(t))??d(e.loadingText)])]):!e.loading&&!e.items.length&&!e.hideNoData?f("tr",{class:"v-data-table-rows-no-data",key:"no-data"},[f("td",{colspan:n.value.length},[((y=t["no-data"])==null?void 0:y.call(t))??d(e.noDataText)])]):f(H,null,[e.items.map((b,g)=>{var S;if(b.type==="group"){const h={index:g,item:b,columns:n.value,isExpanded:o,toggleExpand:u,isSelected:s,toggleSelect:i,toggleGroup:c,isGroupOpen:m};return t["group-header"]?t["group-header"](h):f(Xt,D({key:`group-header_${b.id}`,item:b},me(a,":group-header",()=>h)),t)}const x={index:g,item:b.raw,internalItem:b,columns:n.value,isExpanded:o,toggleExpand:u,isSelected:s,toggleSelect:i},p={...x,props:D({key:`item_${b.key??b.index}`,onClick:r.value?()=>{u(b)}:void 0,index:g,item:b,cellProps:e.cellProps},me(a,":row",()=>x),typeof e.rowProps=="function"?e.rowProps({item:x.item,index:x.index,internalItem:x.internalItem}):e.rowProps)};return f(H,{key:p.props.key},[t.item?t.item(p):f(ta,p.props,t),o(b)&&((S=t["expanded-row"])==null?void 0:S.call(t,x))])})])}),{}}}),aa=k({items:{type:Array,default:()=>[]},itemValue:{type:[String,Array,Function],default:"id"},itemSelectable:{type:[String,Array,Function],default:null},rowProps:[Object,Function],cellProps:[Object,Function],returnObject:Boolean},"DataTable-items");function la(e,l,a,t){const n=e.returnObject?l:M(l,e.itemValue),r=M(l,e.itemSelectable,!0),u=t.reduce((o,s)=>(s.key!=null&&(o[s.key]=M(l,s.value)),o),{});return{type:"item",key:e.returnObject?M(l,e.itemValue):n,index:a,value:n,selectable:r,columns:u,raw:l}}function na(e,l,a){return l.map((t,n)=>la(e,t,n,a))}function ra(e,l){return{items:P(()=>na(e,e.items,l.value))}}function oa(e){let{page:l,itemsPerPage:a,sortBy:t,groupBy:n,search:r}=e;const u=Pe("VDataTable"),o=P(()=>({page:l.value,itemsPerPage:a.value,sortBy:t.value,groupBy:n.value,search:r.value}));let s=null;Se(o,()=>{ke(s,o.value)||(s&&s.search!==o.value.search&&(l.value=1),u.emit("update:options",o.value),s=o.value)},{deep:!0,immediate:!0})}const sa=k({...Me(),width:[String,Number],search:String,...Yt(),...zt(),...Bt(),...aa(),...Nt(),...Ht(),...Ee(),...Te()},"DataTable"),ua=k({...wt(),...sa(),...Pt(),...Ve()},"VDataTable"),ma=I()({name:"VDataTable",props:ua(),emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:groupBy":e=>!0,"update:expanded":e=>!0,"update:currentItems":e=>!0},setup(e,l){let{attrs:a,slots:t}=l;const{groupBy:n}=Wt(e),{sortBy:r,multiSort:u,mustSort:o}=Rt(e),{page:s,itemsPerPage:i}=Tt(e),{columns:c,headers:m,sortFunctions:d,sortRawFunctions:v,filterFunctions:y}=$t(e,{groupBy:n,showSelect:T(e,"showSelect"),showExpand:T(e,"showExpand")}),{items:b}=ra(e,c),g=T(e,"search"),{filteredItems:x}=St(e,b,g,{transform:N=>N.columns,customKeyFilter:y}),{toggleSort:p}=jt({sortBy:r,multiSort:u,mustSort:o,page:s}),{sortByWithGroups:S,opened:h,extractRows:B,isGroupOpen:$,toggleGroup:K}=Kt({groupBy:n,sortBy:r}),{sortedItems:U}=Mt(e,x,S,d,v),{flatItems:E}=Qt(U,n,h),R=P(()=>E.value.length),{startIndex:j,stopIndex:Q,pageCount:qe,setItemsPerPage:ze}=Dt({page:s,itemsPerPage:i,itemsLength:R}),{paginatedItems:J}=It({items:E,startIndex:j,stopIndex:Q,itemsPerPage:i}),X=P(()=>B(J.value)),{isSelected:We,select:Ke,selectAll:Ue,toggleSelect:Qe,someSelected:Je,allSelected:Xe}=Gt(e,{allItems:b,currentPage:X}),{isExpanded:Ye,toggleExpand:Ze}=Zt(e);oa({page:s,itemsPerPage:i,sortBy:r,groupBy:n,search:g}),pt({VDataTableRows:{hideNoData:T(e,"hideNoData"),noDataText:T(e,"noDataText"),loading:T(e,"loading"),loadingText:T(e,"loadingText")}});const w=P(()=>({page:s.value,itemsPerPage:i.value,sortBy:r.value,pageCount:qe.value,toggleSort:p,setItemsPerPage:ze,someSelected:Je.value,allSelected:Xe.value,isSelected:We,select:Ke,selectAll:Ue,toggleSelect:Qe,isExpanded:Ye,toggleExpand:Ze,isGroupOpen:$,toggleGroup:K,items:X.value.map(N=>N.raw),internalItems:X.value,groupedItems:J.value,columns:c.value,headers:m.value}));return O(()=>{const N=be.filterProps(e),et=ye.filterProps(e),tt=pe.filterProps(e),at=he.filterProps(e);return f(he,D({class:["v-data-table",{"v-data-table--show-select":e.showSelect,"v-data-table--loading":e.loading},e.class],style:e.style},at),{top:()=>{var G;return(G=t.top)==null?void 0:G.call(t,w.value)},default:()=>{var G,oe,se,ue,ie,de;return t.default?t.default(w.value):f(H,null,[(G=t.colgroup)==null?void 0:G.call(t,w.value),f("thead",null,[f(ye,et,t)]),(oe=t.thead)==null?void 0:oe.call(t,w.value),f("tbody",null,[(se=t["body.prepend"])==null?void 0:se.call(t,w.value),t.body?t.body(w.value):f(pe,D(a,tt,{items:J.value}),t),(ue=t["body.append"])==null?void 0:ue.call(t,w.value)]),(ie=t.tbody)==null?void 0:ie.call(t,w.value),(de=t.tfoot)==null?void 0:de.call(t,w.value)])},bottom:()=>t.bottom?t.bottom(w.value):f(H,null,[f(kt,null,null),f(be,N,{prepend:t["footer.prepend"]})])})}),{}}});export{he as V,sa as a,Ve as b,Wt as c,Rt as d,Tt as e,$t as f,Kt as g,Dt as h,Qt as i,Gt as j,Zt as k,oa as l,wt as m,be as n,ye as o,jt as p,pe as q,ma as r,ra as u};
