#!/bin/bash
set -e
export COMPOSER_ALLOW_SUPERUSER=1
echo "Deployment started ..."

# Enter maintenance mode or return true
# if already is in maintenance mode
(php artisan down) || true

echo "Before git pull..."

echo "Git status:"
git status

echo "Git log (last 5 commits):"
git log --oneline -n 5

echo "Environment variables:"
env


# For composer
composer clear-cache

# Stash any unstaged changes
git stash

# Pull the latest version of the app
git pull origin hosein

# (Optional) Pop the stashed changes if needed
# git stash pop


# Install composer dependencies
composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader --ignore-platform-req=ext-mongodb

# Clear the old cache
php artisan clear-compiled

# Recreate cache
php artisan optimize:clear

# Compile npm assets
# npm run prod

# Run database migrations
php artisan migrate

# Exit maintenance mode
php artisan up

echo "Deployment finished!"

