<?php

/*
|--------------------------------------------------------------------------
| Webservice API Routes
|--------------------------------------------------------------------------
|
| This file contains API routes related to our web service system.
| These routes handle the creation and management of special users,
| including API token generation, webhook endpoint updates, and IP management.
|
| All routes are secured with authentication middleware to ensure only
| authorized users can access these functionalities.
|
*/

use App\Http\Controllers\Webservice\SpecialUserController;
use App\Http\Controllers\Webservice\SpecialUserTokenController;
use App\Http\Controllers\Webservice\SpecialUserValidIpsController;
use App\Http\Controllers\Webservice\SpecialUserWebhookController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum'])->group(function () {
    Route::prefix('webservice')->group(function () {

        Route::post('/', [SpecialUserController::class, 'store']);
        Route::get('/', [SpecialUserController::class, 'show']);
        Route::put('/webhooks', [SpecialUserWebhookController::class, 'update']);
        Route::put('/valid-ips', [SpecialUserValidIpsController::class, 'update']);
        Route::post('/regenerate-token', [SpecialUserTokenController::class, 'regenerateApiToken']);
    });
});
