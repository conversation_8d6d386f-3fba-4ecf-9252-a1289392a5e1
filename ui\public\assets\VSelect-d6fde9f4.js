import{m as be,V as se}from"./VTextField-a8984053.js";import{ap as $,aq as ve,as as ne,ck as me,w as M,at as ae,b as c,F as z,s as E,b4 as we,aV as O,cx as re,r as U,a8 as B,c3 as pe,cq as te,cC as Se,aN as he,cp as ge,bH as ke,bE as Pe,bN as Te,a1 as xe,b9 as Ce,H as Ie,cD as Re,aW as J,au as De,bg as Ae,ay as Be,bV as Fe,aB as _e,b6 as ie,b_ as Oe,ac as Le,a4 as ce,cE as Ee,bc as Me,v as He,bi as Ne,c4 as de}from"./index-169996dc.js";import{f as qe}from"./VInput-c4d3942a.js";import{m as Ue,u as Ke,V as ze,a as fe}from"./VList-349a1ccf.js";import{V as $e}from"./VMenu-2cfb0f14.js";import{V as We}from"./VCheckboxBtn-be9663e7.js";import{V as je}from"./VChip-ccd89083.js";const Xe=$({renderless:Boolean,...ve()},"VVirtualScrollItem"),Ye=ne()({name:"VVirtualScrollItem",inheritAttrs:!1,props:Xe(),emits:{"update:height":e=>!0},setup(e,d){let{attrs:n,emit:f,slots:s}=d;const{resizeRef:v,contentRect:b}=me(void 0,"border");M(()=>{var o;return(o=b.value)==null?void 0:o.height},o=>{o!=null&&f("update:height",o)}),ae(()=>{var o,a;return e.renderless?c(z,null,[(o=s.default)==null?void 0:o.call(s,{itemRef:v})]):c("div",E({ref:v,class:["v-virtual-scroll__item",e.class],style:e.style},n),[(a=s.default)==null?void 0:a.call(s)])})}}),Ge=-1,Je=1,le=100,Qe=$({itemHeight:{type:[Number,String],default:null},height:[Number,String]},"virtual");function Ze(e,d){const n=we(),f=O(0);re(()=>{f.value=parseFloat(e.itemHeight||0)});const s=O(0),v=O(Math.ceil((parseInt(e.height)||n.height.value)/(f.value||16))||1),b=O(0),o=O(0),a=U(),V=U();let D=0;const{resizeRef:F,contentRect:r}=me();re(()=>{F.value=a.value});const w=B(()=>{var t;return a.value===document.documentElement?n.height.value:((t=r.value)==null?void 0:t.height)||parseInt(e.height)||0}),S=B(()=>!!(a.value&&V.value&&w.value&&f.value));let x=Array.from({length:d.value.length}),m=Array.from({length:d.value.length});const C=O(0);let I=-1;function W(t){return x[t]||f.value}const A=Se(()=>{const t=performance.now();m[0]=0;const u=d.value.length;for(let g=1;g<=u-1;g++)m[g]=(m[g-1]||0)+W(g-1);C.value=Math.max(C.value,performance.now()-t)},C),j=M(S,t=>{t&&(j(),D=V.value.offsetTop,A.immediate(),_(),~I&&he(()=>{ge&&window.requestAnimationFrame(()=>{G(I),I=-1})}))});pe(()=>{A.clear()});function Q(t,u){const g=x[t],p=f.value;f.value=p?Math.min(f.value,u):u,(g!==u||p!==f.value)&&(x[t]=u,A())}function k(t){return t=te(t,0,d.value.length-1),m[t]||0}function X(t){return et(m,t)}let H=0,L=0,K=0;M(w,(t,u)=>{u&&(_(),t<u&&requestAnimationFrame(()=>{L=0,_()}))});function Z(){if(!a.value||!V.value)return;const t=a.value.scrollTop,u=performance.now();u-K>500?(L=Math.sign(t-H),D=V.value.offsetTop):L=t-H,H=t,K=u,_()}function N(){!a.value||!V.value||(L=0,K=0,_())}let Y=-1;function _(){cancelAnimationFrame(Y),Y=requestAnimationFrame(ee)}function ee(){if(!a.value||!w.value)return;const t=H-D,u=Math.sign(L),g=Math.max(0,t-le),p=te(X(g),0,d.value.length),i=t+w.value+le,y=te(X(i)+1,p+1,d.value.length);if((u!==Ge||p<s.value)&&(u!==Je||y>v.value)){const h=k(s.value)-k(p),R=k(y)-k(v.value);Math.max(h,R)>le?(s.value=p,v.value=y):(p<=0&&(s.value=p),y>=d.value.length&&(v.value=y))}b.value=k(s.value),o.value=k(d.value.length)-k(v.value)}function G(t){const u=k(t);!a.value||t&&!u?I=t:a.value.scrollTop=u}const l=B(()=>d.value.slice(s.value,v.value).map((t,u)=>({raw:t,index:u+s.value})));return M(d,()=>{x=Array.from({length:d.value.length}),m=Array.from({length:d.value.length}),A.immediate(),_()},{deep:!0}),{containerRef:a,markerRef:V,computedItems:l,paddingTop:b,paddingBottom:o,scrollToIndex:G,handleScroll:Z,handleScrollend:N,handleItemResize:Q}}function et(e,d){let n=e.length-1,f=0,s=0,v=null,b=-1;if(e[n]<d)return n;for(;f<=n;)if(s=f+n>>1,v=e[s],v>d)n=s-1;else if(v<d)b=s,f=s+1;else return v===d?s:f;return b}const tt=$({items:{type:Array,default:()=>[]},renderless:Boolean,...Qe(),...ve(),...ke()},"VVirtualScroll"),lt=ne()({name:"VVirtualScroll",props:tt(),setup(e,d){let{slots:n}=d;const f=Pe("VVirtualScroll"),{dimensionStyles:s}=Te(e),{containerRef:v,markerRef:b,handleScroll:o,handleScrollend:a,handleItemResize:V,scrollToIndex:D,paddingTop:F,paddingBottom:r,computedItems:w}=Ze(e,xe(e,"items"));return Ce(()=>e.renderless,()=>{function S(){var C,I;const m=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)?"addEventListener":"removeEventListener";v.value===document.documentElement?(document[m]("scroll",o,{passive:!0}),document[m]("scrollend",a)):((C=v.value)==null||C[m]("scroll",o,{passive:!0}),(I=v.value)==null||I[m]("scrollend",a))}Ie(()=>{v.value=Re(f.vnode.el,!0),S(!0)}),pe(S)}),ae(()=>{const S=w.value.map(x=>c(Ye,{key:x.index,renderless:e.renderless,"onUpdate:height":m=>V(x.index,m)},{default:m=>{var C;return(C=n.default)==null?void 0:C.call(n,{item:x.raw,index:x.index,...m})}}));return e.renderless?c(z,null,[c("div",{ref:b,class:"v-virtual-scroll__spacer",style:{paddingTop:J(F.value)}},null),S,c("div",{class:"v-virtual-scroll__spacer",style:{paddingBottom:J(r.value)}},null)]):c("div",{ref:v,class:["v-virtual-scroll",e.class],onScrollPassive:o,onScrollend:a,style:[s.value,e.style]},[c("div",{ref:b,class:"v-virtual-scroll__container",style:{paddingTop:J(F.value),paddingBottom:J(r.value)}},[S])])}),{scrollToIndex:D}}});function nt(e,d){const n=O(!1);let f;function s(o){cancelAnimationFrame(f),n.value=!0,f=requestAnimationFrame(()=>{f=requestAnimationFrame(()=>{n.value=!1})})}async function v(){await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>{if(n.value){const a=M(n,()=>{a(),o()})}else o()})}async function b(o){var D,F;if(o.key==="Tab"&&((D=d.value)==null||D.focus()),!["PageDown","PageUp","Home","End"].includes(o.key))return;const a=(F=e.value)==null?void 0:F.$el;if(!a)return;(o.key==="Home"||o.key==="End")&&a.scrollTo({top:o.key==="Home"?0:a.scrollHeight,behavior:"smooth"}),await v();const V=a.querySelectorAll(":scope > :not(.v-virtual-scroll__spacer)");if(o.key==="PageDown"||o.key==="Home"){const r=a.getBoundingClientRect().top;for(const w of V)if(w.getBoundingClientRect().top>=r){w.focus();break}}else{const r=a.getBoundingClientRect().bottom;for(const w of[...V].reverse())if(w.getBoundingClientRect().bottom<=r){w.focus();break}}}return{onListScroll:s,onListKeydown:b}}const at=$({chips:Boolean,closableChips:Boolean,closeText:{type:String,default:"$vuetify.close"},openText:{type:String,default:"$vuetify.open"},eager:Boolean,hideNoData:Boolean,hideSelected:Boolean,listProps:{type:Object},menu:Boolean,menuIcon:{type:De,default:"$dropdown"},menuProps:{type:Object},multiple:Boolean,noDataText:{type:String,default:"$vuetify.noDataText"},openOnClear:Boolean,itemColor:String,...Ue({itemChildren:!1})},"Select"),ot=$({...at(),...Ae(be({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...Be({transition:{component:Fe}})},"VSelect"),vt=ne()({name:"VSelect",props:ot(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,d){let{slots:n}=d;const{t:f}=_e(),s=U(),v=U(),b=U(),o=ie(e,"menu"),a=B({get:()=>o.value,set:l=>{var t;o.value&&!l&&((t=v.value)!=null&&t.ΨopenChildren)||(o.value=l)}}),{items:V,transformIn:D,transformOut:F}=Ke(e),r=ie(e,"modelValue",[],l=>D(l===null?[null]:Oe(l)),l=>{const t=F(l);return e.multiple?t:t[0]??null}),w=B(()=>typeof e.counterValue=="function"?e.counterValue(r.value):typeof e.counterValue=="number"?e.counterValue:r.value.length),S=qe(),x=B(()=>r.value.map(l=>l.value)),m=O(!1),C=B(()=>a.value?e.closeText:e.openText);let I="",W;const A=B(()=>e.hideSelected?V.value.filter(l=>!r.value.some(t=>t===l)):V.value),j=B(()=>e.hideNoData&&!A.value.length||e.readonly||(S==null?void 0:S.isReadonly.value)),Q=B(()=>{var l;return{...e.menuProps,activatorProps:{...((l=e.menuProps)==null?void 0:l.activatorProps)||{},"aria-haspopup":"listbox"}}}),k=U(),{onListScroll:X,onListKeydown:H}=nt(k,s);function L(l){e.openOnClear&&(a.value=!0)}function K(){j.value||(a.value=!a.value)}function Z(l){var i,y;if(!l.key||e.readonly||S!=null&&S.isReadonly.value)return;["Enter"," ","ArrowDown","ArrowUp","Home","End"].includes(l.key)&&l.preventDefault(),["Enter","ArrowDown"," "].includes(l.key)&&(a.value=!0),["Escape","Tab"].includes(l.key)&&(a.value=!1),l.key==="Home"?(i=k.value)==null||i.focus("first"):l.key==="End"&&((y=k.value)==null||y.focus("last"));const t=1e3;function u(h){const R=h.key.length===1,P=!h.ctrlKey&&!h.metaKey&&!h.altKey;return R&&P}if(e.multiple||!u(l))return;const g=performance.now();g-W>t&&(I=""),I+=l.key.toLowerCase(),W=g;const p=V.value.find(h=>h.title.toLowerCase().startsWith(I));p!==void 0&&(r.value=[p])}function N(l){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(!l.props.disabled)if(e.multiple){const u=r.value.findIndex(p=>e.valueComparator(p.value,l.value)),g=t??!~u;if(~u){const p=g?[...r.value,l]:[...r.value];p.splice(u,1),r.value=p}else g&&(r.value=[...r.value,l])}else{const u=t!==!1;r.value=u?[l]:[],he(()=>{a.value=!1})}}function Y(l){var t;(t=k.value)!=null&&t.$el.contains(l.relatedTarget)||(a.value=!1)}function _(){var l;m.value&&((l=s.value)==null||l.focus())}function ee(l){m.value=!0}function G(l){if(l==null)r.value=[];else if(de(s.value,":autofill")||de(s.value,":-webkit-autofill")){const t=V.value.find(u=>u.title===l);t&&N(t)}else s.value&&(s.value.value="")}return M([a,r],()=>{if(!e.hideSelected&&a.value&&r.value.length){const l=A.value.findIndex(t=>r.value.some(u=>e.valueComparator(u.value,t.value)));ge&&window.requestAnimationFrame(()=>{var t;l>=0&&((t=b.value)==null||t.scrollToIndex(l))})}}),M(()=>e.items,(l,t)=>{a.value||m.value&&!t.length&&l.length&&(a.value=!0)}),ae(()=>{const l=!!(e.chips||n.chip),t=!!(!e.hideNoData||A.value.length||n["prepend-item"]||n["append-item"]||n["no-data"]),u=r.value.length>0,g=se.filterProps(e),p=u||!m.value&&e.label&&!e.persistentPlaceholder?void 0:e.placeholder;return c(se,E({ref:s},g,{modelValue:r.value.map(i=>i.props.value).join(", "),"onUpdate:modelValue":G,focused:m.value,"onUpdate:focused":i=>m.value=i,validationValue:r.externalValue,counterValue:w.value,dirty:u,class:["v-select",{"v-select--active-menu":a.value,"v-select--chips":!!e.chips,[`v-select--${e.multiple?"multiple":"single"}`]:!0,"v-select--selected":r.value.length,"v-select--selection-slot":!!n.selection},e.class],style:e.style,inputmode:"none",placeholder:p,"onClick:clear":L,"onMousedown:control":K,onBlur:Y,onKeydown:Z,"aria-label":f(C.value),title:f(C.value)}),{...n,default:()=>c(z,null,[c($e,E({ref:v,modelValue:a.value,"onUpdate:modelValue":i=>a.value=i,activator:"parent",contentClass:"v-select__content",disabled:j.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterLeave:_},Q.value),{default:()=>[t&&c(ze,E({ref:k,selected:x.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:i=>i.preventDefault(),onKeydown:H,onFocusin:ee,onScrollPassive:X,tabindex:"-1","aria-live":"polite",color:e.itemColor??e.color},e.listProps),{default:()=>{var i,y,h;return[(i=n["prepend-item"])==null?void 0:i.call(n),!A.value.length&&!e.hideNoData&&(((y=n["no-data"])==null?void 0:y.call(n))??c(fe,{title:f(e.noDataText)},null)),c(lt,{ref:b,renderless:!0,items:A.value},{default:R=>{var ue;let{item:P,index:q,itemRef:T}=R;const oe=E(P.props,{ref:T,key:q,onClick:()=>N(P,null)});return((ue=n.item)==null?void 0:ue.call(n,{item:P,index:q,props:oe}))??c(fe,E(oe,{role:"option"}),{prepend:ye=>{let{isSelected:Ve}=ye;return c(z,null,[e.multiple&&!e.hideSelected?c(We,{key:P.value,modelValue:Ve,ripple:!1,tabindex:"-1"},null):void 0,P.props.prependAvatar&&c(Le,{image:P.props.prependAvatar},null),P.props.prependIcon&&c(ce,{icon:P.props.prependIcon},null)])}})}}),(h=n["append-item"])==null?void 0:h.call(n)]}})]}),r.value.map((i,y)=>{function h(T){T.stopPropagation(),T.preventDefault(),N(i,!1)}const R={"onClick:close":h,onKeydown(T){T.key!=="Enter"&&T.key!==" "||(T.preventDefault(),T.stopPropagation(),h(T))},onMousedown(T){T.preventDefault(),T.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},P=l?!!n.chip:!!n.selection,q=P?Ee(l?n.chip({item:i,index:y,props:R}):n.selection({item:i,index:y})):void 0;if(!(P&&!q))return c("div",{key:i.value,class:"v-select__selection"},[l?n.chip?c(Me,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:i.title}}},{default:()=>[q]}):c(je,E({key:"chip",closable:e.closableChips,size:"small",text:i.title,disabled:i.props.disabled},R),null):q??c("span",{class:"v-select__selection-text"},[i.title,e.multiple&&y<r.value.length-1&&c("span",{class:"v-select__selection-comma"},[He(",")])])])})]),"append-inner":function(){var R;for(var i=arguments.length,y=new Array(i),h=0;h<i;h++)y[h]=arguments[h];return c(z,null,[(R=n["append-inner"])==null?void 0:R.call(n,...y),e.menuIcon?c(ce,{class:"v-select__menu-icon",icon:e.menuIcon},null):void 0])}})}),Ne({isFocused:m,menu:a,select:N},s)}});export{vt as V,lt as a,at as m,nt as u};
