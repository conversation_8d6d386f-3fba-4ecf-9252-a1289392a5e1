{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "backendprogramer/snapp-pay": "^1.1", "guzzlehttp/guzzle": "^7.8", "hekmatinasser/verta": "^8.4", "http-interop/http-factory-guzzle": "^1.2", "kasraghoreyshi/persian-faker": "^1.04", "laravel/framework": "^10.0", "laravel/octane": "v2.9.3", "laminas/laminas-diactoros": "^3.0", "laravel/sanctum": "^3.2", "laravel/scout": "^10.10", "laravel/tinker": "^2.8", "meilisearch/meilisearch-php": "^1.9", "morilog/jalali": "3.*", "pbmedia/laravel-ffmpeg": "^8.5", "phpseclib/phpseclib": "^3.0", "predis/predis": "^2.2", "sentry/sentry-laravel": "^4.10", "spatie/laravel-prometheus": "^1.2", "spatie/laravel-ignition": "^2.0", "nunomaduro/collision": "^7.0", "barryvdh/laravel-ide-helper": "^3.1", "laravel/sail": "^1.34", "ext-swoole": "*"}, "require-dev": {"fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.4.4", "laravel/pint": "^1.0", "phpunit/phpunit": "^10.0"}, "autoload": {"files": ["app/Helpers/functions.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}, "laravel": {"dont-discover": ["barryvdh/laravel-ide-helper"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}