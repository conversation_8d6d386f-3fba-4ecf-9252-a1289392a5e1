import{o as t,c as s,x as r,z as i}from"./index-169996dc.js";const n={class:"text-center"},a={key:0,class:"text-h1 font-weight-medium"},c={key:1,class:"text-h4 font-weight-medium mb-3"},d={key:2,style:{"max-inline-size":"50vw"}},_={__name:"ErrorHeader",props:{statusCode:{type:[String,Number],required:!1},title:{type:String,required:!1},description:{type:String,required:!1}},setup(o){const e=o;return(p,l)=>(t(),s("div",n,[e.statusCode?(t(),s("h1",a,r(e.statusCode),1)):i("",!0),e.title?(t(),s("h4",c,r(e.title),1)):i("",!0),e.description?(t(),s("p",d,r(e.description),1)):i("",!0)]))}};export{_};
