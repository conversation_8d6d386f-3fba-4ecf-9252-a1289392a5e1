<?php

use App\Http\Controllers\Ai\AiSettingAdjustmentController;
use App\Http\Controllers\Ai\AiSettingDisplayController;
use App\Http\Controllers\Ai\AiSettingManagementController;
use App\Http\Middleware\Ai\AiSettingOverrideAuthorization;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum'])->group(function () {
    Route::prefix('ai-service')->group(function () {
        Route::post('/', [AiSettingManagementController::class, 'store']);
        Route::put('{id}', [AiSettingManagementController::class, 'update']);
        Route::delete('{id}', [AiSettingManagementController::class, 'delete']);
        Route::get('/', [AiSettingDisplayController::class, 'show']);
        Route::get('/admin-posts', [AiSettingDisplayController::class, 'show']);
    });
});

Route::middleware(AiSettingOverrideAuthorization::class)->group(function () {
    Route::prefix('ai-adjustment')->group(function () {
        Route::post('/override-settings', [AiSettingAdjustmentController::class, 'update']);
    });
});

