<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('basalam_users', function (Blueprint $table) {
            $table->id();
            $table->string('instagram_id')->unique();
            $table->string('last_message')->nullable();
            $table->unsignedInteger('message_count')->default(0);
            $table->boolean('is_follower')->default(false);
            $table->timestamp('last_message_time')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('basalam_users');
    }
};
