<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePlansRequest;
use App\Http\Requests\UpdatePlansRequest;
use App\Jobs\ProcessWaitingTransaction;
use App\Models\CrmAction;
use App\Models\Plans;
use App\Models\Page;
use App\Models\Transaction;
use App\Services\CrmApiService;
use App\Services\PaymentGatewayService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class PlansController extends Controller
{
	private CrmApiService $crmApiService;

	public function __construct(CrmApiService $crmApiService)
	{
		$this->crmApiService = $crmApiService;
	}

	/**
     * Display a listing of the resource.
     */
    public function index()
    {
        $plans = Plans::all();
        return $plans;
    }
    private function generateRandomString($length = 10) {
        return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
    }
    public function buy(StorePlansRequest $request){
        $userId = auth()->user()->id;
        if(!$request->page){
            $page = Page::where('user_id',$userId)->first();
        }else{
            $page = Page::where('user_id',$userId)->where('id',$request->page)->first();
        }
        if(!$page){
            return response()->json(['message'=>'not allowed page','success'=>false],404);
        }
        $plan = Plans::where('id',$request->plan)->first();
        if(!$plan){
            return response()->json(['message'=>'not allowed plan','success'=>false],404);
        }
        $transaction = new Transaction();
        $transaction->user_id = $userId;
        $transaction->plan_id = $plan->id;
        $transaction->page_id = $page->id;
        $transaction->status = 'waiting';
        $transaction->tag = $this->generateRandomString();
        $transaction->save();

	    // Add the transaction to the job queue for processing status after 10 minutes
	     ProcessWaitingTransaction::dispatch($transaction)->delay(now()->addMinutes(10));

        $sign_key = '8A97B68B9CE8D6ED136302302644D72EE3CB02BB14E432F99615588789DA1B5275EDF5A7DD21E2172A1D897329782E090A1A06D4230CE1D3AB0108B562892A3C564D19981CCF2AD07FD32AB28F9A44A21C61FFD7E1D854588AA6E5DEFFAD857D987FCBDB7EA1C73A940068D67624C855D9AC02D9914E0CE8FF2D6A877CBC0E3D';
        $callback = 'https://dmplus.manymessage.com/api/transactionCallback?tarnsaction='.urlencode($transaction->tag);
        $sign_data = $plan->price. '#'. $transaction->id . '#'. $callback;
        $sign = hash_hmac('SHA512', $sign_data, $sign_key);
        $paymentData = ["order_id"=>$transaction->id,"callback"=>$callback,"sign"=>$sign];
        $payment = new PaymentGatewayService('paystar', '2x4kg47qwm69ll');
        try {
            $resPayment = $payment->process($plan->price, $paymentData);
        } catch (\Throwable $th) {
            $resPayment = ["status"=>-1];
        }
        if ($resPayment["status"] !== 1) {
            $transaction->status = 'failed';
            $transaction->save();
            $msg = "درگاه پرداخت به مشکل خورده ولی سفارشتون رو دریافت کردیم";
            return reponse()->json(['success'=>false,'message'=>$msg],503);
        }
        $transaction->ref_num = $resPayment['data']['ref_num'];
        $transaction->token = $resPayment['data']['token'];
        $transaction->save();
        $redirectURL = 'https://core.paystar.ir/api/pardakht/payment?token='.$transaction->token;
        return response()->json(['success'=>true,'url'=>$redirectURL],200);
    }
    public function callback(Request $request){
        if(!$request->has('tarnsaction') || $request->tarnsaction==''){
            return redirect("/payment-result2?tag=0");
        }

        $transaction = Transaction::where('tag',$request->tarnsaction)->first();
        if(!$transaction || $transaction->status!='waiting'){
            return redirect("/payment-result2?tag=0");
        }
        $page = Page::where("id",$transaction->page_id)->first();
        $plan = Plans::where("id",$transaction->plan_id)->first();
        if($request->status != 1){
            $transaction->status = 'failed';
            $transaction->save();
            return redirect('https://dmplus.manymessage.com/payment-result2?tag='.$transaction->tag);
        }
        $sign_key = '8A97B68B9CE8D6ED136302302644D72EE3CB02BB14E432F99615588789DA1B5275EDF5A7DD21E2172A1D897329782E090A1A06D4230CE1D3AB0108B562892A3C564D19981CCF2AD07FD32AB28F9A44A21C61FFD7E1D854588AA6E5DEFFAD857D987FCBDB7EA1C73A940068D67624C855D9AC02D9914E0CE8FF2D6A877CBC0E3D';
        $callback = 'https://dmplus.manymessage.com/api/transactionCallback?tarnsaction='.urlencode($transaction->tag);
        $sign_data = $plan->price . '#' . $transaction->ref_num . '#' . $request->card_number . '#' . $request->tracking_code;
        $sign = hash_hmac('SHA512', $sign_data, $sign_key);
        $paymentData = ['refId' => $transaction->ref_num, 'sign' => $sign, 'amount' => $plan->price];
        $payment = new PaymentGatewayService('paystar', '2x4kg47qwm69ll');
        $resPayment = $payment->verify($paymentData);
        if ($resPayment['status'] == 1) {
            $transaction->status = 'failed';
            $transaction->save();
            return redirect('https://dmplus.manymessage.com/payment-result2?tag='.$transaction->tag);
        }
        $page->plan_id = $plan->id;
        if($page->expired_at){
            $page->expired_at = Carbon::parse($page->expired_at)->addDays($plan->time);
        }else{
            $page->expired_at = Carbon::now()->addDays($plan->time);
        }
        $page->save();
        $transaction->status = 'approved';
        $transaction->card_number = $request->card_number;
        $transaction->tracking_code = $request->tracking_code;
        $transaction->save();

	    // Send transaction data to the CRM
	    $crmAction = CrmAction::where('name', CrmAction::TRANSACTION_APPROVED)->first();
	    $this->crmApiService->sendTransactionData($transaction, $crmAction);

        return redirect('https://dmplus.manymessage.com/payment-result2?tag='.$transaction->tag);
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePlansRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show($tag)
    {
        $userId = auth()->user()->id;
        $transaction = Transaction::where('user_id',$userId)->where('tag',$tag)->first();
        if(!$transaction){
            return response()->json(['success'=>false,'message'=>'تراکنش نامعتبر است']);
        }
        $msg = "";
        if($transaction->status==='waiting'){
            $msg .= "تراکنش پرداخت نشده است";
        }
        if($transaction->status==='failed'){
            $msg .= "پرداخت ناموفق بود";
        }
        if($transaction->status==='approved'){
            $plan = Plans::where('id',$transaction->plan_id)->first();
            $msg .= "پرداخت موفق بود ";
            $msg .= " کد پیگیری: ".$transaction->tracking_code;
            $msg .= " مدت اعتبار: ".$plan->time." روز";
        }
        return response()->json(['success'=>true,'message'=>$msg]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Plans $plans)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePlansRequest $request, Plans $plans)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Plans $plans)
    {
        //
    }
}
