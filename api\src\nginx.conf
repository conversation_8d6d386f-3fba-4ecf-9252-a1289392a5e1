# Events block - Required in every Nginx configuration
events {
    worker_connections 1024;  # Adjust this number based on your system
}

# HTTP block - Contains the server block
http {
    # Server block
    server {
        listen 80;
        server_name dmplus.manymessage.com;

        # Route for the main Laravel app (first app) on port 8080
        location / {
            proxy_pass http://frankenphp:8080;  # Forward to the main Laravel app (port 8080)
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # Route for the webhook Laravel app (second app) on port 8081
        location /webhook/ {
            proxy_pass http://webhook_service:8081/;  # Forward to the webhook app (port 8081)
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

    }
}
