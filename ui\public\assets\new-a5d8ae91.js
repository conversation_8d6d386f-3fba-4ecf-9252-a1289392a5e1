import{k,j as w,r as u,o as h,f as C,e as a,a9 as F,b as e,af as B,d as V,ai as R,n as t,ad as j,a6 as f,aj as q,aa as E,al as I}from"./index-169996dc.js";import{E as S}from"./endpoints-454f23f6.js";import{r as c,a as T}from"./validations-4c0aab88.js";import{A as U}from"./account-svg-********.js";import{V as v,a as s}from"./VRow-6c1d54f3.js";import{V as A}from"./VForm-a73b6b87.js";import{V as p}from"./VTextField-a8984053.js";import"./VInput-c4d3942a.js";import"./index-00c7d20d.js";import"./VField-150a934a.js";const N={class:"d-flex flex-column ga-4"},M=V("p",{class:"text-justify"}," لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ، و با استفاده از طراحان گرافیک است. ",-1),$=V("span",null,"ذخیره",-1),X={__name:"new",setup(z){const g=k(),y=w(),d=u(),n=u(),i=u(),_=u(),m=u(!1),x=()=>{var o;(o=_.value)==null||o.validate().then(({valid:l})=>{l&&b()})},b=async()=>{var o;try{m.value=!0,await I.post(S.pages,{title:d.value,access_token:n.value,page_user_id:i.value}),g.push({name:"pages",query:{...y.query}})}catch(l){await T((o=l==null?void 0:l.data)==null?void 0:o.errors,l.status)}finally{m.value=!1}};return(o,l)=>(h(),C(F,{title:"افزودن اکانت"},{default:a(()=>[e(E,null,{default:a(()=>[e(B,null,{default:a(()=>[e(v,{align:"center"},{default:a(()=>[e(s,{cols:"12",md:"6"},{default:a(()=>[V("div",N,[e(R,{src:t(U),height:"300"},null,8,["src"]),M])]),_:1}),e(s,{cols:"12",md:"6"},{default:a(()=>[e(A,{ref_key:"refVForm",ref:_,onSubmit:j(x,["prevent"])},{default:a(()=>[e(v,null,{default:a(()=>[e(s,{cols:"12"},{default:a(()=>[e(p,{modelValue:t(d),"onUpdate:modelValue":l[0]||(l[0]=r=>f(d)?d.value=r:null),label:"عنوان",rules:t(c)("عنوان"),type:"tel",dir:"rtl"},null,8,["modelValue","rules"])]),_:1}),e(s,{cols:"12",md:"6"},{default:a(()=>[e(p,{modelValue:t(n),"onUpdate:modelValue":l[1]||(l[1]=r=>f(n)?n.value=r:null),label:"access token",type:"tel",rules:t(c)("توکن"),dir:"rtl"},null,8,["modelValue","rules"])]),_:1}),e(s,{cols:"12",md:"6"},{default:a(()=>[e(p,{modelValue:t(i),"onUpdate:modelValue":l[2]||(l[2]=r=>f(i)?i.value=r:null),label:"user id",rules:t(c)("کد کاربر"),type:"tel",dir:"rtl"},null,8,["modelValue","rules"])]),_:1}),e(s,{cols:"12"},{default:a(()=>[e(q,{block:"",loading:t(m),type:"submit"},{default:a(()=>[$]),_:1},8,["loading"])]),_:1}),e(s,{cols:"12",class:"text-center"})]),_:1})]),_:1},512)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}))}};export{X as default};
