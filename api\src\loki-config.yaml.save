auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9095

ingester:
  wal:
    enabled: true
    dir: /loki/data/wal

schema_config:
  configs:
    - from: 2023-01-01
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h
      chunks:
        prefix: chunk_
        period: 24h

storage_config:
  boltdb_shipper:
    active_index_directory: /loki/data/index
    shared_store: filesystem
    cache_location: /loki/data/cache
  filesystem:
    directory: /loki/data/chunks

compactor:
  working_directory: /loki/data/compactor
  shared_store: filesystem

memberlist:
zz  join_members:
    - loki:7946

limits_config:
  enforce_metric_name: false
  max_cache_freshness_per_query: 10m

chunk_store_config:
  max_look_back_period: 0s

table_manager:
  retention_deletes_enabled: true
  retention_period: 120h
