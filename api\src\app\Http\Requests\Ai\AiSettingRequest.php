<?php

namespace App\Http\Requests\Ai;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class AiSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'post_id'          => 'required|string',
            'has_custom_prompt'=> 'required|boolean',
            'ai_driver'        => 'required|string',
            'friendly_tone'    => 'nullable|required_unless:has_custom_prompt,true|integer',
            'bot_character'    => 'nullable|required_unless:has_custom_prompt,true|string',
            'post_description' => 'nullable|required_unless:has_custom_prompt,true|string',
            'custom_prompt'    => 'nullable|required_if:has_custom_prompt,true|string',
        ];
    }

    /**
     * Get custom error messages for validator.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // Required and boolean rules
            'has_custom_prompt.required' => 'You must specify whether a custom prompt is used.',
            'has_custom_prompt.boolean'  => 'The has_custom_prompt field must be true or false.',
            'custom_prompt.required_if'  => 'The custom prompt field is required when a custom prompt is used.',

            // Validation for required_unless
            'post_description.required_unless' => 'The post description is required when no custom prompt is used.',
            'bot_character.required_unless'    => 'The bot character is required when no custom prompt is used.',
            'friendly_tone.required_unless'    => 'The friendly tone is required when no custom prompt is used.',

            // String validation
            'post_id.required'        => 'The post ID is required.',
            'post_id.string'          => 'The post ID must be a string.',
            'bot_character.string'    => 'The bot character must be a string.',
            'post_description.string' => 'The post description must be a string.',
            'custom_prompt.string'    => 'The custom prompt must be a string.',
            'ai_driver.required'      => 'The AI driver is required.',
            'ai_driver.string'        => 'The AI driver must be a string.',

            // Integer validation
            'friendly_tone.integer' => 'The friendly tone must be an integer.',

            // Unique validation
            'post_id.unique' => 'The post ID must be unique.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new ValidationException($validator, response()->json([
            'success' => false,
            'message' => 'Validation failed.',
            'errors' => $validator->errors()
        ], 422));
    }
}
