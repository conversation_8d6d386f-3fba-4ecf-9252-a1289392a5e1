name: Deploy to Production

on:
  push:
    branches:
      - master
      - hosein

jobs:
  deploy-production:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Add SSH key for server access
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_KEY }}

      - name: Deploy to Production Server
        run: |
          ssh -o StrictHostKeyChecking=no root@************* 'bash /root/shop_backend/.scripts/deploy-production.sh'
