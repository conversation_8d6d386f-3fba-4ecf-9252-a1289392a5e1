<?php

namespace App\Services\Instagram\Sync\Post;

use App\Contracts\Instagram\MetaPostServiceInterface;
use App\DTOs\Instagram\MetaConfigDTO;
use App\Services\Instagram\Abstract\AbstractPostService;

class FacebookPostService extends AbstractPostService implements MetaPostServiceInterface
{
    protected string $baseUrl = 'https://graph.facebook.com';

    public function __construct(MetaConfigDTO $config) {
        parent::__construct($config);
    }

    protected function buildInitialEndpoint(): string
    {
        if (empty($this->config->pageId)) {
            throw new \InvalidArgumentException("Facebook page ID is required.");
        }

        return "{$this->baseUrl}/{$this->config->pageId}?fields={$this->getMediaFields()}
        &access_token={$this->config->accessToken}";
    }
}
