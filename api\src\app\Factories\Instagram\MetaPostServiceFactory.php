<?php

namespace App\Factories\Instagram;

use App\Contracts\Instagram\MetaPostServiceInterface;
use App\DTOs\Instagram\MetaConfigDTO;
use App\Models\Page;
use App\Services\Instagram\Sync\Post\FacebookPostService;
use App\Services\Instagram\Sync\Post\InstagramPostService;
use InvalidArgumentException;

class MetaPostServiceFactory
{
    /**
     * Creates and returns an instance of the appropriate MetaPostService.
     *
     * @param int $userId
     * @return MetaPostServiceInterface
     * @throws InvalidArgumentException
     */
    public static function create(int $userId): MetaPostServiceInterface
    {
        $page = Page::where('user_id', $userId)->first();

        if (!$page || !$page->access_token) {
            throw new InvalidArgumentException("Access token not found for the user.");
        }

        $accessToken = $page->access_token;

        if ($page->fb_user_id != 'loggedInWithInstagram') {
            $facebookPageId = $page->fb_user_id;
            return new FacebookPostService(new MetaConfigDTO($accessToken, $facebookPageId));
        }

        return new InstagramPostService(new MetaConfigDTO($accessToken));
    }
}
