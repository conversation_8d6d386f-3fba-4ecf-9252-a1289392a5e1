<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" data-stacking-context="true" aria-owns="sc-kxtuKG1" width="1920" height="1080" viewBox="0 0 1920 1080"><!-- Generated by dom-to-svg from https://design.template.net/editor/115701?activityId=2362013 --><style>@font-face { font-family: "Droid Sans"; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/droidsans/v18/SlGVmQWMvZQIdix7AFxXkHNSbRYXags.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: "Droid Sans"; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/droidsans/v18/SlGWmQWMvZQIdix7AFxXmMh3eDs1ZyHKpWg.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: "Droid Serif"; font-style: italic; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/droidserif/v19/tDbK2oqRg1oM3QBjjcaDkOr4nAfcHi6FRUI.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: "Droid Serif"; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/droidserif/v19/tDbI2oqRg1oM3QBjjcaDkOr9rAXWGQyH.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: "Droid Serif"; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/droidserif/v19/tDbV2oqRg1oM3QBjjcaDkOJGiRD7OwGtT0rU.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: "Merriweather Sans"; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/merriweathersans/v22/2-c99IRs1JiJN1FRAMjTN5zd9vgsFHX4QjXp8Bte9ZM.woff2") format("woff2"); unicode-range: U+460-52F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F; }
@font-face { font-family: "Merriweather Sans"; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/merriweathersans/v22/2-c99IRs1JiJN1FRAMjTN5zd9vgsFHX6QjXp8Bte9ZM.woff2") format("woff2"); unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+1EA0-1EF9, U+20AB; }
@font-face { font-family: "Merriweather Sans"; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/merriweathersans/v22/2-c99IRs1JiJN1FRAMjTN5zd9vgsFHX7QjXp8Bte9ZM.woff2") format("woff2"); unicode-range: U+100-24F, U+259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF; }
@font-face { font-family: "Merriweather Sans"; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/merriweathersans/v22/2-c99IRs1JiJN1FRAMjTN5zd9vgsFHX1QjXp8Bte.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: "Merriweather Sans"; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/merriweathersans/v22/2-c99IRs1JiJN1FRAMjTN5zd9vgsFHX4QjXp8Bte9ZM.woff2") format("woff2"); unicode-range: U+460-52F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F; }
@font-face { font-family: "Merriweather Sans"; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/merriweathersans/v22/2-c99IRs1JiJN1FRAMjTN5zd9vgsFHX6QjXp8Bte9ZM.woff2") format("woff2"); unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+1EA0-1EF9, U+20AB; }
@font-face { font-family: "Merriweather Sans"; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/merriweathersans/v22/2-c99IRs1JiJN1FRAMjTN5zd9vgsFHX7QjXp8Bte9ZM.woff2") format("woff2"); unicode-range: U+100-24F, U+259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF; }
@font-face { font-family: "Merriweather Sans"; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/merriweathersans/v22/2-c99IRs1JiJN1FRAMjTN5zd9vgsFHX1QjXp8Bte.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: "Material Icons"; font-style: normal; font-weight: 400; src: url("https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNcIhQ8tQ.woff2") format("woff2"); }
@font-face { font-family: "Material Icons Outlined"; font-style: normal; font-weight: 400; src: url("https://fonts.gstatic.com/s/materialiconsoutlined/v108/gok-H7zzDkdnRel8-DQ6KAXJ69wP1tGnf4ZGhUcel5euIg.woff2") format("woff2"); }
@font-face { font-family: Roboto; font-style: italic; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOkCnqEu92Fr1Mu51xFIzIXKMnyrYk.woff2") format("woff2"); unicode-range: U+460-52F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOkCnqEu92Fr1Mu51xMIzIXKMnyrYk.woff2") format("woff2"); unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOkCnqEu92Fr1Mu51xEIzIXKMnyrYk.woff2") format("woff2"); unicode-range: U+1F00-1FFF; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOkCnqEu92Fr1Mu51xLIzIXKMnyrYk.woff2") format("woff2"); unicode-range: U+370-3FF; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOkCnqEu92Fr1Mu51xHIzIXKMnyrYk.woff2") format("woff2"); unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+1EA0-1EF9, U+20AB; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOkCnqEu92Fr1Mu51xGIzIXKMnyrYk.woff2") format("woff2"); unicode-range: U+100-24F, U+259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOkCnqEu92Fr1Mu51xIIzIXKMny.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51S7ACc3CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+460-52F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51S7ACc-CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51S7ACc2CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+1F00-1FFF; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51S7ACc5CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+370-3FF; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51S7ACc1CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+1EA0-1EF9, U+20AB; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51S7ACc0CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+100-24F, U+259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51S7ACc6CsTYl4BO.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51TzBic3CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+460-52F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51TzBic-CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51TzBic2CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+1F00-1FFF; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51TzBic5CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+370-3FF; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51TzBic1CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+1EA0-1EF9, U+20AB; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51TzBic0CsTYl4BOQ3o.woff2") format("woff2"); unicode-range: U+100-24F, U+259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF; }
@font-face { font-family: Roboto; font-style: italic; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOjCnqEu92Fr1Mu51TzBic6CsTYl4BO.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu72xKKTU1Kvnz.woff2") format("woff2"); unicode-range: U+460-52F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu5mxKKTU1Kvnz.woff2") format("woff2"); unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7mxKKTU1Kvnz.woff2") format("woff2"); unicode-range: U+1F00-1FFF; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4WxKKTU1Kvnz.woff2") format("woff2"); unicode-range: U+370-3FF; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7WxKKTU1Kvnz.woff2") format("woff2"); unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+1EA0-1EF9, U+20AB; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7GxKKTU1Kvnz.woff2") format("woff2"); unicode-range: U+100-24F, U+259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 400; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fCRc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+460-52F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fABc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fCBc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+1F00-1FFF; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fBxc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+370-3FF; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fCxc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+1EA0-1EF9, U+20AB; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fChc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+100-24F, U+259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 500; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fBBc4AMP6lQ.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfCRc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+460-52F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfABc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfCBc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+1F00-1FFF; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBxc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+370-3FF; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfCxc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+1EA0-1EF9, U+20AB; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfChc4AMP6lbBP.woff2") format("woff2"); unicode-range: U+100-24F, U+259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF; }
@font-face { font-family: Roboto; font-style: normal; font-weight: 700; font-display: swap; src: url("https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4AMP6lQ.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
@font-face { font-family: "Material Design Icons"; src: url("https://design.template.net/static/media/materialdesignicons-webfont.66dc854e.eot#iefix&amp;v=5.4.55") format("embedded-opentype"), url("https://design.template.net/static/media/materialdesignicons-webfont.c0f7978a.woff2") format("woff2"), url("https://design.template.net/static/media/materialdesignicons-webfont.8ac434c8.woff") format("woff"), url("https://design.template.net/static/media/materialdesignicons-webfont.8ed8f071.ttf") format("truetype"); font-weight: 400; font-style: normal; }
@font-face { font-family: "Sifonn Basic Outline"; font-display: swap; src: local("Sifonn Basic Outline"), url("https://design.template.net/static/media/SIFONN_BASIC_OUTLINE.f1b670a9.otf") format("opentype"); }
@font-face { font-family: "Montagu Slab"; font-display: swap; src: local("Montagu Slab"), url("https://design.template.net/static/media/MontaguSlab-Regular.90eba8cf.ttf") format("truetype"); }
@font-face { font-family: "Montagu Slab"; font-display: swap; font-weight: 700; src: local("Montagu Slab"), url("https://design.template.net/static/media/MontaguSlab-Bold.23a5c6cc.ttf") format("truetype"); }
@font-face { font-family: "Zen Dots"; font-display: swap; src: local("Zen Dots"), url("https://design.template.net/static/media/ZenDots-Regular.d92bf543.woff2") format("truetype"); }
@font-face { font-family: Teresa; font-display: swap; font-weight: 700; src: local("Teresa Bold"), url("https://design.template.net/static/media/Teresa-Bold.fcf37877.ttf") format("truetype"); }
@font-face { font-family: Teresa; font-display: swap; src: local("Teresa Semi Bold"), url("https://design.template.net/static/media/Teresa-Semibold.dbcb7a77.ttf") format("truetype"); }
@font-face { font-family: Steppe; font-display: swap; src: local("Steppe"), url("https://design.template.net/static/media/Steppe.bf7defac.ttf") format("truetype"); }
@font-face { font-family: "South Town"; font-display: swap; src: local("South Town"), url("https://design.template.net/static/media/SouthTown.f4e2266c.otf") format("truetype"); }
@font-face { font-family: Sometimes; font-display: swap; font-weight: 700; src: local("Sometimes"), url("https://design.template.net/static/media/SometimesBold.042af6cc.otf") format("truetype"); }
@font-face { font-family: Sometimes; font-display: swap; src: local("Sometimes"), url("https://design.template.net/static/media/Sometimes.a1cc49bc.otf") format("truetype"); }
@font-face { font-family: Blackbird; font-display: swap; src: local("Blackbird"), url("https://design.template.net/static/media/blackbird.5b6af51c.otf") format("truetype"); }
@font-face { font-family: Now; font-display: swap; src: local("Now"), url("https://design.template.net/static/media/now.regular.43bf8f54.otf") format("truetype"); }
@font-face { font-family: Now; font-display: swap; src: local("Now Bold"), url("https://design.template.net/static/media/now.bold.33c1c2f2.otf") format("truetype"); }
@font-face { font-family: Norwester; font-display: swap; src: local("Norwester"), url("https://design.template.net/static/media/norwester.7bf22630.otf") format("truetype"); }
@font-face { font-family: Moniqa; font-display: swap; src: local("Moniqa"), url("https://design.template.net/static/media/Moniqa.7dfc9ecd.ttf") format("truetype"); }
@font-face { font-family: Lovelo; font-display: swap; src: local("Lovelo"), url("https://design.template.net/static/media/Lovelo.fca6bef8.otf") format("truetype"); }
@font-face { font-family: Lovelo; font-display: swap; src: local("Lovelo Bold"), url("https://design.template.net/static/media/LoveloBold.ab3fad68.otf") format("truetype"); }
@font-face { font-family: "League Spartan"; font-display: swap; font-weight: 700; src: local("Lovelo Bold"), url("https://design.template.net/static/media/LeagueSpartan-Bold.a6509aa3.otf") format("truetype"); }
@font-face { font-family: "La Clef"; font-display: swap; src: local("La Clef"), url("https://design.template.net/static/media/LaClef-Regular.dd9f55c6.otf") format("truetype"); }
@font-face { font-family: Kollektif; font-display: swap; src: local("Kollektif"), url("https://design.template.net/static/media/Kollektif.4d49be6e.ttf") format("truetype"); }
@font-face { font-family: Kollektif; font-display: swap; font-weight: 700; src: local("Kollektif Bold"), url("https://design.template.net/static/media/Kollektif-Bold.04c38a99.ttf") format("truetype"); }
@font-face { font-family: Kollektif; font-display: swap; font-weight: 700; font-style: italic; src: local("Kollektif Bold Italic"), url("https://design.template.net/static/media/Kollektif-BoldItalic.6763df24.ttf") format("truetype"); }
@font-face { font-family: Kollektif; font-display: swap; font-style: italic; src: local("Kollektif Italic"), url("https://design.template.net/static/media/Kollektif-Italic.a28604a0.ttf") format("truetype"); }
@font-face { font-family: Kioea; font-display: swap; src: local("Kioea"), url("https://design.template.net/static/media/Kioea.aa832eb7.ttf") format("truetype"); }
@font-face { font-family: Haskoy; font-display: swap; src: local("Haskoy"), url("https://design.template.net/static/media/Haskoy-Regular.f5aaade8.otf") format("truetype"); }
@font-face { font-family: Haskoy; font-display: swap; font-weight: 700; src: local("Haskoy Bold"), url("https://design.template.net/static/media/Haskoy-Bold.2ae29b85.otf") format("truetype"); }
@font-face { font-family: Haskoy; font-display: swap; font-weight: 700; font-style: italic; src: local("Haskoy Bold Italic"), url("https://design.template.net/static/media/Haskoy-BoldItalic.3fe55e16.otf") format("truetype"); }
@font-face { font-family: Haskoy; font-display: swap; font-style: italic; src: local("Haskoy Italic"), url("https://design.template.net/static/media/Haskoy-Italic.95996b54.otf") format("truetype"); }
@font-face { font-family: Gora; font-display: swap; src: local("Gora"), url("https://design.template.net/static/media/Gora-Free.af6eee1b.ttf") format("truetype"); }
@font-face { font-family: "Glacial Indifference"; font-display: swap; src: local("Glacial Indifference Regular"), url("https://design.template.net/static/media/GlacialIndifference-Regular.008080d5.otf") format("truetype"); }
@font-face { font-family: "Glacial Indifference"; font-display: swap; font-weight: 700; src: local("Glacial Indifference Bold"), url("https://design.template.net/static/media/GlacialIndifference-Bold.070a9269.otf") format("truetype"); }
@font-face { font-family: Genesys; font-display: swap; src: local("Genesys"), url("https://design.template.net/static/media/Genesys.fd1cf4f8.ttf") format("truetype"); }
@font-face { font-family: "Data Twenty"; font-display: swap; src: local("Data Twenty"), url("https://design.template.net/static/media/DataTwenty.9c613b87.otf") format("truetype"); }
@font-face { font-family: "Blueberry Sans"; font-display: swap; src: local("Blueberry Sans"), url("https://design.template.net/static/media/BlueberrySans-Regular.5ce52ca0.otf") format("truetype"); }
@font-face { font-family: "Blueberry Sans"; font-display: swap; font-weight: 700; src: local("Blueberry Sans Bold"), url("https://design.template.net/static/media/BlueberrySans-Bold.a8fe47e3.otf") format("truetype"); }
@font-face { font-family: "Bitter Pro"; font-display: swap; src: local("Bitter Pro"), url("https://design.template.net/static/media/BitterPro-Regular.c02b9f84.ttf") format("truetype"); }
@font-face { font-family: "Bitter Pro"; font-display: swap; font-weight: 700; src: local("Bitter Pro Bold"), url("https://design.template.net/static/media/BitterPro-Bold.aae80986.ttf") format("truetype"); }
@font-face { font-family: "Bitter Pro"; font-display: swap; font-style: italic; src: local("Bitter Pro Italic"), url("https://design.template.net/static/media/BitterPro-Italic.ddd0506e.ttf") format("truetype"); }
@font-face { font-family: "Bitter Pro"; font-display: swap; font-weight: 700; font-style: italic; src: local("Bitter Pro Bold Italic"), url("https://design.template.net/static/media/BitterPro-BoldItalic.5ee68c34.ttf") format("truetype"); }
@font-face { font-family: Barracuda; font-display: swap; font-weight: 700; font-style: italic; src: local("Barracuda Bold Italic"), url("https://design.template.net/static/media/Barracuda-BoldItalic.8f2d04f4.ttf") format("truetype"); }
@font-face { font-family: Barracuda; font-display: swap; src: local("Barracuda"), url("https://design.template.net/static/media/BitterPro-Regular.c02b9f84.ttf") format("truetype"); }
@font-face { font-family: Barracuda; font-display: swap; font-weight: 700; src: local("Barracuda Bold"), url("https://design.template.net/static/media/BitterPro-Bold.aae80986.ttf") format("truetype"); }
@font-face { font-family: Barracuda; font-display: swap; font-style: italic; src: local("Barracuda Italic"), url("https://design.template.net/static/media/BitterPro-Italic.ddd0506e.ttf") format("truetype"); }
@font-face { font-family: "Aber Mono Bold"; font-display: swap; src: local("Aber Mono Bold"), url("https://design.template.net/static/media/Aber-Mono-Bold.85cf9270.otf") format("truetype"); }
@font-face { font-family: "Aber Mono Light"; font-display: swap; src: local("Aber Mono Light"), url("https://design.template.net/static/media/Aber-Mono-Light.be3243a1.otf") format("truetype"); }
@font-face { font-family: "Aber Mono Regular"; font-display: swap; src: local("Aber Mono Regular"), url("https://design.template.net/static/media/Aber-Mono-Regular.ed9150b9.otf") format("truetype"); }
@font-face { font-family: "Adventure Hollow"; font-display: swap; src: local("Adventure Hollow"), url("https://design.template.net/static/media/AdventureHollow.14f1578d.otf") format("truetype"); }
@font-face { font-family: Adventure; font-display: swap; src: local("Adventure"), url("https://design.template.net/static/media/Adventure.5a181986.otf") format("truetype"); }
@font-face { font-family: "Bifocals Black Italic"; font-display: swap; src: local("Bifocals Black Italic"), url("https://design.template.net/static/media/BifocalsBlackItalic.56bed3da.otf") format("truetype"); }
@font-face { font-family: "Bifocals Black"; font-display: swap; src: local("Bifocals Black"), url("https://design.template.net/static/media/BifocalsBlack.0ee0c86c.otf") format("truetype"); }
@font-face { font-family: "Bifocals Italic"; font-display: swap; src: local("Bifocals Italic"), url("https://design.template.net/static/media/BifocalsItalic.ab969702.otf") format("truetype"); }
@font-face { font-family: Bifocals; font-display: swap; src: local("Bifocals"), url("https://design.template.net/static/media/Bifocals.14502ca4.otf") format("truetype"); }
@font-face { font-family: "Casanova Font Free"; font-display: swap; src: local("Casanova Font Free"), url("https://design.template.net/static/media/CasanovaFontFree.7297b315.ttf") format("truetype"); }
@font-face { font-family: Droid; font-display: swap; src: local("Droid"), url("https://design.template.net/static/media/Droid.0133b16f.otf") format("truetype"); }
@font-face { font-family: FirstFun; font-display: swap; src: local("FirstFun"), url("https://design.template.net/static/media/FirstFun.7a40a811.otf") format("truetype"); }
@font-face { font-family: Galgo; font-display: swap; src: local("Galgo"), url("https://design.template.net/static/media/Galgo.495e95f9.ttf") format("truetype"); }
@font-face { font-family: GarciaMarquez; font-display: swap; src: local("GarciaMarquez"), url("https://design.template.net/static/media/GarciaMarquez.183f8d5c.otf") format("truetype"); }
@font-face { font-family: "GarmentDistrict Regular"; font-display: swap; src: local("GarmentDistrict Regular"), url("https://design.template.net/static/media/GarmentDistrict-Regular.ee65447f.otf") format("truetype"); }
@font-face { font-family: getaway; font-display: swap; src: local("getaway"), url("https://design.template.net/static/media/getaway.7f270b97.otf") format("truetype"); }
@font-face { font-family: ilyas; font-display: swap; src: local("ilyas"), url("https://design.template.net/static/media/ilyas.75790c94.otf") format("truetype"); }
@font-face { font-family: "ImperialScript Regular"; font-display: swap; src: local("ImperialScript Regular"), url("https://design.template.net/static/media/ImperialScript-Regular.59b9883a.ttf") format("truetype"); }
@font-face { font-family: Jasper; font-display: swap; src: local("Jasper"), url("https://design.template.net/static/media/Jasper.65ab20aa.otf") format("truetype"); }
@font-face { font-family: KOGOK; font-display: swap; src: local("KOGOK"), url("https://design.template.net/static/media/KOGOK.f103334d.otf") format("truetype"); }
@font-face { font-family: Males; font-display: swap; src: local("Males"), url("https://design.template.net/static/media/Males.14f8d9e7.ttf") format("truetype"); }
@font-face { font-family: "MeaCulpa Regular"; font-display: swap; src: local("MeaCulpa Regular"), url("https://design.template.net/static/media/MeaCulpa-Regular.2509685c.ttf") format("truetype"); }
@font-face { font-family: "Mohaw Regular"; font-display: swap; src: local("Mohaw Regular"), url("https://design.template.net/static/media/Mohaw-Regular.bd788d80.otf") format("truetype"); }
@font-face { font-family: "MuskegonCFSemiBold SemiBold"; font-display: swap; src: local("MuskegonCFSemiBold SemiBold"), url("https://design.template.net/static/media/MuskegonCFSemiBold-SemiBold.f1bf3af8.otf") format("truetype"); }
@font-face { font-family: "NeutralFace Bold"; font-display: swap; src: local("NeutralFace Bold"), url("https://design.template.net/static/media/NeutralFace-Bold.25a96bc3.otf") format("truetype"); }
@font-face { font-family: NeutralFace; font-display: swap; src: local("NeutralFace"), url("https://design.template.net/static/media/NeutralFace.715a056c.otf") format("truetype"); }
@font-face { font-family: "Nocturne Rough"; font-display: swap; src: local("Nocturne Rough"), url("https://design.template.net/static/media/Nocturne-Rough.2232f3a9.otf") format("truetype"); }
@font-face { font-family: Nocturne; font-display: swap; src: local("Nocturne"), url("https://design.template.net/static/media/Nocturne.165d6b6f.ttf") format("truetype"); }
@font-face { font-family: NocturneRough; font-display: swap; src: local("NocturneRough"), url("https://design.template.net/static/media/NocturneRough.bb88829a.ttf") format("truetype"); }
@font-face { font-family: "Praise Regular"; font-display: swap; src: local("Praise Regular"), url("https://design.template.net/static/media/Praise-Regular.738c4fa6.ttf") format("truetype"); }
@font-face { font-family: "Protomo Outline"; font-display: swap; src: local("Protomo Outline"), url("https://design.template.net/static/media/Protomo-Outline.fa5c74a2.otf") format("truetype"); }
@font-face { font-family: Protomo; font-display: swap; src: local("Protomo"), url("https://design.template.net/static/media/Protomo.fae37a88.otf") format("truetype"); }
@font-face { font-family: Sesenta; font-display: swap; src: local("Sesenta"), url("https://design.template.net/static/media/Sesenta.a3826d9f.otf") format("truetype"); }
@font-face { font-family: "Stay Classy SLDT"; font-display: swap; src: local("Stay Classy SLDT"), url("https://design.template.net/static/media/StayClassySLDT.780d9dd7.ttf") format("truetype"); }
@font-face { font-family: "Qanaya Regular"; font-display: swap; font-style: italic; src: local("Qanaya Regular"), url("https://design.template.net/static/media/Qanaya-Regular.3e1610e5.otf") format("truetype"); }
@font-face { font-family: "Qanaya Regular"; font-display: swap; font-style: italic; src: local("Qanaya Regular"), url("https://design.template.net/static/media/Qanaya-Round.ab33c701.otf") format("truetype"); }
@font-face { font-family: "Brittany Signature"; font-display: swap; src: local("Brittany Signature"), url("https://design.template.net/static/media/BrittanySignature.b4d584ed.ttf") format("truetype"); }
@font-face { font-family: fa-brands-400; src: url("https://editors-cdn.template.net/lib/font-awesome/fa-brands-400.ttf"); }
@font-face { font-family: fa-duotone-900; src: url("https://editors-cdn.template.net/lib/font-awesome/fa-duotone-900.ttf"); }
@font-face { font-family: fa-light-300; src: url("https://editors-cdn.template.net/lib/font-awesome/fa-light-300.ttf"); }
@font-face { font-family: fa-regular-400; src: url("https://editors-cdn.template.net/lib/font-awesome/fa-regular-400.ttf"); }
@font-face { font-family: fa-sharp-solid-900; src: url("https://editors-cdn.template.net/lib/font-awesome/fa-sharp-solid-900.ttf"); }
@font-face { font-family: "fa-solid-900.ttf"; src: url("https://editors-cdn.template.net/lib/font-awesome/fa-solid-900.ttf"); }
@font-face { font-family: fa-thin-100; src: url("https://editors-cdn.template.net/lib/font-awesome/fa-thin-100.ttf"); }
@font-face { font-family: fa-v4compatibility; src: url("https://editors-cdn.template.net/lib/font-awesome/fa-v4compatibility.ttf"); }
@font-face { font-family: slick; font-weight: 400; font-style: normal; src: url("https://design.template.net/static/media/slick.ced611da.eot#iefix") format("embedded-opentype"), url("https://design.template.net/static/media/slick.b7c9e1e4.woff") format("woff"), url("https://design.template.net/static/media/slick.d41f55a7.ttf") format("truetype"), url("https://design.template.net/static/media/slick.f97e3bbf.svg#slick") format("svg"); }
@font-face { font-family: revicons; src: url("https://design.template.net/static/media/revicons.04eb8fc5.woff") format("woff"), url("https://design.template.net/static/media/revicons.17629a5d.ttf") format("ttf"), url("https://design.template.net/static/media/revicons.2feb69cc.eot") format("ttf"); }
</style><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><g data-tag="div" id="sc-kxtuKG1" class="sc-kxtuKG eFFhPb blockWrapper blockWrapper-0" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-kxtuKG11)" aria-owns="sc-dDPqvT1"><mask id="mask-for-sc-kxtuKG11"><rect width="1920" height="1080" x="0" y="0" fill="#ffffff"/></mask><g data-tag="div" id="sc-dDPqvT1" class="sc-dDPqvT jmmWLk block-wrapper" data-z-index="auto" data-stacking-context="true" aria-owns="s0kdHHj5_"><g data-tag="div" id="s0kdHHj5_" class="sc-MAqyW febzfg graphic-block" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-s0kdHHj5_1)" aria-owns="wrapper-print-block"><mask id="mask-for-s0kdHHj5_1"><rect width="1920" height="1080" x="0" y="0" fill="#ffffff"/><rect width="419.640625" height="430.140625" x="-30" y="-40" fill="#ffffff"/><rect width="1024.625" height="362.890625" x="961.9400024414062" y="-40.122100830078125" fill="#ffffff"/><rect width="629.640625" height="457.421875" x="1405.0999755859375" y="28.996599197387695" fill="#ffffff"/><rect width="454.640625" height="511.5" x="625.989990234375" y="213.6490020751953" fill="#ffffff"/><rect width="384.640625" height="363.3905944824219" x="1245.300048828125" y="447.9389953613281" fill="#ffffff"/><rect width="1184.625" height="283.84375" x="-29.954200744628906" y="780.1409912109375" fill="#ffffff"/><rect width="539.640625" height="496.265625" x="420.0459899902344" y="-72.94660186767578" fill="#ffffff"/><rect width="714.6406860351562" height="380.29693603515625" x="723.0490112304688" y="700.5499877929688" fill="#ffffff"/><rect width="514.640625" height="514.640625" x="35.567901611328125" y="490.22100830078125" fill="#ffffff"/><rect width="429.6405029296875" height="404.7187805175781" x="1687.8800048828125" y="160.76400756835938" fill="#ffffff"/><rect width="584.6407470703125" height="478.12506103515625" x="1474.3499755859375" y="700.4879760742188" fill="#ffffff"/></mask><g data-tag="div" id="wrapper-print-block" class="sc-hQrevV dXrOKw" data-z-index="auto" data-stacking-context="true" aria-owns="DTH1NFPfj xL_G3rC9l TQhZtI3-x ZlZEGieMu 1-CT3t5T0 8iOkaT4Uv etTz4kBVx -RbQuy7Ki ZTFpaVvzu QhfbXYR2h krhR1mXFi"><g data-tag="div" id="DTH1NFPfj" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE1"><g data-tag="div" id="sc-hgYirE1" class="sc-hgYirE fRfoby" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK1"><g data-tag="div" id="sc-gLgtkK1" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL1"><g data-tag="div" id="sc-cNYriL1" class="sc-cNYriL iJzdUB itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc1"><g data-tag="svg" id="sc-cyRTDc1" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc11)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc11"><rect width="419.6409912109375" height="430.1409912109375" x="-30" y="-40" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 233 239" data-width="" data-height="" transform="matrix(1.80103 0 0 1.79975 -30 -40)"><path d="M23.5559,11.778A11.778,11.778,0,1,1,11.7781,0A11.7779,11.7779,0,0,1,23.5559,11.778zM11.7781,43.6768A11.778,11.778,0,1,0,23.5559,55.4547A11.7779,11.7779,0,0,0,11.7781,43.6768zM11.7781,84.8997A11.7781,11.7781,0,1,0,23.5559,96.6778A11.778,11.778,0,0,0,11.7781,84.9zM11.7781,128.5765A11.778,11.778,0,1,0,23.5559,140.3545A11.778,11.778,0,0,0,11.7781,128.5765zM11.7781,172.2533A11.778,11.778,0,1,0,23.5559,184.0313A11.7779,11.7779,0,0,0,11.7781,172.2533zM11.7781,215.93A11.778,11.778,0,1,0,23.5559,227.708A11.778,11.778,0,0,0,11.7781,215.93zM53.001,0A11.778,11.778,0,1,0,64.779,11.778A11.7779,11.7779,0,0,0,53.001,0zM53.001,43.6768A11.778,11.778,0,1,0,64.779,55.4547A11.7778,11.7778,0,0,0,53.001,43.6768zM53.001,84.8997A11.7781,11.7781,0,1,0,64.779,96.6778A11.7779,11.7779,0,0,0,53.001,84.9zM53.001,128.5765A11.778,11.778,0,1,0,64.779,140.3545A11.7779,11.7779,0,0,0,53.001,128.5765zM53.001,172.2533A11.778,11.778,0,1,0,64.779,184.0313A11.7779,11.7779,0,0,0,53.001,172.2533zM53.001,215.93A11.778,11.778,0,1,0,64.779,227.708A11.7779,11.7779,0,0,0,53.001,215.93zM95.2055,0A11.778,11.778,0,1,0,106.9835,11.778A11.7781,11.7781,0,0,0,95.2055,0zM95.2055,43.6768A11.778,11.778,0,1,0,106.9835,55.4547A11.778,11.778,0,0,0,95.2055,43.6768zM95.2055,84.8997A11.7781,11.7781,0,1,0,106.9835,96.6778A11.7781,11.7781,0,0,0,95.2055,84.9zM95.2055,128.5765A11.778,11.778,0,1,0,106.9835,140.3545A11.7781,11.7781,0,0,0,95.2055,128.5765zM95.2055,172.2533A11.778,11.778,0,1,0,106.9835,184.0313A11.778,11.778,0,0,0,95.2055,172.2533zM95.2055,215.93A11.778,11.778,0,1,0,106.9835,227.708A11.7781,11.7781,0,0,0,95.2055,215.93zM136.4286,0A11.778,11.778,0,1,0,148.2065,11.778A11.778,11.778,0,0,0,136.4286,0zM136.4286,43.6768A11.778,11.778,0,1,0,148.2065,55.4547A11.7779,11.7779,0,0,0,136.4286,43.6768zM136.4286,84.8997A11.7781,11.7781,0,1,0,148.2065,96.6778A11.7781,11.7781,0,0,0,136.4286,84.9zM136.4286,128.5765A11.778,11.778,0,1,0,148.2065,140.3545A11.778,11.778,0,0,0,136.4286,128.5765zM136.4286,172.2533A11.778,11.778,0,1,0,148.2065,184.0313A11.778,11.778,0,0,0,136.4286,172.2533zM136.4286,215.93A11.778,11.778,0,1,0,148.2065,227.708A11.778,11.778,0,0,0,136.4286,215.93zM178.1422,0A11.778,11.778,0,1,0,189.92,11.778A11.7779,11.7779,0,0,0,178.1422,0zM178.1422,43.6768A11.778,11.778,0,1,0,189.92,55.4547A11.7778,11.7778,0,0,0,178.1422,43.6768zM178.1422,84.8997A11.7781,11.7781,0,1,0,189.92,96.6778A11.7779,11.7779,0,0,0,178.1422,84.9zM178.1422,128.5765A11.778,11.778,0,1,0,189.9203,140.3545A11.7779,11.7779,0,0,0,178.1422,128.5765zM178.1422,172.2533A11.778,11.778,0,1,0,189.9203,184.0313A11.7779,11.7779,0,0,0,178.1422,172.2533zM178.1422,215.93A11.778,11.778,0,1,0,189.92,227.708A11.7779,11.7779,0,0,0,178.1422,215.93zM221.3283,23.556A11.778,11.778,0,1,0,209.55,11.778A11.7781,11.7781,0,0,0,221.3283,23.556zM221.3283,43.6768A11.778,11.778,0,1,0,233.1063,55.4547A11.778,11.778,0,0,0,221.3283,43.6768zM221.3283,84.8997A11.7781,11.7781,0,1,0,233.1063,96.6778A11.7781,11.7781,0,0,0,221.3283,84.9zM221.3283,128.5765A11.778,11.778,0,1,0,233.1063,140.3545A11.7781,11.7781,0,0,0,221.3283,128.5765zM221.3283,172.2533A11.778,11.778,0,1,0,233.1063,184.0313A11.778,11.778,0,0,0,221.3283,172.2533zM221.3283,215.93A11.778,11.778,0,1,0,233.1063,227.708A11.7781,11.7781,0,0,0,221.3283,215.93z" fill="rgb(143, 111, 249)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="xL_G3rC9l" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE2"><g data-tag="div" id="sc-hgYirE2" class="sc-hgYirE dNfgLv" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK2"><g data-tag="div" id="sc-gLgtkK2" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL2"><g data-tag="div" id="sc-cNYriL2" class="sc-cNYriL cZkNOg itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc2"><g data-tag="svg" id="sc-cyRTDc2" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc21)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc21"><rect width="1024.64013671875" height="362.8909912109375" x="961.9400024414062" y="-40.122100830078125" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 2463 873" data-width="" data-height="" transform="matrix(0.416013 0 0 0.415683 961.94 -40.1221)"><path d="M570.2648,457.307C570.2648,227.1805,756.8192,40.6261,986.9457,40.6261C1217.0722,40.6261,1403.6266,227.1805,1403.6266,457.307C1403.6266,687.4335,1217.0722,873.9879,986.9457,873.9879C756.8192,873.9879,570.2648,687.4335,570.2648,457.307z" fill="rgb(101, 44, 209)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M737.2436,627.5638C809.42,586.5062,792.3925,440.67,902.69,413.0958S1141.6691,575.4787,1291.7968,560.16S1368.569,428.8161,1478.69,376.33C1579.9848,328.0507,1670.8382,386.7522,1788.1368,410.0321C2095.7874,471.0906,2439.662,259.5662,2428.4768,177.181C2420.8662,121.1228,2181.2568,104.3228,1702.3491,73.011C267.5134-20.8017,131.7219-23.4739,96.9032,60.7553C15.1958,258.4117,579.3121,717.4037,737.2436,627.5638z" fill="rgb(143, 111, 249)" order="1" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M628.5981,680.2827C446.0447,682.6964-59.9128,302.8286,5.8576,68.9682C83.9218-78.9525,969.6618,54.0373,1697.0076,80.5968C1971.1339,111.3353,2387.5681,93.6814,2462.8684,191.9339C2465.5372,211.6753,2450.4044,237.667,2419.0968,267.1211C2251.866,405.9826,2017.0108,478.4633,1786.7582,439.1884C1648.99,405.6527,1443.2188,312.1958,1385.91,500.11C1368.9694,595.1712,1244.3271,623.4553,1146.2628,573.9673C990.6299,497.0773,834.2267,319.4614,747.1751,568.991C724.9126,630.178,697.0071,682.76,628.5981,680.2827zM271.4487,7.8013C155.4323,13.3567,45.6321-1.1815,9.5611,70.4849C-68.3181,273.2586,486.3277,747.3944,680.46,664.3565L681.4492,666.0947L680.46,664.3565C735.8963,631.4839,742.7369,540.575,781.9321,489.1307C851.3451,394.9528,946.8974,443.0396,1039.0241,507.0191C1139.6285,587.9447,1343.7405,654.2791,1382.145,498.7448C1419.08,360.2433,1571.92,365.08,1688.8061,408.9729C1835.0668,467.3216,2047.9589,447.2499,2157.0626,405.0563C2270.7974,363.8921,2441.3636,290.6963,2458.9052,192.5006C2455.2182,165.4006,2395.2914,146.0171,2264.5864,129.6445C1764.407,78.3058,399.0653,2.3339,271.4487,7.8013z" fill="rgb(186, 67, 248)" order="2" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="TQhZtI3-x" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE3"><g data-tag="div" id="sc-hgYirE3" class="sc-hgYirE kDSBHQ" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK3"><g data-tag="div" id="sc-gLgtkK3" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL3"><g data-tag="div" id="sc-cNYriL3" class="sc-cNYriL ccxpSH itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc3"><g data-tag="svg" id="sc-cyRTDc3" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc31)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc31"><rect width="629.6409912109375" height="457.4219970703125" x="1405.0999755859375" y="28.996599197387695" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 196 143" data-width="" data-height="" transform="matrix(3.21245 0 0 3.19876 1405.1 28.9966)"><path d="M55.5,21.16C52.34,24.32,59.04,34.47,64.29,29.22S60.36,16.31,55.5,21.16z" fill="rgb(222, 192, 241)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M13.26,62.69C13.26,70.85,27.62,70.39,27.62,62.69S13.26,54.52,13.26,62.69z" fill="rgb(222, 192, 241)" order="1" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M99,120.16C100.85,127.7,113.36,120.16,109.57,116.38S97.14,112.61,99,120.16z" fill="rgb(222, 192, 241)" order="2" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M165.88,107.77C163.34,117.77,182.74,120.25,182.74,111.83S168.16,98.8,165.88,107.77z" fill="rgb(222, 192, 241)" order="3" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M127.11,44.58C118.79,47.58,128.88,61.88,135.39,55.36S132.92,42.51,127.11,44.58z" fill="rgb(222, 192, 241)" order="4" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M52.18,115.72C55.5,111.29,47.55,86.43,42.67,88.07S41.49,101.89,41.49,101.89S33.38,93.33,30.63,95.89S36.44,107.29,36.44,107.29S21.38,101.97,20.05,107.65S48.3,120.89,52.18,115.72z" fill="rgb(222, 192, 241)" order="5" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M84.37,69.84C86.21,77.39,98.72,69.84,94.93,66.06S82.52,62.29,84.37,69.84z" fill="rgb(222, 192, 241)" order="6" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="ZlZEGieMu" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE4"><g data-tag="div" id="sc-hgYirE4" class="sc-hgYirE eOZSLJ" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK4"><g data-tag="div" id="sc-gLgtkK4" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL4"><g data-tag="div" id="sc-cNYriL4" class="sc-cNYriL ZDwsG itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc4"><g data-tag="svg" id="sc-cyRTDc4" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc41)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc41"><rect width="454.6409912109375" height="511.5" x="625.989990234375" y="213.6490020751953" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 152 171" data-width="" data-height="" transform="matrix(2.99106 0 0 2.99123 625.99 213.649)"><path d="M49.0855,83.6488L98.162,96.2558L139.192,171.644L41.03,146.4273L0,71.0393z" fill="rgb(183, 156, 237)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M152.573,0L139.192,171.644L98.162,96.256L152.573,0" fill="rgb(231, 205, 224)" order="1" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M152.5731,0L0,71.0393L98.1619,96.256z" fill="rgb(222, 192, 241)" order="2" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="1-CT3t5T0" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE5"><g data-tag="div" id="sc-hgYirE5" class="sc-hgYirE kwTRDS" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK5"><g data-tag="div" id="sc-gLgtkK5" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL5"><g data-tag="div" id="sc-cNYriL5" class="sc-cNYriL ORODR itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc5"><g data-tag="svg" id="sc-cyRTDc5" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc51)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc51"><rect width="384.6409912109375" height="363.3909606933594" x="1245.300048828125" y="447.9389953613281" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 132 125" data-width="" data-height="" transform="matrix(2.91395 0 0 2.90713 1245.3 447.939)"><path d="M48.3,0L0,36.322L10.866,115.411L59.166,79.089L48.3,0" fill="rgb(222, 192, 241)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M59.166,79.089L10.866,115.411L83.789,125.47L132.089,89.148L59.166,79.089" fill="rgb(183, 156, 237)" order="1" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M121.223,10.059L48.3,0L59.166,79.089L132.089,89.148L121.223,10.059" fill="rgb(249, 220, 223)" order="2" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="8iOkaT4Uv" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE6"><g data-tag="div" id="sc-hgYirE6" class="sc-hgYirE lcykuy" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK6"><g data-tag="div" id="sc-gLgtkK6" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL6"><g data-tag="div" id="sc-cNYriL6" class="sc-cNYriL bjpALJ itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc6"><g data-tag="svg" id="sc-cyRTDc6" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc61)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc61"><rect width="1184.6400146484375" height="283.843994140625" x="-29.954200744628906" y="780.1409912109375" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 772 185" data-width="" data-height="" transform="matrix(1.53451 0 0 1.53429 -29.9542 780.141)"><path d="M679.83,185H0V2.16C48.23-10.52,89.42,36.32,91.21,44C99.57,79.58,101.61,63.35,159.64,78.56C190.04,86.56,234.98,84.62,261.64,96.22C296.94,111.55,289.7,127.52,340.64,125.28C381.95,123.46,417.88,139.44,460.46,142.48C538.54,148,610.92,154.29,679.83,185z" fill="rgb(143, 111, 249)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M768.61,179.17C675.8,138.48,583.59,130,486.69,127C457.58,126.09,432.06,105.92,405.04,109.34C390.44,111.19,375.35,113.1,359.6,114.34C323.12,117.14,311.96,119.89,299.04,110.75C292.04,105.84,284.22,92.82,270.84,88C255.28,82.38,233.34,82.21,212.12,82C194.68,81.86,176.64,84.18,161.52,81.07C113,71,94.11,95.73,93.63,68.22C93.48,59.22,82.92,43.5,78.35,28.67L73.57,26.75C78.4,42.4,89.65,45.24,89.81,54.58S124.21,67.1,124.21,67.1A202.63,202.63,0,0,1,163,71.47C177.18,74.4,194.74,74.95,211.72,75.09C233.72,75.26,256.54,75.44,273.65,81.62C288.22,86.87,296.65,92.81,304.04,98.06C316.42,106.82,325.36,109.95,358.39,107.42C373.91,106.22,388.89,104.32,403.39,102.49C430.8,99.02,456.68,119.14,486.53,120.08C584.69,123.16,678.07,131.92,772.03,173.08z" fill="rgb(143, 111, 249)" order="1" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="etTz4kBVx" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE7"><g data-tag="div" id="sc-hgYirE7" class="sc-hgYirE jTurIR" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK7"><g data-tag="div" id="sc-gLgtkK7" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL7"><g data-tag="div" id="sc-cNYriL7" class="sc-cNYriL cOmzWC itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc7"><g data-tag="svg" id="sc-cyRTDc7" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc71)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc71"><rect width="539.6409912109375" height="496.2659912109375" x="420.0459899902344" y="-72.94660186767578" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 326 301" data-width="" data-height="" transform="matrix(1.65534 0 0 1.64872 420.046 -72.9466)"><path d="M46.9,142.4C54.3,137,60.8,133.6,67.9,131.4C70.4,130.5,73,130.2,75.6,130.5C80.2,131.1,82.1,134.3,79.7,138.3C77.6,141.9,75.7,145.6,72.7,148.7C67.3,154.3,60.9,158.8,53.7,161.8C50.8,163,47.7,163.6,44.5,163.5C40.3,163.4,38.2,160.7,36.7,157.5S36.7,151.7,38.9,149.2C41.5,146.1,45,143.9,46.9,142.4z" fill="rgb(183, 156, 237)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M192.2,47.3C195.8,44.8,198.5,42.8,201.6,41.5C203.4,40.7,205.3,40.3,207.2,40.3C211.2,40.5,213.1,43.6,210.7,46.9C204.5,55.3,196.4,60.9,185.9,62.5C183.2,62.9,181.9,61.3,182.3,58.5S184.6,53.5,186.7,51.5S190.9,48.3,192.2,47.3z" fill="rgb(183, 156, 237)" order="1" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M177.7,123.9C180.1,122.1,182.8,120.9,185.6,120.1C188,119.4,190,119.9,191.4,121.9S192.5,125.9,191.1,128C188.9,131,186.2,133.6,183.2,135.7C180.1,137.7,176.9,139.6,173.6,141.1C171.9,142,170,142.4,168,142.2C164.4,142,163,139.5,164.8,136.3C165.4,135.2,166.1,134.3,167,133.4C170.2,130,173.9,126.8,177.7,123.9z" fill="rgb(183, 156, 237)" order="2" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M115.8,162.8C112.6,164.6,109.1,167.1,105,168.2C101.7,169.1,99.9,168.5,98.3,166.2S96.8,162,99.2,159.3C103.9,154,109.9,150,116.6,147.8C119.3,147,121.8,146.9,123.5,149.3C125,151.5,124.9,153.2,122.6,156.2C120.7,158.7,118.4,160.9,115.8,162.8z" fill="rgb(183, 156, 237)" order="3" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M146.1,184.7C142.2,187.8,137.8,190.2,133.1,191.8C129.1,193,126.4,192.6,125.7,190.7C124.6,187.8,126.7,185.8,128.6,184.1C134.9,178.1,142.5,173.8,150.9,171.5C152.8,171,154.8,170.7,155.9,172.6C156.9,174.3,155.7,175.8,154.7,177.2C152.5,180.5,149.2,182.6,146.1,184.7z" fill="rgb(183, 156, 237)" order="4" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M143.4,112.5C137.4,116.9,130.8,120.3,123.7,122.6C121.8,123.3,119.4,124.5,118.1,122.3C117.1,120.5,118.9,118.8,120.2,117.4C128.2,109.2,138.1,104.4,148.8,101.2C150.1,100.8,151.7,100.5,152.6,101.8C153.7,103.3,152.3,104.6,151.4,105.9C149.4,108.7,146.2,110.3,143.4,112.5z" fill="rgb(183, 156, 237)" order="5" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M189,252.1C185.4,254.6,182.3,256.7,178.7,257.8C177.1,258.4,175.3,258.7,173.6,258.6C170.1,258.3,168.8,255.6,170.6,252.5C172.2,249.7,175,248,177.5,246.1C182.2,242.3,187.8,239.9,193.7,239.1C195.4,238.9,197.3,238.7,198.1,240.6C198.8,242.2,197.9,243.5,197,244.8C194.8,247.7,192.1,250.1,189,252.1z" fill="rgb(183, 156, 237)" order="6" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M236.5,189.2C239,187.3,241.8,185.7,244.7,184.5C246.3,183.8,248.1,183.6,249.8,183.9C253.3,184.6,254.7,187.8,252.5,190.6C247.7,196.8,241,201.1,233.4,203C231.2,203.6,229.2,203.1,228,201.3C226.6,199.3,227.7,197.5,229,195.7C230.9,193,233.8,191.2,236.5,189.2z" fill="rgb(183, 156, 237)" order="7" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M107.7,250.2C103.1,253.6,99,256.7,94,258.3C92.1,258.9,89.7,260.2,88.4,258.1S89,254.2,90.5,252.7C97.7,246,106.1,241.1,115,237.1C115.5,236.9,116,236.7,116.5,236.7C117.5,236.5,118.6,236.3,119.3,237.3S119.6,239.2,119.1,240.1C117.7,242.4,115.9,244.4,113.8,245.9C111.6,247.5,109.4,249,107.7,250.2z" fill="rgb(183, 156, 237)" order="8" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M222.4,62.4C225.5,59.8,229.1,57.8,233,56.6C234.5,56.1,236,55.9,237.6,55.9C240,56.1,240.9,57.6,239.7,59.7C238.9,61.1,237.9,62.3,236.8,63.4C232.1,68.4,226.2,71.5,219.9,73.9C218.9,74.3,217.9,74.5,216.8,74.5C213.6,74.7,212.5,72.9,213.9,70.1C214.8,68.4,216.1,66.9,217.6,65.7C219.2,64.6,220.8,63.5,222.4,62.4z" fill="rgb(183, 156, 237)" order="9" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M90.5,97.8C87.1,100.4,83.3,102.3,79.2,103.4C77.4,103.8,75.2,104.5,74,102.6S74.2,99,75.5,97.6C81.1,91.7,88,87.3,95.7,84.6C95.9,84.5,96,84.5,96.2,84.5C97.8,84.2,99.4,83.8,100.4,85.3C101.2,86.6,100.3,87.9,99.5,89C97.1,92.5,94.1,95.4,90.5,97.8z" fill="rgb(183, 156, 237)" order="10" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M66.3,211C62.1,213.3,58,215.6,53.8,217.9C52.9,218.4,51.9,218.8,50.9,219C49.3,219.4,47.3,220.3,46.3,218.4C45.5,216.7,46.8,215.1,48,213.9C50.2,211.6,52.6,209.6,55.2,207.7C59.3,205,63.7,202.7,68.2,200.8C69.3,200.3,70.5,200,71.7,199.8C74.3,199.5,75.3,200.8,73.9,203.1C72,206.3,69.4,208.9,66.3,211z" fill="rgb(183, 156, 237)" order="11" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M144.6,68.1C141.3,70.5,137.6,72.2,133.7,73.2C132,73.6,130.1,73.7,129.2,72C128.4,70.5,129.2,69,130.2,67.8C134.9,62.1,141.2,57.9,148.3,55.8C149,55.6,149.6,55.5,150.3,55.5C153.6,55.5,154.6,57.2,152.7,60C150.6,63.2,147.8,66,144.6,68.1z" fill="rgb(183, 156, 237)" order="12" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M185.8,11.6C182.2,14.1,179.3,15.8,176,16.5C174,17,172.1,16.8,171,15.1S171,11.8,172.1,10.5C176.1,5.7,181.3,2.2,187.2,0.3C188,0,188.9-0.1,189.7,0C192.8,0.3,193.8,2.6,192.1,5.3C190.4,7.8,188.3,10,185.8,11.6z" fill="rgb(183, 156, 237)" order="13" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M154.3,295.7C151.1,298,148,299.9,144.4,300.8C142.7,301.2,140.9,301.4,139.8,299.8S139.6,296.5,140.7,295.2C145.1,290,151,286.1,157.5,284C159.2,283.5,161,283.5,162.1,285C163.3,286.7,162.2,288.2,161.1,289.5C159.2,291.9,156.9,294,154.3,295.7z" fill="rgb(183, 156, 237)" order="14" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M228.5,255.9C230.4,254.7,232.4,253.4,234.4,252.3C236.2,251.2,238.3,250.6,240.4,250.4C243.6,250.2,244.5,251.7,243,254.4C242.5,255.3,241.8,256.2,241.1,256.9C236.8,261.1,231.8,264.5,226.2,266.7C225.2,267.1,224.2,267.3,223.1,267.4C220.4,267.6,219.4,266,220.6,263.5C221.4,261.9,222.6,260.6,224,259.5C225.5,258.3,227.2,257.3,228.8,256.1L228.5,255.9z" fill="rgb(183, 156, 237)" order="15" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M317.8,220.1C315.2,221.9,312.3,223.3,309.2,224.1C307.5,224.5,305.6,225,304.6,223.2C303.7,221.7,304.7,220.2,305.7,219C309.9,213.9,315.4,210.1,321.7,208.1C323.4,207.6,325.3,207.3,326.3,209C327.2,210.5,326.1,211.9,325.2,213.2C323,216,320.5,218.3,317.8,220.1z" fill="rgb(183, 156, 237)" order="16" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M151.8,245.9C148.5,248.3,145,250.7,140.9,251.7C139.6,252,138,252.5,137.2,251.1S137.2,248.4,138.1,247.4C143.4,241.5,150.1,237.1,157.7,234.5C158.9,234.1,160.2,233.9,161,234.9C162,236.1,161.1,237.4,160.3,238.6C158.1,241.7,155,243.7,151.8,245.9z" fill="rgb(183, 156, 237)" order="17" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M13.9,148.3C10.7,150.6,7.8,152.6,4.3,153.5C3,153.9,1.5,154.2,0.5,153.1S0.1,150.5,1,149.4C5.5,143.3,12,138.8,19.3,136.9C20.5,136.6,21.8,136.6,22.5,137.6C23.4,138.9,22.6,140.2,21.8,141.3C19.6,144,17,146.4,13.9,148.3z" fill="rgb(183, 156, 237)" order="18" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M192.3,186C188.8,188.7,184.8,190.6,180.5,191.6C179.3,191.9,178,192.1,177.1,191.1C176.1,189.8,176.8,188.4,177.7,187.4C183,181.5,189.3,177.2,197.2,175.5C198.4,175.3,199.7,175.1,200.3,176.4S200,178.7,199.3,179.6C197.4,182.2,195,184.3,192.3,186z" fill="rgb(183, 156, 237)" order="19" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M40.6,199.2C37.3,201.7,34.1,203.7,30.5,204.6C29.3,204.9,28,205.2,27.1,204.2C26.1,203,26.6,201.6,27.6,200.5C32.3,195.1,37.8,190.9,44.7,188.8C46,188.4,47.5,188.2,48.4,189.6C49.1,190.8,48.3,191.9,47.6,192.9C45.8,195.4,43.4,197.5,40.6,199.2z" fill="rgb(183, 156, 237)" order="20" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M196.5,103.8C193.1,106.2,189.9,108.3,186,109.2C184.8,109.5,183.5,109.8,182.6,108.8C181.6,107.6,182.3,106.2,183.2,105.1C188.2,99.8,194,95.5,201.2,93.6C202.4,93.3,203.7,93.1,204.3,94.4C204.8,95.4,204.1,96.3,203.5,97.2C201.7,99.9,199.3,102.2,196.5,103.8z" fill="rgb(183, 156, 237)" order="21" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M235.6,119.1C238.3,117,241.4,115.5,244.7,114.7C245.9,114.4,247.2,114.3,247.9,115.5S248,117.8,247.3,118.8C243.7,123.9,238.6,127.6,232.7,129.5C231.2,130,229.5,130.1,228.5,128.7S228.5,125.9,229.4,124.5C231.1,122.3,233.2,120.8,235.6,119.1z" fill="rgb(183, 156, 237)" order="22" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M281.8,209.8C278.6,212.1,275.7,214.1,272.1,214.7C270.9,214.9,269.6,215,269,213.8C268.6,212.8,268.8,211.7,269.5,210.9C274.2,205.2,280,201.1,287.2,199.3C289.7,198.7,290.7,200.3,289.4,202.6C287.6,205.8,284.5,208,281.8,209.8z" fill="rgb(183, 156, 237)" order="23" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M149.8,219.2C152.5,217.2,155.6,215.7,158.8,214.8C160,214.5,161.3,214.4,162.1,215.4S162.2,217.6,161.5,218.6C157.8,223.4,152.8,227,147.2,229C145.7,229.6,144,230,143,228.5S143,225.6,144,224.4C145.6,222.4,147.6,220.6,149.8,219.2z" fill="rgb(183, 156, 237)" order="24" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M186.2,154.9C188.8,153.1,191.3,151.1,194.4,150.4C195.6,150.1,196.9,149.8,197.6,151.1C198.1,152.1,197.5,153,196.8,153.9C193.2,158.5,188.4,161.9,182.9,163.9C181.6,164.4,180,164.7,179.3,163.3C178.7,162.2,179.4,161.1,180.1,160C181.7,157.8,184.1,156.5,186.2,154.9z" fill="rgb(183, 156, 237)" order="25" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M290,158.3C287.8,160,285.3,161.3,282.6,162.1C281.4,162.4,280.1,162.5,279.4,161.4C278.9,160.5,279.1,159.3,279.8,158.6C283.8,154.3,288,150.4,293.9,149C294.9,148.8,296,148.8,296.6,149.7S296.6,151.6,296,152.5C294.5,154.7,292.4,156.7,290,158.3z" fill="rgb(183, 156, 237)" order="26" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="-RbQuy7Ki" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE8"><g data-tag="div" id="sc-hgYirE8" class="sc-hgYirE btfmLo" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK8"><g data-tag="div" id="sc-gLgtkK8" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL8"><g data-tag="div" id="sc-cNYriL8" class="sc-cNYriL hHDNAf itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc8"><g data-tag="svg" id="sc-cyRTDc8" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc81)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc81"><rect width="714.6409301757812" height="380.31207275390625" x="723.0490112304688" y="700.5499877929688" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 131 70" data-width="" data-height="" transform="matrix(5.45527 0 0 5.43303 723.049 700.55)"><path d="M100.344,53.447C96.121,52.749,92.859,51.696,89.8,50.05C88.682,49.512,87.73,48.743,86.978,47.779C85.671,46.049,86.116,44.368,88.235,43.858C90.122,43.379,91.978,42.801,93.994,42.785C97.629,42.751,101.225,43.41,104.583,44.828C105.933,45.402,107.148,46.238,108.163,47.338C109.505,48.771,109.293,50.355,108.718,51.903C108.143,53.451,106.785,53.802,105.232,53.888C103.347,54.036,101.467,53.589,100.344,53.447z" fill="rgb(222, 192, 241)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M21.064,36.163C19.052,35.782,17.501,35.537,16.052,34.93C15.196,34.592,14.44,34.09,13.818,33.457C12.575,32.058,12.986,30.41,14.871,30.129C19.701,29.445,24.22,30.31,28.192,33.286C29.21,34.055,29.102,35.012,28.038,35.796C26.974,36.58,25.618,36.667,24.264,36.622C22.91,36.575,21.823,36.268,21.064,36.163z" fill="rgb(222, 192, 241)" order="1" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M51.34,15.91C49.954,15.7,48.67,15.193,47.486,14.522C46.467,13.951,45.978,13.121,46.186,11.999S47.159,10.322,48.317,10.102C50.037,9.853,51.788,9.901,53.47,10.214C55.152,10.592,56.833,11.037,58.414,11.645C59.271,11.917,60.027,12.419,60.615,13.151C61.728,14.416,61.353,15.702,59.697,16.15C59.134,16.311,58.604,16.371,58.01,16.366C55.829,16.412,53.551,16.227,51.34,15.91z" fill="rgb(222, 192, 241)" order="2" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M84.577,23.802C86.225,24.279,88.204,24.627,89.913,25.633C91.294,26.438,91.683,27.235,91.441,28.521C91.198,29.807,90.533,30.397,88.847,30.481C85.541,30.65,82.243,29.96,79.316,28.447C78.165,27.809,77.313,27.009,77.556,25.656C77.798,24.435,78.397,23.912,80.15,23.696C81.606,23.512,83.093,23.558,84.577,23.802z" fill="rgb(222, 192, 241)" order="3" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M81.952,6.532C84.263,6.816,86.504,7.498,88.576,8.54C90.286,9.48,91.037,10.511,90.633,11.366C90.027,12.682,88.673,12.638,87.484,12.561C83.421,12.426,79.499,11.301,75.982,9.255C75.193,8.786,74.438,8.217,74.711,7.228C74.95,6.338,75.843,6.247,76.637,6.122C78.456,5.774,80.237,6.186,81.952,6.532z" fill="rgb(222, 192, 241)" order="4" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M58.775,31.076C62.207,31.635,65.501,32.721,68.593,34.334C69.448,34.738,70.634,35.145,70.327,36.298C70.055,37.221,68.898,37.178,68.006,37.203C62.653,37.222,57.811,35.494,53.24,32.976C52.681,32.674,52.057,32.239,52.196,31.513C52.336,30.655,53.228,30.696,53.956,30.57C55.543,30.321,57.125,30.863,58.775,31.076z" fill="rgb(222, 192, 241)" order="5" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M16.205,21.152C14.324,20.97,12.478,20.425,10.8,19.518C10.142,19.182,9.584,18.748,9.06,18.214C8.341,17.349,8.546,16.557,9.639,16.269C10.368,16.077,11.095,16.018,11.822,16.024C15.027,15.953,17.993,16.905,20.856,18.218C21.317,18.42,21.711,18.688,22.071,19.055C23.186,20.056,22.946,21.012,21.554,21.462C20.693,21.718,19.767,21.777,18.876,21.67C17.986,21.498,17.095,21.325,16.205,21.152z" fill="rgb(222, 192, 241)" order="6" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M71.202,53.521C73.182,53.803,75.06,54.447,76.769,55.453C77.492,55.922,78.446,56.426,78.205,57.448C77.965,58.47,76.94,58.561,76.048,58.586C72.248,58.651,68.522,57.793,65.1,56.111C65.001,56.077,64.969,56.044,64.903,55.977C64.279,55.542,63.622,55.14,63.794,54.315C63.965,53.623,64.693,53.497,65.322,53.403C67.274,53.057,69.223,53.107,71.202,53.521z" fill="rgb(222, 192, 241)" order="7" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M116.852,24.517C118.994,25.163,121.103,25.777,123.245,26.424C123.706,26.56,124.167,26.763,124.561,27.03C125.218,27.433,126.174,27.804,125.868,28.76C125.563,29.583,124.604,29.674,123.811,29.667C122.324,29.687,120.871,29.542,119.387,29.298C117.144,28.816,114.937,28.103,112.83,27.225C112.303,27.022,111.81,26.72,111.35,26.386C110.399,25.617,110.504,24.858,111.729,24.572C113.419,24.156,115.137,24.171,116.852,24.517z" fill="rgb(222, 192, 241)" order="8" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M43.585,45.216C45.466,45.53,47.244,46.207,48.855,47.179C49.545,47.615,50.201,48.215,49.929,49.072C49.691,49.83,48.929,50.055,48.202,50.114C44.763,50.414,41.3,49.69,38.275,48.011C37.979,47.843,37.749,47.676,37.52,47.443C36.439,46.343,36.679,45.453,38.234,45.17C39.987,44.821,41.838,44.837,43.585,45.216z" fill="rgb(222, 192, 241)" order="9" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M11.262,49.986C13.274,50.367,14.791,50.777,16.105,51.648C16.926,52.151,17.482,52.849,17.276,53.773S16.176,54.854,15.383,54.913C12.473,55.152,9.604,54.565,7.039,53.22C6.677,53.052,6.349,52.784,6.12,52.485C5.205,51.353,5.644,50.267,7.1,49.95C8.49,49.698,9.911,49.677,11.262,49.986z" fill="rgb(222, 192, 241)" order="10" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M113.119,62.515C114.934,62.828,116.55,63.14,117.996,64.011C118.555,64.313,119.146,64.715,119.107,65.409C119.068,66.102,118.371,66.393,117.71,66.454C114.204,66.952,110.575,66.259,107.551,64.448C107.058,64.146,106.632,63.713,106.736,63.153C106.874,62.427,107.57,62.268,108.198,62.174C109.819,62.022,111.47,62.103,113.119,62.515z" fill="rgb(222, 192, 241)" order="11" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M121.337,36.946C123.251,37.227,124.966,37.639,126.445,38.544C126.938,38.846,127.464,39.18,127.425,39.808C127.353,40.534,126.722,40.826,126.028,40.853C122.689,41.055,119.488,40.597,116.529,38.985C115.97,38.683,115.412,38.248,115.584,37.49C115.755,36.863,116.384,36.77,116.946,36.676C118.368,36.458,119.854,36.57,121.337,36.946z" fill="rgb(222, 192, 241)" order="12" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M38.484,16.227C40.397,16.575,42.145,16.953,43.722,17.958C44.215,18.26,44.741,18.594,44.702,19.222C44.63,19.948,43.934,20.173,43.273,20.234C39.869,20.303,36.536,19.778,33.545,18.001C33.052,17.699,32.56,17.331,32.796,16.706C32.966,16.212,33.495,16.15,33.991,16.056C35.481,15.77,37.034,15.818,38.484,16.227z" fill="rgb(222, 192, 241)" order="13" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="ZTFpaVvzu" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE9"><g data-tag="div" id="sc-hgYirE9" class="sc-hgYirE ceIeJk" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK9"><g data-tag="div" id="sc-gLgtkK9" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL9"><g data-tag="div" id="sc-cNYriL9" class="sc-cNYriL cVbTej itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc9"><g data-tag="svg" id="sc-cyRTDc9" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc91)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc91"><rect width="514.6409912109375" height="514.6409912109375" x="35.567901611328125" y="490.22100830078125" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 248 248" data-width="" data-height="" transform="matrix(2.07517 0 0 2.07517 35.5679 490.221)"><path d="M0,124.01C0,55.5212,55.5212,0,124.01,0C192.4988,0,248.02,55.5212,248.02,124.01C248.02,192.4988,192.4988,248.02,124.01,248.02C55.5212,248.02,0,192.4988,0,124.01z" fill="rgb(222, 192, 241)" order="0" opacity="0.3" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M14.58,124.01C14.58,63.5735,63.5735,14.58,124.01,14.58C184.4465,14.58,233.44,63.5735,233.44,124.01C233.44,184.4465,184.4465,233.44,124.01,233.44C63.5735,233.44,14.58,184.4465,14.58,124.01z" fill="rgb(222, 192, 241)" order="1" opacity="0.5" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M31.99,124.01C31.99,73.1888,73.1888,31.99,124.01,31.99C174.8312,31.99,216.03,73.1888,216.03,124.01C216.03,174.8312,174.8312,216.03,124.01,216.03C73.1888,216.03,31.99,174.8312,31.99,124.01z" fill="rgb(186, 67, 248)" order="2" opacity="0.5" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="QhfbXYR2h" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE10"><g data-tag="div" id="sc-hgYirE10" class="sc-hgYirE gqRnWW" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK10"><g data-tag="div" id="sc-gLgtkK10" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL10"><g data-tag="div" id="sc-cNYriL10" class="sc-cNYriL AmFjl itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc10"><g data-tag="svg" id="sc-cyRTDc10" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc101)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc101"><rect width="429.6409912109375" height="404.7190246582031" x="1687.8800048828125" y="160.76400756835938" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 422 398" data-width="" data-height="" transform="matrix(1.01811 0 0 1.01688 1687.88 160.764)"><path d="M393.9,240.7C367.6,373,208.5,441.8,94.9,368.3C4.8,315.3-27.1,191.7,25.8,101.5C26.8,101.4,27.8,101.9,28.3,102.8C-23,190.5,6.4,310.5,93,363.8C204.8,439.6,365.1,372.1,391,240.1C392,240.1,393.1,239.8,393.9,240.7z" fill="rgb(186, 67, 248)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M28.3,102.9C27.9,102,26.8,101.4,25.8,101.6C22.7,98.2,19.1,95.2,17.2,90.8C13.3,81.6,15.6,70,22.8,63.6C31.2,56.2,42.4,55,51.7,60.6C52.8,61.2,53.6,62.3,55.1,62.2C55.1,63.3,56,64.2,57,64.2C73.9,85.8,54,113,28.3,102.9z" fill="rgb(183, 156, 237)" order="1" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M393.9,240.7C393.1,239.8,392,240.1,391,240.1C381.2,235.4,374.7,229,374.2,217.5C373.3,206,383.2,192.7,398.1,192.6C433.2,195.1,428.1,244.2,393.9,240.7z" fill="rgb(183, 156, 237)" order="2" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M398.1,192.6L395.2,192.8C390.4,19.8,175.6-62.1,57,64.1C55.9,64.1,55.1,63.2,55.1,62.1C175.6-65.7,392.7,17.4,398.1,192.6z" fill="rgb(186, 67, 248)" order="3" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M199.5,350.2C0.1,343.7,0,57.9,195.5,48C397.8,50.1,401.1,343.1,199.5,350.2zM51,199.4C57.7,393.6,336.4,394.8,347.4,203.5C345.5,3.4,57.1,1.6,51,199.4z" fill="rgb(183, 156, 237)" order="4" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/><path d="M334.7,199.1C330.5,376.8,69.4,379.8,63.5,199.2C69.4,18.7,329.7,20.2,334.7,199.1z" fill="rgb(183, 156, 237)" order="5" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g><g data-tag="div" id="krhR1mXFi" class="sc-jaZyJE dermLn graphic-layer  " data-z-index="auto" data-stacking-context="true" aria-owns="sc-hgYirE11"><g data-tag="div" id="sc-hgYirE11" class="sc-hgYirE dlMzYQ" data-z-index="auto" data-stacking-context="true" aria-owns="sc-gLgtkK11"><g data-tag="div" id="sc-gLgtkK11" class="sc-gLgtkK cKJBHU" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cNYriL11"><g data-tag="div" id="sc-cNYriL11" class="sc-cNYriL cUcdMf itemAnimation" data-z-index="auto" data-stacking-context="true" aria-owns="sc-cyRTDc11"><g data-tag="svg" id="sc-cyRTDc11" class="sc-cyRTDc NvcuY itemAnimation" data-z-index="auto" data-stacking-context="true" mask="url(#mask-for-sc-cyRTDc111)"><g data-stacking-layer="rootBackgroundAndBorders"/><g data-stacking-layer="childStackingContextsWithNegativeStackLevels"/><g data-stacking-layer="inFlowNonInlineNonPositionedDescendants"/><g data-stacking-layer="nonPositionedFloats"/><g data-stacking-layer="inFlowInlineLevelNonPositionedDescendants"/><g data-stacking-layer="childStackingContextsWithStackLevelZeroAndPositionedDescendantsWithStackLevelZero"/><g data-stacking-layer="childStackingContextsWithPositiveStackLevels"/><mask id="mask-for-sc-cyRTDc111"><rect width="584.6409912109375" height="478.12506103515625" x="1474.3499755859375" y="700.4879760742188" fill="#ffffff"/></mask><g class="svg-content sc-cyRTDc NvcuY itemAnimation" data-view-box="0 0 216 177" data-width="" data-height="" transform="matrix(2.70667 0 0 2.70127 1474.35 700.488)"><path d="M4.6467,83.6308C16.9207,63.5006,38.2196,71.7085,61.6467,45.6308C77.2452,28.2674,74.2997,17.3964,89.6467,7.6308C142.5035-18.1966,211.4446,25.3888,216.6484,80.6272C218.5374,187.6189-37.2945,229.0743,4.6467,83.6308z" fill="rgb(143, 111, 249)" order="0" opacity="1" color="rgb(0, 0, 0)" stroke="none" stroke-linejoin="round"/></g></g></g></g></g></g></g></g></g></g></svg>