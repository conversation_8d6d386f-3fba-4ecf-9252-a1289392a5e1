<?php
namespace App\Services;

use App\Contracts\PaymentGateway;

class PaymentProcessorService
{
    private $gateway;

    public function __construct(PaymentGateway $gateway) {
        $this->gateway = $gateway;
    }

    public function processPayment(int $amount,array $data) {
        return $this->gateway->processPayment($amount,$data);
    }
    public function verifyPayment(array $data) {
        return $this->gateway->verifyPayment($data);
    }
    public function cancelPayment(array $data){
        return $this->gateway->cancelPayment($data);
    }
}
