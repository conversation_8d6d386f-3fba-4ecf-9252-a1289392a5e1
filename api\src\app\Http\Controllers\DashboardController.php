<?php

namespace App\Http\Controllers;

use App\Models\Customers;
use App\Models\PaymentMethod;
use App\Models\order;
use App\Models\Page;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Hekmatinasser\Verta\Verta;
class DashboardController extends Controller
{
    public function index($id=null){
        $user_id = auth()->user()->id;
        $pageId = null;
        if($id){
            $pageId = Page::where('id',$id)->where('user_id',$user_id)->first();
            if(!$pageId){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $pageId->id;
        }
        $verta = Verta::jalaliToGregorian(
            verta()->addDay()->year,
            verta()->addDay()->month,
            verta()->addDay()->day
        );
        $today = Carbon::createFromFormat(
            'Y/m/d',
            $verta[0].'/'.$verta[1].'/'.$verta[2]
        );
        $verta2 = Verta::jalaliToGregorian(
            verta()->subDay()->year,
            verta()->subDay()->month,
            verta()->subDay()->day
        );
        $today2 = Carbon::createFromFormat(
            'Y/m/d',
            $verta2[0].'/'.$verta2[1].'/'.$verta2[2]
        );
        $ordersToday = Order::where('user_id', $user_id);
        if($pageId){
            $ordersToday = $ordersToday->where('page_id',$pageId);
        }
        $ordersToday = $ordersToday
            ->whereIn('status',['approved','ordered'])
            ->whereBetween('created_at', [$today2,$today])
            ->orderBy('created_at', 'ASC')
            ->with(['product','product.paymentMethods'])
            ->get()
            ->groupBy(function($date) {
                return verta($date->created_at)->timezone('Asia/Tehran')->format('Y-m-d H'); // گروه‌بندی بر اساس ساعت
            })
            ->map(function ($hour) {
                $price = 0;
                foreach($hour as $d){
                    \Log::info($d);
                    if($d->product){
                        if(count($d->product->paymentMethods)===0&&$d->status==='ordered'){
                            $price += (int)$d->payment_amount;
                        }
                    }else
                    if($d->status==='approved'){
                        $price += (int)$d->payment_amount;
                    }
                }
                return $price;
            })
            ->all();
        // return $ordersToday;
        $hours = [];
        $ordersTodayPrice = 0;
        for ($i = 0; $i < 24; $i++) {
            $date1 = verta();
            $date1->hour = 0;
            // $date1->minute = 0;
            // $date1->second = 0;
            $adddate1 = $date1->addHours($i)->format('Y-m-d H');
            // $hours[] = [$date,verta()->format('Y-m-d H')];
            $hours[$adddate1.':00:00'] = $ordersToday[$adddate1] ?? 0;
            $ordersTodayPrice += $ordersToday[$adddate1] ?? 0;
        }

        $verta = Verta::jalaliToGregorian(
            verta()->startWeek()->year,
            verta()->startWeek()->month,
            verta()->startWeek()->day
        );
        $verta2 = Verta::jalaliToGregorian(
            verta()->endWeek()->year,
            verta()->endWeek()->month,
            verta()->endWeek()->day
        );
        $startOfWeek = Carbon::createFromFormat(
            'Y/m/d',
            $verta[0].'/'.$verta[1].'/'.$verta[2]
        );
        $endOfWeek = Carbon::createFromFormat(
            'Y/m/d',
            $verta2[0].'/'.$verta2[1].'/'.$verta2[2]
        );
        $ordersTodayArray = $hours;
        $ordersThisWeek = Order::where('user_id', $user_id);
        if($pageId){
            $ordersThisWeek = $ordersThisWeek->where('page_id',$pageId);
        }
        $ordersThisWeek = $ordersThisWeek
            ->whereIn('status',['approved','ordered'])
            ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
            ->orderBy('created_at', 'ASC')
            ->with(['product','product.paymentMethods'])
            ->get()
            ->groupBy(function($date) {
                return verta(Carbon::parse($date->created_at))->format('Y-m-d'); // گروه‌بندی بر اساس روز
            })
            ->map(function ($day) {
                $price = 0;
                foreach($day as $d){
                    if($d->product){
                        if(count($d->product->paymentMethods)===0&&$d->status==='ordered'){
                            $price += (int)$d->payment_amount;
                        }
                    }else
                    if($d->status==='approved'){
                        $price += (int)$d->payment_amount;
                    }
                }
                return $price;
            })
            ->all();
        $weekDays = [];
        $ordersThisWeekPrice = 0;
        for ($i = 0; $i < 7; $i++) {
            $date = verta()->startWeek()->addDays($i)->format('Y-m-d');
            $weekDays[$date] = $ordersThisWeek[$date] ?? 0;
            $ordersThisWeekPrice += $ordersThisWeek[$date] ?? 0;
        }
        $customersCount = Customers::where('user_id', $user_id);
        if($pageId){
            $customersCount = $customersCount->where('page_id',$pageId);
        }
        $customersCount = $customersCount->count();
        $pageExist = Page::where('user_id',$user_id);
        if($pageId){
            $pageExist = $pageExist->where('id',$pageId);
        }
        $pageExist = $pageExist->first();
        $pageExist = $pageExist->access_token===null?0:1;
        return [
            'ordersTodayPrice' => $ordersTodayPrice,
            'ordersToday'=> $ordersTodayArray,
            'ordersThisWeekPrice' => $ordersThisWeekPrice,
            'orderThisWeek' => $weekDays,
            'customersCount' => $customersCount,
            'pageExist' => $pageExist
        ];
    }
    public function allOrdersPrice($id=null){
        $user_id = auth()->user()->id;
        $pageId = null;
        if($id){
            $pageId = Page::where('id',$id)->where('user_id',$user_id)->first();
            if(!$pageId){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $pageId->id;
        }
        $paymentMethod = PaymentMethod::where("user_id",auth()->user()->id);
        if($pageId){
            $paymentMethod = $paymentMethod->where('page_id',$pageId);
        }
        $paymentMethod = $paymentMethod
            ->where("payment_method","paystar")
            ->first();
        $allOrders = Order::where('user_id', $user_id);
        if($pageId){
            $allOrders = $allOrders->where('page_id',$pageId);
        }
        $allOrders = $allOrders
            ->whereIn('status',['approved','ordered'])
            ->orderBy('created_at', 'ASC')
            ->with(['product','product.paymentMethods'])
            ->get()
            ->groupBy(function($date) {
                return verta(Carbon::parse($date->created_at))->format('Y-m-d'); // گروه‌بندی بر اساس روز
            })
            ->map(function ($day) {
                $price = 0;
                foreach($day as $d){
                    if($d->product){
                        if(count($d->product->paymentMethods)===0&&$d->status==='ordered'){
                            $price += (int)$d->payment_amount;
                        }
                    }else
                    if($d->status==='approved'){
                        $price += (int)$d->payment_amount;
                    }
                    // $price += (int)$d->payment_amount;
                }
                return $price;
            })
            ->filter(function (int $value){
                return $value!==0;
            })
            ->all();
        $allPrice = 0;
        foreach($allOrders as $order){
            $allPrice += (int)$order;
        }
        $todayVerta = verta()->format('Y-m-d');
        if(count($allOrders)===1){
            $allOrders[$todayVerta.' .'] = 0;
        }else
        if(count($allOrders)===0){
            $yesterdayVerta = verta()->addDays(-1)->format('Y-m-d');

            $allOrders = [
                $yesterdayVerta.' .'=>0,
                $todayVerta.' .'=>0,
            ];
        }
        $allpaystar = 0;
        if($paymentMethod){
            $paystarOrders = Order::where('user_id', $user_id);
            if($pageId){
                $paystarOrders = $paystarOrders->where('page_id',$pageId);
            }
            $paystarOrders = $paystarOrders
                ->where('status','approved')
                // ->orWhere('status','ordered')
                ->where('payment_method_id',$paymentMethod->id)
                ->with(['product','product.paymentMethods'])
                ->orderBy('created_at', 'ASC')
                ->get();
            foreach($paystarOrders as $o){
                // if(count($o->product->paymentMethods)===0&&$o->status==='ordered'){
                //     $allpaystar += (int)$o->payment_amount;
                // }else
                // if($o->status==='approved'){
                    // $allpaystar += (int)$o->payment_amount;
                // }
                $allpaystar += (int)$o->payment_amount;
            }
        }
        return ['orders'=>$allOrders,'prices'=>$allPrice,'paystar'=>$allpaystar];
    }
}
