<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('special_users', function (Blueprint $table) {
            # Columns
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->text('api_token')->nullable();
            $table->text('access_token');
            $table->string('instagram_id');
            $table->timestamps();

            # Json Columns
            $table->json('webhook_endpoints')->nullable();
            $table->json('valid_endpoints')->nullable();

            # Foreign Keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('special_users');
    }
};
