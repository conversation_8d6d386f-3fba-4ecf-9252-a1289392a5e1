<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'phone',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'phone_verified_at' => 'datetime',
    ];

	protected $appends = [
		'creation_date',
	];

	public function getCreationDateAttribute()
	{
		return jdate($this->created_at)->format('Y/m/d H:i:s');
	}

    public function smsVerifications(): HasMany
    {
        return $this->hasMany(SmsVerification::class);
    }
    public function pages(): HasMany
    {
        return $this->hasMany(Page::class);
    }
    public function productGalleries(): HasMany
    {
        return $this->hasMany(ProductsGallery::class);
    }
    public function productPrices(): HasMany
    {
        return $this->hasMany(ProductPrices::class);
    }
    public function productMedia(): HasMany
    {
        return $this->hasMany(ProductsMedia::class);
    }
    public function settings(): HasMany
    {
        return $this->hasMany(Settings::class);
    }
    public function orders(): HasMany
    {
        return $this->hasMany(order::class);
    }
    public function products(): HasMany
    {
        return $this->hasMany(Products::class);
    }
    public function orderAtrributes(): HasMany
    {
        return $this->hasMany(orderAttribute::class);
    }

    public function payment_methods(): HasOne
    {
        return $this->hasOne(PaymentMethod::class);
    }
    public function orderShippings(): HasMany
    {
        return $this->hasMany(OrderShipping::class);
    }

	// Last successful transaction for the user
	// The function was named "transaction" to shorten the object keys of json responses
	public function transaction(): HasOne
	{
		return $this->hasOne(Transaction::class)
			->where('status', 'approved')
			->orderByDesc('created_at');
	}
}
