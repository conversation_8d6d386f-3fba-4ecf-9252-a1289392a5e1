<?php

namespace App\Providers;

use App\Repositories\Ai\AiSettingRepository;
use App\Repositories\Interfaces\AiSettingRepositoryInterface;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface;
use App\Repositories\Webservice\SpecialUserRepository;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(SpecialUserRepositoryInterface::class, SpecialUserRepository::class);
        $this->app->bind(AiSettingRepositoryInterface::class, AiSettingRepository::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Json::encodeUsing(fn ($value) => json_encode($value, JSON_UNESCAPED_UNICODE));
    }
}
