import { V as I, a as O } from "./VBadge-751ce1d1.js";
import { V as b } from "./VDivider-12bfa926.js";
import { a as $, b as B, V as H, c as J } from "./VList-349a1ccf.js";
import { V as j } from "./VMenu-2cfb0f14.js";
import { V as G } from "./VTooltip-b4170ac2.js";
import { a as L } from "./formatters-c282fd5f.js";
import "./index-00c7d20d.js";
import { ab as A, a8 as C, ad as D, s as E, r as F, i as M, F as N, a9 as P, aa as R, L as S, a as T, ac as U, n as c, b as e, v as f, c as h, d as k, y as q, o as r, e as t, x as u, a4 as v, f as x, K as y, z } from "./index-169996dc.js";
import "./ssrBoot-c101cd97.js";
import { P as K } from "./vue3-perfect-scrollbar.esm-e1f82a0e.js"; /* empty css                                                      */
const Q={key:0},W={class:"text-xs text-disabled"},X={class:"d-flex flex-column align-center gap-4"},Y={style:{"block-size":"28px","inline-size":"28px"}},Z={__name:"Notifications",props:{notifications:{type:Array,required:!0},badgeProps:{type:null,required:!1,default:void 0},location:{type:null,required:!1,default:"bottom end"}},emits:["read","unread","remove","click:notification"],setup(V,{emit:l}){const i=V,d=l,m=C(()=>i.notifications.some(a=>a.isSeen===!1)),p=()=>{const a=i.notifications.map(n=>n.id);m.value?d("read",a):d("unread",a)},o=C(()=>i.notifications.filter(a=>a.isSeen===!1).length);return(a,n)=>{const _=T("IconBtn");return r(),x(_,{id:"notification-btn"},{default:t(()=>[e(I,E(i.badgeProps,{"model-value":i.notifications.some(s=>!s.isSeen),color:"error",content:c(o),class:"notification-badge"}),{default:t(()=>[e(v,{size:"26",icon:"tabler-bell"})]),_:1},16,["model-value","content"]),e(j,{activator:"parent",width:"380px",location:i.location,offset:"14px","close-on-content-click":!1},{default:t(()=>[e(P,{class:"d-flex flex-column"},{default:t(()=>[e(R,{class:"notification-section"},{append:t(()=>[y(e(_,{onClick:p},{default:t(()=>[e(v,{icon:c(m)?"tabler-mail-opened":"tabler-mail"},null,8,["icon"]),e(G,{activator:"parent",location:"start"},{default:t(()=>[f(u(c(m)?"Mark all as read":"Mark all as unread"),1)]),_:1})]),_:1},512),[[S,i.notifications.length]])]),default:t(()=>[e(A,{class:"text-lg"},{default:t(()=>[f(" اعلان ها ")]),_:1})]),_:1}),e(b),e(c(K),{options:{wheelPropagation:!1},style:{"max-block-size":"23.75rem"}},{default:t(()=>[e(H,{class:"notification-list rounded-0 py-0"},{default:t(()=>[(r(!0),h(N,null,M(i.notifications,(s,w)=>(r(),h(N,{key:s.title},[w>0?(r(),x(b,{key:0})):z("",!0),e($,{link:"",lines:"one","min-height":"66px",class:"list-item-hover-class",onClick:g=>a.$emit("click:notification",s)},{prepend:t(()=>[e(O,{start:""},{default:t(()=>[e(U,{size:"40",color:s.color&&s.icon?s.color:void 0,image:s.img||void 0,icon:s.icon||void 0,variant:s.img?void 0:"tonal"},{default:t(()=>[s.text?(r(),h("span",Q,u(("avatarText"in a?a.avatarText:c(L))(s.text)),1)):z("",!0)]),_:2},1032,["color","image","icon","variant"])]),_:2},1024)]),append:t(()=>[k("div",X,[e(I,{dot:"",color:s.isSeen?"#a8aaae":"primary",class:q(`${s.isSeen?"visible-in-hover":""} ms-1`),onClick:D(g=>a.$emit(s.isSeen?"unread":"read",[s.id]),["stop"])},null,8,["color","class","onClick"]),k("div",Y,[e(_,{size:"small",class:"visible-in-hover",onClick:g=>a.$emit("remove",s.id)},{default:t(()=>[e(v,{size:"20",icon:"tabler-x"})]),_:2},1032,["onClick"])])])]),default:t(()=>[e(B,{class:"font-weight-medium"},{default:t(()=>[f(u(s.title),1)]),_:2},1024),e(J,null,{default:t(()=>[f(u(s.subtitle),1)]),_:2},1024),k("span",W,u(s.time),1)]),_:2},1032,["onClick"])],64))),128)),y(e($,{class:"text-center text-medium-emphasis",style:{"block-size":"56px"}},{default:t(()=>[e(B,null,{default:t(()=>[f("هیچ اعلان جدیدی نیست!")]),_:1})]),_:1},512),[[S,!i.notifications.length]])]),_:1})]),_:1}),e(b)]),_:1})]),_:1},8,["location"])]),_:1})}}},ee="/assets/avatar-4-b0163f21.png",fe={__name:"NavBarNotifications",setup(V){const l=F([{id:1,img:ee,title:"DMPlus آپدیت شد! 🎉",subtitle:"اکنون با آخرین نسخه میتونین دایرکت هوشمندتون رو مدیریت کنین",time:"امروز",isSeen:!0}]),i=o=>{l.value.forEach((a,n)=>{o===a.id&&l.value.splice(n,1)})},d=o=>{l.value.forEach(a=>{o.forEach(n=>{n===a.id&&(a.isSeen=!0)})})},m=o=>{l.value.forEach(a=>{o.forEach(n=>{n===a.id&&(a.isSeen=!1)})})},p=o=>{o.isSeen||d([o.id])};return(o,a)=>{const n=Z;return r(),x(n,{notifications:c(l),onRemove:i,onRead:d,onUnread:m,"onClick:notification":p},null,8,["notifications"])}}};export { fe as default };

