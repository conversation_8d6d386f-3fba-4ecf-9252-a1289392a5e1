<?php

namespace App\Http\Controllers;

use App\Http\Requests\productAttributesRequest;
use App\Http\Requests\UpdateProductAttributesRequest;

use App\Models\productAttributes;
use App\Models\Products;
use Illuminate\Http\Request;

class ProductAttributesController extends Controller
{
    public function index(){
        $attrs = productAttributes::where('user_id',auth()->user()->id)->paginate(15);
        return $attrs;
    }
    public function create(productAttributesRequest $request){
        $productAttr = new productAttributes();
        $productAttr->type = $request->type;
        $productAttr->key = $request->key;
        $productAttr->order = $request->order??0;
        $productAttr->product_id = $request->product_id;
        $productAttr->user_id = auth()->user()->id;
        $productAttr->save();
        return $productAttr;
    }
    public function show($id)
    {
        $productAttr = productAttributes::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        return $productAttr;
    }
    public function showProduct($id)
    {
        $productAttr = productAttributes::where('product_id',$id)->where('user_id',auth()->user()->id)->orderBy('order','asc')->paginate(15);
        return $productAttr;
    }
    public function update(UpdateProductAttributesRequest $request, $id){
        $productAttr = productAttributes::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        if($request->has('type')){
            $productAttr->type = $request->type;
        }
        if($request->has('key')){
            $productAttr->key = $request->key;
        }
        if($request->has('order')){
            $productAttr->order = $request->order??0;
        }
        $productAttr->save();
        return $productAttr;
    }
    public function destroy(Request $request,$id){
        $productAttr = productAttributes::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        $productAttr->delete();
        return $productAttr;
    }
}
