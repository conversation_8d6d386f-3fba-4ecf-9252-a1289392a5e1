name: Deploy to Server on Alireza Branch

on:
  push:
    branches:
      - alireza

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Add SSH key for server access
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_KEY }}

      - name: Deploy to Server
        run: |
          ssh -o StrictHostKeyChecking=no root@************* 'bash /root/shop_frontend/deploy-alireza.sh'
