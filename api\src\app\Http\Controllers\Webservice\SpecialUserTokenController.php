<?php

namespace App\Http\Controllers\Webservice;

use App\Http\Controllers\Controller;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SpecialUserTokenController extends Controller
{
    protected SpecialUserRepoInterface $specialUserRepository;

    public function __construct(SpecialUserRepoInterface $specialUserRepository)
    {
        $this->specialUserRepository = $specialUserRepository;
    }

    /**
     * @OA\Post(
     *     path="/api/webservice/regenerate-token",
     *     summary="Regenerate API token for a special user",
     *     description="Generates a new API token for an authenticated special user. The new token will be shown only once, so the user must store it securely.",
     *     operationId="regenerateApiToken",
     *     tags={"Special User API Token"},
     *     security={{"sanctum":{}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="API token regenerated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="API token regenerated successfully"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="api_token", type="string", example="tk7b85sbbre9gf8v")
     *             )
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized - User does not have access",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="User is not eligible for web service")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized - Authentication required",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated")
     *         )
     *     )
     * )
     */
    public function regenerateApiToken(Request $request): JsonResponse
    {
        $userId = auth()->user()->id;
        $specialUser = $this->specialUserRepository->findByUserId($userId);

        $apiToken = $this->specialUserRepository->generateApiToken($specialUser->id);

        return response()->json([
            'success' => true,
            'message' => 'API token regenerated successfully',
            'data'    => [
                'api_token' => $apiToken,
            ]
        ], 200);
    }
}
