<?php
namespace App\Services\Instagram\Abstract;

use App\DTOs\Instagram\MetaConfigDTO;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;

abstract class AbstractPostService
{
    protected MetaConfigDTO $config;

    public function __construct(MetaConfigDTO $config)
    {
        $this->config = $config;
    }

    public function getAllPosts(): Collection
    {
        $endpoint = $this->buildInitialEndpoint();
        return $this->fetchPaginatedData($endpoint);
    }

    abstract protected function buildInitialEndpoint(): string;

    protected function fetchPaginatedData(string $url, Collection $results = null): Collection
    {
        $results = $results ?? collect();

        $response = $this->makeApiCall($url);
        $data = collect($response['data'] ?? []);
        $results = $results->merge($data);

        if (isset($response['paging']['next'])) {
            return $this->fetchPaginatedData($response['paging']['next'], $results);
        }

        return $results;
    }

    public function getFirstPage(): array
    {
        return $this->getPostsPage();
    }

    public function getPageByToken(string $token): array
    {
        if (!preg_match('/^(next|prev):\d+:\d+:[a-zA-Z0-9]+$/', $token)) {
            throw new Exception("Invalid token format.");
        }

        $url = Cache::get("ig_page_token:$token");

        if (!$url) {
            throw new Exception("Token expired or not found.");
        }

        return $this->getPostsPage($url);
    }

    protected function getPostsPage(string $url = null): array
    {
        $endpoint = $url ?? $this->buildInitialEndpoint();
        $response = $this->makeApiCall($endpoint);
        $data = collect($response['data'] ?? []);

        $userId = auth()->user()->id;
        $timestamp = now()->timestamp;

        $nextToken = $prevToken = null;

        if (!empty($response['paging']['next'])) {
            $nextTokenRaw = "next:$userId:$timestamp:" . Str::random(8);
            Cache::put("ig_page_token:$nextTokenRaw", $response['paging']['next'], now()->addMinutes(15));
            $nextToken = $nextTokenRaw;
        }

        if (!empty($response['paging']['previous'])) {
            $prevTokenRaw = "prev:$userId:$timestamp:" . Str::random(8);
            Cache::put("ig_page_token:$prevTokenRaw", $response['paging']['previous'], now()->addMinutes(15));
            $prevToken = $prevTokenRaw;
        }

        return [
            'posts' => $data,
            'next_token' => $nextToken,
            'prev_token' => $prevToken,
        ];
    }

    protected function makeApiCall(string $url): array
    {
        $response = Http::get($url);

        if (!$response->successful()) {
            throw new Exception("API request failed: " . $response->body());
        }

        return $response->json();
    }

    protected function getMediaFields(): string
    {
        return 'id,like_count,comments_count,media_url,thumbnail_url,media_type';
    }
}
