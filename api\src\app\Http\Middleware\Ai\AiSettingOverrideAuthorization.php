<?php

namespace App\Http\Middleware\Ai;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AiSettingOverrideAuthorization
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $validToken = config('services.ai_settings.override_token');

        $authorizationHeader = $request->header('Authorization');

        if (!$authorizationHeader) {
            return response()->json(['error' => 'Authorization header is missing.'], 401);
        }

        if ($authorizationHeader !== "Bearer {$validToken}") {
            return response()->json(['error' => 'Invalid Authorization token.'], 403);
        }

        return $next($request);
    }
}
