server {
    listen 80;

    server_name _;

    root /usr/share/nginx/html;
    index index.html;

	# .html fallback
    location / {
        try_files $uri /index.html;
    }

	# favicon.ico
	location = /favicon.ico {
		log_not_found	off;
	}

	# robots.txt
	location = /robots.txt {
		log_not_found	off;
	}

	# assets, media
    location ~* \.(?:ico|css|js|gif|jpe?g|png|woff2?|eot|ttf|svg)$ {
        expires 6M;
        access_log off;
        add_header Cache-Control "public";
    }

	# gzip
	gzip on;
	gzip_vary	on;
	gzip_proxied	any;
	gzip_comp_level	6;
	gzip_types	text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;

}
