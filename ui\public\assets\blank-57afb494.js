import{_ as f}from"./AppLoadingIndicator-1aa55cc8.js";import{u as v,r as o,w as m,a as p,o as n,c as k,b as l,d as b,e as s,f as r,S as w,h as y,F as g}from"./index-169996dc.js";const S={class:"layout-wrapper layout-blank"},V={__name:"blank",setup(x){const{injectSkinClasses:u}=v();u();const a=o(!1),e=o(null);return m([a,e],()=>{a.value&&e.value&&e.value.fallbackHandle(),!a.value&&e.value&&e.value.resolveHandle()},{immediate:!0}),(B,t)=>{const i=f,c=p("RouterView");return n(),k(g,null,[l(i,{ref_key:"refLoadingIndicator",ref:e},null,512),b("div",S,[l(c,null,{default:s(({Component:_})=>[(n(),r(w,{timeout:0,onFallback:t[0]||(t[0]=d=>a.value=!0),onResolve:t[1]||(t[1]=d=>a.value=!1)},{default:s(()=>[(n(),r(y(_)))]),_:2},1024))]),_:1})])],64)}}};export{V as default};
