<?php

namespace App\Http\Controllers;

use App\Jobs\ExportPageContents;
use App\Models\Page;
use App\Http\Requests\StorePageRequest;
use App\Http\Requests\UpdatePageRequest;
use App\Models\User;
use App\Repositories\Interfaces\SpecialUserRepositoryInterface as SpecialUserRepoInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Exception;



class PageController extends Controller
{
    private $baseUrl = 'https://graph.facebook.com/v20.0';
    private $instaUrl = 'https://graph.instagram.com/v21.0';
    protected SpecialUserRepoInterface $specialUserRepository;

    public function __construct(SpecialUserRepoInterface $specialUserRepository)
    {
        $this->specialUserRepository = $specialUserRepository;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pages = Page::where('user_id',auth()->user()->id);
        if($pages->count()===0){
            $user = auth()->user();
            $page = new Page();
            $page->title = $user->phone;
            $page->user_id = $user->id;
            $page->save();
            $pages = Page::where('user_id',auth()->user()->id);
        }
        $pages = $pages->paginate(15);
        $now = Carbon::now();
        foreach($pages as $page){
            if($page->expired_at){
                $page->expired_at = Carbon::parse($page->expired_at)->diffInDays($now);
            }else{
                $page->expired_at = 0;
            }
        }
        return $pages;
    }


    /**
     * Store a newly created resource in storage.
     */
    public function create(StorePageRequest $request)
    {
        $page = new Page();
        $page->fill($request->all());
        $page->user_id = auth()->user()->id;
        $page->save();
        return $page;
    }
    public function exportRequest(){
        $page = Page::where('user_id',auth()->user()->id)->firstOrFail();

        ExportPageContents::dispatch($page);
        return $page;
    }
    public function getPageContents($id)
    {
        $page = Page::whereId($id)->where('user_id',auth()->user()->id)->first();

        $access_token = $page->access_token;
        $userId = $page->page_user_id;

        $fields = $this->getMediaFields($page->fb_access_token);
        $limit= 20;
        $fields_string = implode(',', $fields);


        $url = "{$this->baseUrl}/$userId/media?fields=$fields_string&access_token=$access_token&limit=$limit";

        if ($page->fb_access_token == 'loggedInWithInstagram') {
            $url = "{$this->instaUrl}/me/media?fields=$fields_string&access_token=$access_token&limit=$limit";
        }


        $response = Http::get($url);
        if ($response->successful()) {
            $res = $response->json();
            $data = [];
            foreach($res['data'] as $r){
                $d = [
                    'id'=>$r['id'],
                    'caption'=>$r['caption']??null,
                    'media_url'=>null,
                    'media_type'=>$r['media_type'],
                    'like_count'=>$r['like_count']??0,
                    'comments_count'=>$r['comments_count']??0
                ];
                if($r['media_type']==='IMAGE'){
                    $d['media_url'] = $r['media_url'];
                }else if($r['media_type']==='VIDEO'){
                    $d['media_url'] = $r['thumbnail_url'];
                }else if($r['media_type']==='CAROUSEL_ALBUM'){
                    if ($page->fb_access_token == 'loggedInWithInstagram') {
                        $response = Http::get('https://graph.instagram.com'.$d['id'].'/children?fields=media_url&access_token='.$page->access_token);
                    } else {
                        $response = Http::get('https://graph.facebook.com/'.$d['id'].'/children?fields=media_url&access_token='.$page->access_token);
                    }
                    $i = 0;
                    $d['media_url'] = [];
                    foreach($response['data'] as $dd){
                        if($i>=10){
                            break;
                        }
                        $d['media_url'][] = $dd['media_url'];
                        $i++;
                    }

                }
                $data[] = $d;

            }
            return ['data'=>$data];
        }

        if ($response->failed()) {
            $error = $response->json();
            return [];
        }
    }

    /**
     * Get fields based on the facebook access token type.
     *
     * @param string $fb_access_token Facebook token that for logged in with instagram users is 'loggedInWithInstagram'
     * @return array These fields will be used for getting an Instagram page Media
     */
    private function getMediaFields($fb_access_token)
    {
        $fields = [
            'id',
            'caption',
            'like_count',
            'comments_count',
            'title',
            'description',
            'text',
            'media_url',
            'thumbnail_url',
            'media_type'
        ];

        if ($fb_access_token == 'loggedInWithInstagram') {
            unset($fields[array_search('text', $fields)]);
        }

        return $fields;
    }

    /**
     * Display the specified resource.
     */
    public function show($id=null)
    {
        if(!$id){
            $page = Page::where('user_id',auth()->user()->id)->firstOrFail();
        }else{
            $page = Page::where('user_id',auth()->user()->id)->where('id',$id)->firstOrFail();
        }
        if($page->expired_at){
            $now = Carbon::now();
            $page->expired_at = Carbon::parse($page->expired_at)->diffInDays($now);
        }else{
            $page->expired_at = 0;
        }
        return $page;
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Page $page)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePageRequest $request, Page $page)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $page = Page::where('user_id',auth()->user()->id)->where('id',$id)->firstOrFail();
        $page->delete();
        return ['success' => true];
    }

    public function loginToInstagram(Request $request): JsonResponse
    {
        $uid = auth()->user()->id;

        if($request->page){
            $page = Page::where('user_id',$uid)->where('id',$request->page)->first();
        }else{
            $page = Page::where('user_id',$uid)->first();
        }
        if(!$page){
            $page = new Page();
            $page->title = auth()->user()->phone;
            $page->save();
        }
        $pageId = $page->id;
        $state = ['uid'=>$uid,'pid'=>$pageId];
        $state = urlencode(json_encode($state));
        if ($page->access_token!==null) {
            $this->disconnectFromMeta($pageId);
        }

        $url = "https://www.instagram.com/oauth/authorize?enable_fb_login=0&force_authentication=1&client_id=****************&redirect_uri=https://directam-plus.manymessage.com/api/instalogin/instagramBack.php&response_type=code&scope=instagram_business_basic%2Cinstagram_business_manage_messages%2Cinstagram_business_manage_comments%2Cinstagram_business_content_publish&state=$state";

        return response()->json([
            'success' => true,
            'data' => $url,
            'message' => 'ارسال شد'
        ]);
    }

    public function saveInstagramAccountToMongo(Request $request, $userId): JsonResponse
    {
        $at = $request->input('at');
        $accountsData = $request->input('accounts');

        if ($accountsData) {
            for ($i = 0; $i < count($accountsData); $i++) {
                $accountsData[$i]['access_token'] = encryptData($accountsData[$i]['access_token']);
            }
        }

        $data = [
            'user_id' => $userId,
            'date' => Carbon::now()->timestamp,
            'accounts' => $accountsData,
            'at' => encryptData($at),
        ];

        Redis::set("instagram:connection_attempt:{$userId}", json_encode($data));

        return response()->json([
            "success" => true,
            'message' => 'Facebook accounts saved successfully!',
        ]);
    }

    public function connectToInstagramAccount(Request $request)
    {
        $userId = $request->user_id;
        $user = User::find($userId);
        $pageId = $request->page_id;
        $instagramId = strval($request->input('instagram_id'));
        if(!$pageId){
            $pageId = $user->pages()->first()->id;
        }
        if (!$instagramId) {
            return response()->json(
                [
                    "success" => false,
                    'message' => 'آیدی اینستاگرام را وارد کنید'
                ],
                422
            );
        }

        $json = Redis::get("instagram:connection_attempt:{$userId}");
        $latestConnectionAttempt = $json ? json_decode($json, true) : null;

        if (!$latestConnectionAttempt) {
            return response()->json(['success' => false, 'message' => 'هیچ اکانت فیسبوکی یافت نشد']);
        }

        $accountData = null;

        if (isset($latestConnectionAttempt['accounts'])) {
            foreach ($latestConnectionAttempt['accounts'] as $account) {
                if ($account['insta_id'] == $instagramId) {
                    $accountData = $account;
                    break;
                }
            }
        }
        if (!$accountData) {
            return response()->json(['success' => false, 'message' => 'اکانت اینستاگرامی یافت نشد'], 404);
        }

        $encryptedToken = $accountData['access_token'];

        $instagram_access_token = decryptData(
            base64_decode($encryptedToken[0]),
            base64_decode($encryptedToken[1]),
            base64_decode($encryptedToken[2])
        );

        $fields = [
            'live_comments',
            'comments',
            'messages',
            'messaging_optins',
            'messaging_postbacks',
            'message_reactions',
            'messaging_referral',
            'messaging_seen',
        ];

        $fields_string = implode(',', $fields);

        $url = "{$this->instaUrl}/me/subscribed_apps?subscribed_fields={$fields_string}&access_token=$instagram_access_token";
        $ig_access_token = json_encode($encryptedToken);

        $client = new Client();
        try {
            $client->post($url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در اتصال وب هوک',
                'error' => $e->getMessage(),
            ]);
        }

        $existPage = Page::where('page_user_id',$accountData['insta_id'])->exists();
        if($existPage){
            return response()->json(['success' => false, 'message' => 'اکانت مجاز نیست'], 404);
        }

        $userPage = Page::updateOrCreate(
            [
                'user_id' => $userId,
                'id'      => $pageId
            ],
            [
                'title'          => $accountData['insta_username'],
                'access_token'   => $ig_access_token,
                'page_user_id'   => $accountData['insta_id'],
                'fb_user_id'     => 'loggedInWithInstagram',
                'fb_access_token'=> 'loggedInWithInstagram',
                'profile'        => $accountData['fb_picture_url']??''
            ]
        );

        // Updating the webservice access token if one found for the user
        $specialUser = $this->specialUserRepository->findByUserId($userId);
        if ($specialUser) {
            $specialUser->instagram_id = $accountData['insta_id'];
            $specialUser->save();
            $this->specialUserRepository->updateAccessToken($specialUser->id, $ig_access_token);
        }

        return response()->json(['success' => true, 'account' => $userPage]);

    }

    public function loginToFacebook(Request $request){
        $user = auth()->user();
        $uid = $user->id;

        if($request->page){
            $page = Page::where('user_id',$uid)->where('id',$request->page)->first();
        }else{
            $page = Page::where('user_id',$uid)->first();
        }
        if(!$page){
            $page = new Page();
            $page->title = auth()->user()->phone;
            $page->save();
        }
        $pageId = $page->id;
        $state = ['uid'=>$uid,'pid'=>$pageId];
        $state = urlencode(json_encode($state));
        // $userFbId =$userPage->page_user_id;
        if ($page->access_token!==null) {
            $this->disconnectFromMeta($pageId);
        }

        $redirectUri = "https://directam-plus.manymessage.com/instalogin/back.php";
        $fbLatestApiVersion = 'v20.0';
        $clientId = "***************";
        $scopes = [
            "email",
            "public_profile",
            "pages_show_list",
            "pages_read_engagement",
            "pages_messaging",
            "pages_manage_metadata",
            "instagram_basic",
            "instagram_manage_messages",
            "instagram_manage_comments",
            "business_management"
        ];

        $scopeString = implode(',', $scopes);


        $url = "https://facebook.com/dialog/oauth?client_id=$clientId&redirect_uri=$redirectUri&scope=$scopeString&state=$state&response_type=code&display=page";
        return response()->json([
            'success' => true,
            'data' => $url,
            'message' => 'ارسال شد'
        ]);
    }

    public function saveFacebookAccountsToMongo(Request $request, $userId)
    {
        try {
            $at = $request->input('at');
            $accountsData = $request->input('accounts');

            if ($accountsData) {
                for ($i = 0; $i < count($accountsData); $i++) {
                    $accountsData[$i]['access_token'] = encryptData($accountsData[$i]['access_token']);
                }
            }

            $data = [
                'user_id' => $userId,
                'date'     => Carbon::now()->timestamp,
                'accounts' => $accountsData,
                'at'       => encryptData($at),
            ];

            Redis::set("facebook:connection_attempt:{$userId}", json_encode($data));

        } catch (Exception $e) {
            return response()->json([
                "success" => false,
                "message" => 'failed to save facebook accounts',
                "error"   => $e->getMessage()
            ]);
        }

        return response()->json([
            "success" => true,
            'message' => 'Facebook accounts saved successfully!',
        ]);
    }

    public function getFacebookAccounts()
    {
        $userId = auth()->user()->id;

        $json = Redis::get("facebook:connection_attempt:{$userId}");
        $latestFacebookAccount = $json ? json_decode($json, true) : null;

        if (!$latestFacebookAccount) {
            return response()->json(
                [
                    "success" => false,
                    'message' => 'پیدا نشد!'
                ]
            );
        }

        $accountsData = $latestFacebookAccount->accounts;

        if (isset($accountsData)) {
            foreach ($accountsData as &$account) {
                unset($account['access_token']);
                if (isset($account['instagram_business_account'])) {
                    unset($account['instagram_business_account']['id']);
                }
            }
        }
        \Log::info('Facebook Account:', ['accounts' => $accountsData]);
        return response()->json(
            [
                "success" => true,
                "data" => [
                    'accounts' => $accountsData,
                ]
            ]
        );
    }

    public function connectToFacebookAccount(Request $request,$id=null)
    {
        $user = auth()->user();
        $userId = $user->id;
        $pageId = $request->pageId;
        $fbpage_id = strval($request->input('fb_id'));
        if(!$pageId){
            $pageId = $user->pages()->first()->id;
        }
        if (!$fbpage_id) {
            return response()->json(
                [
                    "success" => false,
                    'message' => 'آیدی فیسبوک را وارد کنید'
                ],
                422
            );
        }


        $json = Redis::get("facebook:connection_attempt:{$userId}");
        $latestConnectionAttempt = $json ? json_decode($json, true) : null;


        if (!$latestConnectionAttempt) {
            return response()->json(['success' => false, 'message' => 'هیچ اکانت فیسبوکی یافت نشد']);
        }

        $accountData = null;


        if (isset($latestConnectionAttempt['accounts'])) {
            foreach ($latestConnectionAttempt['accounts'] as $account) {
                if ($account['fbpage_id'] == $fbpage_id) {
                    $accountData = $account;
                    break;
                }
            }
        }
        if (!$accountData) {
            return response()->json(['success' => false, 'message' => 'اکانت فیسبوکی یافت نشد'], 404);
        }
        if (!isset($accountData['insta_id'])) {
            return response()->json(['success' => false, 'message' => 'هیچ اکانت اینستاگرامی یافت نشد']);
        }

        $encryptedToken = $accountData['access_token'];
        $encryptedAt = $latestConnectionAttempt['at'];

        $fbpage_access_token = decryptData(
            base64_decode($encryptedToken[0]),
            base64_decode($encryptedToken[1]),
            base64_decode($encryptedToken[2])
        );

        $at = decryptData(
            base64_decode($encryptedAt[0]),
            base64_decode($encryptedAt[1]),
            base64_decode($encryptedAt[2])
        );

        // Define the fields array
        $fields = ['feed'];

        // Implode the fields array into a comma-separated string
        $fields_string = implode(',', $fields);

        $url = "{$this->baseUrl}/$fbpage_id/subscribed_apps?subscribed_fields={$fields_string}&access_token=$fbpage_access_token";
        $fbpage_access_token = json_encode($encryptedToken);

        $at = json_encode($encryptedAt);

        $client = new Client();
        try {
            $client->post($url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'خطا در اتصال به فیسبوک', 'error' => $e->getMessage()]);
        }

        $existPage = Page::where('page_user_id',$accountData['insta_id'])->exists();
        if($existPage){
            return response()->json(['success' => false, 'message' => 'اکانت مجاز نیست'], 404);
        }
        $userPage = Page::updateOrCreate(
            [
                'user_id' => $userId,
                'id'      => $pageId
            ],
            [
                'title'          => $accountData['insta_username'],
                'access_token'   =>$fbpage_access_token,
                'page_user_id'   => $accountData['insta_id'],
                'fb_user_id'     => $fbpage_id,
                'fb_access_token'=>$at,
                'profile'        =>$accountData['fb_picture_url']??''
            ]
        );
        return response()->json(['success' => true, 'account' => $accountData]);
    }

    public function disconnectFromMeta($id=null)
    {
        $user = auth()->user();
        if(!$id){
            $userPage = Page::where('user_id',$user->id)->first();
        }else{
            $userPage = Page::where('id',$id)->where('user_id',$user->id)->first();
        }

        if (!$userPage) {

            return response()->json([
                'success' => false
            ]);
        }

        if ($userPage->fb_access_token !== 'loggedInWithInstagram') {
            try {
                $accessToken = $userPage->fb_access_token;
                $url = "https://graph.facebook.com/v20.0/me/permissions?access_token=$accessToken";

                Http::delete($url);
            } catch (Exception $e) {

            }
        }

        $userPage->access_token = null;
        $userPage->page_user_id = null;
        $userPage->fb_user_id = null;
        $userPage->fb_access_token = null;
        $userPage->save();

        return response()->json([
            'success' => true
        ]);
    }

    public function getPageStatus(Request $request,$id = null)
    {
        $finalResult = [];
        $user = auth()->user();
        if($id){
            $userPage = Page::where('user_id',$user->id)->where('id',$id)->first();
        }else{
            $userPage = $user->pages()->first();
        }
        if(!$userPage||$userPage->access_token===null){
            return response()->json([
                'success' => false,
                'message' => ' شما به فیسبوک متصل نمیباشید'
            ]);
        }

        if ($userPage->fb_access_token == 'loggedInWithInstagram') {
            $status = "instagram";
            $InstaAccessToken= $userPage->access_token;

            $isPermissionGranted = true;
            $allPermissionGranted = true;
            $client = new Client();
        } else {
            $status = "facebook";

            $accessToken= $userPage->fb_access_token;
            $InstaAccessToken= $userPage->access_token;
            $fbPageId= $userPage->fb_user_id;

            $client = new Client();
            $url = "{$this->baseUrl}/me/permissions?access_token=$accessToken" ;

            $options = [
                'timeout' => 10,
                'connect_timeout' => 10,
            ];

            try {
                $response = $client->request('GET', $url, $options);
                $responseData = json_decode($response->getBody()->getContents(), true);

                $permissionToCheck = 'instagram_manage_comments';
                $isPermissionGranted = false;
                $allPermissionGranted = true;

                if (isset($responseData['data'])) {
                    foreach ($responseData['data'] as $permission) {
                        if ($permission['status'] != 'granted') {
                            $allPermissionGranted = false;
                        }
                        if ($permission['permission'] == $permissionToCheck && $permission['status'] == 'granted') {
                            $isPermissionGranted = true;
                        }
                    }
                }
            } catch (\GuzzleHttp\Exception\RequestException $e) {
                return response()->json([
                    'success' => false,
                    'message' => ' شما به فیسبوک متصل نمیباشید'
                ]);
                // Log the error or handle it as needed
                \Log::error('Error: ' . $e->getMessage());
                echo 'Error: ' . $e->getMessage();
            }
        }

        if ($status == "instagram") {
            $url = "{$this->instaUrl}/me/conversations?platform=instagram&access_token=$InstaAccessToken";

        } elseif ($status == "facebook") {
            $url = "{$this->baseUrl}/{$fbPageId}/conversations?platform=instagram&access_token=$InstaAccessToken";
        }

        try {
            $response = $client->request('GET', $url);
            $result = json_decode($response->getBody()->getContents(), true);

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            // Log error or handle it according to your application's needs
            if($e->getResponse()->getStatusCode()===403){
                $result = $e->getResponse()->getBody()->getContents();
                $result = json_decode($result,true);
            }else{
                $result = [
                    'error' => true,
                    'message' => $e->getMessage()
                ];
                return response()->json($result);
            }

        }
        if (isset($result['error'])) {
            $finalResult['status'] = 'error';
            if ($result['error']['code'] == '200') {
                $finalResult['persian_error'] = 'از طریق اینستاگرام دکمه ی allow access to messages فعال نشده';
            } elseif ($result['error']['code'] == '36103') {
                $finalResult['persian_error'] = "اکانت اینستاگرام شما مناسب نیست ، اکانت میبایست professional account در حالت business یا creator باشد ، در صورتی که اکانت در حالت creator با بیش از ۵۰۰ کا فالور میباشد میبایست اکانت به business تغییر  کند";
            } else {
                $finalResult['persian_error'] = $result['error']['message'];
            }
            $finalResult['message_error'] = $result['error']['message'];
            $finalResult['error_code'] = $result['error']['code'];
        } elseif (isset($result['data'])) {
            $finalResult['status'] = 'ok';
        }
        $data=$finalResult;
        // Check the status in the response
        if (!isset($data['status']) || $data['status'] !== 'ok') {
            // Status is not ok, handle the error
            $finalResult = isset($data['persian_error']) ? $data['persian_error'] : $data['message_error'] ?? "";
            // Log the error or handle it as required
        }
        // $responseType = Http::get('https://graph.facebook.com/v20.0/'.$userPage->page_user_id.'?fields=name,username&access_token='.$userPage->access_token);
        // return ($responseType);
        $res = [
            'message' => $finalResult ?? ['status' => 'ok'],
            'data' => [
                'insta_username'         => $userPage->title,
                'insta_profile'          => $userPage->profile,
                'permission'             => $isPermissionGranted,
                'all_permission_granted' => $allPermissionGranted
            ]
        ];
        if (isset($data['error_code'])) {
            $res['data']['code'] = $data['error_code'];
        }
        return response()->json($res);
    }

}
