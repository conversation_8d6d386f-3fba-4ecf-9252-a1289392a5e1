<script setup>
const props = defineProps({
  selectedCheckbox: {
    type: Array,
    required: true,
  },
  checkboxContent: {
    type: Array,
    required: true,
  },
  gridColumn: {
    type: null,
    required: false,
  },
  showDesc: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(['update:selectedCheckbox'])

const updateSelectedOption = value => {
  if (typeof value !== 'boolean')
    emit('update:selectedCheckbox', value)
}
</script>

<template>
  <template v-if="props.checkboxContent && props.selectedCheckbox">
    <div class="d-flex flex-column flex-md-row flex-wrap ga-2">
      <div
        v-for="item in props.checkboxContent"
        :key="item.title"
        v-bind="gridColumn"
      >
        <VLabel
          class="custom-input custom-checkbox-icon rounded cursor-pointer pa-2 w-100"
          :class="props.selectedCheckbox.includes(item.value) ? 'active' : ''"
        >
          <slot :item="item">
            <div class="d-flex align-center text-center gap-2">
              <VIcon
                :icon="item.icon"
                class="text-high-emphasis"
              />

              <div class="d-flex flex-column ga-2">
                <h6 class="cr-title text-base">
                  {{ item.title }}
                </h6>
                <p v-if="showDesc" class="text-sm clamp-text mb-0">
                  {{ item.subtitle }}
                </p>
              </div>
            </div>
          </slot>
          <div class="align-self-center mt-0">
            <VCheckbox
              :model-value="props.selectedCheckbox"
              :value="item.value"
              @update:model-value="updateSelectedOption"
            />
          </div>
        </VLabel>
      </div>
    </div>
  </template>
</template>

<style lang="scss" scoped>
.custom-checkbox-icon {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 0.375rem;
  width: 200px;

  .v-checkbox {
    margin-block-end: 0;

    .v-selection-control__wrapper {
      margin-inline-start: 0;
    }
  }

  .cr-title {
    font-weight: 500;
  }
}
</style>

<style lang="scss">
.custom-checkbox-icon {
  .v-checkbox {
    margin-block-end: -0.375rem;

    .v-selection-control__wrapper {
      margin-inline-start: 0;
    }
  }
}
</style>
