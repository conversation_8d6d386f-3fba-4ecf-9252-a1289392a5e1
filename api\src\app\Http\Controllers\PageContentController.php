<?php

namespace App\Http\Controllers;

use App\Models\PageContent;
use App\Http\Requests\StorePageContentRequest;
use App\Http\Requests\UpdatePageContentRequest;

class PageContentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $contents = PageContent::where('user_id',auth()->user()->id)->paginate(15);
        return $contents;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(StorePageContentRequest $request)
    {
        $pageContent = new PageContent();
        $pageContent->fill($request->all());
        $pageContent->save();
        return $pageContent;
    }


    /**
     * Display the specified resource.
     */
    public function show(PageContent $pageContent)
    {
        return $pageContent;
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePageContentRequest $request, PageContent $pageContent)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PageContent $pageContent)
    {
        //
    }
}
