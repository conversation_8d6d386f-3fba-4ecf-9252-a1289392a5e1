<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class productsUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if(Auth::user() == null) {
            return false;
        }
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "name"=>"required|max:255",
            "description"=>"required|max:2000",
            "post_id"=>"required",
            "triggers"=>"required",
            "delivery_methods"=>"array|min:0|max:5|exists:delivery_methods,id",
            "payment_methods"=>"array|min:0|max:4|exists:payment_methods,id",
            "price"=>"required|max:255",
            "multiple"=>"boolean",
            "multipleMessage"=>"max:255",
        ];
    }
    public function messages(): array
    {
        return [
            "name.required"=>"نام اجباری است",
//            "name.unique"=>"name must be unique",
            "name.max"=>"نام حداکثر 255 کاراکتر میباشد",
            "description.required"=>"توضیحات اجباری است",
            "description.max"=>"توضیحات حداکثر 2000 کاراکتر میباشد",
            "post_id.required"=>"پست اجباری است",
            "triggers.required"=>"فعال کننده اجباری است",
            "price.required"=>"قیمت اجباری است",
            "price.max"=>"قیمت مجاز نیست",
            "delivery_methods.required"=>"انتخاب روش ارسال اجباری است",
            "payment_methods.required"=>"انتخاب روش پرداخت اجباری است",
            "delivery_methods.min"=>"انتخاب روش ارسال اجباری است",
            "payment_methods.min"=>"انتخاب روش پرداخت اجباری است",
            "delivery_methods.max"=>"طول ورودی مجاز نیست",
            "payment_methods.max"=>"طول ورودی مجاز نیست",
            "delivery_methods.array"=>"روش ارسال نامعتبر است",
            "payment_methods.array"=>"روش پرداخت نامعتبر است",
            "delivery_methods.exists"=>"روش ارسال نامعتبر است",
            "payment_methods.exists"=>"روش پرداخت نامعتبر است",

        ];
    }
}
