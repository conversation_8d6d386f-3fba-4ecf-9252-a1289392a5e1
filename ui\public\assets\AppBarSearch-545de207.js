import{_ as $,cV as R,r as _,w as y,a as L,o as V,f as C,e as t,b as s,a9 as K,af as S,n as l,a6 as F,aP as b,d as o,a4 as m,K as g,c as U,F as z,i as N,A as d,v as P,x as D,L as v,ak as T,R as j,U as q}from"./index-169996dc.js";import{P as E}from"./vue3-perfect-scrollbar.esm-e1f82a0e.js";import{V as M}from"./VTextField-a8984053.js";import{V as O}from"./VDivider-12bfa926.js";import{V as Q,a as X}from"./VList-349a1ccf.js";import"./VField-150a934a.js";import"./index-00c7d20d.js";import"./VInput-c4d3942a.js";import"./ssrBoot-c101cd97.js";const G=c=>(j("data-v-a6198595"),c=c(),q(),c),H={class:"d-flex align-center text-high-emphasis me-1"},J={class:"d-flex align-start"},W={class:"h-100"},Y={class:"h-100"},Z={class:"app-bar-search-suggestions d-flex flex-column align-center justify-center text-high-emphasis h-100"},ee={class:"d-flex align-center flex-wrap justify-center gap-2 text-h6 my-3"},se=G(()=>o("span",null,"No Result For ",-1)),ae={__name:"AppBarSearch",props:{isDialogVisible:{type:Boolean,required:!0},searchResults:{type:Array,required:!0}},emits:["update:isDialogVisible","search"],setup(c,{emit:w}){const r=c,h=w,{ctrl_k:x,meta_k:k}=R({passive:!1,onEventFired(e){e.ctrlKey&&e.key==="k"&&e.type==="keydown"&&e.preventDefault()}}),f=_(),B=_(),a=_("");y([x,k],()=>{h("update:isDialogVisible",!0)});const u=()=>{a.value="",h("update:isDialogVisible",!1)},A=e=>{var i,p;e.key==="ArrowDown"?(e.preventDefault(),(i=f.value)==null||i.focus("next")):e.key==="ArrowUp"&&(e.preventDefault(),(p=f.value)==null||p.focus("prev"))},I=e=>{a.value="",h("update:isDialogVisible",e)};return y(()=>r.isDialogVisible,()=>{a.value=""}),(e,i)=>{const p=L("IconBtn");return V(),C(T,{"max-width":"600","model-value":r.isDialogVisible,height:e.$vuetify.display.smAndUp?"550":"100%",fullscreen:e.$vuetify.display.width<600,class:"app-bar-search-dialog","onUpdate:modelValue":I,onKeyup:b(u,["esc"])},{default:t(()=>[s(K,{height:"100%",width:"100%",class:"position-relative"},{default:t(()=>[s(S,{class:"pt-2"},{default:t(()=>[s(M,{ref_key:"refSearchInput",ref:B,modelValue:l(a),"onUpdate:modelValue":[i[0]||(i[0]=n=>F(a)?a.value=n:null),i[1]||(i[1]=n=>e.$emit("search",l(a)))],autofocus:"",density:"comfortable",variant:"plain",onKeyup:b(u,["esc"]),onKeydown:A},{"prepend-inner":t(()=>[o("div",H,[s(m,{size:"22",icon:"tabler-search",style:{opacity:"1"}})])]),"append-inner":t(()=>[o("div",J,[o("div",{class:"text-base text-disabled cursor-pointer me-1",onClick:u}," [esc] "),s(p,{size:"22",onClick:u},{default:t(()=>[s(m,{icon:"tabler-x",size:"20"})]),_:1})])]),_:1},8,["modelValue"])]),_:1}),s(O),s(l(E),{options:{wheelPropagation:!1,suppressScrollX:!0},class:"h-100"},{default:t(()=>[g(s(l(Q),{ref_key:"refSearchList",ref:f,density:"compact",class:"app-bar-search-list"},{default:t(()=>[(V(!0),U(z,null,N(r.searchResults,n=>d(e.$slots,"searchResult",{key:n,item:n},()=>[s(l(X),null,{default:t(()=>[P(D(n),1)]),_:2},1024)],!0)),128))]),_:3},512),[[v,l(a).length&&!!r.searchResults.length]]),g(o("div",W,[d(e.$slots,"suggestions",{},void 0,!0)],512),[[v,!!r.searchResults&&!l(a)&&e.$slots.suggestions]]),g(o("div",Y,[d(e.$slots,"noData",{},()=>[s(S,{class:"h-100"},{default:t(()=>[o("div",Z,[s(m,{size:"75",icon:"tabler-file-x"}),o("div",ee,[se,o("span",null,'"'+D(l(a))+'"',1)]),d(e.$slots,"noDataSuggestion",{},void 0,!0)])]),_:3})],!0)],512),[[v,!r.searchResults.length&&l(a).length]])]),_:3})]),_:3})]),_:3},8,["model-value","height","fullscreen"])}}},de=$(ae,[["__scopeId","data-v-a6198595"]]);export{de as default};
