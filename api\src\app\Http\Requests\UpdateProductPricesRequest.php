<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProductPricesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if(auth()->user() == null) {
            return false;
        }
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'=>'max:255',
            'image'=>'image|mimes:jpeg,png,gif',
            'price'=>'integer',
            'description'=>'max:2000',
            'product_id'=>'required|exists:products,id'
        ];
    }
    public function messages(): array
    {
        return [
            'description.required'=>'توضیحات اجباری است',
            'description.max'=>'توضیحات نمیتواند بیشتر از 2000 کاراکتر باشد',
            'product_id.required'=>'محصول اجباری است',
            'product_id.exists'=>'محصول پیدا نشد',
            'name.max'=>'نام نمیتواند بیشتر از 255 کاراکتر باشد',
            'price.integer'=>'قیمت معتبر نیست',
            'image.required'=>'عکس اجباری است',
            'image.image'=>'عکس معتبر نیست',
            'image.mimes'=>'فرمت ارسال شده معتبر نیست فرمت های معتبر: jpeg,png,gif',
        ];
    }
}
