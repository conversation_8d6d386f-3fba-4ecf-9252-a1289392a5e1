<?php

namespace App\Jobs;

use App\Models\CrmAction;
use App\Models\Transaction;
use App\Services\CrmApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessWaitingTransaction implements ShouldQueue
{
	use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

	/**
	 * Create a new job instance.
	 */
	public function __construct(public Transaction $transaction)
	{
		//
	}

	/**
	 * Execute the job.
	 */
	public function handle(): void
	{
		// If the transaction status is "waiting" send its data to the CRM
		if ($this->transaction->status === 'waiting') {
			$crmAction = CrmAction::where('name', CrmAction::TRANSACTION_WAITING)->first();
			$crmApiService = new CrmApiService();
			$crmApiService->sendTransactionData($this->transaction, $crmAction);
		}
	}
}
