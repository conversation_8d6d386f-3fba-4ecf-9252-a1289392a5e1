import{M as y,a8 as _,o as d,f as b,e as a,b as t,A as l,y as n,ab as C,ao as T,i as k,aI as p,d as s,v as m,x as c,c as v,n as $,z as B,af as w,a9 as I}from"./index-169996dc.js";import{r as P}from"./VDataTable-a3a359fa.js";import{V as N}from"./VTextField-a8984053.js";import{V as S}from"./VSkeletonLoader-4c44fbcf.js";import{V as z}from"./VPagination-8a53e08f.js";const D={class:"d-flex flex-column flex-md-row justify-between"},L={class:"mt-5"},M=s("p",{class:"pt-4"}," اطلاعاتی وجود ندارد. ",-1),j={key:0,class:"text-center mt-5"},G=y({__name:"TableComponent",props:{page:{default:1},perPage:{default:10},columns:{},data:{},totalItem:{},loading:{type:Boolean,default:!1},search:{},paginate:{type:Boolean,default:!0}},emits:["pagination"],setup(u,{emit:g}){const r=u,f=g,h=_(()=>Math.ceil(r.totalItem/r.perPage)),V=e=>{f("pagination",e)};return(e,A)=>(d(),b(I,{flat:""},{default:a(()=>[t(C,{class:n(e.$slots.title?"pt-5":"")},{default:a(()=>[l(e.$slots,"title")]),_:3},8,["class"]),t(w,{class:n(e.$slots.title?"mt-5":"")},{default:a(()=>[t(P,{page:e.page,"items-per-page":e.perPage,headers:e.columns,items:e.data,"items-length":e.totalItem,loading:e.loading,search:e.search},T({top:a(()=>[t(p,null,{default:a(()=>[s("div",D,[t(p,null,{default:a(()=>[t(N,{density:"compact",label:"جستجو","prepend-inner-icon":"tabler-search",variant:"solo-filled",flat:"","hide-details":"","single-line":"",class:n(["mt-3",e.$slots.extraTop?"w-100":"search-input"])},null,8,["class"])]),_:1}),l(e.$slots,"extraTop"),s("p",L,[m(" مجموع کل رکوردها: "),s("strong",null,c(e.totalItem||"-"),1)])])]),_:3})]),loading:a(()=>[t(S,{type:"table-row@10"})]),"no-data":a(()=>[M]),bottom:a(()=>[e.paginate?(d(),v("div",j,[t(z,{length:$(h),"onUpdate:modelValue":V},null,8,["length"])])):B("",!0)]),_:2},[k(e.columns,o=>({name:`item.${o.key}`,fn:a(({item:i})=>[l(e.$slots,o.key,{item:i},()=>[m(c(i[o.key]),1)])])}))]),1032,["page","items-per-page","headers","items","items-length","loading","search"])]),_:3},8,["class"])]),_:3}))}});export{G as _};
