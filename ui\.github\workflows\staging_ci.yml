name: Vue.js CI and Deployment For staging

on:
  push:
    branches: [staging]
  pull_request:
    branches: [staging]
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "18.16.0"
      - name: Cache pnpm modules
        uses: actions/cache@v2
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install Dependencies
        run: pnpm install

      - name: Build
        run: pnpm build

      - name: Copy .htaccess to dist directory
        run: cp .htaccess dist/

      - name: Remove specific files and directories
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.SSH_PORT }}
          script: |
            rm -rf ${{secrets.STAGING_SERVER_ADDRESS}}/assets
            rm -rf ${{secrets.STAGING_SERVER_ADDRESS}}/images
            rm -f ${{secrets.STAGING_SERVER_ADDRESS}}/index.html
            rm -f ${{secrets.STAGING_SERVER_ADDRESS}}/favicon.ico
            rm -f ${{secrets.STAGING_SERVER_ADDRESS}}/loader.css
            rm -f ${{secrets.STAGING_SERVER_ADDRESS}}/mockServiceWorker.js
            rm -f ${{secrets.STAGING_SERVER_ADDRESS}}/logo.svg
            rm -f ${{secrets.STAGING_SERVER_ADDRESS}}/manifest.json
      - name: Copy repository contents to remote server via scp
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "dist/"
          target: "${{secrets.STAGING_SERVER_ADDRESS}}"

      - name: move dist files to public html
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          # password: ${{ secrets.SERVER_PASSWORD }} # Use the SSH password secret
          port: ${{secrets.SSH_PORT}}
          script: |
            cd ${{secrets.STAGING_SERVER_ADDRESS}}
            mv dist/* .
