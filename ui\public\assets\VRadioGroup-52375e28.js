import{m as k,V as p,a as A,b as C}from"./VSelectionControl-8ecbcf09.js";import{ap as V,as as f,at as b,b as a,s as n,bg as O,au as c,by as h,a8 as F,b6 as U,bA as x,F as N}from"./index-169996dc.js";import{m as D,V as m,a as L}from"./VInput-c4d3942a.js";const M=V({...k({falseIcon:"$radioOff",trueIcon:"$radioOn"})},"VRadio"),z=f()({name:"VRadio",props:M(),setup(e,l){let{slots:s}=l;return b(()=>a(p,n(e,{class:["v-radio",e.class],style:e.style,type:"radio"}),s)),{}}});const T=V({height:{type:[Number,String],default:"auto"},...D(),...O(A(),["multiple"]),trueIcon:{type:c,default:"$radioOn"},falseIcon:{type:c,default:"$radioOff"},type:{type:String,default:"radio"}},"VRadioGroup"),B=f()({name:"VRadioGroup",inheritAttrs:!1,props:T(),emits:{"update:modelValue":e=>!0},setup(e,l){let{attrs:s,slots:o}=l;const y=h(),i=F(()=>e.id||`radio-group-${y}`),t=U(e,"modelValue");return b(()=>{const[v,I]=x(s),g=m.filterProps(e),R=p.filterProps(e),r=o.label?o.label({label:e.label,props:{for:i.value}}):e.label;return a(m,n({class:["v-radio-group",e.class],style:e.style},v,g,{modelValue:t.value,"onUpdate:modelValue":u=>t.value=u,id:i.value}),{...o,default:u=>{let{id:d,messagesId:P,isDisabled:G,isReadonly:S}=u;return a(N,null,[r&&a(L,{id:d.value},{default:()=>[r]}),a(C,n(R,{id:d.value,"aria-describedby":P.value,defaultsTarget:"VRadio",trueIcon:e.trueIcon,falseIcon:e.falseIcon,type:e.type,disabled:G.value,readonly:S.value,"aria-labelledby":r?d.value:void 0,multiple:!1},I,{modelValue:t.value,"onUpdate:modelValue":$=>t.value=$}),o)])}})}),{}}});export{B as V,z as a};
