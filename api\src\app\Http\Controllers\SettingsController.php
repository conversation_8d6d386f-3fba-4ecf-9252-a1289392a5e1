<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSettingsRequest;
use App\Http\Requests\UpdateSettingsRequest;
use App\Models\Settings;
use Illuminate\Http\Request;
use App\Models\Page;

class SettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request,$id=null)
    {
        $pageId = null;
        if($id){
            $page = Page::where('id',$id)->where('user_id',auth()->user()->id)->first();
            if(!$page){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $page->id;
        }
        if(!$pageId){
            $page = Page::where('user_id',auth()->user()->id)->first();
            $pageId = $page->id;
        }
        $key = $request->key;
        if(
            $key!="orderedMessage"&&
            $key!="PaymentMessage"
        ){
            return abort(403,"not allowed");
        }
        $setting = Settings::updateOrCreate(
            [
                'key'=>$key,
                'page_id'=>$pageId,
                'user_id'=>auth()->user()->id,
            ],
            [
                'value'=>$request->value
            ]
        );
        if($setting->value===''||$setting->value===null){
            if($key==='orderedMessage'){
                $setting->value = '✅ سفارش شما با موفقیت ثبت شد';
            }else
            if($key==='PaymentMessage'){
                $setting->value = 'با استفاده از دکمه زیر میتونید پرداخت رو ثبت کنید مبلغ: *$*';
            }
        }
        return $setting;
    }



    /**
     * Display the specified resource.
     */
    public function show(Request $request,$id=null)
    {
        $pageId = null;
        if($id){
            $page = Page::where('id',$id)->where('user_id',auth()->user()->id)->first();
            if(!$page){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $page->id;
        }
        $key = $request->key;
        if(
            $key!="orderedMessage"&&
            $key!="PaymentMessage"
        ){
            return abort(403,"not allowed");
        }
        $setting = Settings::where("user_id",auth()->user()->id);
        if($pageId){
            $setting = $setting->where('page_id',$pageId);
        }
        $setting = $setting->where("key",$request->key)->first();
        if(!$setting){
            $setting = new Settings();
        }
        if($setting->value===''||$setting->value===null){
            if($key==='orderedMessage'){
                $setting->value = '✅ سفارش شما با موفقیت ثبت شد';
            }else
            if($key==='PaymentMessage'){
                $setting->value = 'با استفاده از دکمه زیر میتونید پرداخت رو ثبت کنید مبلغ: *$*';
            }
        }
        return $setting;
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Settings $settings)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSettingsRequest $request, Settings $settings)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Settings $settings)
    {
        //
    }
}
