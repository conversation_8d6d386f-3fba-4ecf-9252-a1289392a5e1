import{ap as C,bG as x,aq as _,ax as Y,as as F,at as z,b as m,aB as J,a4 as W,c2 as G,b6 as R,a8 as c,ay as X,b_ as P,aC as Z,aG as ee,aV as A,r as D,w as B,P as ae,a1 as ne,V as te,by as K,n as se,aQ as le,aS as ie,H as ue,b9 as H,aN as re,au as U,bl as oe,bm as de,b0 as ce}from"./index-169996dc.js";import{a as ve}from"./index-00c7d20d.js";const fe=C({text:String,onClick:x(),..._(),...Y()},"VLabel"),Se=F()({name:"VLabel",props:fe(),setup(e,d){let{slots:r}=d;return z(()=>{var n;return m("label",{class:["v-label",{"v-label--clickable":!!e.onClick},e.class],style:e.style,onClick:e.onClick},[e.text,(n=r.default)==null?void 0:n.call(r)])}),{}}});function ge(e){const{t:d}=J();function r(n){let{name:o}=n;const a={prepend:"prependAction",prependInner:"prependAction",append:"appendAction",appendInner:"appendAction",clear:"clear"}[o],s=e[`onClick:${o}`],f=s&&a?d(`$vuetify.input.${a}`,e.label??""):void 0;return m(W,{icon:e[`${o}Icon`],"aria-label":f,onClick:s},null)}return{InputIcon:r}}const me=C({focused:Boolean,"onUpdate:focused":x()},"focus");function Ce(e){let d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:G();const r=R(e,"focused"),n=c(()=>({[`${d}--focused`]:r.value}));function o(){r.value=!0}function a(){r.value=!1}return{focusClasses:n,isFocused:r,focus:o,blur:a}}const ye=C({active:Boolean,color:String,messages:{type:[Array,String],default:()=>[]},..._(),...X({transition:{component:ve,leaveAbsolute:!0,group:!0}})},"VMessages"),he=F()({name:"VMessages",props:ye(),setup(e,d){let{slots:r}=d;const n=c(()=>P(e.messages)),{textColorClasses:o,textColorStyles:a}=Z(c(()=>e.color));return z(()=>m(ee,{transition:e.transition,tag:"div",class:["v-messages",o.value,e.class],style:[a.value,e.style],role:"alert","aria-live":"polite"},{default:()=>[e.active&&n.value.map((s,f)=>m("div",{class:"v-messages__message",key:`${f}-${n.value}`},[r.message?r.message({message:s}):s]))]})),{}}}),j=Symbol.for("vuetify:form"),Be=C({disabled:Boolean,fastFail:Boolean,readonly:Boolean,modelValue:{type:Boolean,default:null},validateOn:{type:String,default:"input"}},"form");function $e(e){const d=R(e,"modelValue"),r=c(()=>e.disabled),n=c(()=>e.readonly),o=A(!1),a=D([]),s=D([]);async function f(){const l=[];let t=!0;s.value=[],o.value=!0;for(const v of a.value){const i=await v.validate();if(i.length>0&&(t=!1,l.push({id:v.id,errorMessages:i})),!t&&e.fastFail)break}return s.value=l,o.value=!1,{valid:t,errors:s.value}}function M(){a.value.forEach(l=>l.reset())}function V(){a.value.forEach(l=>l.resetValidation())}return B(a,()=>{let l=0,t=0;const v=[];for(const i of a.value)i.isValid===!1?(t++,v.push({id:i.id,errorMessages:i.errorMessages})):i.isValid===!0&&l++;s.value=v,d.value=t>0?!1:l===a.value.length?!0:null},{deep:!0,flush:"post"}),ae(j,{register:l=>{let{id:t,validate:v,reset:i,resetValidation:p}=l;a.value.some(I=>I.id===t),a.value.push({id:t,validate:v,reset:i,resetValidation:p,isValid:null,errorMessages:[]})},unregister:l=>{a.value=a.value.filter(t=>t.id!==l)},update:(l,t,v)=>{const i=a.value.find(p=>p.id===l);i&&(i.isValid=t,i.errorMessages=v)},isDisabled:r,isReadonly:n,isValidating:o,isValid:d,items:a,validateOn:ne(e,"validateOn")}),{errors:s,isDisabled:r,isReadonly:n,isValidating:o,isValid:d,items:a,validate:f,reset:M,resetValidation:V}}function pe(){return te(j,null)}const Ve=C({disabled:{type:Boolean,default:null},error:Boolean,errorMessages:{type:[Array,String],default:()=>[]},maxErrors:{type:[Number,String],default:1},name:String,label:String,readonly:{type:Boolean,default:null},rules:{type:Array,default:()=>[]},modelValue:null,validateOn:String,validationValue:null,...me()},"validation");function be(e){let d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:G(),r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:K();const n=R(e,"modelValue"),o=c(()=>e.validationValue===void 0?n.value:e.validationValue),a=pe(),s=D([]),f=A(!0),M=c(()=>!!(P(n.value===""?null:n.value).length||P(o.value===""?null:o.value).length)),V=c(()=>!!(e.disabled??(a==null?void 0:a.isDisabled.value))),l=c(()=>!!(e.readonly??(a==null?void 0:a.isReadonly.value))),t=c(()=>{var u;return(u=e.errorMessages)!=null&&u.length?P(e.errorMessages).concat(s.value).slice(0,Math.max(0,+e.maxErrors)):s.value}),v=c(()=>{let u=(e.validateOn??(a==null?void 0:a.validateOn.value))||"input";u==="lazy"&&(u="input lazy");const g=new Set((u==null?void 0:u.split(" "))??[]);return{blur:g.has("blur")||g.has("input"),input:g.has("input"),submit:g.has("submit"),lazy:g.has("lazy")}}),i=c(()=>{var u;return e.error||(u=e.errorMessages)!=null&&u.length?!1:e.rules.length?f.value?s.value.length||v.value.lazy?null:!0:!s.value.length:!0}),p=A(!1),I=c(()=>({[`${d}--error`]:i.value===!1,[`${d}--dirty`]:M.value,[`${d}--disabled`]:V.value,[`${d}--readonly`]:l.value})),k=c(()=>e.name??se(r));le(()=>{a==null||a.register({id:k.value,validate:y,reset:$,resetValidation:S})}),ie(()=>{a==null||a.unregister(k.value)}),ue(async()=>{v.value.lazy||await y(!0),a==null||a.update(k.value,i.value,t.value)}),H(()=>v.value.input,()=>{B(o,()=>{if(o.value!=null)y();else if(e.focused){const u=B(()=>e.focused,g=>{g||y(),u()})}})}),H(()=>v.value.blur,()=>{B(()=>e.focused,u=>{u||y()})}),B([i,t],()=>{a==null||a.update(k.value,i.value,t.value)});async function $(){n.value=null,await re(),await S()}async function S(){f.value=!0,v.value.lazy?s.value=[]:await y(!0)}async function y(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const g=[];p.value=!0;for(const b of e.rules){if(g.length>=+(e.maxErrors??1))break;const h=await(typeof b=="function"?b:()=>b)(o.value);if(h!==!0){if(h!==!1&&typeof h!="string"){console.warn(`${h} is not a valid value. Rule functions must return boolean true or a string.`);continue}g.push(h||"")}}return s.value=g,p.value=!1,f.value=u,s.value}return{errorMessages:t,isDirty:M,isDisabled:V,isReadonly:l,isPristine:f,isValid:i,isValidating:p,reset:$,resetValidation:S,validate:y,validationClasses:I}}const ke=C({id:String,appendIcon:U,centerAffix:{type:Boolean,default:!0},prependIcon:U,hideDetails:[Boolean,String],hideSpinButtons:Boolean,hint:String,persistentHint:Boolean,messages:{type:[Array,String],default:()=>[]},direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},"onClick:prepend":x(),"onClick:append":x(),..._(),...oe(),...Ve()},"VInput"),Pe=F()({name:"VInput",props:{...ke()},emits:{"update:modelValue":e=>!0},setup(e,d){let{attrs:r,slots:n,emit:o}=d;const{densityClasses:a}=de(e),{rtlClasses:s}=ce(),{InputIcon:f}=ge(e),M=K(),V=c(()=>e.id||`input-${M}`),l=c(()=>`${V.value}-messages`),{errorMessages:t,isDirty:v,isDisabled:i,isReadonly:p,isPristine:I,isValid:k,isValidating:$,reset:S,resetValidation:y,validate:u,validationClasses:g}=be(e,"v-input",V),b=c(()=>({id:V,messagesId:l,isDirty:v,isDisabled:i,isReadonly:p,isPristine:I,isValid:k,isValidating:$,reset:S,resetValidation:y,validate:u})),w=c(()=>{var h;return(h=e.errorMessages)!=null&&h.length||!I.value&&t.value.length?t.value:e.hint&&(e.persistentHint||e.focused)?e.hint:e.messages});return z(()=>{var O,T,L,N;const h=!!(n.prepend||e.prependIcon),q=!!(n.append||e.appendIcon),E=w.value.length>0,Q=!e.hideDetails||e.hideDetails==="auto"&&(E||!!n.details);return m("div",{class:["v-input",`v-input--${e.direction}`,{"v-input--center-affix":e.centerAffix,"v-input--hide-spin-buttons":e.hideSpinButtons},a.value,s.value,g.value,e.class],style:e.style},[h&&m("div",{key:"prepend",class:"v-input__prepend"},[(O=n.prepend)==null?void 0:O.call(n,b.value),e.prependIcon&&m(f,{key:"prepend-icon",name:"prepend"},null)]),n.default&&m("div",{class:"v-input__control"},[(T=n.default)==null?void 0:T.call(n,b.value)]),q&&m("div",{key:"append",class:"v-input__append"},[e.appendIcon&&m(f,{key:"append-icon",name:"append"},null),(L=n.append)==null?void 0:L.call(n,b.value)]),Q&&m("div",{class:"v-input__details"},[m(he,{id:l.value,active:E,messages:w.value},{message:n.message}),(N=n.details)==null?void 0:N.call(n,b.value)])])}),{reset:S,resetValidation:y,validate:u,isValid:k,errorMessages:t}}});export{Pe as V,Se as a,me as b,ge as c,Be as d,$e as e,pe as f,ke as m,Ce as u};
