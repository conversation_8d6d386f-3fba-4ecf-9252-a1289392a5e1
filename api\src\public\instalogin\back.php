<?php
/**
 * back.php - Handles Facebook OAuth callback and saves user account details.
 *
 * This script processes the OAuth callback from Facebook, exchanges the authorization code for an access token,
 * retrieves user and page information, and saves the account details to MongoDB via an API call to Laravel.
 *
 * Steps:
 *
 * Step 1: Capture Authorization Code from Facebook Response
 * - The script captures the 'code' and 'state' parameters returned from Facebook after the user authorizes the app.
 * - It validates that the 'state' parameter contains the required 'uid' and 'pid'.
 *
 * Step 2: Exchange the Authorization Code for an Access Token
 * - Sends a request to Facebook's Graph API to exchange the authorization code for an access token.
 * - If successful, it retrieves the access token; otherwise, it exits with an error.
 *
 * Step 3: Use the Access Token to Get User and Page Information
 * - Uses the access token to retrieve user and page information, including linked Instagram business accounts.
 * - If the retrieval fails, it exits with an error.
 *
 * Step 4: Retrieve Instagram Business Account Details
 * - Iterates through the user's Facebook pages to find linked Instagram business accounts.
 * - If an Instagram business account is found, it collects its details (ID, username, follower count).
 * - If no Instagram business account is linked, only the Facebook page details are collected.
 * - Also if there was a facebook picture. it will store the picture url as well.
 *
 * Step 5: Send Data to Laravel API for Storage (POST request)
 * - Sends the retrieved Facebook and Instagram account details to a Laravel API endpoint for storage.
 * - The payload includes the access token, Facebook page ID, name, and (if available) Instagram account details.
 * - If the POST request fails, the script exits with an error.
 *
 * Step 6: Redirect to Vue Page
 * - If all steps are successful, redirects the user to the appropriate Vue page.
 */

// Step 1: Capture Authorization Code from Facebook Response
$code = $_GET['code'] ?? null;
$state = $_GET['state'] ?? null;

$redirectUrl = "https://dmplus.manymessage.com/pages";

if ($code && $state) {
    $stateData = json_decode(urldecode($state), true);

    if (!isset($stateData['uid'], $stateData['pid'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid state parameter']);
        exit;
    }

    // Step 2: Exchange the authorization code for an access token
    $clientId = "***************";
    $clientSecret = "67cc90427deb30de9931d8f069e6b16b";
    $redirectUri = "https://dmplus.manymessage.com/instalogin/back.php";

    $tokenUrl = "https://graph.facebook.com/v21.0/oauth/access_token";
    $params = [
        'client_id' => $clientId,
        'redirect_uri' => $redirectUri,
        'client_secret' => $clientSecret,
        'code' => $code,
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $tokenUrl . '?' . http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // Only for debugging
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Only for debugging
    $response = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($response, true);

    if (isset($data['access_token'])) {
        $accessToken = $data['access_token'];
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to obtain access token', 'data' => $data]);
        exit;
    }
} else {
//    echo json_encode(['success' => false, 'message' => 'Authorization code or state missing']);
    header("Location: $redirectUrl");
    exit;
}

// Step 3: Use the Access Token to Get User and Page Information
$userDataUrl = "https://graph.facebook.com/v21.0/me?fields=id,name,email,picture,accounts{id,name,access_token,instagram_business_account{id,followers_count,username,name,profile_picture_url}}&access_token=$accessToken";

$ch = curl_init($userDataUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // Only for debugging
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Only for debugging
$response = curl_exec($ch);
curl_close($ch);

$userData = json_decode($response, true);

if (isset($userData['error'])) {
//    echo json_encode(['success' => false, 'message' => 'Error fetching user data: ' . $userData['error']['message']]);
    header("Location: $redirectUrl");
    exit;
}

if (!isset($userData['accounts'])) {
    header("Location: $redirectUrl");
    exit;
}


$fbpageAccessToken = '';
$fbpageId = '';
$fbpageName = '';
$fbPicture = null;
$instaId = null;
$instaUsername = null;
$instaFollowerCount = null;

// Step 4: Retrieve Instagram Business Account Details
foreach ($userData['accounts']['data'] as $account) {
    $accountPayload = [
        'access_token' => $account['access_token'],
        'fbpage_id' => $account['id'],
        'fbpage_name' => $account['name'],
    ];

    if (isset($account['instagram_business_account'])) {
        $accountPayload['insta_id'] = $account['instagram_business_account']['id'] ?? null;
        $accountPayload['insta_username'] = $account['instagram_business_account']['username'] ?? null;
        $accountPayload['insta_follower_count'] = $account['instagram_business_account']['followers_count'] ?? null;
    }

    $accountsPayload[] = $accountPayload;
}

// Get user's profile picture if available
if (isset($userData['picture']['data']['url'])) {
    $fbPicture = $userData['picture']['data']['url'];
}

// Step 5: Send Data to Laravel API for Storage (POST request)
$apiUrl = "https://dmplus.manymessage.com/api/facebook-accounts/{$stateData['uid']}";

$payload = json_encode([
    'at' => $accessToken,
    'accounts' => $accountsPayload
]);

$ch = curl_init($apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($payload)
]);
$response = curl_exec($ch);
curl_close($ch);

$postResponse = json_decode($response, true);
if (!$postResponse || $postResponse['success'] !== true) {
    echo $payload;
    echo json_encode(['success' => false, 'message' => 'Failed to save data to API', 'data' => $postResponse]);
    exit;
}

// Step 6: Redirect to Vue Page with Query Parame
$redirectUrl = "https://dmplus.manymessage.com/pages?redirect=1";
header("Location: $redirectUrl");
exit;
