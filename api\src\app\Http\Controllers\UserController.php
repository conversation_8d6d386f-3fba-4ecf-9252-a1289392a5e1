<?php

namespace App\Http\Controllers;
use App\Http\Requests\authRequest;
use App\Http\Requests\checkCodeRequest;
use App\Http\Requests\updateUserRequest;
use App\Services\CrmApiService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\SmsVerification;
use Laravel\Sanctum\PersonalAccessToken;
use Illuminate\Support\Facades\Http;
class UserController extends Controller
{
	private CrmApiService $crmApiService;

	public function __construct(CrmApiService $crmApiService)
	{
		$this->crmApiService = $crmApiService;
	}

	public function update(updateUserRequest $request)
    {
        $user = $request->user();
        $user->name = $request->name;
        $user->save();
        return response()->json($user);
    }
    public function sendSms($phone,$code){
        $payload = [
            'from' => '3000481407',
            'to' => [$phone],
            'text' => 'سلام کد تایید شما: ' . $code,
        ];
        $response = Http::withHeaders([
            '-X-API-KEY' => '38580:e9157825c9ed489fa7da1829e7b90605'
        ])->post('https://api.sapak.me/v1/messages', $payload);
        return $response;
    }
    public function reSendOtp(Request $request){
        if(auth()->check()){
            return [
                'failed'=>true,
                'message'=>"you are logged in!"
            ];
        }
        $user = User::wherePhone($request->mobile)->firstOrFail();
        $smsVerifications = $user->smsVerifications()->first();
        if(!$smsVerifications){
            return [
                'failed'=>true,
                'message'=>'مجاز نیست!',
            ];
        }
        if($smsVerifications->created_at->diffInSeconds(Carbon::now())>60){
            $user->smsVerifications()->delete();
            $randcode = rand(100000, 999999);
            $sms = SmsVerification::create([
                "code" => $randcode,
                "user_id" => $user->id
            ]);
            $sms->save();
            $response = $this->sendSms($user->phone,$randcode);
            \Log::info($response);
            return [
                'failed'=>false,
                'message'=>'کد ارسال شد',
            ];
        }else{
            return [
                'failed'=>true,
                'message'=>"در هر 1 دقیقه فقط یک درخواست میتونید بفرستید"
            ];
        }
    }
    public function register($phone){
        $user = new User;
        $user->phone = $phone;
        $user->save();
        $randcode = rand(100000, 999999);
        $sms = SmsVerification::create([
            "code" => $randcode,
            "user_id" => $user->id
        ]);
        $sms->save();

        $response = $this->sendSms($phone,$randcode);
        return [
            'failed'=>false,
            'message'=>'code send successfully',
        ];
    }
    public function login($user){
        $randcode = rand(100000, 999999);
        $sms = SmsVerification::create([
            "code" => $randcode,
            "user_id" => $user->id
        ]);
        $sms->save();
        $response = $this->sendSms($user->phone,$randcode);
        return [
            'failed'=>false,
            'message'=>'code send successfully',
        ];
    }
    public function checkCode(checkCodeRequest $request)
    {
        if(auth()->check()){
            return [
                'failed'=>true,
                'message'=>"you are logged in!"
            ];
        }
        $user = User::wherePhone($request->phone)->firstOrFail();
        $smsVerifications = $user->smsVerifications()->whereCode($request->code)->count();
        if(!$smsVerifications){
            return response()->json([
                'verified'=>false,
                'message'=>'wrong auth code'
            ]);

        }
        $user->smsVerifications()->delete();

	    // Send recently registered user data to the CRM
	    $this->crmApiService->sendUserData($user);

        return response()->json([
            'verified'=>true,
            'message'=>'success',
            'token'=> $user->createToken('shop')->plainTextToken,
            'user'=> $user
        ]);
    }
    public function auth(authRequest $request)
    {
        if(auth()->check()){
            return [
                'failed'=>true,
                'message'=>"you are logged in!"
            ];
        }
        $phone = $request->phone;
        $user = User::wherePhone($phone)->first();
        if($user){
            return $this->login($user);
        }
        return $this->register($phone);

    }
    public function logout(Request $request){
        $request->user()->currentAccessToken()->delete();
        return [
            'verified'=>true,
            'message'=>'Logged out'
        ];
    }
}
