import{ap as Z,as as ee,b$ as te,b6 as ae,a8 as y,r as v,aV as ne,cx as le,H as oe,w as m,aS as ue,at as ie,bA as re,b as l,s as I,F as R,K as _,bP as se,cH as ce,bi as de,aN as S,c1 as fe,aW as ve,cq as me}from"./index-169996dc.js";import{m as xe,f as ge,V as he,a as Ve}from"./VField-150a934a.js";import{m as we,u as ye,V as G}from"./VInput-c4d3942a.js";const Fe=Z({autoGrow:Boolean,autofocus:Boolean,counter:[Boolean,Number,String],counterValue:Function,prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,noResize:Boolean,rows:{type:[Number,String],default:5,validator:e=>!isNaN(parseFloat(e))},maxRows:{type:[Number,String],validator:e=>!isNaN(parseFloat(e))},suffix:String,modelModifiers:Object,...we(),...xe()},"VTextarea"),ke=ee()({name:"VTextarea",directives:{Intersect:te},inheritAttrs:!1,props:Fe(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,D){let{attrs:F,emit:H,slots:i}=D;const o=ae(e,"modelValue"),{isFocused:f,focus:E,blur:U}=ye(e),$=y(()=>typeof e.counterValue=="function"?e.counterValue(o.value):(o.value||"").toString().length),O=y(()=>{if(F.maxlength)return F.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter});function j(t,n){var a,u;!e.autofocus||!t||(u=(a=n[0].target)==null?void 0:a.focus)==null||u.call(a)}const M=v(),x=v(),N=ne(""),g=v(),q=y(()=>e.persistentPlaceholder||f.value||e.active);function P(){var t;g.value!==document.activeElement&&((t=g.value)==null||t.focus()),f.value||E()}function K(t){P(),H("click:control",t)}function W(t){H("mousedown:control",t)}function p(t){t.stopPropagation(),P(),S(()=>{o.value="",fe(e["onClick:clear"],t)})}function J(t){var a;const n=t.target;if(o.value=n.value,(a=e.modelModifiers)!=null&&a.trim){const u=[n.selectionStart,n.selectionEnd];S(()=>{n.selectionStart=u[0],n.selectionEnd=u[1]})}}const c=v(),h=v(+e.rows),C=y(()=>["plain","underlined"].includes(e.variant));le(()=>{e.autoGrow||(h.value=+e.rows)});function d(){e.autoGrow&&S(()=>{if(!c.value||!x.value)return;const t=getComputedStyle(c.value),n=getComputedStyle(x.value.$el),a=parseFloat(t.getPropertyValue("--v-field-padding-top"))+parseFloat(t.getPropertyValue("--v-input-padding-top"))+parseFloat(t.getPropertyValue("--v-field-padding-bottom")),u=c.value.scrollHeight,V=parseFloat(t.lineHeight),b=Math.max(parseFloat(e.rows)*V+a,parseFloat(n.getPropertyValue("--v-input-control-height"))),k=parseFloat(e.maxRows)*V+a||1/0,s=me(u??0,b,k);h.value=Math.floor((s-a)/V),N.value=ve(s)})}oe(d),m(o,d),m(()=>e.rows,d),m(()=>e.maxRows,d),m(()=>e.density,d);let r;return m(c,t=>{t?(r=new ResizeObserver(d),r.observe(c.value)):r==null||r.disconnect()}),ue(()=>{r==null||r.disconnect()}),ie(()=>{const t=!!(i.counter||e.counter||e.counterValue),n=!!(t||i.details),[a,u]=re(F),{modelValue:V,...b}=G.filterProps(e),k=ge(e);return l(G,I({ref:M,modelValue:o.value,"onUpdate:modelValue":s=>o.value=s,class:["v-textarea v-text-field",{"v-textarea--prefixed":e.prefix,"v-textarea--suffixed":e.suffix,"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-textarea--auto-grow":e.autoGrow,"v-textarea--no-resize":e.noResize||e.autoGrow,"v-input--plain-underlined":C.value},e.class],style:e.style},a,b,{centerAffix:h.value===1&&!C.value,focused:f.value}),{...i,default:s=>{let{id:w,isDisabled:A,isDirty:B,isReadonly:L,isValid:Q}=s;return l(he,I({ref:x,style:{"--v-textarea-control-height":N.value},onClick:K,onMousedown:W,"onClick:clear":p,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"]},k,{id:w.value,active:q.value||B.value,centerAffix:h.value===1&&!C.value,dirty:B.value||e.dirty,disabled:A.value,focused:f.value,error:Q.value===!1}),{...i,default:X=>{let{props:{class:T,...z}}=X;return l(R,null,[e.prefix&&l("span",{class:"v-text-field__prefix"},[e.prefix]),_(l("textarea",I({ref:g,class:T,value:o.value,onInput:J,autofocus:e.autofocus,readonly:L.value,disabled:A.value,placeholder:e.placeholder,rows:e.rows,name:e.name,onFocus:P,onBlur:U},z,u),null),[[se("intersect"),{handler:j},null,{once:!0}]]),e.autoGrow&&_(l("textarea",{class:[T,"v-textarea__sizer"],id:`${z.id}-sizer`,"onUpdate:modelValue":Y=>o.value=Y,ref:c,readonly:!0,"aria-hidden":"true"},null),[[ce,o.value]]),e.suffix&&l("span",{class:"v-text-field__suffix"},[e.suffix])])}})},details:n?s=>{var w;return l(R,null,[(w=i.details)==null?void 0:w.call(i,s),t&&l(R,null,[l("span",null,null),l(Ve,{active:e.persistentCounter||f.value,value:$.value,max:O.value},i.counter)])])}:void 0})}),de({},M,x,g)}});export{ke as V};
