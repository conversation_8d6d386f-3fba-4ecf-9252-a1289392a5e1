import{V as U,_ as Y}from"./VSkeletonLoader-4c44fbcf.js";import{a as G}from"./profile_ph-c1153e1f.js";import{m as O}from"./jalali-moment-c79ac113.js";import{_ as A,o as u,c as V,d as a,b as e,n as i,ai as K,x as h,e as s,v as j,aj as E,R as z,U as q,r as _,a8 as J,a2 as Q,w as W,H as X,a6 as I,ak as Z,a9 as B,F as w,al as S,aM as D,af as ee,a7 as te,aa as P,aP as ae,a4 as se,i as M,f as $,z as oe}from"./index-169996dc.js";import{_ as le}from"./AppTextField-ca1883d7.js";import{E as b}from"./endpoints-454f23f6.js";import{V as ne,a as R}from"./VRow-6c1d54f3.js";import{V as re}from"./VPagination-8a53e08f.js";import"./VInput-c4d3942a.js";import"./index-00c7d20d.js";import"./VTextField-a8984053.js";import"./VField-150a934a.js";const ie=o=>(z("data-v-f575f5e7"),o=o(),q(),o),ce={class:"card-customer"},de={class:"d-flex justify-space-between align-center"},ue={class:"d-flex ga-4 align-center"},me={class:"d-flex flex-column"},fe={class:"font-weight-bold text-lg"},_e={class:"font-weight-medium text-sm"},pe={class:"text-xs"},ve={class:"d-flex align-end flex-column align-self-start mt-1"},ge={class:"text-sm"},he={class:"text-sm"},Ve=ie(()=>a("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"#ffffff",width:"16",height:"16"},[a("g",{id:"SVGRepo_bgCarrier","stroke-width":"0"}),a("g",{id:"SVGRepo_tracerCarrier","stroke-linecap":"round","stroke-linejoin":"round"}),a("g",{id:"SVGRepo_iconCarrier"},[a("path",{d:"M12 19H12.01M8.21704 7.69689C8.75753 6.12753 10.2471 5 12 5C14.2091 5 16 6.79086 16 9C16 10.6565 14.9931 12.0778 13.558 12.6852C12.8172 12.9988 12.4468 13.1556 12.3172 13.2767C12.1629 13.4209 12.1336 13.4651 12.061 13.6634C12 13.8299 12 14.0866 12 14.6L12 16",stroke:"#ffffff","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1)),xe={__name:"CustomerItem",props:{customer:Object},emits:["delete"],setup(o,{emit:C}){const v=(m,n)=>O(m).locale("fa").format(n);return(m,n)=>{var l,p,f,g,x;return u(),V("div",ce,[a("div",de,[a("div",ue,[e(K,{src:o.customer.profile??i(G),alt:"Avatar",width:"65",height:"65",cover:"",class:"avatar-customer pa-0 rounded-circle"},null,8,["src"]),a("div",me,[a("div",fe,h(((l=o.customer)==null?void 0:l.name)??"-"),1),a("div",_e,h(((p=o.customer)==null?void 0:p.username)??"-"),1),a("div",pe,"شناسه: "+h(((f=o.customer)==null?void 0:f.id)??"-"),1)])]),a("div",ve,[a("div",ge,h(v((g=o.customer)==null?void 0:g.created_at,"YYYY/M/D")),1),a("div",he,h(v((x=o.customer)==null?void 0:x.created_at,"HH:mm:ss")),1)])]),e(E,{variant:"flat",block:"",onClick:n[0]||(n[0]=y=>{var c;return m.$router.push(`/customers/${(c=o.customer)==null?void 0:c.id}`)}),class:"mt-5"},{prepend:s(()=>[Ve]),default:s(()=>[j(" جزئیات ")]),_:1})])}}},we=A(xe,[["__scopeId","data-v-f575f5e7"]]);const Ce={class:"d-flex align-center ga-2"},Ee={__name:"index",setup(o){const C=_(null),v=_(null),m=_(!1),n=_(!1),l=_(1),p=_([]),f=_(null),g=J(()=>Math.ceil(C.value/v.value)),x=r=>{l.value=r},y=Q("pageId").value,c=async()=>{try{m.value=!0;let r=b.customers;y&&(r=b.customers+"/page/"+(y??""));const{data:t}=await S.get(r,{params:{page:l.value,search:f.value}});p.value=t.data,C.value=t.total,l.value>t.last_page&&(l.value=1),v.value=t.per_page}catch{D({text:"خظا در دریافت اطلاعات",icon:"error"})}finally{m.value=!1}},H=async r=>{try{n.value=!0,await S.delete(`${b.customers}/${r}`),await c()}catch(t){throw new Error(t)}finally{n.value=!1}},T=r=>{D({text:"آیا مایل به حذف سفارش هستید ؟",icon:"error",confirmButtonText:"بله",cancelButtonText:"خیر",showCloseButton:!0,showCancelButton:!0}).then(t=>{t.isConfirmed&&H(r)})};return W(l,async()=>{await c()}),X(async()=>{await c()}),(r,t)=>{const F=le,L=we,N=Y;return u(),V(w,null,[e(Z,{modelValue:i(n),"onUpdate:modelValue":t[0]||(t[0]=d=>I(n)?n.value=d:null),persistent:"",width:"300"},{default:s(()=>[e(B,{width:"300"},{default:s(()=>[e(ee,{class:"pt-3"},{default:s(()=>[e(te,{indeterminate:"",height:8,class:"mb-0 mt-4"})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(B,null,{default:s(()=>[e(P,null,{default:s(()=>[a("div",Ce,[e(F,{modelValue:i(f),"onUpdate:modelValue":t[1]||(t[1]=d=>I(f)?f.value=d:null),placeholder:"جستجو",class:"input-search",onKeyup:ae(c,["enter"])},null,8,["modelValue"]),e(E,{variant:"flat",onClick:c},{append:s(()=>[e(se,{icon:"tabler-search"})]),default:s(()=>[j(" جستجو ")]),_:1})]),e(ne,{class:"my-4"},{default:s(()=>{var d;return[i(m)?(u(),V(w,{key:0},M(12,k=>e(R,{cols:"12",md:"4"},{default:s(()=>[e(B,null,{default:s(()=>[e(P,null,{default:s(()=>[e(U,{type:"list-item-avatar-three-line"})]),_:1})]),_:1})]),_:1})),64)):(u(),V(w,{key:1},[(d=i(p))!=null&&d.length?(u(!0),V(w,{key:0},M(i(p),k=>(u(),$(R,{key:k.id,cols:"12",md:"4"},{default:s(()=>[e(L,{customer:k,onDelete:T},null,8,["customer"])]),_:2},1024))),128)):(u(),$(N,{key:1}))],64))]}),_:1}),i(g)?(u(),$(re,{key:0,modelValue:i(l),"onUpdate:modelValue":[t[2]||(t[2]=d=>I(l)?l.value=d:null),x],length:i(g)},null,8,["modelValue","length"])):oe("",!0)]),_:1})]),_:1})],64)}}};export{Ee as default};
