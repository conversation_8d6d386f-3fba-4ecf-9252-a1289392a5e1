import{V as z,_ as A}from"./VSkeletonLoader-4c44fbcf.js";import{_ as K}from"./OrderItem-e54d913a.js";import{_ as R}from"./AppTextField-ca1883d7.js";import{r as s,a8 as H,a2 as q,w as T,H as G,o as i,c as f,b as e,e as l,a9 as h,af as J,a7 as Q,n as r,a6 as w,ak as W,aa as $,d as C,aP as X,aj as Y,a4 as Z,v as ee,F as _,i as k,f as g,x as ae,z as te,al as E,aM as I}from"./index-169996dc.js";import{E as x}from"./endpoints-454f23f6.js";import{V as le}from"./VChip-ccd89083.js";import{V as ne,a as S}from"./VRow-6c1d54f3.js";import{V as oe}from"./VPagination-8a53e08f.js";const se={class:"d-flex align-center ga-2"},re={class:"d-flex flex-wrap ga-2 align-center mt-5"},ie={class:"text-xs font-weight-medium"},Ve={__name:"OrderScreenComponent",setup(ue){const b=s([{id:1,title:"همه"},{id:"waiting",title:"درانتظار پرداخت"},{id:"approved",title:"پرداخت شده"},{id:"rejected",title:"رد شده"},{id:"delivered",title:"تحویل شده"}]),c=s(1),B=s(null),D=s(null),V=s(!1),m=s(!1),u=s(1),y=s([]),v=s();s();const O=H(()=>Math.ceil(B.value/D.value)),F=n=>{u.value=n},N=n=>{c.value=n},P=q("pageId").value,d=async()=>{try{V.value=!0;const n={page:u.value,search:v.value,status:c.value};c.value===1&&delete n.status;let a=x.orders;P&&(a=x.orders+"/page/"+P);const{data:o}=await E.get(a,{params:n});y.value=o.data,B.value=o==null?void 0:o.total,u.value>o.last_page&&(u.value=1),D.value=o==null?void 0:o.per_page}catch{I({title:"خطا در انجام عملیات",icon:"error",confirmButtonText:"باشه"})}finally{V.value=!1}},L=async n=>{try{m.value=!0,await E.delete(`${x.orders}/${n}`),await d()}catch(a){throw new Error(a)}finally{m.value=!1}},M=n=>{I({text:"آیا مایل به حذف سفارش هستید ؟",icon:"error",confirmButtonText:"بله",cancelButtonText:"خیر",showCloseButton:!0,showCancelButton:!0}).then(a=>{a.isConfirmed&&L(n)})};return T(u,async()=>{await d()}),T(c,async()=>{await d()}),G(async()=>{await d()}),(n,a)=>{const o=R,U=K,j=A;return i(),f(_,null,[e(W,{modelValue:r(m),"onUpdate:modelValue":a[0]||(a[0]=t=>w(m)?m.value=t:null),persistent:"",width:"300"},{default:l(()=>[e(h,{width:"300"},{default:l(()=>[e(J,{class:"pt-3"},{default:l(()=>[e(Q,{indeterminate:"",height:8,class:"mb-0 mt-4"})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(h,null,{default:l(()=>[e($,null,{default:l(()=>[C("div",se,[e(o,{modelValue:r(v),"onUpdate:modelValue":a[1]||(a[1]=t=>w(v)?v.value=t:null),placeholder:"جستجو",class:"input-search",onKeyup:X(d,["enter"])},null,8,["modelValue"]),e(Y,{variant:"flat",onClick:d},{append:l(()=>[e(Z,{icon:"tabler-search"})]),default:l(()=>[ee(" جستجو ")]),_:1})]),C("div",re,[(i(!0),f(_,null,k(r(b),t=>(i(),g(le,{key:t,color:"primary",variant:r(c)===t.id?"flat":"tonal",class:"cursor-pointer w-fit",size:"large",onClick:p=>N(t.id)},{default:l(()=>[C("span",ie,ae(t.title),1)]),_:2},1032,["variant","onClick"]))),128))]),e(ne,{class:"my-4"},{default:l(()=>{var t;return[r(V)?(i(),f(_,{key:0},k(12,p=>e(S,{key:p,cols:"12",md:"4"},{default:l(()=>[e(h,null,{default:l(()=>[e($,null,{default:l(()=>[e(z,{type:"list-item-avatar-three-line"})]),_:1})]),_:1})]),_:2},1024)),64)):(i(),f(_,{key:1},[(t=r(y))!=null&&t.length?(i(!0),f(_,{key:0},k(r(y),p=>(i(),g(S,{key:p.id,cols:"12",md:"4"},{default:l(()=>[e(U,{order:p,onDelete:M},null,8,["order"])]),_:2},1024))),128)):(i(),g(j,{key:1}))],64))]}),_:1}),r(O)?(i(),g(oe,{key:0,modelValue:r(u),"onUpdate:modelValue":[a[2]||(a[2]=t=>w(u)?u.value=t:null),F],length:r(O)},null,8,["modelValue","length"])):te("",!0)]),_:1})]),_:1})],64)}}};export{Ve as _};
