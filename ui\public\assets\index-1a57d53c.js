import{_ as U,V as K}from"./VSkeletonLoader-4c44fbcf.js";import{m as A}from"./jalali-moment-c79ac113.js";import{_ as O,o as m,c as V,d as o,x,b as e,e as t,a4 as j,v as b,aj as w,r as _,a8 as q,k as G,a2 as J,w as Q,H as W,n,a6 as B,ak as X,a9 as M,F as $,al as E,aM as P,af as Z,a7 as ee,aa as F,aP as te,i as L,f as T,z as ae}from"./index-169996dc.js";import{_ as le}from"./AppTextField-ca1883d7.js";import{E as N}from"./endpoints-454f23f6.js";import{I as se}from"./iconify-64e5a48d.js";import{V as ne,a as S}from"./VRow-6c1d54f3.js";import{V as oe}from"./VPagination-8a53e08f.js";import"./VInput-c4d3942a.js";import"./index-00c7d20d.js";import"./VTextField-a8984053.js";import"./VField-150a934a.js";const ie={class:"card-delivery"},re={class:"d-flex justify-space-between align-center"},de={class:"d-flex flex-column"},ce={class:"font-weight-bold text-lg"},ue={class:"font-weight-medium text-sm"},me={class:"text-xs"},ve={class:"d-flex align-end flex-column align-self-start mt-1"},_e={class:"text-sm"},fe={class:"text-sm"},pe={class:"d-flex justify-center ga-2"},ye={__name:"DeliveryItem",props:{delivery:Object},emits:["delete"],setup(u,{emit:k}){const C=k,f=(i,l)=>A(i).locale("fa").format(l);return(i,l)=>{var p,v,y,g,h;return m(),V("div",ie,[o("div",re,[o("div",de,[o("div",ce,x((p=u.delivery)==null?void 0:p.name),1),o("div",ue,x(Number.parseInt((v=u.delivery)==null?void 0:v.price).toLocaleString())+" ریال",1),o("div",me,"شناسه: "+x((y=u.delivery)==null?void 0:y.id),1)]),o("div",ve,[o("div",_e,x(f((g=u.delivery)==null?void 0:g.created_at,"YYYY/M/D")),1),o("div",fe,x(f((h=u.delivery)==null?void 0:h.created_at,"HH:mm:ss")),1)])]),o("div",pe,[e(w,{variant:"flat",onClick:l[0]||(l[0]=I=>{var s;return i.$router.push(`/delivery/${(s=u.delivery)==null?void 0:s.id}`)}),class:"mt-5 w-50"},{prepend:t(()=>[e(j,{icon:"tabler-pencil"})]),default:t(()=>[b(" ویرایش ")]),_:1}),e(w,{variant:"tonal",color:"red",onClick:l[1]||(l[1]=I=>{var s;return C("delete",(s=u.delivery)==null?void 0:s.id)}),class:"mt-5 w-50"},{prepend:t(()=>[e(j,{icon:"tabler-trash"})]),default:t(()=>[b(" حذف ")]),_:1})])])}}},ge=O(ye,[["__scopeId","data-v-7e740274"]]);const he={class:"d-flex align-center ga-2"},Te={__name:"index",setup(u){const k=_(null),C=_(null),f=_(!1),i=_(!1),l=_(1),p=_([]),v=_(),y=q(()=>Math.ceil(k.value/C.value)),g=G(),h=J("pageId").value,I=r=>{l.value=r},s=async()=>{try{f.value=!0;const r={page:l.value,search:v.value};let a=N.deliveryMethods;h&&(a=N.deliveryMethods+"/page/"+(h??""));const{data:d}=await E.get(a,{params:r});p.value=d.data,k.value=d==null?void 0:d.total,C.value=d==null?void 0:d.per_page}catch{P({title:"خطا در انجام عملیات",icon:"error",confirmButtonText:"باشه"})}finally{f.value=!1}},Y=async r=>{try{i.value=!0,await E.delete(`${N.deliveryMethods}/${r}`),await s()}catch{P({text:"خطا در دریافت اطلاعات",icon:"error"})}finally{i.value=!1}},z=r=>{P({text:"آیا مایل به حذف آیتم هستید ؟",icon:"error",confirmButtonText:"بله",cancelButtonText:"خیر",showCloseButton:!0,showCancelButton:!0}).then(a=>{a.isConfirmed&&Y(r)})};return Q(l,async()=>{await s()}),W(async()=>{await s()}),(r,a)=>{const d=le,H=ge,R=U;return m(),V($,null,[e(X,{modelValue:n(i),"onUpdate:modelValue":a[0]||(a[0]=c=>B(i)?i.value=c:null),persistent:"",width:"300"},{default:t(()=>[e(M,{width:"300"},{default:t(()=>[e(Z,{class:"pt-3"},{default:t(()=>[e(ee,{indeterminate:"",height:8,class:"mb-0 mt-4"})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(w,{icon:"tabler-plus",size:"x-large",class:"add-delivery",style:{"font-size":"2rem"},onClick:a[1]||(a[1]=c=>n(g).push("/delivery/new"))}),e(M,null,{default:t(()=>[e(F,null,{default:t(()=>[o("div",he,[e(d,{modelValue:n(v),"onUpdate:modelValue":a[2]||(a[2]=c=>B(v)?v.value=c:null),placeholder:"جستجو",class:"input-search",onKeyup:te(s,["enter"])},null,8,["modelValue"]),e(w,{variant:"flat",onClick:s},{append:t(()=>[e(j,{icon:"tabler-search"})]),default:t(()=>[b(" جستجو ")]),_:1})]),e(ne,{class:"my-4"},{default:t(()=>{var c;return[n(f)?(m(),V($,{key:0},L(12,D=>e(S,{cols:"12",md:"4"},{default:t(()=>[e(M,null,{default:t(()=>[e(F,null,{default:t(()=>[e(K,{type:"list-item-avatar-three-line"})]),_:1})]),_:1})]),_:1})),64)):(m(),V($,{key:1},[(c=n(p))!=null&&c.length?(m(!0),V($,{key:0},L(n(p),D=>(m(),T(S,{key:D.id,cols:"12",md:"4"},{default:t(()=>[e(H,{delivery:D,onDelete:z},null,8,["delivery"])]),_:2},1024))),128)):(m(),T(R,{key:1},{default:t(()=>[e(w,{variant:"flat",onClick:a[3]||(a[3]=D=>n(g).push("/delivery/new")),class:"mt-5"},{default:t(()=>[e(n(se),{icon:"tabler-plus"}),b(" افزودن روش ارسال ")]),_:1})]),_:1}))],64))]}),_:1}),n(y)?(m(),T(oe,{key:0,modelValue:n(l),"onUpdate:modelValue":[a[4]||(a[4]=c=>B(l)?l.value=c:null),I],length:n(y)},null,8,["modelValue","length"])):ae("",!0)]),_:1})]),_:1})],64)}}};export{Te as default};
