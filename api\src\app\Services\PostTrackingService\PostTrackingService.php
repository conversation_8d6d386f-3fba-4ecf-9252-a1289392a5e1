<?php

namespace App\Services\PostTrackingService;

use Illuminate\Support\Facades\Http;
use App\Services\PythonService\PythonServiceHealthChecker;
use Exception;

/**
 * PostTrackingService
 *
 * Handles post.ir tracking requests by sending the tracking number to the Python service
 * and retrieving the tracking information.
 */
class PostTrackingService
{
    protected PythonServiceHealthChecker $healthChecker;
    protected string $pythonServiceUrl;

    public function __construct(PythonServiceHealthChecker $healthChecker)
    {
        $this->healthChecker = $healthChecker;
        $this->pythonServiceUrl = env('PYTHON_SERVICE_URL', 'http://python_service:8000');
    }

    /**
     * Sends a post.ir tracking number to the Python service and retrieves tracking information.
     *
     * This method checks if the Python service is healthy before sending the request.
     * If the service is unavailable or if the request fails, it throws an exception.
     *
     * @param string $trackingNumber The tracking number to query.
     * @return array The tracking data as an associative array.
     * @throws Exception If the Python service is unavailable or the request fails.
     */
    public function trackPackage(string $trackingNumber): array
    {
        if (!$this->healthChecker->isHealthy()) {
            throw new Exception("Python service is unavailable.");
        }

        try {
            $response = Http::post("{$this->pythonServiceUrl}/track", [
                'tracking_number' => $trackingNumber
            ]);

            if ($response->failed()) {
                throw new Exception("Failed to retrieve tracking data from Python service.");
            }

            return $response->json();
        } catch (Exception $e) {
            throw new Exception("Error raised while communicating with the PythonService: " . $e->getMessage());
        }
    }
}
