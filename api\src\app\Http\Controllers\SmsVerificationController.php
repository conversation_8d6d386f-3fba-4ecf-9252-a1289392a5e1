<?php

namespace App\Http\Controllers;

use App\Models\SmsVerification;
use Illuminate\Http\Request;

class SmsVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(SmsVerification $smsVerification)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SmsVerification $smsVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SmsVerification $smsVerification)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SmsVerification $smsVerification)
    {
        //
    }
}
