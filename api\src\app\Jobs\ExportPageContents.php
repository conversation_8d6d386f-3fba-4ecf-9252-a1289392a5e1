<?php

namespace App\Jobs;

use App\Models\Page;
use App\Models\PageContent;
use App\Models\Products;
use App\Models\ProductsGallery;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class ExportPageContents implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $baseUrl = 'https://graph.facebook.com/v19.0';
    /**
     * Create a new job instance.
     */
    public function __construct(
        public Page $page
    )
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $page = $this->page;
        $access_token = $page->access_token;
        $userId = $page->page_user_id;
        // Build the base URL with query parameters
//        $beforeId = 'QVFIUmF5NlRfWTNwXzA1X3EtejFMdkRqMmNNTnpOOVRWQ1dkREdyNk9WS3E1M2NNU3ZAYcnBCajhsRmFlNC1wY2puRGpweDdIeDF2cEx0UEQyWWI2S3hnNUNn';
        $pagingAfter = null;
        while(true){
            if($pagingAfter){
                $url = "{$this->baseUrl}/{$userId}/media?fields=id,caption,like_count,comments_count,title,description,text,media_url,thumbnail_url,media_type,children{media_url,thumbnail_url}&access_token={$access_token}&limit=20&after={$pagingAfter}";
            }else{
                $url = "{$this->baseUrl}/{$userId}/media?fields=id,caption,like_count,comments_count,title,description,text,media_url,thumbnail_url,media_type,children{media_url,thumbnail_url}&access_token={$access_token}&limit=20";
            }

            $response = Http::get($url);
            $resJson = $response->json();
            if($resJson["paging"]){
                $pagingAfter = $resJson["paging"]["cursors"]["after"];
            }
            if(!count($resJson['data'])){
                break;
            }
            foreach ($resJson["data"] as $data){
                $product = new Products();
                $product->name = null;
                $product->description = $data['caption'];
                $product->post_id =  $data['id'];
                $product->triggers = null;
                $product->price = null;
                $product->user_id = $page->user_id;
                $product->save();
                foreach ($data["children"]["data"] as $child){
                    $gallery = new ProductsGallery();
                    $gallery->from = 'instagram';
                    $gallery->path = $child["media_url"];
                    $gallery->media_id = $child["id"];
                    $gallery->product_id = $product->id;
                    $gallery->save();
                }
            }
        }



    }
}
