<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Products;
use App\Models\productAttributes;
use App\Models\ProductsGallery;
use App\Models\Customers;
use App\Models\order;
use App\Models\OrderAttribute;
use App\Models\DeliveryMethod;
use App\Models\PaymentMethod;
class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $phones = ['09140294579','09364540286','09335251871','09392687812','09923703955'];
        foreach ($phones as $phone) {
            $user = User::wherePhone($phone)->first();
            $this->command->comment('Seeding Users...');
            if($user==null){
                $user = User::factory()->create([
                    'phone' => $phone,
                ]);
            }
            $this->command->comment('Seeding Products for User '.$user->phone.'...');
            $delivery = new DeliveryMethod();
            $delivery->name = "عادی";
            $delivery->description = "ارسال با پست عادی";
            $delivery->price = 10000;
            $delivery->user_id = $user->id;
            $delivery->save();
            $delivery = new DeliveryMethod();
            $delivery->name = "عادی 2";
            $delivery->description = "ارسال با پست عادی 2";
            $delivery->price = 20000;
            $delivery->user_id = $user->id;
            $delivery->save();
            $payment = new PaymentMethod();
            $payment->payment_method = "snapp";
            $payment->token = "MlSLeoxCt1I2vnVo";
            $payment->data = json_encode([
                "user"=>"directam-purchase",
                "pass"=>"J4MEihTNVwXpt3LW",
                "clientId"=>"directam"
            ]);
            $payment->user_id = $user->id;
            $payment->save();
            $products = Products::factory()
                ->count(3)
                ->create([
                    'user_id'=>$user->id,
                ]);
            foreach($products as $product){
                $product->deliveryMethods()->attach($delivery->id);
                $this->command->comment('Seeding Product Attributes for Product '.$product->name.' ...');
                $productAttributes = productAttributes::factory()
                    ->count(3)
                    ->create([
                        'user_id'=>$user->id,
                        'product_id'=>$product->id
                    ]);
                $this->command->comment('Seeding Product Gallery for Product '.$product->name.' ...');
                $productGallery = ProductsGallery::factory()
                    ->count(1)
                    ->create([
                        'product_id'=>$product->id,
                        'user_id'=>$user->id
                    ]);
                $this->command->comment('Seeding Customers for Product '.$product->name.' ...');
                $customers = Customers::factory()
                    ->count(3)
                    ->create([
                        'user_id'=>$user->id
                    ]);
                foreach($customers as $customer){
                    $this->command->comment('Seeding Orders for Customer '.$customer->name.' ...');
                    $orders = order::factory()
                        ->count(30)
                        ->create([
                            'user_id'=>$user->id,
                            'product_id'=>$product->id,
                            'delivery_method_id'=>$delivery->id,
                            'customer_id'=>$customer->id
                        ]);
                    foreach($orders as $order){
                        foreach($productAttributes as $productAttribute){
                            $this->command->comment('Seeding ProductAttributes for Order '.$order->id.' ...');
                            $orderAttributes = OrderAttribute::factory()
                                ->count(3)
                                ->create([
                                    'user_id'=>$user->id,
                                    'product_id'=>$product->id,
                                    'customer_id'=>$customer->id,
                                    'order_id'=>$order->id,
                                    'product_attributes_id'=>$productAttribute->id
                                ]);
                        }
                    }
                    
                }
            }
        }
    }
}
