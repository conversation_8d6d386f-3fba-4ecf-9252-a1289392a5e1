import{_ as r}from"./ErrorHeader-538afbf6.js";import{u as n,a as c,b as i}from"./misc-mask-light-c60b2a6b.js";import{o as d,c as m,b as e,e as u,v as _,aj as l,d as h,n as t,ai as a}from"./index-169996dc.js";const p="/assets/401-30d2f70f.png";const f={class:"misc-wrapper"},g={class:"misc-avatar w-100 text-center"},b={__name:"not-authorized",setup(v){const s=n(i,c);return(k,x)=>{const o=r;return d(),m("div",f,[e(o,{"status-code":"401",title:"You are not authorized! 🔐",description:`You do not have permission to view this page using the credentials that you have provided while login.
       Please contact your site administrator.`}),e(l,{class:"mt-2 mb-10",to:"/"},{default:u(()=>[_(" Back Home ")]),_:1}),h("div",g,[e(a,{src:t(p),alt:"Coming Soon","max-width":170,class:"mx-auto"},null,8,["src"])]),e(a,{src:t(s),class:"misc-footer-img d-none d-md-block"},null,8,["src"])])}}};export{b as default};
