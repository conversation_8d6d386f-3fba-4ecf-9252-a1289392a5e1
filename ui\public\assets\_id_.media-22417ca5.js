import{_ as r}from"./MessageBox-e9a5b0b1.js";import{E as l}from"./endpoints-454f23f6.js";import{a as c}from"./validations-4c0aab88.js";import"./iconify-64e5a48d.js";import{k as d,j as p,r as t,H as u,o as _,c as f,b as v,al as g}from"./index-169996dc.js";import"./VRow-6c1d54f3.js";import"./VAlert-fc722507.js";import"./profile_ph-c1153e1f.js";import"./DialogCloseBtn-b32209d9.js";const V={__name:"[id].media",setup(b){d();const i=p(),o=t([]),s=t(!1);t(!1),t(null);const n=async()=>{var e;try{s.value=!0;const{data:a}=await g.get(`${l.productsMedia}/product/${i.params.id}`);o.value=a.data}catch(a){await c((e=a==null?void 0:a.data)==null?void 0:e.errors,a==null?void 0:a.status)}finally{s.value=!1}};return u(()=>{n()}),(e,a)=>{const m=r;return _(),f("div",null,[v(m,{class:"mx-auto",templates:o.value,hasCommandTrigger:"",templateButtonsList:[{name:"متن",emitName:"addText",icon:"tabler-file-description"},{name:"عکس",emitName:"addImage",icon:"tabler-photo"},{name:"ویدئو",emitName:"addVideo",icon:"tabler-video"},{name:"ویس",emitName:"addVoice",icon:"tabler-microphone"}]},null,8,["templates"])])}}};export{V as default};
