<?php

namespace App\Models\Webservice;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class SpecialUser extends Model
{
    use HasFactory;

    protected $table = 'special_users';

    protected $fillable = [
        'user_id',
        'api_token',
        'access_token',
        'instagram_id',
        'webhook_endpoints',
        'valid_endpoints',
    ];

    protected $casts = [
        'webhook_endpoints' => 'array',
        'valid_endpoints'   => 'array',
    ];


    /**
     * Defines a One-To-One relationship with the User Model.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Generate a hashed API token and store it
     *
     * @param string $api_token
     * @return void
     */
    public function setApiTokenAttribute(string $api_token): void
    {
        $this->attributes['api_token'] = bcrypt($api_token);
    }

    /**
     * Checks if the given endpoint is valid for this special user
     *
     * @param string $endpoint
     * @return bool
     */
    public function isValidEndpoint(string $endpoint): bool
    {
        return in_array($endpoint, $this->valid_endpoints);
    }

    /**
     * Checks if a given Instagram ID belongs to a SpecialUser.
     *
     * @param string $instagramId The Instagram ID to check.
     * @return bool Returns `true` if a SpecialUser exists with the given Instagram ID, `false` otherwise.
     */
    public static function hasInstagramId(string $instagramId): bool
    {
        return self::where('instagram_id', $instagramId)->exists();
    }

    /**
     * Decrypts the stored access token.
     *
     * This accessor method decrypts the `access_token` stored in the database, which
     * has been encrypted before saving.
     *
     * @param string $encryptedToken The encrypted access token in JSON format.
     * @return string The decrypted access token, or the original encrypted data if decryption fails.
     */
    public function getAccessTokenAttribute(string $encryptedToken): string
    {
        try {
            $encrypted = json_decode($encryptedToken);
            $accessToken = decryptData(
                base64_decode($encrypted[0]),
                base64_decode($encrypted[1]),
                base64_decode($encrypted[2])
            );
        } catch (\Throwable $th) {
            $accessToken = $encryptedToken;
        }

        return $accessToken;
    }

    /**
     * Returns which owns the given Api Token.
     *
     * @param string $apiToken the Api-Token for searching the Special User with
     * @return SpecialUser|null Either the related Special User or null if did not find
     */
    public static function getUserByApiToken(string $apiToken): ?SpecialUser
    {
        return SpecialUser::whereNotNull('api_token')
            ->get()
            ->first(function ($user) use ($apiToken) {
                return Hash::check($apiToken, $user->api_token);
            });
    }
}
