import{_ as ne}from"./AppLoadingIndicator-1aa55cc8.js";import{_ as K,M as Y,N as b,T as E,r as k,O as se,P as le,Q as J,l as B,j,w as S,H as Q,a as F,o as r,f as d,e as v,d as A,A as D,b as h,n as e,q as u,K as V,x as R,L as w,h as g,s as N,c as P,F as X,i as Z,y as H,R as re,U as oe,k as ee,V as ce,m as U,W as O,X as ue,p as de,t as T,v as M,z as x,Y as te,Z as ve,$ as pe,a0 as me,a1 as fe,B as ae,C as ge,D as he,a2 as ye,a3 as be,a4 as Ne,I as G,S as ke}from"./index-169996dc.js";import Ce from"./Footer-6787fa0c.js";/* empty css                                                      */import Ve from"./NavbarThemeSwitcher-85b3d65e.js";import we from"./UserProfile-e69d82d2.js";import{V as Ie,_ as $e}from"./I18n-f35fb544.js";import{P as Se}from"./vue3-perfect-scrollbar.esm-e1f82a0e.js";import{V as Ae}from"./VSpacer-fcf88198.js";import"./VSkeletonLoader-4c44fbcf.js";import"./DialogCloseBtn-b32209d9.js";import"./validations-4c0aab88.js";import"./profile_ph-c1153e1f.js";import"./VRadioGroup-52375e28.js";import"./VSelectionControl-8ecbcf09.js";import"./VInput-c4d3942a.js";import"./index-00c7d20d.js";import"./VRow-6c1d54f3.js";import"./VTextField-a8984053.js";import"./VField-150a934a.js";import"./useAbility-e6e49333.js";import"./VMenu-2cfb0f14.js";import"./VList-349a1ccf.js";import"./ssrBoot-c101cd97.js";import"./VDivider-12bfa926.js";import"./VBadge-751ce1d1.js";const _e=Y({name:"TransitionExpand",setup(a,{slots:i}){const c=t=>{const l=getComputedStyle(t).width;t.style.width=l,t.style.position="absolute",t.style.visibility="hidden",t.style.height="auto";const y=getComputedStyle(t).height;t.style.width="",t.style.position="",t.style.visibility="",t.style.height="0px",getComputedStyle(t).height,requestAnimationFrame(()=>{t.style.height=y})},f=t=>{t.style.height="auto"},o=t=>{const l=getComputedStyle(t).height;t.style.height=l,getComputedStyle(t).height,requestAnimationFrame(()=>{t.style.height="0px"})};return()=>b(b(E),{name:"expand",onEnter:c,onAfterEnter:f,onLeave:o},()=>{var t;return(t=i.default)==null?void 0:t.call(i)})}}),Oe=K(_e,[["__scopeId","data-v-e5b9cc91"]]),Re=a=>(re("data-v-5397d761"),a=a(),oe(),a),xe={id:"",class:"nav-header"},Le=Re(()=>A("div",{class:"vertical-nav-items-shadow"},null,-1)),Pe={__name:"VerticalNav",props:{tag:{type:[String,Object,Function],required:!1,default:"aside"},navItems:{type:null,required:!0},isOverlayNavActive:{type:Boolean,required:!0},toggleIsOverlayNavActive:{type:Function,required:!0}},setup(a){const i=a,c=k(),f=se(c);le(J,f);const o=B(),t=n=>"heading"in n?Fe:"children"in n?Be:ie,l=j();S(()=>l.name,()=>{i.toggleIsOverlayNavActive(!1)});const y=k(!1),m=n=>y.value=n,I=n=>{y.value=n.target.scrollTop>0},$=o.isVerticalNavMini(f),C=()=>{document.querySelector(".nav-header");for(let n=0;n<50;n++){const s=document.createElement("div");s.classList.add("flower",`flower${Math.ceil(Math.random()*3)}`),s.style.left=`${Math.random()*100}%`,s.style.animationDuration=`${Math.random()*5+5}s`,s.style.animationDelay=`-${Math.random()*2}s`}};return Q(()=>{C()}),(n,s)=>{const p=F("RouterLink");return r(),d(g(i.tag),{ref_key:"refNav",ref:c,class:H(["layout-vertical-nav",[{"overlay-nav":e(o).isLessThanOverlayNavBreakpoint,hovered:e(f),visible:a.isOverlayNavActive,scrolled:y.value}]])},{default:v(()=>[A("div",xe,[D(n.$slots,"nav-header",{},()=>[h(p,{to:"/",class:"app-logo app-title-wrapper"},{default:v(()=>[h(e(Ie),{nodes:e(u).app.logo},null,8,["nodes"]),h(E,{name:"vertical-nav-app-title"},{default:v(()=>[V(A("h1",{class:"app-logo-title leading-normal"},R(e(u).app.title),513),[[w,!e($)]])]),_:1})]),_:1}),V((r(),d(g(e(u).app.iconRenderer||"div"),N({class:["header-action d-none nav-unpin",e(o).isVerticalNavCollapsed&&"d-lg-block"]},e(u).icons.verticalNavUnPinned,{onClick:s[0]||(s[0]=_=>e(o).isVerticalNavCollapsed=!e(o).isVerticalNavCollapsed)}),null,16,["class"])),[[w,e(o).isVerticalNavCollapsed]]),V((r(),d(g(e(u).app.iconRenderer||"div"),N({class:["header-action d-none nav-pin",!e(o).isVerticalNavCollapsed&&"d-lg-block"]},e(u).icons.verticalNavPinned,{onClick:s[1]||(s[1]=_=>e(o).isVerticalNavCollapsed=!e(o).isVerticalNavCollapsed)}),null,16,["class"])),[[w,!e(o).isVerticalNavCollapsed]]),(r(),d(g(e(u).app.iconRenderer||"div"),N({class:"header-action d-lg-none"},e(u).icons.close,{onClick:s[2]||(s[2]=_=>a.toggleIsOverlayNavActive(!1))}),null,16))],!0)]),D(n.$slots,"before-nav-items",{},()=>[Le],!0),D(n.$slots,"nav-items",{updateIsVerticalNavScrolled:m},()=>[(r(),d(e(Se),{key:e(o).isAppRTL,tag:"ul",class:"nav-items",options:{wheelPropagation:!1},onPsScrollY:I},{default:v(()=>[(r(!0),P(X,null,Z(a.navItems,(_,q)=>(r(),d(g(t(_)),{key:q,item:_},null,8,["item"]))),128))]),_:1}))],!0)]),_:3},8,["class"])}}},Te=K(Pe,[["__scopeId","data-v-5397d761"]]),Me={class:"nav-group-children"},Be=Object.assign({name:"VerticalNavGroup"},{__name:"VerticalNavGroup",props:{item:{type:null,required:!0}},setup(a){const i=a,c=j(),f=ee(),o=B(),t=o.isVerticalNavMini(),l=ce(J,k(!1)),y=k(!1),m=k(!1),I=n=>n.some(s=>{let p=O.value.includes(s.title);return"children"in s&&(p=I(s.children)||p),p}),$=n=>{n.forEach(s=>{"children"in s&&$(s.children),O.value=O.value.filter(p=>p!==s.title)})};S(()=>c.path,()=>{const n=U(i.item.children,f);m.value=n&&!o.isVerticalNavMini(l).value,y.value=n},{immediate:!0}),S(m,n=>{const s=O.value.indexOf(i.item.title);n&&s===-1?O.value.push(i.item.title):!n&&s!==-1&&(O.value.splice(s,1),$(i.item.children))},{immediate:!0}),S(O,n=>{if(n.at(-1)===i.item.title)return;const p=U(i.item.children,f);p||I(i.item.children)||(m.value=p,y.value=p)},{deep:!0}),S(o.isVerticalNavMini(l),n=>{m.value=n?!1:y.value});const C=ue();return(n,s)=>e(de)(a.item)?(r(),P("li",{key:0,class:H(["nav-group",[{active:e(y),open:e(m),disabled:a.item.disable}]])},[A("div",{class:"nav-group-label",onClick:s[0]||(s[0]=p=>m.value=!e(m))},[(r(),d(g(e(u).app.iconRenderer||"div"),N(a.item.icon||e(u).verticalNav.defaultNavItemIconProps,{class:"nav-item-icon"}),null,16)),(r(),d(g(e(C)?te:"div"),N({name:"transition-slide-x"},e(C)?void 0:{class:"d-flex align-center flex-grow-1"}),{default:v(()=>[V((r(),d(g(e(u).app.i18n.enable?"i18n-t":"span"),N(e(T)(a.item.title,"span"),{key:"title",class:"nav-item-title"}),{default:v(()=>[M(R(a.item.title),1)]),_:1},16)),[[w,!e(t)]]),a.item.badgeContent?V((r(),d(g(e(u).app.i18n.enable?"i18n-t":"span"),N({key:0},e(T)(a.item.badgeContent,"span"),{key:"badge",class:["nav-item-badge",a.item.badgeClass]}),{default:v(()=>[M(R(a.item.badgeContent),1)]),_:1},16,["class"])),[[w,!e(t)]]):x("",!0),V((r(),d(g(e(u).app.iconRenderer||"div"),N(e(u).icons.chevronRight,{key:"arrow",class:"nav-group-arrow"}),null,16)),[[w,!e(t)]])]),_:1},16))]),h(e(Oe),null,{default:v(()=>[V(A("ul",Me,[(r(!0),P(X,null,Z(a.item.children,p=>(r(),d(g("children"in p?"VerticalNavGroup":e(ie)),{key:p.title,item:p},null,8,["item"]))),128))],512),[[w,e(m)]])]),_:1})],2)):x("",!0)}}),qe=Y({props:{navItems:{type:Array,required:!0},verticalNavAttrs:{type:Object,default:()=>({})}},setup(a,{slots:i}){const{width:c}=ve(),f=B(),o=k(!1),t=k(!1),l=pe(o);return me(o,t),S(c,()=>{!f.isLessThanOverlayNavBreakpoint&&t.value&&(t.value=!1)}),()=>{var q,W,z;const y=fe(a,"verticalNavAttrs"),{wrapper:m,wrapperProps:I,...$}=y.value,C=b(Te,{isOverlayNavActive:o.value,toggleIsOverlayNavActive:l,navItems:a.navItems,...$},{"nav-header":()=>{var L;return(L=i["vertical-nav-header"])==null?void 0:L.call(i)},"before-nav-items":()=>{var L;return(L=i["before-vertical-nav-items"])==null?void 0:L.call(i)}}),n=b("header",{class:["layout-navbar",{"navbar-blur":f.isNavbarBlurEnabled}]},[b("div",{class:"navbar-content-container"},(q=i.navbar)==null?void 0:q.call(i,{toggleVerticalOverlayNavActive:l}))]),s=b("main",{class:"layout-page-content"},b("div",{class:"page-content-container"},(W=i.default)==null?void 0:W.call(i))),p=b("footer",{class:"layout-footer"},[b("div",{class:"footer-content-container"},(z=i.footer)==null?void 0:z.call(i))]),_=b("div",{class:["layout-overlay",{visible:t.value}],onClick:()=>{t.value=!t.value}});return b("div",{class:["layout-wrapper",...f._layoutClasses]},[m?b(m,I,{default:()=>C}):C,b("div",{class:"layout-content-wrapper"},[n,s,p]),_])}}}),ie={__name:"VerticalNavLink",props:{item:{type:null,required:!0}},setup(a){const c=B().isVerticalNavMini();return(f,o)=>e(ae)(a.item.action,a.item.subject)?(r(),P("li",{key:0,class:H(["nav-link",{disabled:a.item.disable}])},[(r(),d(g(a.item.to?"RouterLink":"a"),N(e(he)(a.item),{class:{"router-link-active router-link-exact-active":e(ge)(a.item,f.$router)}}),{default:v(()=>[(r(),d(g(e(u).app.iconRenderer||"div"),N(a.item.icon||e(u).verticalNav.defaultNavItemIconProps,{class:"nav-item-icon"}),null,16)),h(te,{name:"transition-slide-x"},{default:v(()=>[V((r(),d(g(e(u).app.i18n.enable?"i18n-t":"span"),N({key:"title",class:"nav-item-title"},e(T)(a.item.title,"span")),{default:v(()=>[M(R(a.item.title),1)]),_:1},16)),[[w,!e(c)]]),a.item.badgeContent?V((r(),d(g(e(u).app.i18n.enable?"i18n-t":"span"),N({key:"badge",class:["nav-item-badge",a.item.badgeClass]},e(T)(a.item.badgeContent,"span")),{default:v(()=>[M(R(a.item.badgeContent),1)]),_:1},16,["class"])),[[w,!e(c)]]):x("",!0)]),_:1})]),_:1},16,["class"]))],2)):x("",!0)}},De={key:0,class:"nav-section-title"},Ge={class:"title-wrapper"},Fe={__name:"VerticalNavSectionTitle",props:{item:{type:null,required:!0}},setup(a){const c=B().isVerticalNavMini();return(f,o)=>e(ae)(a.item.action,a.item.subject)?(r(),P("li",De,[A("div",Ge,[h(E,{name:"vertical-nav-section-title",mode:"out-in"},{default:v(()=>[(r(),d(g(e(c)?e(u).app.iconRenderer:e(u).app.i18n.enable?"i18n-t":"span"),N({key:e(c),class:e(c)?"placeholder-icon":"title-text"},{...e(u).icons.sectionTitlePlaceholder,...e(T)(a.item.heading,"span")}),{default:v(()=>[M(R(e(c)?null:a.item.heading),1)]),_:1},16,["class"]))]),_:1})])])):x("",!0)}},Ee=[{title:"داشبورد",icon:{icon:"tabler-dashboard"},to:"dashboard"},{title:"محصولات",icon:{icon:"tabler-basket"},to:"products"},{title:"سفارشات",icon:{icon:"tabler-truck-delivery"},to:"orders"},{title:"مشتریان",icon:{icon:"tabler-users"},to:"customers"},{title:"اتصال به اینستاگرام",icon:{icon:"tabler-brand-instagram"},to:"pages"},{title:"تنظیمات",icon:{icon:"tabler-settings"},children:[{title:"درگاه اقساطی",to:"snapp"},{title:"درگاه کارت به کارت",to:"paystarcard"},{title:"درگاه آنلاین",to:"settings"},{title:"روش های ارسال",to:"delivery"},{title:"پیام ها",to:"messages"}]},{title:"خروج",icon:{icon:"tabler-logout"},to:"logout"}],je=[...Ee],He={class:"d-flex h-100 align-center"},We={class:"font-weight-bold"},yt={__name:"DefaultLayoutWithVerticalNav",setup(a){const i=k(!1),c=k(null);ee();const f=ye("userData").value,o=k(0),t=j(),l=k(null);return S([i,c],()=>{i.value&&c.value&&c.value.fallbackHandle(),!i.value&&c.value&&c.value.resolveHandle()},{immediate:!0}),S(t,()=>{if(/^\/customers\/\d+$/.test(t.path))l.value="جزئیات مشتری";else if(t.path.includes("setting")&&t.path.includes("products"))l.value="ثبت سوال";else if(/^\/products\/\d+$/.test(t.path))l.value="ویرایش محصول";else if(/^\/orders\/\d+$/.test(t.path))l.value="تاریخچه سفارشات";else switch(t==null?void 0:t.path){case"/dashboard":l.value="داشبورد";break;case"/products":l.value="لیست محصولات";break;case"/orders":l.value="لیست سفارشات";break;case"/customers":l.value="لیست مشتریان";break;case"/pages":l.value="اتصال به اینستاگرام";break;case"/snapp":l.value="درگاه اقساطی";break;case"/paystarcard":l.value="درگاه کارت به کارت";break;case"/settings":l.value="درگاه آنلاین";break;case"/messages":l.value="پیام ها";break;case"/delivery":l.value="روش های ارسال";break}},{deep:!0,immediate:!0}),Q(()=>{o.value=be(f.expiration_date)}),(y,m)=>{const I=F("IconBtn"),$=ne,C=F("RouterView");return r(),d(e(qe),{"nav-items":e(je)},{navbar:v(({toggleVerticalOverlayNavActive:n})=>{var s;return[A("div",He,[h(I,{id:"vertical-nav-toggle-btn",class:"ms-n3 d-lg-none",onClick:p=>n(!0)},{default:v(()=>[h(Ne,{size:"26",icon:"tabler-menu-2"})]),_:2},1032,["onClick"]),A("span",We,R(e(l)),1),h(Ae),e(G).app.i18n.enable&&((s=e(G).app.i18n.langConfig)!=null&&s.length)?(r(),d($e,{key:0,languages:e(G).app.i18n.langConfig},null,8,["languages"])):x("",!0),h(Ve),h(we)])]}),footer:v(()=>[h(Ce)]),default:v(()=>[h($,{ref_key:"refLoadingIndicator",ref:c},null,512),h(C,null,{default:v(({Component:n})=>[(r(),d(ke,{timeout:0,onFallback:m[0]||(m[0]=s=>i.value=!0),onResolve:m[1]||(m[1]=s=>i.value=!1)},{default:v(()=>[(r(),d(g(n)))]),_:2},1024))]),_:1})]),_:1},8,["nav-items"])}}};export{yt as default};
