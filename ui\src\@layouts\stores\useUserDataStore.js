const useUserDataStore = defineStore('userData', {
  state: () => ({
    credit: useCookie('userData').value?useCookie('userData').value['credit']??0:0,
    freeComments: useCookie('userData').value?useCookie('userData').value['comments_count']??501:501,
  }),
  actions: {
    setCredit(credit) {
      this.credit = credit
    },
    setFreeComments(comments) {
      this.freeComments = comments
    },
  },
})

export { useUserDataStore }
