import{ap as Y,au as O,aq as X,aZ as ke,ar as j,cn as ce,as as N,b0 as Se,b4 as ge,co as re,aV as R,a8 as S,ck as ee,cp as Ce,w as xe,at as ve,b as n,a4 as _,bY as Ve,cq as Ie,bS as Pe,ax as de,bJ as fe,b1 as pe,bb as ze,a1 as M,s as me,bG as ae,aY as Ae,bl as we,a_ as Re,cr as _e,aw as Te,bI as Oe,cj as Ge,bK as Be,aB as Ee,b2 as Fe,bM as Me,bm as Le,b3 as De,aA as He,cs as $e,b6 as qe,ct as Ke,bL as Ye,K as le,bP as Xe,bQ as je,bc as L,L as Ne,F as te,ac as ne}from"./index-169996dc.js";import{c as se,b as We}from"./index-00c7d20d.js";function ie(e){const i=Math.abs(e);return Math.sign(e)*(i/((1/.501-2)*(1-i)+1))}function ue(e){let{selectedElement:m,containerSize:i,contentSize:c,isRtl:o,currentScrollOffset:p,isHorizontal:u}=e;const r=u?m.clientWidth:m.clientHeight,l=u?m.offsetLeft:m.offsetTop,v=o&&u?c-l-r:l,d=i+p,f=r+v,x=r*.4;return v<=p?p=Math.max(v-x,0):d<=f&&(p=Math.min(p-(d-f-x),c-i)),p}function Ue(e){let{selectedElement:m,containerSize:i,contentSize:c,isRtl:o,isHorizontal:p}=e;const u=p?m.clientWidth:m.clientHeight,r=p?m.offsetLeft:m.offsetTop,l=o&&p?c-r-u/2-i/2:r+u/2-i/2;return Math.min(c-i,Math.max(0,l))}const Je=Symbol.for("vuetify:v-slide-group"),he=Y({centerActive:Boolean,direction:{type:String,default:"horizontal"},symbol:{type:null,default:Je},nextIcon:{type:O,default:"$next"},prevIcon:{type:O,default:"$prev"},showArrows:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["always","desktop","mobile"].includes(e)},...X(),...ke(),...j(),...ce({selectedClass:"v-slide-group-item--active"})},"VSlideGroup"),oe=N()({name:"VSlideGroup",props:he(),emits:{"update:modelValue":e=>!0},setup(e,m){let{slots:i}=m;const{isRtl:c}=Se(),{displayClasses:o,mobile:p}=ge(e),u=re(e,e.symbol),r=R(!1),l=R(0),v=R(0),d=R(0),f=S(()=>e.direction==="horizontal"),{resizeRef:x,contentRect:T}=ee(),{resizeRef:h,contentRect:I}=ee(),t=S(()=>u.selected.value.length?u.items.value.findIndex(a=>a.id===u.selected.value[0]):-1),g=S(()=>u.selected.value.length?u.items.value.findIndex(a=>a.id===u.selected.value[u.selected.value.length-1]):-1);if(Ce){let a=-1;xe(()=>[u.selected.value,T.value,I.value,f.value],()=>{cancelAnimationFrame(a),a=requestAnimationFrame(()=>{if(T.value&&I.value){const s=f.value?"width":"height";v.value=T.value[s],d.value=I.value[s],r.value=v.value+1<d.value}if(t.value>=0&&h.value){const s=h.value.children[g.value];t.value===0||!r.value?l.value=0:e.centerActive?l.value=Ue({selectedElement:s,containerSize:v.value,contentSize:d.value,isRtl:c.value,isHorizontal:f.value}):r.value&&(l.value=ue({selectedElement:s,containerSize:v.value,contentSize:d.value,isRtl:c.value,currentScrollOffset:l.value,isHorizontal:f.value}))}})})}const P=R(!1);let C=0,G=0;function B(a){const s=f.value?"clientX":"clientY";G=(c.value&&f.value?-1:1)*l.value,C=a.touches[0][s],P.value=!0}function D(a){if(!r.value)return;const s=f.value?"clientX":"clientY",k=c.value&&f.value?-1:1;l.value=k*(G+C-a.touches[0][s])}function b(a){const s=d.value-v.value;l.value<0||!r.value?l.value=0:l.value>=s&&(l.value=s),P.value=!1}function V(){x.value&&(x.value[f.value?"scrollLeft":"scrollTop"]=0)}const z=R(!1);function H(a){if(z.value=!0,!(!r.value||!h.value)){for(const s of a.composedPath())for(const k of h.value.children)if(k===s){l.value=ue({selectedElement:k,containerSize:v.value,contentSize:d.value,isRtl:c.value,currentScrollOffset:l.value,isHorizontal:f.value});return}}}function E(a){z.value=!1}function F(a){var s;!z.value&&!(a.relatedTarget&&((s=h.value)!=null&&s.contains(a.relatedTarget)))&&y()}function $(a){h.value&&(f.value?a.key==="ArrowRight"?y(c.value?"prev":"next"):a.key==="ArrowLeft"&&y(c.value?"next":"prev"):a.key==="ArrowDown"?y("next"):a.key==="ArrowUp"&&y("prev"),a.key==="Home"?y("first"):a.key==="End"&&y("last"))}function y(a){var s,k,J,Q,Z;if(h.value)if(!a)(s=Ve(h.value)[0])==null||s.focus();else if(a==="next"){const w=(k=h.value.querySelector(":focus"))==null?void 0:k.nextElementSibling;w?w.focus():y("first")}else if(a==="prev"){const w=(J=h.value.querySelector(":focus"))==null?void 0:J.previousElementSibling;w?w.focus():y("last")}else a==="first"?(Q=h.value.firstElementChild)==null||Q.focus():a==="last"&&((Z=h.value.lastElementChild)==null||Z.focus())}function A(a){const s=l.value+(a==="prev"?-1:1)*v.value;l.value=Ie(s,0,d.value-v.value)}const ye=S(()=>{let a=l.value>d.value-v.value?-(d.value-v.value)+ie(d.value-v.value-l.value):-l.value;l.value<=0&&(a=ie(-l.value));const s=c.value&&f.value?-1:1;return{transform:`translate${f.value?"X":"Y"}(${s*a}px)`,transition:P.value?"none":"",willChange:P.value?"transform":""}}),q=S(()=>({next:u.next,prev:u.prev,select:u.select,isSelected:u.isSelected})),K=S(()=>{switch(e.showArrows){case"always":return!0;case"desktop":return!p.value;case!0:return r.value||Math.abs(l.value)>0;case"mobile":return p.value||r.value||Math.abs(l.value)>0;default:return!p.value&&(r.value||Math.abs(l.value)>0)}}),W=S(()=>Math.abs(l.value)>0),U=S(()=>d.value>Math.abs(l.value)+v.value);return ve(()=>n(e.tag,{class:["v-slide-group",{"v-slide-group--vertical":!f.value,"v-slide-group--has-affixes":K.value,"v-slide-group--is-overflowing":r.value},o.value,e.class],style:e.style,tabindex:z.value||u.selected.value.length?-1:0,onFocus:F},{default:()=>{var a,s,k;return[K.value&&n("div",{key:"prev",class:["v-slide-group__prev",{"v-slide-group__prev--disabled":!W.value}],onClick:()=>W.value&&A("prev")},[((a=i.prev)==null?void 0:a.call(i,q.value))??n(se,null,{default:()=>[n(_,{icon:c.value?e.nextIcon:e.prevIcon},null)]})]),n("div",{key:"container",ref:x,class:"v-slide-group__container",onScroll:V},[n("div",{ref:h,class:"v-slide-group__content",style:ye.value,onTouchstartPassive:B,onTouchmovePassive:D,onTouchendPassive:b,onFocusin:H,onFocusout:E,onKeydown:$},[(s=i.default)==null?void 0:s.call(i,q.value)])]),K.value&&n("div",{key:"next",class:["v-slide-group__next",{"v-slide-group__next--disabled":!U.value}],onClick:()=>U.value&&A("next")},[((k=i.next)==null?void 0:k.call(i,q.value))??n(se,null,{default:()=>[n(_,{icon:c.value?e.prevIcon:e.nextIcon},null)]})])]}})),{selected:u.selected,scrollTo:A,scrollOffset:l,focus:y}}}),be=Symbol.for("vuetify:v-chip-group"),Qe=Y({column:Boolean,filter:Boolean,valueComparator:{type:Function,default:Pe},...he(),...X(),...ce({selectedClass:"v-chip--selected"}),...j(),...de(),...fe({variant:"tonal"})},"VChipGroup");N()({name:"VChipGroup",props:Qe(),emits:{"update:modelValue":e=>!0},setup(e,m){let{slots:i}=m;const{themeClasses:c}=pe(e),{isSelected:o,select:p,next:u,prev:r,selected:l}=re(e,be);return ze({VChip:{color:M(e,"color"),disabled:M(e,"disabled"),filter:M(e,"filter"),variant:M(e,"variant")}}),ve(()=>{const v=oe.filterProps(e);return n(oe,me(v,{class:["v-chip-group",{"v-chip-group--column":e.column},c.value,e.class],style:e.style}),{default:()=>{var d;return[(d=i.default)==null?void 0:d.call(i,{isSelected:o,select:p,next:u,prev:r,selected:l.value})]}})}),{}}});const Ze=Y({activeClass:String,appendAvatar:String,appendIcon:O,closable:Boolean,closeIcon:{type:O,default:"$delete"},closeLabel:{type:String,default:"$vuetify.close"},draggable:Boolean,filter:Boolean,filterIcon:{type:String,default:"$complete"},label:Boolean,link:{type:Boolean,default:void 0},pill:Boolean,prependAvatar:String,prependIcon:O,ripple:{type:[Boolean,Object],default:!0},text:String,modelValue:{type:Boolean,default:!0},onClick:ae(),onClickOnce:ae(),...Ae(),...X(),...we(),...Re(),..._e(),...Te(),...Oe(),...Ge(),...j({tag:"span"}),...de(),...fe({variant:"tonal"})},"VChip"),la=N()({name:"VChip",directives:{Ripple:Be},props:Ze(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0,"group:selected":e=>!0,click:e=>!0},setup(e,m){let{attrs:i,emit:c,slots:o}=m;const{t:p}=Ee(),{borderClasses:u}=Fe(e),{colorClasses:r,colorStyles:l,variantClasses:v}=Me(e),{densityClasses:d}=Le(e),{elevationClasses:f}=De(e),{roundedClasses:x}=He(e),{sizeClasses:T}=$e(e),{themeClasses:h}=pe(e),I=qe(e,"modelValue"),t=Ke(e,be,!1),g=Ye(e,i),P=S(()=>e.link!==!1&&g.isLink.value),C=S(()=>!e.disabled&&e.link!==!1&&(!!t||e.link||g.isClickable.value)),G=S(()=>({"aria-label":p(e.closeLabel),onClick(b){b.preventDefault(),b.stopPropagation(),I.value=!1,c("click:close",b)}}));function B(b){var V;c("click",b),C.value&&((V=g.navigate)==null||V.call(g,b),t==null||t.toggle())}function D(b){(b.key==="Enter"||b.key===" ")&&(b.preventDefault(),B(b))}return()=>{const b=g.isLink.value?"a":e.tag,V=!!(e.appendIcon||e.appendAvatar),z=!!(V||o.append),H=!!(o.close||e.closable),E=!!(o.filter||e.filter)&&t,F=!!(e.prependIcon||e.prependAvatar),$=!!(F||o.prepend),y=!t||t.isSelected.value;return I.value&&le(n(b,{class:["v-chip",{"v-chip--disabled":e.disabled,"v-chip--label":e.label,"v-chip--link":C.value,"v-chip--filter":E,"v-chip--pill":e.pill},h.value,u.value,y?r.value:void 0,d.value,f.value,x.value,T.value,v.value,t==null?void 0:t.selectedClass.value,e.class],style:[y?l.value:void 0,e.style],disabled:e.disabled||void 0,draggable:e.draggable,href:g.href.value,tabindex:C.value?0:void 0,onClick:B,onKeydown:C.value&&!P.value&&D},{default:()=>{var A;return[je(C.value,"v-chip"),E&&n(We,{key:"filter"},{default:()=>[le(n("div",{class:"v-chip__filter"},[o.filter?n(L,{key:"filter-defaults",disabled:!e.filterIcon,defaults:{VIcon:{icon:e.filterIcon}}},o.filter):n(_,{key:"filter-icon",icon:e.filterIcon},null)]),[[Ne,t.isSelected.value]])]}),$&&n("div",{key:"prepend",class:"v-chip__prepend"},[o.prepend?n(L,{key:"prepend-defaults",disabled:!F,defaults:{VAvatar:{image:e.prependAvatar,start:!0},VIcon:{icon:e.prependIcon,start:!0}}},o.prepend):n(te,null,[e.prependIcon&&n(_,{key:"prepend-icon",icon:e.prependIcon,start:!0},null),e.prependAvatar&&n(ne,{key:"prepend-avatar",image:e.prependAvatar,start:!0},null)])]),n("div",{class:"v-chip__content","data-no-activator":""},[((A=o.default)==null?void 0:A.call(o,{isSelected:t==null?void 0:t.isSelected.value,selectedClass:t==null?void 0:t.selectedClass.value,select:t==null?void 0:t.select,toggle:t==null?void 0:t.toggle,value:t==null?void 0:t.value.value,disabled:e.disabled}))??e.text]),z&&n("div",{key:"append",class:"v-chip__append"},[o.append?n(L,{key:"append-defaults",disabled:!V,defaults:{VAvatar:{end:!0,image:e.appendAvatar},VIcon:{end:!0,icon:e.appendIcon}}},o.append):n(te,null,[e.appendIcon&&n(_,{key:"append-icon",end:!0,icon:e.appendIcon},null),e.appendAvatar&&n(ne,{key:"append-avatar",end:!0,image:e.appendAvatar},null)])]),H&&n("button",me({key:"close",class:"v-chip__close",type:"button"},G.value),[o.close?n(L,{key:"close-defaults",defaults:{VIcon:{icon:e.closeIcon,size:"x-small"}}},o.close):n(_,{key:"close-icon",icon:e.closeIcon,size:"x-small"},null)])]}}),[[Xe("ripple"),C.value&&e.ripple,null]])}}});export{la as V,oe as a,he as m};
