<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class checkCodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if(auth()->user() == null) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'phone'=>'required|regex:/^0?9\d{9}$/',
            'code'=>'required|regex:/^[0-9]{6}$/'
        ];
    }
    public function messages(): array
    {
        return [
            'phone.required'=>'شماره تلفن اجباری است',
            'phone.regex'=>'شماره تلفن معتبر نیست',
            'code.required'=>'کد اجباری است',
            'code.regex'=>'کد معتبر نیست',
        ];
    }

}
