<?php

namespace App\Http\Controllers;

use App\Models\BillTrigger;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Class BillTriggerController
 *
 * Controller to manage bill triggers for authenticated users. Provides endpoints
 * to retrieve and update keywords (`bill_triggers`) associated with a user.
 *
 * Usage:
 * - Users can fetch their saved bill triggers.
 * - Users can update their triggers list by replacing existing entries.
 *
 * @package App\Http\Controllers
 */
class BillTriggerController extends Controller
{

    public function getAllTriggers(): JsonResponse
    {
        $userId = auth()->user()->id;
        $triggers = BillTrigger::where('user_id', $userId)->pluck('trigger_name');
        return response()->json(['success' => true, 'bill_triggers' => $triggers]);
    }

    public function updateOrCreateTriggers(Request $request): JsonResponse
    {
        $triggers = $request->input('bill_triggers');
        $userId = auth()->user()->id;

        try {
            BillTrigger::where('user_id', $userId)->delete();

            foreach ($triggers as $trigger) {
                BillTrigger::create([
                    'trigger_name' => $trigger,
                    'user_id' => $userId
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Triggers updated successfully.'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update triggers.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
