import{a8 as o,cd as n,o as i,c as p,n as t,f,z as u,b,ao as x,i as _,e as h,A as $,ce as c,cf as d,y as k}from"./index-169996dc.js";import{a as A}from"./VInput-c4d3942a.js";import{V}from"./VTextField-a8984053.js";const B=Object.assign({name:"AppTextField",inheritAttrs:!1},{__name:"AppTextField",setup(g){const s=o(()=>{const e=n(),a=e.id||e.label;return a?`app-text-field-${a}-${Math.random().toString(36).slice(2,7)}`:void 0}),r=o(()=>n().label);return(e,a)=>(i(),p("div",{class:k(["app-text-field",e.$attrs.class])},[t(r)?(i(),f(A,{key:0,for:t(s),class:"mb-1 text-body-2 text-high-emphasis",text:t(r)},null,8,["for","text"])):u("",!0),b(V,c(d({...e.$attrs,class:null,label:void 0,variant:"outlined",id:t(s)})),x({_:2},[_(e.$slots,(T,l)=>({name:l,fn:h(m=>[$(e.$slots,l,c(d(m||{})))])}))]),1040)],2))}});export{B as _};
