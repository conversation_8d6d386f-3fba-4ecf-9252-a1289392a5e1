<?php

namespace App\Http\Controllers\Instagram;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\Controller;
use Exception;

class InstagramOAuthController extends Controller
{
    public function callback(Request $request)
    {
        $redirectUrl = "https://directam-plus.manymessage.com/pages";

        try {
            // Step 1: Capture Authorization Code from Facebook Response
            $code = $request->get('code');
            $state = $request->get('state');

            if (!$code || !$state) {
                throw new Exception('Authorization code or state parameter missing');
            }

            $stateData = json_decode(urldecode($state), true);

            if (!isset($stateData['uid'], $stateData['pid'])) {
                throw new Exception('Invalid state parameter');
            }

            // Step 2: Exchange the authorization code for an access token
            $clientId = "1552180625370135";
            $clientSecret = "cc187cdc00f53e81debc2ccd77470bce";
            $redirectUri = "https://directam-plus.manymessage.com/api/instalogin/instagramBack.php";
            $tokenEndpoint = "https://api.instagram.com/oauth/access_token";

            $response = Http::asForm()->post($tokenEndpoint, [
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
                'grant_type' => 'authorization_code',
                'redirect_uri' => $redirectUri,
                'code' => $code,
            ]);

            if (!$response->successful() || !isset($response['access_token'])) {
                throw new Exception('Failed to retrieve access token');
            }

            $shortLiveAccessToken = $response['access_token'];
            $userId = $response['user_id'];

            // Step 3: Exchange short live access token with a long live one
            $longLiveResponse = Http::get('https://graph.instagram.com/access_token', [
                'grant_type' => 'ig_exchange_token',
                'client_secret' => $clientSecret,
                'access_token' => $shortLiveAccessToken,
            ]);

            if (!$longLiveResponse->successful() || !isset($longLiveResponse['access_token'])) {
                throw new Exception('Failed to receive long live access token');
            }

            $accessToken = $longLiveResponse['access_token'];

            // Step 4: Fetch user data using the access token
            $userResponse = Http::get("https://graph.instagram.com/me", [
                'fields' => 'user_id,username,account_type',
                'access_token' => $accessToken,
            ]);

            if (!$userResponse->successful() || !isset($userResponse['user_id'], $userResponse['username'])) {
                throw new Exception('Failed to retrieve user data');
            }

            // Step 5: Save Instagram account data to MongoDB
            $saveResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post("https://directam-plus.manymessage.com/api/save-insta-to-mongo/{$stateData['uid']}", [
                'at' => $accessToken,
                'accounts' => [[
                    'access_token' => $accessToken,
                    'fbpage_id' => null,
                    'fbpage_name' => null,
                    'insta_id' => $userResponse['user_id'],
                    'insta_username' => $userResponse['username'],
                    'insta_follower_count' => $userResponse['followers_count'] ?? '0',
                ]]
            ]);

            if (!$saveResponse->successful()) {
                throw new Exception('PageController@saveInstagramAccountToMongo failed');
            }

            $this->sendToTelegram("🔵 Connecting Instagram account...\nURL: https://directam-plus.manymessage.com/api/instagram-account\nPayload: " . json_encode([
                'user_id' => $stateData['uid'],
                'page_id' => $stateData['pid'],
                'instagram_id' => $userResponse['user_id'],
            ]), **********);

            // Step 6: Finalize Instagram connection
            $connectResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post("https://directam-plus.manymessage.com/api/instagram-account", [
                'user_id' => $stateData['uid'],
                'page_id' => $stateData['pid'],
                'instagram_id' => $userResponse['user_id'],
            ]);

            $this->sendToTelegram("🟢 Response Status: " . $connectResponse->status() .
                "\nBody: " . $connectResponse->body(), **********);

            if (!$connectResponse->successful()) {
                throw new Exception('PageController@connectToInstagramAccount failed');
            }

            // Step 7: Redirect back to Prest application
            return redirect()->away($redirectUrl);

        } catch (Exception $e) {
            $this->sendToTelegram("🔴 ERROR: " . $e->getMessage(), **********);
        }
    }

    private  function sendToTelegram($text, $chat_id)
    {

        $botApiToken = '**********:AAGOrMFIBAQU8POIe0Z4gG8NmbjPmZnJJM4';
        $channelId = $chat_id;

        $query = http_build_query([
            'chat_id' => $channelId,
            'text' => $text,
        ]);
        $url = "https://api.telegram.org/bot{$botApiToken}/sendMessage?{$query}";

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        curl_exec($curl);
        curl_close($curl);
    }
}
