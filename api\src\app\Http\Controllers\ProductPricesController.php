<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProductPricesRequest;
use App\Http\Requests\UpdateProductPricesRequest;
use App\Models\ProductPrices;
use App\Models\Products;

class ProductPricesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(StoreProductPricesRequest $request)
    {
        $userId = auth()->user()->id;
        $product = Products::where('id',$request->product_id)->where('user_id',$userId)->first();
        if(!$product){
            return response()->json(['success'=>false,'message'=>'product not found!'],404);
        }
        $price = new ProductPrices();
        $price->user_id = $userId;
        $price->name = $request->name;
        $price->price = $request->price;
        $price->description = $request->description;
        $price->product_id = $product->id;
        $imageName = time().$this->generateRandomString().'.'.$request->image->extension();
        $request->image->move(public_path('storage/images'), $imageName);
        $price->image = '/storage/images/'. $imageName;
        $price->save();
        return $price;
    }
    private function generateRandomString($length = 10) {
        return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
    }
    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $userId = auth()->user()->id;
        $product = Products::where('id',$id)->where('user_id',$userId)->first();
        if(!$product){
            return response()->json(['success'=>false,'message'=>'product not found!'],404);
        }
        $price = ProductPrices::where('user_id',$userId)->where('product_id',$id)->get();
        return $price;
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProductPricesRequest $request, $id)
    {
        $userId = auth()->user()->id;
        $product = Products::where('id',$request->product_id)->where('user_id',$userId)->first();
        if(!$product){
            return response()->json(['success'=>false,'message'=>'product not found!'],404);
        }
        $price = ProductPrices::where('user_id',$userId)->where('id',$id)->first();
        if(!$price){
            return response()->json(['success'=>false,'message'=>'price not found!'],404);
        }
        $price->user_id = $userId;
        if($request->has('name')){
            $price->name = $request->name;
        }
        if($request->has('price')){
            $price->price = $request->price;
        }
        if($request->has('description')){
            $price->description = $request->description;
        }
        $price->product_id = $product->id;
        if($request->hasFile('image')){
            if(file_exists(public_path($price->image))){
                unlink(public_path($price->image));
            }
            $imageName = time().$this->generateRandomString().'.'.$request->image->extension();
            $request->image->move(public_path('storage/images'), $imageName);
            $price->image = '/storage/images/'. $imageName;
        }
        $price->save();
        return $price;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $userId = auth()->user()->id;
        $price = ProductPrices::where('user_id',$userId)->where('id',$id)->first();
        if(!$price){
            return response()->json(['success'=>false,'message'=>'price not found!'],404);
        }
        if(file_exists(public_path($price->image))){
            unlink(public_path($price->image));
        }
        $price->delete();
        return response()->json(['success'=>true]);
    }
}
