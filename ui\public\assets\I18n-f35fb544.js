import{M as i,a5 as u,a as p,o,f as l,e,b as a,a4 as d,n as m,c as f,F as _,i as V,v as g,x as y}from"./index-169996dc.js";import{V as b}from"./VMenu-2cfb0f14.js";import{V as v,a as x,b as I}from"./VList-349a1ccf.js";const q=i({name:"VNodeRenderer",props:{nodes:{type:[Array,Object],required:!0}},setup(n){return()=>n.nodes}}),w={__name:"I18n",props:{languages:{type:Array,required:!0},location:{type:null,required:!1,default:"bottom end"}},setup(n){const r=n,{locale:s}=u({useScope:"global"});return(L,k)=>{const c=p("IconBtn");return o(),l(c,null,{default:e(()=>[a(d,{size:"26",icon:"tabler-language"}),a(b,{activator:"parent",location:r.location,offset:"14px"},{default:e(()=>[a(v,{selected:[m(s)],color:"primary","min-width":"175px"},{default:e(()=>[(o(!0),f(_,null,V(r.languages,t=>(o(),l(x,{key:t.i18nLang,value:t.i18nLang,onClick:B=>s.value=t.i18nLang},{default:e(()=>[a(I,null,{default:e(()=>[g(y(t.label),1)]),_:2},1024)]),_:2},1032,["value","onClick"]))),128))]),_:1},8,["selected"])]),_:1},8,["location"])]),_:1})}}};export{q as V,w as _};
