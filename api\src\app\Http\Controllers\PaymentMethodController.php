<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use App\Models\Page;
use App\Http\Requests\StorePaymentMethodRequest;
use App\Http\Requests\UpdatePaymentMethodRequest;

class PaymentMethodController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return PaymentMethod::where('user_id',auth()->id())->get();
    }
    public function indexPage($id=null)
    {
        $pageId = null;
        if($id){
            $page = Page::where('id',$id)->where('user_id',auth()->user()->id)->first();
            if(!$page){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $page->id;
        }
        if(!$pageId){
            $page = Page::where('user_id',auth()->user()->id)->first();
            $pageId = $page->id;
        }
        return PaymentMethod::where('user_id',auth()->id())->where('page_id',$pageId)->get();
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create(StorePaymentMethodRequest $request,$id=null)
    {
        $userId =auth()->id();
        $paymentMethod = null;
        $pageId = null;
        if($id){
            $page = Page::where('id',$id)->where('user_id',auth()->user()->id)->first();
            if(!$page){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $page->id;
        }
        if(!$pageId){
            $page = Page::where('user_id',auth()->user()->id)->first();
            $pageId = $page->id;
        }
        if($request->payment_method==='paystar'){
            $paymentMethod = PaymentMethod::updateOrCreate(
                [
                    'user_id'=>$userId,
                    'payment_method'=>$request->payment_method,
                    'page_id'=>$pageId
                ],
                [
                    'token'=>$request->token,
                    'secret'=>$request->secret,
                ]
            );
        }else if($request->payment_method==='paystarCard'){
            $paymentMethod = PaymentMethod::updateOrCreate(
                [
                    'user_id'=>$userId,
                    'payment_method'=>$request->payment_method,
                    'page_id'=>$pageId
                ],
                [
                    'token'=>$request->token
                ]
            );
        }else if($request->payment_method==='snapp'){
            $paymentMethod = PaymentMethod::updateOrCreate(
                [
                    'user_id'=>$userId,
                    'payment_method'=>$request->payment_method,
                    'page_id'=>$pageId
                ],
                [
                    'token'=>$request->token,
                    'data'=> json_encode([
                        'user'=>$request->user,
                        'pass'=>$request->pass,
                        'clientId'=>$request->clientId,
                    ]),
                ]
            );
        }
        return $paymentMethod;
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $payment = PaymentMethod::whereId($id)->where('user_id',auth()->id())->first();
        return $payment;
    }



    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePaymentMethodRequest $request, $id)
    {
        $payment = PaymentMethod::whereId($id)->whereUserId(auth()->id())->first();
        $payment->token = $request->token;
        $payment->payment_method = $request->payment_method;
        if($payment->payment_method==='paystar'){
            $payment->secret = $request->secret;
        }else if($payment->payment_method==='snapp'){
            $payment->data = [
                'user'=>$request->user,
                'pass'=>$request->pass,
                'clientId'=>$request->clientId,
            ];
        }
        $payment->save();
        return $payment;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $paument = PaymentMethod::whereId($id)->whereUserId(auth()->id())->first();
        $paument->delete();
        return response('Success', 204);
    }
}
