import{ap as m,aq as g,ar as b,as as v,at as f,b as o,au as L,av as N,aw as A,ax as I,ay as T,az as R,a1 as d,aA as w,aB as _,aC as $,aD as D,aE as F,aF as X,aG as Y,K as q,L as z,s as u,a4 as E}from"./index-169996dc.js";const G=m({start:Boolean,end:Boolean,...g(),...b()},"VListItemAction"),W=v()({name:"VListItemAction",props:G(),setup(e,t){let{slots:s}=t;return f(()=>o(e.tag,{class:["v-list-item-action",{"v-list-item-action--start":e.start,"v-list-item-action--end":e.end},e.class],style:e.style},s)),{}}});const K=m({bordered:Boolean,color:String,content:[Number,String],dot:Boolean,floating:Boolean,icon:L,inline:<PERSON><PERSON>an,label:{type:String,default:"$vuetify.badge"},max:[Number,String],modelValue:{type:Boolean,default:!0},offsetX:[Number,String],offsetY:[Number,String],textColor:String,...g(),...N({location:"top end"}),...A(),...b(),...I(),...T({transition:"scale-rotate-transition"})},"VBadge"),j=v()({name:"VBadge",inheritAttrs:!1,props:K(),setup(e,t){const{backgroundColorClasses:s,backgroundColorStyles:C}=R(d(e,"color")),{roundedClasses:V}=w(e),{t:y}=_(),{textColorClasses:B,textColorStyles:k}=$(d(e,"textColor")),{themeClasses:S}=D(),{locationStyles:P}=F(e,!0,a=>(e.floating?e.dot?2:4:e.dot?8:12)+(["top","bottom"].includes(a)?+(e.offsetY??0):["left","right"].includes(a)?+(e.offsetX??0):0));return f(()=>{const a=Number(e.content),n=!e.max||isNaN(a)?e.content:a<=+e.max?a:`${e.max}+`,[h,x]=X(t.attrs,["aria-atomic","aria-label","aria-live","role","title"]);return o(e.tag,u({class:["v-badge",{"v-badge--bordered":e.bordered,"v-badge--dot":e.dot,"v-badge--floating":e.floating,"v-badge--inline":e.inline},e.class]},x,{style:e.style}),{default:()=>{var l,i;return[o("div",{class:"v-badge__wrapper"},[(i=(l=t.slots).default)==null?void 0:i.call(l),o(Y,{transition:e.transition},{default:()=>{var r,c;return[q(o("span",u({class:["v-badge__badge",S.value,s.value,V.value,B.value],style:[C.value,k.value,e.inline?{}:P.value],"aria-atomic":"true","aria-label":y(e.label,a),"aria-live":"polite",role:"status"},h),[e.dot?void 0:t.slots.badge?(c=(r=t.slots).badge)==null?void 0:c.call(r):e.icon?o(E,{icon:e.icon},null):n]),[[z,e.modelValue]])]}})])]}})}),{}}});export{j as V,W as a};
