import { Injectable } from '@nestjs/common';
import { MetaApiBaseService } from '../abstract/meta-api-base.service';
import { MetaApiClientService } from './meta-api-client.service';
import { IMetaApiHandler } from '../../interfaces/meta-api-handler.interface';

/**
 * Instagram Meta service responsible for interacting with the Instagram API to send replies.
 */
@Injectable()
export class InstagramApiService
  extends MetaApiBaseService
  implements IMetaApiHandler
{
  protected setBaseUrl(): void {
    this.baseUrl = 'https://graph.instagram.com';
  }

  protected setApiVersion(): void {
    this.apiVersion = '22.0';
  }

  constructor(protected readonly apiRequestService: MetaApiClientService) {
    super(apiRequestService);
  }
}
