import{_ as r}from"./VSkeletonLoader-4c44fbcf.js";import{o as s,c as a,F as _,i as d,d as e,x as l,f as u}from"./index-169996dc.js";const p={class:"chat-log"},x={class:"chat-body d-inline-flex flex-column align-end"},f={class:"text-message"},h={class:"d-flex flex-row-reverse w-100 mt-15"},y={class:"chat-body d-inline-flex flex-column align-start"},v={class:"text-answer"},w={__name:"Questions",props:{attrs:Object},setup(o){return(k,m)=>{var n;const i=r;return s(),a("div",p,[(n=o.attrs)!=null&&n.length?(s(!0),a(_,{key:0},d(o.attrs,t=>{var c;return s(),a("div",{key:t.id,class:"chat-group d-flex align-start"},[e("div",x,[e("p",f,l((c=t==null?void 0:t.product_attributes)==null?void 0:c.key),1)]),e("div",h,[e("div",y,[e("p",v,l(t==null?void 0:t.value),1)])])])}),128)):(s(),u(i,{key:1,class:"mx-auto"}))])}}};export{w as _};
