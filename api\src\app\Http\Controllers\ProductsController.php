<?php

namespace App\Http\Controllers;

use App\Http\Requests\productsRequest;
use App\Http\Requests\productsUpdateRequest;
use App\Models\productAttributes;
use App\Models\DeliveryMethod;
use App\Models\Products;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Database\Eloquent\Builder;
use App\Models\ProductsGallery;
use App\Models\Page;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
class ProductsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $search = $request->search;
        if(!$search){
            return Products::where('user_id',auth()->user()->id)
                ->orderBy('created_at','desc')
                ->with(['productGallery','deliveryMethods','paymentMethods'])
                ->paginate(10);
        }
        return Products::search($search)
            ->query(function (Builder $query) {
                $query->with(['productGallery','deliveryMethods','paymentMethods']);
                $query->where('user_id',auth()->user()->id);
            })
            ->orderBy('created_at','desc')
            ->paginate(10);
    }
    public function indexPage(Request $request,$id=null)
    {
        $search = $request->search;
        $pageId = null;
        if($id){
            $page = Page::where('id',$id)->where('user_id',auth()->user()->id)->first();
            if(!$page){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $page->id;
        }
        if(!$search){
            return Products::where('user_id',auth()->user()->id)
                ->where('page_id',$pageId)
                ->orderBy('created_at','desc')
                ->with(['productGallery','deliveryMethods','paymentMethods'])
                ->paginate(10);
        }
        return Products::search($search)
            ->query(function (Builder $query) use ($search, $pageId) {
                $query->with(['productGallery','deliveryMethods','paymentMethods']);
                $query->where('user_id', auth()->user()->id);
                $query->where('page_id',$pageId)
                ->where(function ($query) use ($search) {
                    $query->orWhere('description', 'like', "%{$search}%");
                    $query->orWhere('name', 'like', "%{$search}%");
                    $query->orWhere('triggers', 'like', "%{$search}%");
                    $query->orWhere('price', 'like', "%{$search}%");
                });
            })
            ->orderBy('created_at','desc')
            ->paginate(10);
    }
    public function indexTrashed(Request $request)
    {
        $search = $request->search;
        if(!$search){
            return Products::onlyTrashed()
                ->where('user_id', auth()->user()->id)
                ->orderBy('created_at','desc')
                ->with(['productGallery','deliveryMethods','paymentMethods'])
                ->paginate(10);
        }
        return Products::search($search)
            ->query(function (Builder $query) {
                $query->with(['productGallery','deliveryMethods','paymentMethods']);
                $query->where('user_id',auth()->user()->id);
                $query->whereNotNull('deleted_at');
            })
            ->orderBy('created_at','desc')
            ->paginate(10);
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create(productsRequest $request)
    {
        $product = new Products();
        $product->name = $request->name;
        $product->description = $request->description;
        $product->post_id = $request->post_id;
        $product->triggers = Json::encode($request->triggers);
        $product->price = $request->price;
        $product->multiple = $request->multiple;
        $product->multipleMessage = $request->multipleMessage;
        $product->triggers_mode = $request->triggers_mode;
        $product->page_id = $request->page_id;
        $product->user_id = auth()->user()->id;
        $product->save();
        $page = Page::where('user_id',auth()->user()->id)->where('id',$request->page_id)->first();
        if ($page->fb_access_token == 'loggedInWithInstagram') {
            $apiUrl = 'https://graph.instagram.com/'.$request->post_id.'?fields=id,media_type,media_url,thumbnail_url&access_token='.$page->access_token;
        } else {
            $apiUrl = 'https://graph.facebook.com/'.$request->post_id.'?fields=id,media_type,media_url,thumbnail_url&access_token='.$page->access_token;
        }
        $responseType = Http::get($apiUrl);
        $gallery = new ProductsGallery();
        $gallery->product_id = $product->id;
        $gallery->from = 'instagram';
        $gallery->user_id = auth()->user()->id;
        $gallery->media_id = $responseType['id'];
        if($responseType['media_type']==='CAROUSEL_ALBUM'){
            if ($page->fb_access_token == 'loggedInWithInstagram') {
                $apiUrl = 'https://graph.instagram.com/'.$request->post_id.'/children?fields=id,media_url&access_token='.$page->access_token;
            } else {
                $apiUrl = 'https://graph.facebook.com/'.$request->post_id.'/children?fields=id,media_url&access_token='.$page->access_token;
            }
            $response = Http::get($apiUrl);
            $i = 1;
            foreach($response['data'] as $data){
                if($i>=10){
                    break;
                }
                $gallery = new ProductsGallery();
                $gallery->product_id = $product->id;
                $gallery->from = 'instagram';
                $gallery->user_id = auth()->user()->id;
                $gallery->media_id = $data['id'];
                $gallery->order = $i;
                $gallery->path = $data['media_url'];
                $photo = Http::get($gallery->path);
                $imageName = time().$this->generateRandomString().$i.'.jpeg';
                Storage::put('public/images/'.$imageName, $photo->getBody());
                $gallery->path = '/storage/images/'. $imageName;
                $gallery->from = 'server';
                $gallery->save();
                $i++;
            }

        }else if($responseType['media_type']==='IMAGE'){
            $gallery->path = $responseType['media_url'];
            $photo = Http::get($gallery->path);
            $imageName = time().$this->generateRandomString().'.jpeg';
            Storage::put('public/images/'.$imageName, $photo->getBody());
            $gallery->path = '/storage/images/'. $imageName;
            $gallery->from = 'server';
            $gallery->save();
        }else if($responseType['media_type']==='VIDEO'){
            $gallery->path = $responseType['thumbnail_url'];
            $photo = Http::get($gallery->path);
            $imageName = time().$this->generateRandomString().'.jpeg';
            Storage::put('public/images/'.$imageName, $photo->getBody());
            $gallery->path = '/storage/images/'. $imageName;
            $gallery->from = 'server';
            $gallery->save();
        }

        $productGalleries = ProductsGallery::where('user_id', auth()->user()->id)
            ->whereNotNull('media_id')
            ->get();

        foreach ($productGalleries as $gallery) {
            $gallery->delete();
        }

        $product->deliveryMethods()->attach($request->delivery_methods);
        $product->paymentMethods()->attach($request->payment_methods);
        $products = Products::where('user_id',auth()->id())->count();
        if($products>0){
            $firstProduct = Products::where('user_id',auth()->user()->id)->with(['productAttribute'])->first();
            foreach($firstProduct->productAttribute as $productAttribute){
                $attribute = new productAttributes();
                $attribute->product_id = $product->id;
                $attribute->type = $productAttribute->type;
                $attribute->key = $productAttribute->key;
                $attribute->user_id = $productAttribute->user_id;
                $attribute->save();
            }
        }
        return $product;
    }



    /**
     * Display the specified resource.
     */
    public function show($id)
    {

        $product = Products::whereId($id)
            ->where('user_id',auth()->user()->id)
            ->with(['productGallery' => fn ($query) => $query->orderBy('order', 'asc'),'deliveryMethods','paymentMethods','productMedia'])
            ->firstOrFail();
        return $product;
    }




    /**
     * Update the specified resource in storage.
     */
    public function update(productsUpdateRequest $request,$id)
    {
        $products = Products::whereId($id)->where('user_id',auth()->user()->id)
            ->with(['deliveryMethods','paymentMethods'])->firstOrFail();
        $products->name = $request->name;
        $products->description = $request->description;
        // $products->post_id = $request->post_id;
        $products->triggers = Json::encode($request->triggers);
        $products->price = $request->price;
        $products->multiple = $request->multiple;
        $products->multipleMessage = $request->multipleMessage;
        $products->triggers_mode = $request->triggers_mode;
        $products->save();
        $attachedDeliveryIds = $products->deliveryMethods->pluck('id');
        $products->deliveryMethods()->detach($attachedDeliveryIds);
        $products->deliveryMethods()->attach($request->delivery_methods);
        $attachedPaymentIds = $products->paymentMethods->pluck('id');
        $products->paymentMethods()->detach($attachedPaymentIds);
        $products->paymentMethods()->attach($request->payment_methods);
        return $products;
    }

    /**
     * Remove the specified resource from storage.
     */
    private function generateRandomString($length = 10) {
        return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
    }
    public function destroy($id)
    {
        $products = Products::where('user_id',auth()->user()->id)->whereId($id)->firstOrFail();
        $products->delete();
        return $products;
    }
}
