<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFbAccessTokenToPagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Schema::table('pages', function (Blueprint $table) {
        //     $table->text('fb_access_token', 255)->after('fb_user_id');
        // });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Schema::table('pages', function (Blueprint $table) {
        //     $table->dropColumn('fb_access_token');
        // });
    }
}
