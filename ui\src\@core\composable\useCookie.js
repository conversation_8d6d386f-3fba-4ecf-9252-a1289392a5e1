import { parse, serialize } from 'cookie-es'
import { destr } from 'destr'

const CookieDefaults = {
  path: '/',

  // secure: true, // Added for HTTPS only
  // httpOnly: true, // Added to prevent access via JavaScript
  // sameSite: 'Strict', // Added for same-site policy
  watch: true,
  decode: val => destr(decodeURIComponent(val)),
  encode: val => encodeURIComponent(typeof val === 'string' ? val : JSON.stringify(val)),
}

export const useCookie = (name, _opts) => {
  const opts = { ...CookieDefaults, ..._opts || {} }

  // Try to use cookies first
  let cookies
  try {
    cookies = parse(document.cookie, opts)
  } catch (error) {
    cookies = {}
  }

  const cookie = ref(cookies[name] ?? opts.default?.())

  watch(cookie, () => {
    try {
      // Try to save in cookies
      document.cookie = serializeCookie(name, cookie.value, opts)
    } catch (error) {
      // Fallback to local storage
      localStorage.setItem(name, opts.encode(cookie.value))
    }
  })

  return cookie
}

function serializeCookie(name, value, opts = {}) {
  if (value === null || value === undefined) {
    return serialize(name, '', { ...opts, maxAge: -1 })
  }

  return serialize(name, value, { ...opts, maxAge: 2592000000  })
}

// Additional function to initialize data from local storage
function initDataFromLocalStorage(name, opts) {
  let value = localStorage.getItem(name)

  return opts.decode(value)
}
