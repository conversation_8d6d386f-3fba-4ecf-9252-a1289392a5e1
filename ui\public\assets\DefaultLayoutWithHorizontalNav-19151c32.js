import{_ as Qt}from"./AppLoadingIndicator-1aa55cc8.js";import{o as h,c as z,F as Dt,i as Ot,f as C,h as P,j as Ft,k as Zt,l as dt,r as V,w as ot,m as te,n as u,p as ee,e as _,d as w,q as O,s as q,t as Mt,v as $t,x as ft,y as Q,z as mt,A as I,b as S,B as oe,C as ne,D as ie,E as re,G as se,H as ae,I as N,J as ct,K as Rt,L as _t,T as le,a as At,S as ce}from"./index-169996dc.js";import ue from"./Footer-6787fa0c.js";import pe from"./NavBarNotifications-31c72487.js";import de from"./NavSearchBar-82d8ca26.js";import fe from"./NavbarShortcuts-97421026.js";import me from"./NavbarThemeSwitcher-85b3d65e.js";import ge from"./UserProfile-e69d82d2.js";import{V as he,_ as be}from"./I18n-f35fb544.js";import{V as ve}from"./VSpacer-fcf88198.js";import"./formatters-c282fd5f.js";import"./vue3-perfect-scrollbar.esm-e1f82a0e.js";/* empty css                                                      */import"./VBadge-751ce1d1.js";import"./VMenu-2cfb0f14.js";import"./VTooltip-b4170ac2.js";import"./VDivider-12bfa926.js";import"./VList-349a1ccf.js";import"./index-00c7d20d.js";import"./ssrBoot-c101cd97.js";import"./index-d1f9a878.js";import"./VRow-6c1d54f3.js";import"./VSkeletonLoader-4c44fbcf.js";import"./DialogCloseBtn-b32209d9.js";import"./validations-4c0aab88.js";import"./profile_ph-c1153e1f.js";import"./VRadioGroup-52375e28.js";import"./VSelectionControl-8ecbcf09.js";import"./VInput-c4d3942a.js";import"./VTextField-a8984053.js";import"./VField-150a934a.js";import"./useAbility-e6e49333.js";const ye={class:"nav-items"},we={__name:"HorizontalNav",props:{navItems:{type:null,required:!0}},setup(t){const e=o=>"children"in o?ke:Bt;return(o,n)=>(h(),z("ul",ye,[(h(!0),z(Dt,null,Ot(t.navItems,(i,r)=>(h(),C(P(e(i)),{key:r,item:i},null,8,["item"]))),128))]))}},xe={class:"nav-group-label"},ke=Object.assign({name:"HorizontalNavGroup"},{__name:"HorizontalNavGroup",props:{item:{type:null,required:!0},childrenAtEnd:{type:Boolean,required:!1,default:!1},isSubItem:{type:Boolean,required:!1,default:!1}},setup(t){const e=t,o=Ft(),n=Zt(),i=dt(),r=V(!1);return ot(()=>o.path,()=>{const s=te(e.item.children,n);r.value=s},{immediate:!0}),(s,a)=>u(ee)(t.item)?(h(),C(u(ao),{key:0,"is-rtl":u(i).isAppRTL,class:Q(["nav-group",[{active:u(r),"children-at-end":t.childrenAtEnd,"sub-item":t.isSubItem,disabled:t.item.disable}]]),tag:"li","content-container-tag":"ul","popper-inline-end":t.childrenAtEnd},{content:_(()=>[(h(!0),z(Dt,null,Ot(t.item.children,l=>(h(),C(P("children"in l?"HorizontalNavGroup":u(Bt)),{key:l.title,item:l,"children-at-end":"","is-sub-item":""},null,8,["item"]))),128))]),default:_(()=>[w("div",xe,[(h(),C(P(u(O).app.iconRenderer||"div"),q({class:"nav-item-icon"},t.item.icon||u(O).verticalNav.defaultNavItemIconProps),null,16)),(h(),C(P(u(O).app.i18n.enable?"i18n-t":"span"),q(u(Mt)(t.item.title,"span"),{class:"nav-item-title"}),{default:_(()=>[$t(ft(t.item.title),1)]),_:1},16)),(h(),C(P(u(O).app.iconRenderer||"div"),q(u(O).icons.chevronDown,{class:"nav-group-arrow"}),null,16))])]),_:1},8,["is-rtl","class","popper-inline-end"])):mt("",!0)}}),Ce={class:"layout-navbar"},Re={class:"navbar-content-container"},_e={class:"layout-horizontal-nav"},Ae={class:"horizontal-nav-content-container"},Se={class:"layout-page-content"},Le={class:"layout-footer"},Pe={class:"footer-content-container"},Ee={__name:"HorizontalNavLayout",props:{navItems:{type:null,required:!0}},setup(t){const e=dt();return(o,n)=>(h(),z("div",{class:Q(["layout-wrapper",u(e)._layoutClasses])},[w("div",{class:Q(["layout-navbar-and-nav-container",u(e).isNavbarBlurEnabled&&"header-blur"])},[w("div",Ce,[w("div",Re,[I(o.$slots,"navbar")])]),w("div",_e,[w("div",Ae,[S(u(we),{"nav-items":t.navItems},null,8,["nav-items"])])])],2),w("main",Se,[I(o.$slots,"default")]),w("footer",Le,[w("div",Pe,[I(o.$slots,"footer")])])],2))}},Bt={__name:"HorizontalNavLink",props:{item:{type:null,required:!0},isSubItem:{type:Boolean,required:!1,default:!1}},setup(t){const e=t;return(o,n)=>u(oe)(t.item.action,t.item.subject)?(h(),z("li",{key:0,class:Q(["nav-link",[{"sub-item":e.isSubItem,disabled:t.item.disable}]])},[(h(),C(P(t.item.to?"RouterLink":"a"),q(u(ie)(t.item),{class:{"router-link-active router-link-exact-active":u(ne)(t.item,o.$router)}}),{default:_(()=>[(h(),C(P(u(O).app.iconRenderer||"div"),q({class:"nav-item-icon"},t.item.icon||u(O).verticalNav.defaultNavItemIconProps),null,16)),(h(),C(P(u(O).app.i18n.enable?"i18n-t":"span"),q({class:"nav-item-title"},u(Mt)(t.item.title,"span")),{default:_(()=>[$t(ft(t.item.title),1)]),_:1},16))]),_:1},16,["class"]))],2)):mt("",!0)}},Te=Math.min,Ne=Math.max,De={left:"right",right:"left",bottom:"top",top:"bottom"},Oe={start:"end",end:"start"};function St(t,e,o){return Ne(t,Te(e,o))}function gt(t,e){return typeof t=="function"?t(e):t}function G(t){return t.split("-")[0]}function ht(t){return t.split("-")[1]}function Vt(t){return t==="x"?"y":"x"}function It(t){return t==="y"?"height":"width"}function bt(t){return["top","bottom"].includes(G(t))?"y":"x"}function zt(t){return Vt(bt(t))}function Fe(t,e,o){o===void 0&&(o=!1);const n=ht(t),i=zt(t),r=It(i);let s=i==="x"?n===(o?"end":"start")?"right":"left":n==="start"?"bottom":"top";return e.reference[r]>e.floating[r]&&(s=nt(s)),[s,nt(s)]}function Me(t){const e=nt(t);return[ut(t),e,ut(e)]}function ut(t){return t.replace(/start|end/g,e=>Oe[e])}function $e(t,e,o){const n=["left","right"],i=["right","left"],r=["top","bottom"],s=["bottom","top"];switch(t){case"top":case"bottom":return o?e?i:n:e?n:i;case"left":case"right":return e?r:s;default:return[]}}function Be(t,e,o,n){const i=ht(t);let r=$e(G(t),o==="start",n);return i&&(r=r.map(s=>s+"-"+i),e&&(r=r.concat(r.map(ut)))),r}function nt(t){return t.replace(/left|right|bottom|top/g,e=>De[e])}function Ve(t){return{top:0,right:0,bottom:0,left:0,...t}}function Ie(t){return typeof t!="number"?Ve(t):{top:t,right:t,bottom:t,left:t}}function it(t){const{x:e,y:o,width:n,height:i}=t;return{width:n,height:i,top:o,left:e,right:e+n,bottom:o+i,x:e,y:o}}function Lt(t,e,o){let{reference:n,floating:i}=t;const r=bt(e),s=zt(e),a=It(s),l=G(e),c=r==="y",d=n.x+n.width/2-i.width/2,p=n.y+n.height/2-i.height/2,g=n[a]/2-i[a]/2;let f;switch(l){case"top":f={x:d,y:n.y-i.height};break;case"bottom":f={x:d,y:n.y+n.height};break;case"right":f={x:n.x+n.width,y:p};break;case"left":f={x:n.x-i.width,y:p};break;default:f={x:n.x,y:n.y}}switch(ht(e)){case"start":f[s]-=g*(o&&c?-1:1);break;case"end":f[s]+=g*(o&&c?-1:1);break}return f}const ze=async(t,e,o)=>{const{placement:n="bottom",strategy:i="absolute",middleware:r=[],platform:s}=o,a=r.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(e));let c=await s.getElementRects({reference:t,floating:e,strategy:i}),{x:d,y:p}=Lt(c,n,l),g=n,f={},m=0;for(let b=0;b<a.length;b++){const{name:v,fn:y}=a[b],{x,y:k,data:T,reset:R}=await y({x:d,y:p,initialPlacement:n,placement:g,strategy:i,middlewareData:f,rects:c,platform:s,elements:{reference:t,floating:e}});d=x??d,p=k??p,f={...f,[v]:{...f[v],...T}},R&&m<=50&&(m++,typeof R=="object"&&(R.placement&&(g=R.placement),R.rects&&(c=R.rects===!0?await s.getElementRects({reference:t,floating:e,strategy:i}):R.rects),{x:d,y:p}=Lt(c,g,l)),b=-1)}return{x:d,y:p,placement:g,strategy:i,middlewareData:f}};async function Ht(t,e){var o;e===void 0&&(e={});const{x:n,y:i,platform:r,rects:s,elements:a,strategy:l}=t,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:p="floating",altBoundary:g=!1,padding:f=0}=gt(e,t),m=Ie(f),v=a[g?p==="floating"?"reference":"floating":p],y=it(await r.getClippingRect({element:(o=await(r.isElement==null?void 0:r.isElement(v)))==null||o?v:v.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:l})),x=p==="floating"?{x:n,y:i,width:s.floating.width,height:s.floating.height}:s.reference,k=await(r.getOffsetParent==null?void 0:r.getOffsetParent(a.floating)),T=await(r.isElement==null?void 0:r.isElement(k))?await(r.getScale==null?void 0:r.getScale(k))||{x:1,y:1}:{x:1,y:1},R=it(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:x,offsetParent:k,strategy:l}):x);return{top:(y.top-R.top+m.top)/T.y,bottom:(R.bottom-y.bottom+m.bottom)/T.y,left:(y.left-R.left+m.left)/T.x,right:(R.right-y.right+m.right)/T.x}}const He=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var o,n;const{placement:i,middlewareData:r,rects:s,initialPlacement:a,platform:l,elements:c}=e,{mainAxis:d=!0,crossAxis:p=!0,fallbackPlacements:g,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:b=!0,...v}=gt(t,e);if((o=r.arrow)!=null&&o.alignmentOffset)return{};const y=G(i),x=G(a)===a,k=await(l.isRTL==null?void 0:l.isRTL(c.floating)),T=g||(x||!b?[nt(a)]:Me(a));!g&&m!=="none"&&T.push(...Be(a,b,m,k));const R=[a,...T],lt=await Ht(e,v),et=[];let X=((n=r.flip)==null?void 0:n.overflows)||[];if(d&&et.push(lt[y]),p){const B=Fe(i,s,k);et.push(lt[B[0]],lt[B[1]])}if(X=[...X,{placement:i,overflows:et}],!et.every(B=>B<=0)){var wt,xt;const B=(((wt=r.flip)==null?void 0:wt.index)||0)+1,Ct=R[B];if(Ct)return{data:{index:B,overflows:X},reset:{placement:Ct}};let Y=(xt=X.filter(H=>H.overflows[0]<=0).sort((H,W)=>H.overflows[1]-W.overflows[1])[0])==null?void 0:xt.placement;if(!Y)switch(f){case"bestFit":{var kt;const H=(kt=X.map(W=>[W.placement,W.overflows.filter(K=>K>0).reduce((K,Jt)=>K+Jt,0)]).sort((W,K)=>W[1]-K[1])[0])==null?void 0:kt[0];H&&(Y=H);break}case"initialPlacement":Y=a;break}if(i!==Y)return{reset:{placement:Y}}}return{}}}},We=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:o,y:n,placement:i}=e,{mainAxis:r=!0,crossAxis:s=!1,limiter:a={fn:v=>{let{x:y,y:x}=v;return{x:y,y:x}}},...l}=gt(t,e),c={x:o,y:n},d=await Ht(e,l),p=bt(G(i)),g=Vt(p);let f=c[g],m=c[p];if(r){const v=g==="y"?"top":"left",y=g==="y"?"bottom":"right",x=f+d[v],k=f-d[y];f=St(x,f,k)}if(s){const v=p==="y"?"top":"left",y=p==="y"?"bottom":"right",x=m+d[v],k=m-d[y];m=St(x,m,k)}const b=a.fn({...e,[g]:f,[p]:m});return{...b,data:{x:b.x-o,y:b.y-n}}}}},Pt=Math.min,J=Math.max,rt=Math.round,F=t=>({x:t,y:t});function M(t){return Wt(t)?(t.nodeName||"").toLowerCase():"#document"}function A(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function $(t){var e;return(e=(Wt(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Wt(t){return t instanceof Node||t instanceof A(t).Node}function D(t){return t instanceof Element||t instanceof A(t).Element}function E(t){return t instanceof HTMLElement||t instanceof A(t).HTMLElement}function Et(t){return typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof A(t).ShadowRoot}function tt(t){const{overflow:e,overflowX:o,overflowY:n,display:i}=L(t);return/auto|scroll|overlay|hidden|clip/.test(e+n+o)&&!["inline","contents"].includes(i)}function qe(t){return["table","td","th"].includes(M(t))}function vt(t){const e=yt(),o=L(t);return o.transform!=="none"||o.perspective!=="none"||(o.containerType?o.containerType!=="normal":!1)||!e&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!e&&(o.filter?o.filter!=="none":!1)||["transform","perspective","filter"].some(n=>(o.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(o.contain||"").includes(n))}function je(t){let e=U(t);for(;E(e)&&!st(e);){if(vt(e))return e;e=U(e)}return null}function yt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function st(t){return["html","body","#document"].includes(M(t))}function L(t){return A(t).getComputedStyle(t)}function at(t){return D(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function U(t){if(M(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Et(t)&&t.host||$(t);return Et(e)?e.host:e}function qt(t){const e=U(t);return st(e)?t.ownerDocument?t.ownerDocument.body:t.body:E(e)&&tt(e)?e:qt(e)}function pt(t,e,o){var n;e===void 0&&(e=[]),o===void 0&&(o=!0);const i=qt(t),r=i===((n=t.ownerDocument)==null?void 0:n.body),s=A(i);return r?e.concat(s,s.visualViewport||[],tt(i)?i:[],s.frameElement&&o?pt(s.frameElement):[]):e.concat(i,pt(i,[],o))}function jt(t){const e=L(t);let o=parseFloat(e.width)||0,n=parseFloat(e.height)||0;const i=E(t),r=i?t.offsetWidth:o,s=i?t.offsetHeight:n,a=rt(o)!==r||rt(n)!==s;return a&&(o=r,n=s),{width:o,height:n,$:a}}function Gt(t){return D(t)?t:t.contextElement}function j(t){const e=Gt(t);if(!E(e))return F(1);const o=e.getBoundingClientRect(),{width:n,height:i,$:r}=jt(e);let s=(r?rt(o.width):o.width)/n,a=(r?rt(o.height):o.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const Ge=F(0);function Ut(t){const e=A(t);return!yt()||!e.visualViewport?Ge:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function Ue(t,e,o){return e===void 0&&(e=!1),!o||e&&o!==A(t)?!1:e}function Z(t,e,o,n){e===void 0&&(e=!1),o===void 0&&(o=!1);const i=t.getBoundingClientRect(),r=Gt(t);let s=F(1);e&&(n?D(n)&&(s=j(n)):s=j(t));const a=Ue(r,o,n)?Ut(r):F(0);let l=(i.left+a.x)/s.x,c=(i.top+a.y)/s.y,d=i.width/s.x,p=i.height/s.y;if(r){const g=A(r),f=n&&D(n)?A(n):n;let m=g.frameElement;for(;m&&n&&f!==g;){const b=j(m),v=m.getBoundingClientRect(),y=L(m),x=v.left+(m.clientLeft+parseFloat(y.paddingLeft))*b.x,k=v.top+(m.clientTop+parseFloat(y.paddingTop))*b.y;l*=b.x,c*=b.y,d*=b.x,p*=b.y,l+=x,c+=k,m=A(m).frameElement}}return it({width:d,height:p,x:l,y:c})}function Xe(t){let{rect:e,offsetParent:o,strategy:n}=t;const i=E(o),r=$(o);if(o===r)return e;let s={scrollLeft:0,scrollTop:0},a=F(1);const l=F(0);if((i||!i&&n!=="fixed")&&((M(o)!=="body"||tt(r))&&(s=at(o)),E(o))){const c=Z(o);a=j(o),l.x=c.x+o.clientLeft,l.y=c.y+o.clientTop}return{width:e.width*a.x,height:e.height*a.y,x:e.x*a.x-s.scrollLeft*a.x+l.x,y:e.y*a.y-s.scrollTop*a.y+l.y}}function Ye(t){return Array.from(t.getClientRects())}function Xt(t){return Z($(t)).left+at(t).scrollLeft}function Ke(t){const e=$(t),o=at(t),n=t.ownerDocument.body,i=J(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),r=J(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight);let s=-o.scrollLeft+Xt(t);const a=-o.scrollTop;return L(n).direction==="rtl"&&(s+=J(e.clientWidth,n.clientWidth)-i),{width:i,height:r,x:s,y:a}}function Je(t,e){const o=A(t),n=$(t),i=o.visualViewport;let r=n.clientWidth,s=n.clientHeight,a=0,l=0;if(i){r=i.width,s=i.height;const c=yt();(!c||c&&e==="fixed")&&(a=i.offsetLeft,l=i.offsetTop)}return{width:r,height:s,x:a,y:l}}function Qe(t,e){const o=Z(t,!0,e==="fixed"),n=o.top+t.clientTop,i=o.left+t.clientLeft,r=E(t)?j(t):F(1),s=t.clientWidth*r.x,a=t.clientHeight*r.y,l=i*r.x,c=n*r.y;return{width:s,height:a,x:l,y:c}}function Tt(t,e,o){let n;if(e==="viewport")n=Je(t,o);else if(e==="document")n=Ke($(t));else if(D(e))n=Qe(e,o);else{const i=Ut(t);n={...e,x:e.x-i.x,y:e.y-i.y}}return it(n)}function Yt(t,e){const o=U(t);return o===e||!D(o)||st(o)?!1:L(o).position==="fixed"||Yt(o,e)}function Ze(t,e){const o=e.get(t);if(o)return o;let n=pt(t,[],!1).filter(a=>D(a)&&M(a)!=="body"),i=null;const r=L(t).position==="fixed";let s=r?U(t):t;for(;D(s)&&!st(s);){const a=L(s),l=vt(s);!l&&a.position==="fixed"&&(i=null),(r?!l&&!i:!l&&a.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||tt(s)&&!l&&Yt(t,s))?n=n.filter(d=>d!==s):i=a,s=U(s)}return e.set(t,n),n}function to(t){let{element:e,boundary:o,rootBoundary:n,strategy:i}=t;const s=[...o==="clippingAncestors"?Ze(e,this._c):[].concat(o),n],a=s[0],l=s.reduce((c,d)=>{const p=Tt(e,d,i);return c.top=J(p.top,c.top),c.right=Pt(p.right,c.right),c.bottom=Pt(p.bottom,c.bottom),c.left=J(p.left,c.left),c},Tt(e,a,i));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function eo(t){return jt(t)}function oo(t,e,o){const n=E(e),i=$(e),r=o==="fixed",s=Z(t,!0,r,e);let a={scrollLeft:0,scrollTop:0};const l=F(0);if(n||!n&&!r)if((M(e)!=="body"||tt(i))&&(a=at(e)),n){const c=Z(e,!0,r,e);l.x=c.x+e.clientLeft,l.y=c.y+e.clientTop}else i&&(l.x=Xt(i));return{x:s.left+a.scrollLeft-l.x,y:s.top+a.scrollTop-l.y,width:s.width,height:s.height}}function Nt(t,e){return!E(t)||L(t).position==="fixed"?null:e?e(t):t.offsetParent}function Kt(t,e){const o=A(t);if(!E(t))return o;let n=Nt(t,e);for(;n&&qe(n)&&L(n).position==="static";)n=Nt(n,e);return n&&(M(n)==="html"||M(n)==="body"&&L(n).position==="static"&&!vt(n))?o:n||je(t)||o}const no=async function(t){let{reference:e,floating:o,strategy:n}=t;const i=this.getOffsetParent||Kt,r=this.getDimensions;return{reference:oo(e,await i(o),n),floating:{x:0,y:0,...await r(o)}}};function io(t){return L(t).direction==="rtl"}const ro={convertOffsetParentRelativeRectToViewportRelativeRect:Xe,getDocumentElement:$,getClippingRect:to,getOffsetParent:Kt,getElementRects:no,getClientRects:Ye,getDimensions:eo,getScale:j,isElement:D,isRTL:io},so=(t,e,o)=>{const n=new Map,i={platform:ro,...o},r={...i.platform,_c:n};return ze(t,e,{...i,platform:r})},ao={__name:"HorizontalNavPopper",props:{popperInlineEnd:{type:Boolean,required:!1,default:!1},tag:{type:String,required:!1,default:"div"},contentContainerTag:{type:String,required:!1,default:"div"},isRtl:{type:Boolean,required:!1}},setup(t){const e=t,o=dt(),n=V(),i=V(),r=V({left:"0px",top:"0px"}),s=async()=>{if(n.value!==void 0&&i.value!==void 0){const{x:p,y:g}=await so(n.value,i.value,{placement:e.popperInlineEnd?e.isRtl?"left-start":"right-start":"bottom-start",middleware:[He({boundary:document.querySelector("body")}),We({boundary:document.querySelector("body")})]});r.value.left=`${p}px`,r.value.top=`${g}px`}};re(()=>o.horizontalNavType).toMatch(p=>p==="static").then(()=>{se("scroll",s)});const a=V(!1),l=()=>{a.value=!0,s()},c=()=>{a.value=!1};ae(s),ot([()=>o.isAppRTL,()=>o.appContentWidth],s);const d=Ft();return ot(()=>d.fullPath,c),(p,g)=>(h(),z("div",{class:Q(["nav-popper",[{"popper-inline-end":t.popperInlineEnd,"show-content":u(a)}]])},[w("div",{ref_key:"refPopperContainer",ref:n,class:"popper-triggerer",onMouseenter:l,onMouseleave:c},[I(p.$slots,"default")],544),u(N).horizontalNav.transition?typeof u(N).horizontalNav.transition=="string"?(h(),C(le,{key:1,name:u(N).horizontalNav.transition},{default:_(()=>[Rt(w("div",{ref_key:"refPopper",ref:i,class:"popper-content",style:ct(u(r)),onMouseenter:l,onMouseleave:c},[w("div",null,[I(p.$slots,"content")])],36),[[_t,u(a)]])]),_:3},8,["name"])):(h(),C(P(u(N).horizontalNav.transition),{key:2},{default:_(()=>[Rt(w("div",{ref_key:"refPopper",ref:i,class:"popper-content",style:ct(u(r)),onMouseenter:l,onMouseleave:c},[w("div",null,[I(p.$slots,"content")])],36),[[_t,u(a)]])]),_:3})):(h(),z("div",{key:0,ref_key:"refPopper",ref:i,class:"popper-content",style:ct(u(r)),onMouseenter:l,onMouseleave:c},[w("div",null,[I(p.$slots,"content")])],36))],2))}},lo=[{title:"Apps",icon:{icon:"tabler-layout-grid-add"},children:[{title:"Ecommerce",icon:{icon:"tabler-shopping-cart-plus"},children:[{title:"Product",children:[{title:"List",to:"apps-ecommerce-product-list"},{title:"Add",to:"apps-ecommerce-product-add"},{title:"Category",to:"apps-ecommerce-product-category-list"}]},{title:"Order",children:[{title:"List",to:"apps-ecommerce-order-list"},{title:"Details",to:{name:"apps-ecommerce-order-details-id",params:{id:"9042"}}}]},{title:"Customer",children:[{title:"List",to:"apps-ecommerce-customer-list"}]},{title:"Settings",to:"apps-ecommerce-settings"}]},{title:"Academy",icon:{icon:"tabler-book"},children:[{title:"Dashboard",to:"apps-academy-dashboard"},{title:"My Course",to:"apps-academy-my-course"},{title:"Course Details",to:"apps-academy-course-details"}]},{title:"Logistics",icon:{icon:"tabler-truck"},children:[{title:"Dashboard",to:"apps-logistics-dashboard"},{title:"Fleet",to:"apps-logistics-fleet"}]},{title:"Email",icon:{icon:"tabler-mail"},to:"apps-email"},{title:"Chat",icon:{icon:"tabler-message-circle"},to:"apps-chat"},{title:"Calendar",to:"apps-calendar",icon:{icon:"tabler-calendar"}},{title:"Invoice",icon:{icon:"tabler-file-dollar"},children:[{title:"List",to:"apps-invoice-list"},{title:"Preview",to:{name:"apps-invoice-preview-id",params:{id:"5036"}}},{title:"Edit",to:{name:"apps-invoice-edit-id",params:{id:"5036"}}},{title:"Add",to:"apps-invoice-add"}]},{title:"User",icon:{icon:"tabler-users"},children:[{title:"List",to:"apps-user-list"},{title:"View",to:{name:"apps-user-view-id",params:{id:21}}}]},{title:"Roles & Permissions",icon:{icon:"tabler-settings"},children:[{title:"Roles",to:"apps-roles"},{title:"Permissions",to:"apps-permissions"}]}]}],co=[{title:"Charts",icon:{icon:"tabler-chart-bar"},children:[{title:"Apex Chart",to:"charts-apex-chart",icon:{icon:"tabler-chart-line"}},{title:"Chartjs",to:"charts-chartjs",icon:{icon:"tabler-chart-pie"}}]}],uo=[{title:"Dashboards",icon:{icon:"tabler-smart-home"},children:[{title:"Analytics",to:"dashboards-analytics",icon:{icon:"tabler-chart-pie-2"}},{title:"eCommerce",to:"dashboards-ecommerce",icon:{icon:"tabler-atom-2"}},{title:"CRM",to:"dashboards-crm",icon:{icon:"tabler-3d-cube-sphere"}},{title:"Ecommerce",to:"dashboards-ecommerce",icon:{icon:"tabler-shopping-cart"}},{title:"Academy",to:"dashboards-academy",icon:{icon:"tabler-book"}},{title:"Logistics",to:"dashboards-logistics",icon:{icon:"tabler-truck"}}]}],po=[{title:"Forms",icon:{icon:"tabler-forms"},children:[{title:"Form Elements",icon:{icon:"tabler-copy"},children:[{title:"Autocomplete",to:"forms-autocomplete"},{title:"Checkbox",to:"forms-checkbox"},{title:"Combobox",to:"forms-combobox"},{title:"Date Time Picker",to:"forms-date-time-picker"},{title:"Editors",to:"forms-editors"},{title:"File Input",to:"forms-file-input"},{title:"Radio",to:"forms-radio"},{title:"Custom Input",to:"forms-custom-input"},{title:"Range Slider",to:"forms-range-slider"},{title:"Rating",to:"forms-rating"},{title:"Select",to:"forms-select"},{title:"Slider",to:"forms-slider"},{title:"Switch",to:"forms-switch"},{title:"Textarea",to:"forms-textarea"},{title:"Textfield",to:"forms-textfield"}]},{title:"Form Layouts",icon:{icon:"tabler-circle-check"},to:"forms-form-layouts"},{title:"Form Wizard",icon:{icon:"tabler-align-center"},children:[{title:"Numbered",to:"forms-form-wizard-numbered"},{title:"Icons",to:"forms-form-wizard-icons"}]},{title:"Form Validation",icon:{icon:"tabler-circle-check"},to:"forms-form-validation"}]}],fo=[{title:"Misc",icon:{icon:"tabler-box-multiple"},children:[{title:"Access Control",icon:{icon:"tabler-command"},to:"access-control",action:"read",subject:"AclDemo"},{title:"Nav Levels",icon:{icon:"tabler-menu-2"},children:[{title:"Level 2.1",to:null},{title:"Level 2.2",children:[{title:"Level 3.1",to:null},{title:"Level 3.2",to:null}]}]},{title:"Disabled Menu",to:null,icon:{icon:"tabler-eye-off"},disable:!0},{title:"Raise Support",href:"https://pixinvent.ticksy.com/",icon:{icon:"tabler-headphones"},target:"_blank"},{title:"Documentation",href:"https://demos.pixinvent.com/vuexy-vuejs-admin-template/documentation/",icon:{icon:"tabler-file-text"},target:"_blank"}]}],mo=[{title:"Pages",icon:{icon:"tabler-file"},children:[{title:"User Profile",icon:{icon:"tabler-user-circle"},to:{name:"pages-user-profile-tab",params:{tab:"profile"}}},{title:"Account Settings",icon:{icon:"tabler-settings"},to:{name:"pages-account-settings-tab",params:{tab:"account"}}},{title:"FAQ",icon:{icon:"tabler-help"},to:"pages-faq"},{title:"Pricing",icon:{icon:"tabler-diamond"},to:"pages-pricing"},{title:"Misc",icon:{icon:"tabler-3d-cube-sphere"},children:[{title:"Coming Soon",to:"pages-misc-coming-soon"},{title:"Under Maintenance",to:"pages-misc-under-maintenance",target:"_blank"},{title:"Page Not Found - 404",to:{path:"/pages/misc/not-found"},target:"_blank"},{title:"Not Authorized - 401",to:{path:"/pages/misc/not-authorized"},target:"_blank"}]},{title:"Authentication",icon:{icon:"tabler-lock"},children:[{title:"Login",children:[{title:"Login v1",to:"pages-authentication-login-v1",target:"_blank"},{title:"Login v2",to:"pages-authentication-login-v2",target:"_blank"}]},{title:"Register",children:[{title:"Register v1",to:"pages-authentication-register-v1",target:"_blank"},{title:"Register v2",to:"pages-authentication-register-v2",target:"_blank"},{title:"Register Multi-Steps",to:"pages-authentication-register-multi-steps",target:"_blank"}]},{title:"Verify Email",children:[{title:"Verify Email v1",to:"pages-authentication-verify-email-v1",target:"_blank"},{title:"Verify Email v2",to:"pages-authentication-verify-email-v2",target:"_blank"}]},{title:"Forgot Password",children:[{title:"Forgot Password v1",to:"pages-authentication-forgot-password-v1",target:"_blank"},{title:"Forgot Password v2",to:"pages-authentication-forgot-password-v2",target:"_blank"}]},{title:"Reset Password",children:[{title:"Reset Password v1",to:"pages-authentication-reset-password-v1",target:"_blank"},{title:"Reset Password v2",to:"pages-authentication-reset-password-v2",target:"_blank"}]},{title:"Two Steps",children:[{title:"Two Steps v1",to:"pages-authentication-two-steps-v1",target:"_blank"},{title:"Two Steps v2",to:"pages-authentication-two-steps-v2",target:"_blank"}]}]},{title:"Wizard Pages",icon:{icon:"tabler-forms"},children:[{title:"Checkout",to:{name:"wizard-examples-checkout"}},{title:"Property Listing",to:{name:"wizard-examples-property-listing"}},{title:"Create Deal",to:{name:"wizard-examples-create-deal"}}]},{title:"Dialog Examples",icon:{icon:"tabler-square"},to:"pages-dialog-examples"},{title:"Front Pages",icon:{icon:"tabler-files"},children:[{title:"Landing",to:"front-pages-landing-page",target:"_blank"},{title:"Pricing",to:"front-pages-pricing",target:"_blank"},{title:"Payment",to:"front-pages-payment",target:"_blank"},{title:"Checkout",to:"front-pages-checkout",target:"_blank"},{title:"Help Center",to:"front-pages-help-center",target:"_blank"}]}]}],go=[{title:"Tables",icon:{icon:"tabler-layout-grid"},children:[{title:"Simple Table",icon:{icon:"tabler-table"},to:"tables-simple-table"},{title:"Data Table",icon:{icon:"tabler-layout-grid"},to:"tables-data-table"}]}],ho=[{title:"User Interface",icon:{icon:"tabler-color-swatch"},children:[{title:"Icons",icon:{icon:"tabler-brand-tabler"},to:"pages-icons"},{title:"Typography",icon:{icon:"tabler-square-letter-t"},to:"pages-typography"},{title:"Cards",icon:{icon:"tabler-id"},children:[{title:"Basic",to:"pages-cards-card-basic"},{title:"Advance",to:"pages-cards-card-advance"},{title:"Statistics",to:"pages-cards-card-statistics"},{title:"Widgets",to:"pages-cards-card-widgets"},{title:"Actions",to:"pages-cards-card-actions"}]},{title:"Components",icon:{icon:"tabler-toggle-left"},children:[{title:"Alert",to:"components-alert"},{title:"Avatar",to:"components-avatar"},{title:"Badge",to:"components-badge"},{title:"Button",to:"components-button"},{title:"Chip",to:"components-chip"},{title:"Dialog",to:"components-dialog"},{title:"Expansion Panel",to:"components-expansion-panel"},{title:"List",to:"components-list"},{title:"Menu",to:"components-menu"},{title:"Pagination",to:"components-pagination"},{title:"Progress Circular",to:"components-progress-circular"},{title:"Progress Linear",to:"components-progress-linear"},{title:"Snackbar",to:"components-snackbar"},{title:"Tabs",to:"components-tabs"},{title:"Timeline",to:"components-timeline"},{title:"Tooltip",to:"components-tooltip"}]},{title:"Extensions",icon:{icon:"tabler-box"},children:[{title:"Tour",to:"extensions-tour"},{title:"Swiper",to:"extensions-swiper"}]}]}],bo=[...uo,...lo,...mo,...ho,...po,...go,...co,...fo],vo={class:"app-title font-weight-bold leading-normal text-xl text-capitalize"},Jo={__name:"DefaultLayoutWithHorizontalNav",setup(t){const e=V(!1),o=V(null);return ot([e,o],()=>{e.value&&o.value&&o.value.fallbackHandle(),!e.value&&o.value&&o.value.resolveHandle()},{immediate:!0}),(n,i)=>{const r=At("RouterLink"),s=Qt,a=At("RouterView");return h(),C(u(Ee),{"nav-items":u(bo)},{navbar:_(()=>{var l;return[S(r,{to:"/",class:"app-logo d-flex align-center gap-x-3"},{default:_(()=>[S(u(he),{nodes:u(N).app.logo},null,8,["nodes"]),w("h1",vo,ft(u(N).app.title),1)]),_:1}),S(ve),S(de,{"trigger-btn-class":"ms-lg-n3"}),u(N).app.i18n.enable&&((l=u(N).app.i18n.langConfig)!=null&&l.length)?(h(),C(be,{key:0,languages:u(N).app.i18n.langConfig},null,8,["languages"])):mt("",!0),S(me),S(fe),S(pe,{class:"me-2"}),S(ge)]}),footer:_(()=>[S(ue)]),default:_(()=>[S(s,{ref_key:"refLoadingIndicator",ref:o},null,512),S(a,null,{default:_(({Component:l})=>[(h(),C(ce,{timeout:0,onFallback:i[0]||(i[0]=c=>e.value=!0),onResolve:i[1]||(i[1]=c=>e.value=!1)},{default:_(()=>[(h(),C(P(l)))]),_:2},1024))]),_:1})]),_:1},8,["nav-items"])}}};export{Jo as default};
