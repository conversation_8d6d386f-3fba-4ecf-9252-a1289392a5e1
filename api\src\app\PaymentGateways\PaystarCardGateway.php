<?php
namespace App\PaymentGateways;

use App\Contracts\PaymentGateway;
use Illuminate\Support\Facades\Http;
class PaystarCardGateway implements PaymentGateway
{
    private $APIKey;
    public function __construct(string $apiKey) {
        $this->APIKey = $apiKey;
    }
    public function processPayment(int $amount,array $data) {
        $payload = [
            "amount"=> $amount,
            "order_id"=> $data['order_id'],
            "callback"=> $data['callback'],
            // "sign"=> $data['sign'],

        ];
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '. $this->APIKey,
        ])->post('https://core.paystar.ir/api/card-to-card/payment/create', $payload);
        return $response;

    }
    public function verifyPayment(array $data) {
        $payload = [
            "ref_num"=> $data['refId']
        ];
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '. $this->APIKey
        ])->post('https://core.paystar.ir/api/card-to-card/payment/verify', $payload);
        return $response;

    }

}
