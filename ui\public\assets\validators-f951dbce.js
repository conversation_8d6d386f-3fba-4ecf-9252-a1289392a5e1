import{br as e,bs as i,bt as n,aL as a}from"./index-169996dc.js";const m=r=>e(r)||i(r)||r===!1?"این فیلد اجباری میباشد":!!String(r).trim().length||"این فیلد اجباری میباشد",d=r=>{if(n(r))return!0;const t=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return Array.isArray(r)?r.every(s=>t.test(String(s)))||"ایمیل وارد شده معتبر نمیباشد":t.test(String(r))||"ایمیل وارد شده معتبر نمیباشد"},f=r=>{var t=/^09\d{9}$/;return t.test(a(r))||"شماره تماس معتبر نمیباشد"};export{d as e,f as i,m as r};
