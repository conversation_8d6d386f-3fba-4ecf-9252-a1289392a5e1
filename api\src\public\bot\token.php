<?php

require __DIR__ .'/../../vendor/autoload.php';

use phpseclib3\Crypt\AES;
use phpseclib3\Crypt\RSA;
use phpseclib3\Crypt\RSA as CryptRSA;

use phpseclib3\Crypt\Random;
use phpseclib3\Crypt\PublicKeyLoader;





function decryptData($encryptedData,$encryptedAesKey,$iv,$privateKeyString){

    // 4. Decrypt the AES key using the recipient's RSA private key
    $rsaPrivate = PublicKeyLoader::load($privateKeyString);
    $decryptedAesKey = $rsaPrivate->decrypt($encryptedAesKey);

    // 5. Decrypt the data using the decrypted AES key
    $cipher = new AES('cbc');
    $cipher->setKey($decryptedAesKey);
    //$iv = Random::string($cipher->getBlockLength() >> 3);
    $cipher->setIV($iv);
    return $cipher->decrypt($encryptedData);  // This should output your original data string

}
