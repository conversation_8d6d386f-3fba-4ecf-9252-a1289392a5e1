import{_ as R,V as S}from"./VSkeletonLoader-4c44fbcf.js";import{k as G,j as H,r as m,H as K,o as r,c as w,b as l,e as s,d as u,aj as k,ak as O,al as _,a9 as A,af as E,a7 as Y,F as D,i as $,f as p,n as x,Y as J,v as Q,x as W,aK as X,z as Z,ad as ee,aM as N}from"./index-169996dc.js";import{E as y}from"./endpoints-454f23f6.js";import{a as v}from"./validations-4c0aab88.js";import{I as ae}from"./iconify-64e5a48d.js";import{V as te,t as le,a as se,b as oe,D as ne,c as ie}from"./VExpansionPanel-909a4c05.js";import{V as re}from"./VAlert-fc722507.js";import{V as P,a as I}from"./VRow-6c1d54f3.js";import{V as ue}from"./VTextarea-1c13d440.js";import"./index-00c7d20d.js";import"./VField-150a934a.js";import"./VInput-c4d3942a.js";const de=u("span",{class:""},"در این صفحه میتوانید سوالاتی که میخواهید برای ثبت سفارش محصول از کاربر بپرسید را تعریف کنید.",-1),ce=u("span",{class:""}," برای افزودن سوال روی دکمه + پایین صفحه کلیک کنید",-1),me={class:"d-flex justify-space-between align-center w-100"},pe={class:"d-flex align-center ga-2"},fe=["src"],Ae={__name:"[id].setting",setup(_e){G();const c=H(),i=m([]),f=m(!1),d=m(!1);m(null);const b=()=>{const a=Math.max(...i.value.map(e=>e.order))+1;i.value.push({key:"",type:"question",order:a})},g=async()=>{var a;try{f.value=!0;const{data:e}=await _.get(`${y.productAttributes}/product/${c.params.id}`);i.value=e.data}catch(e){await v((a=e==null?void 0:e.data)==null?void 0:a.errors,e==null?void 0:e.status)}finally{f.value=!1}},U=async a=>{var e;try{d.value=!0,await _.delete(y.productAttributes+"/"+a.id),await g()}catch(t){await v((e=t==null?void 0:t.data)==null?void 0:e.errors,t==null?void 0:t.status)}finally{d.value=!1}},z=(a,e)=>{N({text:"آیا مایل به حذف سوال هستید ؟",icon:"error",confirmButtonText:"بله",cancelButtonText:"خیر",showCloseButton:!0,showCancelButton:!0}).then(t=>{t.isConfirmed&&(e.id?U(e):i.value.splice(a,1))})},M=async a=>{var e;try{d.value=!0,await _.post(y.productAttributes,{type:"question",key:a.key,product_id:c.params.id,order:Number(a.order)}),await g()}catch(t){await v((e=t==null?void 0:t.data)==null?void 0:e.errors,t==null?void 0:t.status)}finally{d.value=!1}},C=async a=>{var e;try{await _.put(`${y.productAttributes}/${a.id}`,a)}catch(t){await v((e=t==null?void 0:t.data)==null?void 0:e.errors,t==null?void 0:t.status)}finally{}},j=async a=>{var e;for(const t in i.value){const n=i.value[t];n.loading=!0;const o={id:n.id,type:"message",key:n.key,product_id:Number((e=c==null?void 0:c.params)==null?void 0:e.id),order:Number.parseInt(t)};(async()=>{await C({...o}),n.loading=!1})()}},B=m([]),q=a=>{console.log("Dragged element:",a)};let T=null;const F=a=>{clearTimeout(T),a.loading=!0,T=setTimeout(()=>{L(a)},1e3)},L=a=>{if(!a.key)return N({icon:"error",text:"عنوان سوال نمی تواند خالی باشد.",confirmButtonText:"متوجه شدم"}),!1;a.loading&&(a.loading=!1),a.id?C(a):M(a)};return K(()=>{g()}),(a,e)=>{const t=R;return r(),w("div",null,[l(re,{class:"mb-3",border:"start",color:"warning"},{default:s(()=>[de,ce]),_:1}),u("div",null,[l(k,{icon:"tabler-plus",size:"x-large",class:"add-question",disabled:f.value,style:{"font-size":"2rem"},onClick:b},null,8,["disabled"]),l(O,{modelValue:d.value,"onUpdate:modelValue":e[0]||(e[0]=n=>d.value=n),persistent:"",width:"300"},{default:s(()=>[l(A,{width:"300"},{default:s(()=>[l(E,{class:"pt-3"},{default:s(()=>[l(Y,{indeterminate:"",height:8,class:"mb-0 mt-4"})]),_:1})]),_:1})]),_:1},8,["modelValue"]),l(P,null,{default:s(()=>[f.value?(r(),w(D,{key:0},$(15,n=>l(I,{key:n,cols:"12"},{default:s(()=>[l(A,null,{default:s(()=>[l(E,null,{default:s(()=>[l(S,{type:"image ,actions"})]),_:1})]),_:1})]),_:2},1024)),64)):(r(),p(I,{key:1,cols:12},{default:s(()=>{var n;return[(n=i.value)!=null&&n.length?(r(),p(te,{key:0,modelValue:B.value,"onUpdate:modelValue":e[2]||(e[2]=o=>B.value=o),multiple:""},{default:s(()=>[l(x(le),{modelValue:i.value,"onUpdate:modelValue":e[1]||(e[1]=o=>i.value=o),scroll:"",handle:".handle",class:"w-100",onEnd:j,onChange:q},{default:s(()=>[l(J,{type:"transition",name:"fade"},{default:s(()=>[(r(!0),w(D,null,$(i.value,(o,V)=>(r(),p(se,{key:o.id},{default:s(()=>[l(oe,{class:"handle"},{default:s(()=>[u("div",me,[u("div",pe,[u("img",{src:x(ne),class:"cursor-move",width:"21",height:"21"},null,8,fe),u("span",null,"سوال "+W(V+1),1)]),u("div",null,[o.loading?(r(),p(X,{key:0,indeterminate:"",color:"primary",style:{height:"25px",width:"25px","margin-top":"-20px"},class:"ml-2"})):Z("",!0),l(k,{variant:"tonal",color:"red",icon:"tabler-trash",size:"x-small",onClick:ee(h=>z(V,o),["stop"])},null,8,["onClick"])])])]),_:2},1024),l(ie,null,{default:s(()=>[l(ue,{modelValue:o.key,"onUpdate:modelValue":h=>o.key=h,label:"متن",placeholder:"متن",rows:"2",dir:"rtl",autofocus:"",class:"mt-4",onInput:h=>F(o)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])):(r(),p(P,{key:1},{default:s(()=>[l(t,null,{default:s(()=>[l(k,{variant:"flat",onClick:b},{default:s(()=>[l(x(ae),{icon:"tabler-plus"}),Q(" افزودن سوال ")]),_:1})]),_:1})]),_:1}))]}),_:1}))]),_:1})])])}}};export{Ae as default};
