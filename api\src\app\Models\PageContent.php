<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PageContent extends Model
{
    use HasFactory;
    protected $fillable = [
        'content_id',
        'caption',
        'like_count',
        'comments_count',
        'media_url',
        'media_type',
    ];
    public function page(): BelongsTo
    {
        return $this->belongsTo(Page::class);
    }
    public function galleries(): HasMany
    {
        return $this->hasMany(ProductsGallery::class);
    }
}
