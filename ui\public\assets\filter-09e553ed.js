import{d as $}from"./index-00c7d20d.js";import{ap as z,as as Q,b6 as L,c6 as G,r as K,a8 as P,by as H,at as J,bA as W,b as s,s as A,F as X,bc as Y,a4 as Z,c8 as q,aK as p,n as S,cx as ee,b_ as j,bR as te}from"./index-169996dc.js";import{m as ae,u as le,V as B}from"./VInput-c4d3942a.js";import{m as re,V as O}from"./VSelectionControl-8ecbcf09.js";const ce=z({indeterminate:Boolean,inset:Boolean,flat:Boolean,loading:{type:[<PERSON>olean,String],default:!1},...ae(),...re()},"VSwitch"),oe=Q()({name:"VSwitch",inheritAttrs:!1,props:ce(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,c){let{attrs:t,slots:a}=c;const i=L(e,"indeterminate"),u=L(e,"modelValue"),{loaderClasses:b}=G(e),{isFocused:F,focus:l,blur:d}=le(e),v=K(),h=P(()=>typeof e.loading=="string"&&e.loading!==""?e.loading:e.color),f=H(),r=P(()=>e.id||`switch-${f}`);function y(){i.value&&(i.value=!1)}function g(o){var n,k;o.stopPropagation(),o.preventDefault(),(k=(n=v.value)==null?void 0:n.input)==null||k.click()}return J(()=>{const[o,n]=W(t),k=B.filterProps(e),V=O.filterProps(e);return s(B,A({class:["v-switch",{"v-switch--flat":e.flat},{"v-switch--inset":e.inset},{"v-switch--indeterminate":i.value},b.value,e.class]},o,k,{modelValue:u.value,"onUpdate:modelValue":C=>u.value=C,id:r.value,focused:F.value,style:e.style}),{...a,default:C=>{let{id:D,messagesId:R,isDisabled:U,isReadonly:E,isValid:x}=C;const M={model:u,isValid:x};return s(O,A({ref:v},V,{modelValue:u.value,"onUpdate:modelValue":[w=>u.value=w,y],id:D.value,"aria-describedby":R.value,type:"checkbox","aria-checked":i.value?"mixed":void 0,disabled:U.value,readonly:E.value,onFocus:l,onBlur:d},n),{...a,default:w=>{let{backgroundColorClasses:_,backgroundColorStyles:m}=w;return s("div",{class:["v-switch__track",..._.value],style:m.value,onClick:g},[a["track-true"]&&s("div",{key:"prepend",class:"v-switch__track-true"},[a["track-true"](M)]),a["track-false"]&&s("div",{key:"append",class:"v-switch__track-false"},[a["track-false"](M)])])},input:w=>{let{inputNode:_,icon:m,backgroundColorClasses:N,backgroundColorStyles:T}=w;return s(X,null,[_,s("div",{class:["v-switch__thumb",{"v-switch__thumb--filled":m||e.loading},e.inset?void 0:N.value],style:e.inset?void 0:T.value},[a.thumb?s(Y,{defaults:{VIcon:{icon:m,size:"x-small"}}},{default:()=>[a.thumb({...M,icon:m})]}):s($,null,{default:()=>[e.loading?s(q,{name:"v-switch",active:!0,color:x.value===!1?void 0:h.value},{default:I=>a.loader?a.loader(I):s(p,{active:I.isActive,color:I.color,indeterminate:!0,size:"16",width:"2"},null)}):m&&s(Z,{key:String(m),icon:m,size:"x-small"},null)]})])])}})}})}),{}}}),ne=(e,c,t)=>e==null||c==null?-1:e.toString().toLocaleLowerCase().indexOf(c.toString().toLocaleLowerCase()),me=z({customFilter:Function,customKeyFilter:Object,filterKeys:[Array,String],filterMode:{type:String,default:"intersection"},noFilter:Boolean},"filter");function se(e,c,t){var F;const a=[],i=(t==null?void 0:t.default)??ne,u=t!=null&&t.filterKeys?j(t.filterKeys):!1,b=Object.keys((t==null?void 0:t.customKeyFilter)??{}).length;if(!(e!=null&&e.length))return a;e:for(let l=0;l<e.length;l++){const[d,v=d]=j(e[l]),h={},f={};let r=-1;if(c&&!(t!=null&&t.noFilter)){if(typeof d=="object"){const o=u||Object.keys(v);for(const n of o){const k=te(v,n),V=(F=t==null?void 0:t.customKeyFilter)==null?void 0:F[n];if(r=V?V(k,c,d):i(k,c,d),r!==-1&&r!==!1)V?h[n]=r:f[n]=r;else if((t==null?void 0:t.filterMode)==="every")continue e}}else r=i(d,c,d),r!==-1&&r!==!1&&(f.title=r);const y=Object.keys(f).length,g=Object.keys(h).length;if(!y&&!g||(t==null?void 0:t.filterMode)==="union"&&g!==b&&!y||(t==null?void 0:t.filterMode)==="intersection"&&(g!==b||!y))continue}a.push({index:l,matches:{...f,...h}})}return a}function ve(e,c,t,a){const i=K([]),u=K(new Map),b=P(()=>a!=null&&a.transform?S(c).map(l=>[l,a.transform(l)]):S(c));ee(()=>{const l=typeof t=="function"?t():S(t),d=typeof l!="string"&&typeof l!="number"?"":String(l),v=se(b.value,d,{customKeyFilter:{...e.customKeyFilter,...S(a==null?void 0:a.customKeyFilter)},default:e.customFilter,filterKeys:e.filterKeys,filterMode:e.filterMode,noFilter:e.noFilter}),h=S(c),f=[],r=new Map;v.forEach(y=>{let{index:g,matches:o}=y;const n=h[g];f.push(n),r.set(n.value,o)}),i.value=f,u.value=r});function F(l){return u.value.get(l.value)}return{filteredItems:i,filteredMatches:u,getMatches:F}}export{oe as V,me as m,ve as u};
