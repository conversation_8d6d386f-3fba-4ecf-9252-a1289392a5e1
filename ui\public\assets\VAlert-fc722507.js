import{bo as $,ap as D,au as L,aq as z,bl as w,bH as E,a_ as F,av as R,cg as j,aw as M,ar as N,ax as O,bJ as q,as as H,b6 as J,a8 as o,b1 as Q,bM as p,bm as G,bN as K,b3 as U,aE as W,ch as X,aA as Y,aC as Z,a1 as ee,aB as ae,b as t,bQ as te,a4 as le,bc as d,aj as se,s as ne}from"./index-169996dc.js";const oe=$("v-alert-title"),re=["success","info","warning","error"],ie=D({border:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["top","end","bottom","start"].includes(e)},borderColor:String,closable:Boolean,closeIcon:{type:L,default:"$close"},closeLabel:{type:String,default:"$vuetify.close"},icon:{type:[Boolean,String,Function,Object],default:null},modelValue:{type:Boolean,default:!0},prominent:Boolean,title:String,text:String,type:{type:String,validator:e=>re.includes(e)},...z(),...w(),...E(),...F(),...R(),...j(),...M(),...N(),...O(),...q({variant:"flat"})},"VAlert"),ue=H()({name:"VAlert",props:ie(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0},setup(e,v){let{emit:m,slots:a}=v;const r=J(e,"modelValue"),s=o(()=>{if(e.icon!==!1)return e.type?e.icon??`$${e.type}`:e.icon}),y=o(()=>({color:e.color??e.type,variant:e.variant})),{themeClasses:b}=Q(e),{colorClasses:f,colorStyles:k,variantClasses:P}=p(y),{densityClasses:V}=G(e),{dimensionStyles:C}=K(e),{elevationClasses:g}=U(e),{locationStyles:x}=W(e),{positionClasses:S}=X(e),{roundedClasses:_}=Y(e),{textColorClasses:A,textColorStyles:B}=Z(ee(e,"borderColor")),{t:h}=ae(),i=o(()=>({"aria-label":h(e.closeLabel),onClick(n){r.value=!1,m("click:close",n)}}));return()=>{const n=!!(a.prepend||s.value),T=!!(a.title||e.title),I=!!(a.close||e.closable);return r.value&&t(e.tag,{class:["v-alert",e.border&&{"v-alert--border":!!e.border,[`v-alert--border-${e.border===!0?"start":e.border}`]:!0},{"v-alert--prominent":e.prominent},b.value,f.value,V.value,g.value,S.value,_.value,P.value,e.class],style:[k.value,C.value,x.value,e.style],role:"alert"},{default:()=>{var c,u;return[te(!1,"v-alert"),e.border&&t("div",{key:"border",class:["v-alert__border",A.value],style:B.value},null),n&&t("div",{key:"prepend",class:"v-alert__prepend"},[a.prepend?t(d,{key:"prepend-defaults",disabled:!s.value,defaults:{VIcon:{density:e.density,icon:s.value,size:e.prominent?44:28}}},a.prepend):t(le,{key:"prepend-icon",density:e.density,icon:s.value,size:e.prominent?44:28},null)]),t("div",{class:"v-alert__content"},[T&&t(oe,{key:"title"},{default:()=>{var l;return[((l=a.title)==null?void 0:l.call(a))??e.title]}}),((c=a.text)==null?void 0:c.call(a))??e.text,(u=a.default)==null?void 0:u.call(a)]),a.append&&t("div",{key:"append",class:"v-alert__append"},[a.append()]),I&&t("div",{key:"close",class:"v-alert__close"},[a.close?t(d,{key:"close-defaults",defaults:{VBtn:{icon:e.closeIcon,size:"x-small",variant:"text"}}},{default:()=>{var l;return[(l=a.close)==null?void 0:l.call(a,{props:i.value})]}}):t(se,ne({key:"close-btn",icon:e.closeIcon,size:"x-small",variant:"text"},i.value),null)])]}})}}});export{ue as V,oe as a};
