<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CrmTrack extends Model
{
	use HasFactory;

	protected $fillable = [
		'crm_action_id',
		'user_id',
		'page_id',
		'transaction_id',
		'is_processed',
	];

	protected $casts = [
		'is_processed' => 'boolean',
	];

	public function action(): BelongsTo
	{
		return $this->belongsTo(CrmAction::class);
	}
}
