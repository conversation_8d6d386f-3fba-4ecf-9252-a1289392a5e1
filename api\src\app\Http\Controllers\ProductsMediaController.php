<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProductsMediaRequest;
use App\Http\Requests\UpdateProductsMediaRequest;
use App\Models\ProductsMedia;
use App\Models\Products;
use App\Models\Page;
use Illuminate\Support\Facades\Http;
use ProtoneMedia\LaravelFFMpeg\Exporters\EncodingException;
use FFMpeg\Exception\RuntimeException;
class ProductsMediaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $media = ProductsMedia::where('user_id',auth()->user()->id)->get();
        return $media;
    }

    /**
     * Show the form for creating a new resource.
     */
    private function generateRandomString($length = 10) {
        return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
    }
    public function create(StoreProductsMediaRequest $request)
    {
        $product = Products::where('user_id',auth()->user()->id)
            ->where('id',$request->product_id)
            ->first();
        if(!$product){
            return response()->json([
                'success'=>false,
                'message'=>'محصول نامعتبر است'
            ],422);
        }
        $media = new ProductsMedia();
        $media->product_id = $request->product_id;
        $media->type = $request->type;
        if($media->type!='text'){
            if(!$request->has("file")){
                return response()->json([
                    "success"=>false,
                    "message"=>"فایل اجباری میباشد"
                ],422);
            }
            $ext = $request->file->extension();
            if(!$this->validateFiles($media->type,$ext)){
                return response()->json([
                    "success"=>false,
                    "message"=>$this->validateFilesMessage($media->type)
                ],422);
            }
            $fileName = time().$this->generateRandomString();
            try {
                if($media->type=='image'){
                    $request->file->move(public_path('storage/media'), $fileName. '.' . $ext);
                    $u = public_path('storage/media').'/'.$fileName. '.' . $ext;
                    if($ext==='gif'){
                        $media->value = '/storage/media/'. $fileName. '.gif';
                    }else{
                        $uploaded = $this->converToPng($u,$ext);
                        $media->value = '/storage/media/'. $fileName. '.png';
                    }
                }else
                if($media->type == 'video'){
                    // $u = 'public/media/'.$fileName. '.ogg';
                    // \FFMpeg::open($request->file('file'))
                    // ->export()
                    // ->toDisk('local')
                    // ->inFormat(new \FFMpeg\Format\Video\Ogg)

                    // ->save($u);

                    // $media->value = '/storage/media/'. $fileName. '.ogg';
                    $request->file->move(public_path('storage/media'), $fileName. '.' . $ext);

                    $media->value = '/storage/media/'. $fileName. '.' .$ext;
                }else
                if($media->type == 'audio'){
                    $u = 'public/media/'.$fileName. '.wav';
                    // $request->file->move(public_path('storage/media'), $fileName. '.' . $ext);
                    // return $request->file->getClientMimeType();
                    \FFMpeg::open($request->file('file'))
                        ->export()
                        ->toDisk('local')
                        ->inFormat(new \FFMpeg\Format\Audio\Wav)
                        ->save($u);
                    $media->value = '/storage/media/'. $fileName. '.wav';
                }
            } catch (\Throwable $th) {
                return response()->json([
                    "success"=>false,
                    "message"=>"فایل مجاز نیست"
                ],422);
            }

            // if()
            $fullPath = 'https://dmplus.manymessage.com'.$media->value;
            $page = Page::where('user_id',auth()->user()->id)->first();
            $message = [
                "attachment"=>[
                    "type"=>$media->type,
                    "payload"=>[
                        "url"=>$fullPath,
                        "is_reusable"=>true
                    ]
                ]
            ];
            // $message = urlencode(json_encode($message));
            // $url = "https://graph.facebook.com/v20.0/me/message_attachments?platform=instagram&message=$message&access_token={$page->access_token}";
            // return $url;
            // $response = Http::post($url);
            // return $response;
            // $media->attachment_id = $response->object()->attachment_id??'';
        }else{
            $media->value = $request->text;
        }
        $media->user_id = auth()->user()->id;
        $media->save();

        return $media;
    }

    private function converToPng($file,$ext)
    {
        $image = imagecreatefromstring(
            file_get_contents($file)
        );
        ob_start();
        imagepng($image);

        $pngImageData = ob_get_contents();
        ob_end_clean();
        unlink($file);
        $file = str_replace('.'.$ext,'.png',$file);
        file_put_contents($file, $pngImageData);
        return $file;
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $media = ProductsMedia::where('user_id',auth()->user()->id)->whereId($id)->get();
        return $media;
    }
    public function productShow($id)
    {
        $media = ProductsMedia::where('user_id',auth()->user()->id)->where('product_id',$id)->get();
        return $media;
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProductsMediaRequest $request, $id)
    {
        $product = Products::where('user_id',auth()->user()->id)
            ->where('id',$request->product_id)
            ->first();
        if(!$product){
            return response()->json([
                'success'=>false,
                'message'=>'محصول نامعتبر است'
            ],422);
        }
        $media = ProductsMedia::where('id',$id)->first();
        if(!$media){
            return response()->json([
                'success'=>false,
                'message'=>'مدیا نامعتبر است'
            ],422);
        }
        $media->product_id = $request->product_id;
        $media->type = $request->type;
        if($media->type!='text'){
            if(!$request->has("file")){
                return response()->json([
                    "success"=>false,
                    "message"=>"فایل اجباری میباشد"
                ],422);
            }
            $ext = $request->file->extension();
            if(!$this->validateFiles($media->type,$ext)){
                return response()->json([
                    "success"=>false,
                    "message"=>$this->validateFilesMessage($media->type)
                ],422);
            }
            if(file_exists(public_path($media->value))){
                unlink(public_path($media->value));
            }
            $fileName = time().$this->generateRandomString();
            try {
                if($media->type=='image'){
                    $request->file->move(public_path('storage/media'), $fileName. '.' . $ext);
                    $u = public_path('storage/media').'/'.$fileName. '.' . $ext;
                    if($ext==='gif'){
                        $media->value = '/storage/media/'. $fileName. '.gif';
                    }else{
                        $uploaded = $this->converToPng($u,$ext);
                        $media->value = '/storage/media/'. $fileName. '.png';
                    }
                }else
                if($media->type == 'video'){
                    // $u = 'public/media/'.$fileName. '.ogg';
                    // try {
                    //     \FFMpeg::open($request->file('file'))
                    //         ->export()
                    //         ->toDisk('local')
                    //         ->inFormat(new \FFMpeg\Format\Video\Ogg)
                    //         ->save($u);
                    // } catch (\Throwable $th) {
                    //     return $th;
                    // }
                    $request->file->move(public_path('storage/media'), $fileName. '.' . $ext);

                    $media->value = '/storage/media/'. $fileName. '.' .$ext;
                }else
                if($media->type == 'audio'){
                    $u = 'public/media/'.$fileName. '.wav';
                    \FFMpeg::open($request->file('file'))
                        ->export()
                        ->toDisk('local')
                        ->inFormat(new \FFMpeg\Format\Audio\Aac)
                        ->save($u);
                    // $request->file->move(public_path('storage/media'), $fileName. '.' . $ext);
                    $media->value = '/storage/media/'. $fileName. '.wav';
                }
            } catch (\Throwable $th) {
                return response()->json([
                    "success"=>false,
                    "message"=>"فایل مجاز نیست"
                ],422);
            }

            // $imageName = time().'.'.$request->file->extension();
            // $request->file->move(public_path('storage/media'), $imageName);
            // $media->value = '/storage/media/'. $imageName;
            $fullPath = 'https://dmplus.manymessage.com'.$media->value;
            $page = Page::where('user_id',auth()->user()->id)->first();
            $message = [
                "attachment"=>[
                    "type"=>$media->type,
                    "payload"=>[
                        "url"=>$fullPath,
                        "is_reusable"=>true
                    ]
                ]
            ];
            $message = urlencode(json_encode($message));
            $url = "https://graph.facebook.com/v20.0/me/message_attachments?platform=instagram&message=$message&access_token={$page->access_token}";
            // return $url;
            // $response = Http::post($url);
            // return $response;
            // $media->attachment_id = $response->object()->attachment_id??'';
            $media->attachment_id = null;
        }else{
            $media->value = $request->text;
        }
        $media->user_id = auth()->user()->id;
        $media->save();

        return $media;
    }
    private function imageMimes(){
        return [
            'jpeg',
            'jpg',
            'png',
            'gif'

        ];
    }
    private function videoMimes(){
        return [
            'mp4',
            'mov',
            'webm',
            'ogg',
            'avi'
        ];
    }
    private function audioMimes(){
        return [
            'webm',
            'mp3',
            'mkv',
            'ogg',
            'aac',
            'm4a',
            'wav',
            'mp4'
        ];
    }
    private function validateFilesMessage($type){
        $msg = "فرمت فایل مجاز نمیباشد فرمت های مجاز: ";
        if($type==='image'){
            return $msg.implode(",",$this->imageMimes());
        }
        if($type==='video'){
            return $msg.implode(",",$this->videoMimes());
        }
        if($type==='audio'){
            return $msg.implode(",",$this->audioMimes());
        }
        return 'فرمت فایل مجاز نمیباشد';
    }
    private function validateFiles($type,$mimeType){
        if($type==='image'){
            if(in_array($mimeType,$this->imageMimes())){
                return true;
            }
        }
        if($type==='video'){
            if(in_array($mimeType,$this->videoMimes())){
                return true;
            }
        }
        if($type==='audio'){
            if(in_array($mimeType,$this->audioMimes())){
                return true;
            }
        }
        return false;
    }
    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $media = ProductsMedia::where('user_id',auth()->user()->id)->whereId($id)->firstOrFail();
        if($media->type!=='text'){
            if(file_exists(public_path($media->value))){
                unlink(public_path($media->value));
            }
        }
        $media->delete();
        return $media;
    }
}
