import{_ as o}from"./OrderScreenComponent-a61e9c71.js";import{o as r,f as t}from"./index-169996dc.js";import"./VSkeletonLoader-4c44fbcf.js";import"./OrderItem-e54d913a.js";import"./profile_ph-c1153e1f.js";import"./jalali-moment-c79ac113.js";import"./endpoints-454f23f6.js";import"./VTooltip-b4170ac2.js";import"./VChip-ccd89083.js";import"./index-00c7d20d.js";import"./VList-349a1ccf.js";import"./ssrBoot-c101cd97.js";import"./VDivider-12bfa926.js";import"./VMenu-2cfb0f14.js";import"./AppTextField-ca1883d7.js";import"./VInput-c4d3942a.js";import"./VTextField-a8984053.js";import"./VField-150a934a.js";import"./VRow-6c1d54f3.js";import"./VPagination-8a53e08f.js";const w={__name:"index",setup(m){return(p,i)=>(r(),t(o))}};export{w as default};
