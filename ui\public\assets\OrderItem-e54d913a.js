import{o as l,c as g,d as e,_ as ee,r as K,b as r,n as C,ai as te,v as d,x as c,e as a,aK as oe,f as m,s as h,z as P,F as ae,i as se,a6 as re,aj as q,a4 as ne,al as le,R as ie,U as de}from"./index-169996dc.js";import{a as ce}from"./profile_ph-c1153e1f.js";import{m as ue}from"./jalali-moment-c79ac113.js";import{E as fe}from"./endpoints-454f23f6.js";import{V}from"./VTooltip-b4170ac2.js";import{V as _}from"./VChip-ccd89083.js";import{V as me,a as he,b as _e}from"./VList-349a1ccf.js";import{V as ve}from"./VMenu-2cfb0f14.js";const ge={width:"15px",height:"15px",viewBox:"0 0 30 30",fill:"none",xmlns:"http://www.w3.org/2000/svg"},pe=["fill"],ye={__name:"ArrowDownComponent",props:{fill:String,default:"#0F0F0F"},setup(t){return(b,x)=>(l(),g("svg",ge,[e("path",{d:"M5.70711 9.71069C5.31658 10.1012 5.31658 10.7344 5.70711 11.1249L10.5993 16.0123C11.3805 16.7927 12.6463 16.7924 13.4271 16.0117L18.3174 11.1213C18.708 10.7308 18.708 10.0976 18.3174 9.70708C17.9269 9.31655 17.2937 9.31655 16.9032 9.70708L12.7176 13.8927C12.3271 14.2833 11.6939 14.2832 11.3034 13.8927L7.12132 9.71069C6.7308 9.32016 6.09763 9.32016 5.70711 9.71069Z",fill:t.fill},null,8,pe)]))}};const p=t=>(ie("data-v-40753732"),t=t(),de(),t),we={class:"card-order"},xe={class:"d-flex justify-space-between align-center"},ke={class:"d-flex ga-4 align-center"},Ce={class:"d-flex flex-column"},Ve={class:"font-weight-bold text-lg text-elipsis"},be={class:"font-weight-bold text-lg"},je={style:{"min-width":"68px !important"}},Ie={key:0},Me={style:{height:"25px !important",margin:"0 auto",width:"25px !important"}},Se={key:1},Le=p(()=>e("span",null,"رد شده",-1)),$e=p(()=>e("span",null,"پرداخت شده",-1)),Be=p(()=>e("span",null,"در انتظار پرداخت",-1)),De=p(()=>e("span",null,"تحویل شده",-1)),Fe=p(()=>e("span",null,"ویرایش وضعیت",-1)),Ne={class:"d-flex align-end flex-column align-self-start mt-1"},Ae={class:"text-sm"},Re={class:"text-sm"},He={class:"d-flex justify-space-between align-center mt-2"},Ye={class:"text-sm font-weight-bold"},Ze={class:"text-sm"},ze={class:"d-flex"},Ee={class:"text-sm mt-2"},Ge={class:"text-sm mt-2 mr-auto"},Oe={class:"texte"},Te={class:"font-weight-bold text-lg"},Ue={key:0,class:"d-flex align-center justify-center ga-2 w-100 mt-2"},Ke=p(()=>e("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"#ffffff",width:"16",height:"16"},[e("g",{id:"SVGRepo_bgCarrier","stroke-width":"0"}),e("g",{id:"SVGRepo_tracerCarrier","stroke-linecap":"round","stroke-linejoin":"round"}),e("g",{id:"SVGRepo_iconCarrier"},[e("g",{id:"Menu / More_Horizontal"},[e("g",{id:"Vector"},[e("path",{d:"M17 12C17 12.5523 17.4477 13 18 13C18.5523 13 19 12.5523 19 12C19 11.4477 18.5523 11 18 11C17.4477 11 17 11.4477 17 12Z",stroke:"#ffffff","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),e("path",{d:"M11 12C11 12.5523 11.4477 13 12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12Z",stroke:"#ffffff","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),e("path",{d:"M5 12C5 12.5523 5.44772 13 6 13C6.55228 13 7 12.5523 7 12C7 11.4477 6.55228 11 6 11C5.44772 11 5 11.4477 5 12Z",stroke:"#ffffff","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])])])],-1)),Pe={__name:"OrderItem",props:{order:Object,hasAction:{type:Boolean,default:!0}},emits:["delete"],setup(t,{emit:b}){const x=t,J=b,w=K(!1),k=K(!1),Q=[{title:"در انتظار پرداخت",value:"waiting"},{title:"کنسل شده",value:"canceled"},{title:"سفارش ثبت شده",value:"ordered"},{title:"رد شده",value:"rejected"},{title:"پرداخت شده",value:"approved"},{title:"تحویل شده",value:"delivered"}],W=async n=>{console.log(n);try{const i=x.order.id,v={status:n.value};w.value=!0;const{data:y}=await le.put(fe.orders+"/"+i,v);console.log(y),x.order.status=y.status,w.value=!1}catch(i){console.log(i),w.value=!1}},j=n=>n==="paystar"?"درگاه آنلاین (پی استار)":n==="paystarCard"?"کارت به کارت":n==="zarinpal"?"درگاه آنلاین (زرین پال)":n==="payping"?"درگاه آنلاین (پی پینگ)":n==="snapp"?"پرداخت اقساطی":"ندارد",I=(n,i)=>ue(n).locale("fa").format(i),X=n=>{J("delete",n)};return(n,i)=>{var y,M,S,L,$,B,D,F,N,A,R,H,Y,Z,z,E;const v=ye;return l(),g("div",we,[e("div",xe,[e("div",ke,[r(te,{src:((M=(y=t.order)==null?void 0:y.customer)==null?void 0:M.profile)??C(ce),alt:"Avatar",width:"65",height:"65",class:"avatar-order rounded-circle"},null,8,["src"]),e("div",Ce,[e("span",Ve,[d(c(((L=(S=t.order)==null?void 0:S.customer)==null?void 0:L.name)??((B=($=t.order)==null?void 0:$.customer)==null?void 0:B.username))+" ",1),r(V,{location:"top",transition:"scale-transition",activator:"parent"},{default:a(()=>{var o,s,u,f;return[e("span",be,c(((s=(o=t.order)==null?void 0:o.customer)==null?void 0:s.name)??((f=(u=t.order)==null?void 0:u.customer)==null?void 0:f.username)),1)]}),_:1})]),e("div",je,[r(ve,{modelValue:C(k),"onUpdate:modelValue":i[0]||(i[0]=o=>re(k)?k.value=o:null),transition:"scale-transition"},{activator:a(({props:o})=>[r(V,{location:"top"},{activator:a(({props:s})=>{var u,f,G,O,T,U;return[C(w)?(l(),g("div",Ie,[e("div",Me,[r(oe,{color:"primary",indeterminate:"",class:"h-100"})])])):(l(),g("div",Se,[((u=t.order)==null?void 0:u.status)==="rejected"||((f=t.order)==null?void 0:f.status)==="canceled"?(l(),m(_,h({key:0},h(o,s),{color:"red"}),{default:a(()=>[Le,r(v,{fill:"red",class:"mr-2"})]),_:2},1040)):((G=t.order)==null?void 0:G.status)==="approved"?(l(),m(_,h({key:1},h(o,s),{color:"green"}),{default:a(()=>[$e,r(v,{fill:"green",class:"mr-2"})]),_:2},1040)):((O=t.order)==null?void 0:O.status)==="waiting"||((T=t.order)==null?void 0:T.status)==="ordered"?(l(),m(_,h({key:2},h(o,s),{color:"#faad14"}),{default:a(()=>[Be,r(v,{fill:"#faad14",class:"mr-2"})]),_:2},1040)):((U=t.order)==null?void 0:U.status)==="delivered"?(l(),m(_,h({key:3},h(o,s),{color:"blue"}),{default:a(()=>[De,r(v,{fill:"#2196F3",class:"mr-2"})]),_:2},1040)):P("",!0)]))]}),default:a(()=>[Fe]),_:2},1024)]),default:a(()=>[r(me,null,{default:a(()=>[(l(),g(ae,null,se(Q,o=>r(he,{key:o.value,value:o.value,onClick:s=>W(o)},{default:a(()=>[r(_e,null,{default:a(()=>[d(c(o.title),1)]),_:2},1024)]),_:2},1032,["value","onClick"])),64))]),_:1})]),_:1},8,["modelValue"])])])]),e("div",Ne,[e("div",Ae,c(I((D=t.order)==null?void 0:D.created_at,"YYYY/M/D")),1),e("div",Re,c(I((F=t.order)==null?void 0:F.created_at,"HH:mm:ss")),1)])]),e("div",He,[e("div",Ye," محصول : "+c(((A=(N=t.order)==null?void 0:N.product)==null?void 0:A.name)??((H=(R=t.order)==null?void 0:R.product_prices)==null?void 0:H.name)),1),e("div",Ze,c(((Z=Number((Y=t.order)==null?void 0:Y.payment_amount))==null?void 0:Z.toLocaleString())??0)+" ریال ",1)]),e("div",ze,[e("div",Ee,[d(" ارسال : "),(z=t.order)!=null&&z.delivery_method?(l(),m(_,{key:0,color:"primary"},{default:a(()=>{var o,s;return[d(c((s=(o=t.order)==null?void 0:o.delivery_method)==null?void 0:s.name),1)]}),_:1})):(l(),m(_,{key:1,color:"red"},{default:a(()=>[d(" ندارد ")]),_:1}))]),e("div",Ge,[d(" پرداخت : "),(E=t.order)!=null&&E.payment_method?(l(),m(_,{key:0,color:"green"},{default:a(()=>{var o,s;return[e("span",Oe,[d(c(j((s=(o=t.order)==null?void 0:o.payment_method)==null?void 0:s.payment_method))+" ",1),r(V,{location:"top",transition:"scale-transition",activator:"parent"},{default:a(()=>{var u,f;return[e("span",Te,c(j((f=(u=t.order)==null?void 0:u.payment_method)==null?void 0:f.payment_method)),1)]}),_:1})])]}),_:1})):(l(),m(_,{key:1,color:"red"},{default:a(()=>[d(" ندارد ")]),_:1}))])]),t.hasAction?(l(),g("div",Ue,[r(q,{variant:"flat",class:"w-50",onClick:i[1]||(i[1]=o=>{var s;return n.$router.push(`/orders/${(s=t.order)==null?void 0:s.id}`)})},{prepend:a(()=>[Ke]),default:a(()=>[d(" اطلاعات بیشتر ")]),_:1}),r(q,{variant:"tonal",class:"w-50",color:"red",onClick:i[2]||(i[2]=o=>{var s;return X((s=t.order)==null?void 0:s.id)})},{prepend:a(()=>[r(ne,{icon:"tabler-trash"})]),default:a(()=>[d(" حذف ")]),_:1})])):P("",!0)])}}},at=ee(Pe,[["__scopeId","data-v-40753732"]]);export{at as _};
