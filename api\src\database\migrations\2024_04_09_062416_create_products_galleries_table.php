<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products_galleries', function (Blueprint $table) {
            $table->id();
//            $table->unsignedBigInteger("page_content_id");
            $table->string("media_id")->nullable();
            $table->text("path");
            $table->bigInteger('order')->default(0);
            $table->unsignedBigInteger('product_id');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->enum('from',['instagram','server'])->default('instagram');
            $table->unsignedBigInteger("user_id");
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
//            $table->foreign('page_content_id')->references('id')->on('page_contents');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products_galleries');
    }
};
