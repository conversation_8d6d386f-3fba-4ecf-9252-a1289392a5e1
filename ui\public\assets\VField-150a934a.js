import{ap as B,aq as w,ay as le,as as L,a8 as s,at as $,b as a,K as O,L as N,aG as ne,au as P,bG as x,c5 as te,aw as ie,ax as oe,b1 as se,c6 as re,aA as de,b0 as ce,by as ue,r as S,az as ve,a1 as fe,aC as ye,w as be,c7 as me,aW as ge,bj as Ce,bk as ke,c8 as he,bc as Ve,F as _e,s as Ie,c9 as Fe,ca as Pe}from"./index-169996dc.js";import{a as xe,b as Se}from"./index-00c7d20d.js";import{a as Be,b as we,u as Le,c as $e}from"./VInput-c4d3942a.js";const Re=B({active:Boolean,max:[Number,String],value:{type:[Number,String],default:0},...w(),...le({transition:{component:xe}})},"VCounter"),Oe=L()({name:"VCounter",functional:!0,props:Re(),setup(e,r){let{slots:i}=r;const I=s(()=>e.max?`${e.value} / ${e.max}`:String(e.value));return $(()=>a(ne,{transition:e.transition},{default:()=>[O(a("div",{class:["v-counter",e.class],style:e.style},[i.default?i.default({counter:I.value,max:e.max,value:e.value}):I.value]),[[N,e.active]])]})),{}}});const Te=B({floating:Boolean,...w()},"VFieldLabel"),_=L()({name:"VFieldLabel",props:Te(),setup(e,r){let{slots:i}=r;return $(()=>a(Be,{class:["v-field-label",{"v-field-label--floating":e.floating},e.class],style:e.style,"aria-hidden":e.floating||void 0},i)),{}}}),Ae=["underlined","outlined","filled","solo","solo-inverted","solo-filled","plain"],Ee=B({appendInnerIcon:P,bgColor:String,clearable:Boolean,clearIcon:{type:P,default:"$clear"},active:Boolean,centerAffix:{type:Boolean,default:void 0},color:String,baseColor:String,dirty:Boolean,disabled:{type:Boolean,default:null},error:Boolean,flat:Boolean,label:String,persistentClear:Boolean,prependInnerIcon:P,reverse:Boolean,singleLine:Boolean,variant:{type:String,default:"filled",validator:e=>Ae.includes(e)},"onClick:clear":x(),"onClick:appendInner":x(),"onClick:prependInner":x(),...w(),...te(),...ie(),...oe()},"VField"),De=L()({name:"VField",inheritAttrs:!1,props:{id:String,...we(),...Ee()},emits:{"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,r){let{attrs:i,emit:I,slots:l}=r;const{themeClasses:U}=se(e),{loaderClasses:W}=re(e),{focusClasses:j,isFocused:R,focus:y,blur:b}=Le(e),{InputIcon:F}=$e(e),{roundedClasses:q}=de(e),{rtlClasses:G}=ce(),m=s(()=>e.dirty||e.active),g=s(()=>!e.singleLine&&!!(e.label||l.label)),z=ue(),d=s(()=>e.id||`input-${z}`),X=s(()=>`${d.value}-messages`),T=S(),C=S(),A=S(),E=s(()=>["plain","underlined"].includes(e.variant)),{backgroundColorClasses:Y,backgroundColorStyles:H}=ve(fe(e,"bgColor")),{textColorClasses:D,textColorStyles:p}=ye(s(()=>e.error||e.disabled?void 0:m.value&&R.value?e.color:e.baseColor));be(m,n=>{if(g.value){const t=T.value.$el,c=C.value.$el;requestAnimationFrame(()=>{const u=me(t),o=c.getBoundingClientRect(),k=o.x-u.x,h=o.y-u.y-(u.height/2-o.height/2),f=o.width/.75,V=Math.abs(f-u.width)>1?{maxWidth:ge(f)}:void 0,Q=getComputedStyle(t),M=getComputedStyle(c),Z=parseFloat(Q.transitionDuration)*1e3||150,ee=parseFloat(M.getPropertyValue("--v-field-label-scale")),ae=M.getPropertyValue("color");t.style.visibility="visible",c.style.visibility="hidden",Ce(t,{transform:`translate(${k}px, ${h}px) scale(${ee})`,color:ae,...V},{duration:Z,easing:ke,direction:n?"normal":"reverse"}).finished.then(()=>{t.style.removeProperty("visibility"),c.style.removeProperty("visibility")})})}},{flush:"post"});const v=s(()=>({isActive:m,isFocused:R,controlRef:A,blur:b,focus:y}));function J(n){n.target!==document.activeElement&&n.preventDefault()}function K(n){var t;n.key!=="Enter"&&n.key!==" "||(n.preventDefault(),n.stopPropagation(),(t=e["onClick:clear"])==null||t.call(e,new MouseEvent("click")))}return $(()=>{var k,h,f;const n=e.variant==="outlined",t=!!(l["prepend-inner"]||e.prependInnerIcon),c=!!(e.clearable||l.clear),u=!!(l["append-inner"]||e.appendInnerIcon||c),o=()=>l.label?l.label({...v.value,label:e.label,props:{for:d.value}}):e.label;return a("div",Ie({class:["v-field",{"v-field--active":m.value,"v-field--appended":u,"v-field--center-affix":e.centerAffix??!E.value,"v-field--disabled":e.disabled,"v-field--dirty":e.dirty,"v-field--error":e.error,"v-field--flat":e.flat,"v-field--has-background":!!e.bgColor,"v-field--persistent-clear":e.persistentClear,"v-field--prepended":t,"v-field--reverse":e.reverse,"v-field--single-line":e.singleLine,"v-field--no-label":!o(),[`v-field--variant-${e.variant}`]:!0},U.value,Y.value,j.value,W.value,q.value,G.value,e.class],style:[H.value,e.style],onClick:J},i),[a("div",{class:"v-field__overlay"},null),a(he,{name:"v-field",active:!!e.loading,color:e.error?"error":typeof e.loading=="string"?e.loading:e.color},{default:l.loader}),t&&a("div",{key:"prepend",class:"v-field__prepend-inner"},[e.prependInnerIcon&&a(F,{key:"prepend-icon",name:"prependInner"},null),(k=l["prepend-inner"])==null?void 0:k.call(l,v.value)]),a("div",{class:"v-field__field","data-no-activator":""},[["filled","solo","solo-inverted","solo-filled"].includes(e.variant)&&g.value&&a(_,{key:"floating-label",ref:C,class:[D.value],floating:!0,for:d.value,style:p.value},{default:()=>[o()]}),a(_,{ref:T,for:d.value},{default:()=>[o()]}),(h=l.default)==null?void 0:h.call(l,{...v.value,props:{id:d.value,class:"v-field__input","aria-describedby":X.value},focus:y,blur:b})]),c&&a(Se,{key:"clear"},{default:()=>[O(a("div",{class:"v-field__clearable",onMousedown:V=>{V.preventDefault(),V.stopPropagation()}},[a(Ve,{defaults:{VIcon:{icon:e.clearIcon}}},{default:()=>[l.clear?l.clear({...v.value,props:{onKeydown:K,onFocus:y,onBlur:b,onClick:e["onClick:clear"]}}):a(F,{name:"clear",onKeydown:K,onFocus:y,onBlur:b},null)]})]),[[N,e.dirty]])]}),u&&a("div",{key:"append",class:"v-field__append-inner"},[(f=l["append-inner"])==null?void 0:f.call(l,v.value),e.appendInnerIcon&&a(F,{key:"append-icon",name:"appendInner"},null)]),a("div",{class:["v-field__outline",D.value],style:p.value},[n&&a(_e,null,[a("div",{class:"v-field__outline__start"},null),g.value&&a("div",{class:"v-field__outline__notch"},[a(_,{ref:C,floating:!0,for:d.value},{default:()=>[o()]})]),a("div",{class:"v-field__outline__end"},null)]),E.value&&g.value&&a(_,{ref:C,floating:!0,for:d.value},{default:()=>[o()]})])])}),{controlRef:A}}});function Ne(e){const r=Object.keys(De.props).filter(i=>!Fe(i)&&i!=="class"&&i!=="style");return Pe(e,r)}export{De as V,Oe as a,Ne as f,Ee as m};
