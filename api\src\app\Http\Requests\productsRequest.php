<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
class productsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if(Auth::user() == null) {
            return false;
        }
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "name"=>"required|max:255",
            "description"=>"required|max:2000",
            "post_id"=>[
                "required",
                Rule::unique('products')->whereNull('deleted_at')
            ],
            "delivery_methods"=>"array|min:0|max:5|exists:delivery_methods,id",
            "payment_methods"=>"array|min:0|max:4|exists:payment_methods,id",
            "triggers"=>"required",
            "price"=>"required|max:255",
            "multiple"=>"boolean",
            "multipleMessage"=>"max:255",
        ];
    }
    public function messages(): array
    {
        return [
            "name.required"=>"نام اجباری است",
            "name.max"=>"نام باید حداکثر 255 کاراکتر باشد",
            "description.required"=>"توضیحات اجباری است",
            "description.max"=>"توضیحات باید حداکثر 2000 کاراکتر باشد",
            "post_id.required"=>"پست اجباری است",
            'post_id.unique'=> 'برای این پست قبلا محصول تعریف شده است',
            "triggers.required"=>"فعال کننده اجباری است",
            "price.required"=>"قیمت اجباری است",
            "price.max"=>"قیمت مجاز نیست",
            "delivery_methods.required"=>"انتخاب روش ارسال اجباری است",
            "payment_methods.required"=>"انتخاب روش پرداخت اجباری است",
            "delivery_methods.min"=>"انتخاب روش ارسال اجباری است",
            "payment_methods.min"=>"انتخاب روش پرداخت اجباری است",
            "delivery_methods.max"=>"طول ورودی مجاز نیست",
            "payment_methods.max"=>"طول ورودی مجاز نیست",
            "delivery_methods.array"=>"روش ارسال نامعتبر است",
            "payment_methods.array"=>"روش پرداخت نامعتبر است",
            "delivery_methods.exists"=>"روش ارسال نامعتبر است",
            "payment_methods.exists"=>"روش پرداخت نامعتبر است",

        ];
    }
}
