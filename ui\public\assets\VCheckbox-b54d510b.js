import{m as A,V as t}from"./VCheckboxBtn-be9663e7.js";import{m as F,u as I,V as l}from"./VInput-c4d3942a.js";import{ap as B,bg as U,as as R,b6 as $,by as D,a8 as M,at as N,bA as j,b as u,s as r}from"./index-169996dc.js";const q=B({...F(),...U(A(),["inline"])},"VCheckbox"),G=R()({name:"VCheckbox",inheritAttrs:!1,props:q(),emits:{"update:modelValue":e=>!0,"update:focused":e=>!0},setup(e,c){let{attrs:d,slots:a}=c;const s=$(e,"modelValue"),{isFocused:n,focus:i,blur:m}=I(e),b=D(),V=M(()=>e.id||`checkbox-${b}`);return N(()=>{const[p,f]=j(d),k=l.filterProps(e),v=t.filterProps(e);return u(l,r({class:["v-checkbox",e.class]},p,k,{modelValue:s.value,"onUpdate:modelValue":o=>s.value=o,id:V.value,focused:n.value,style:e.style}),{...a,default:o=>{let{id:x,messagesId:h,isDisabled:P,isReadonly:C,isValid:y}=o;return u(t,r(v,{id:x.value,"aria-describedby":h.value,disabled:P.value,readonly:C.value},f,{error:y.value===!1,modelValue:s.value,"onUpdate:modelValue":g=>s.value=g,onFocus:i,onBlur:m}),a)}})}),{}}});export{G as V};
