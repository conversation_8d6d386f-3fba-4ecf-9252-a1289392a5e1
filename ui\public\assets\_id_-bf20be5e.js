import{V as N,_ as q}from"./VSkeletonLoader-4c44fbcf.js";import{_ as A}from"./Questions-b920d8a1.js";import{_ as I}from"./OrderItem-e54d913a.js";import{_ as L}from"./AppSelect-ec319d99.js";import{_ as R}from"./AppTextField-ca1883d7.js";import{E as x}from"./endpoints-454f23f6.js";import{V as U,a as z}from"./VRow-6c1d54f3.js";import{V as H}from"./VContainer-63fa55f4.js";import{o,f as _,e as l,b as t,c as f,F as v,i as M,y as Q,j as D,r as a,H as G,a9 as C,al as h,aa as w,d as J,aj as K,v as P,af as W}from"./index-169996dc.js";import"./profile_ph-c1153e1f.js";import"./jalali-moment-c79ac113.js";import"./VTooltip-b4170ac2.js";import"./VChip-ccd89083.js";import"./index-00c7d20d.js";import"./VList-349a1ccf.js";import"./ssrBoot-c101cd97.js";import"./VDivider-12bfa926.js";import"./VMenu-2cfb0f14.js";import"./VInput-c4d3942a.js";import"./VSelect-d6fde9f4.js";import"./VTextField-a8984053.js";import"./VField-150a934a.js";import"./VCheckboxBtn-be9663e7.js";import"./VSelectionControl-8ecbcf09.js";const X={__name:"SkeletonChat",setup(B){const i=r=>r%2===0?"align-self-end":"align-self-start";return(r,b)=>(o(),_(H,null,{default:l(()=>[t(U,{class:"d-flex flex-column"},{default:l(()=>[(o(),f(v,null,M(12,c=>t(z,{key:c,class:Q(i(c)),cols:"6",md:"1"},{default:l(()=>[t(N,{type:"chip",class:"skeleton-bubble"})]),_:2},1032,["class"])),64))]),_:1})]),_:1}))}},Y={class:"d-flex flex-column flex-md-row justify-space-between align-center ga-4"},Ce={__name:"[id]",setup(B){const i=D(),r=a(),b=a(null),c=a(null),y=a(null),$=a(0),s=a(!1);a();const j=a(null),g=a(null),V=[{state:"در انتظار پرداخت",abbr:"waiting"},{state:"کنسل شده",abbr:"canceled"},{state:"سفارش ثبت شده",abbr:"ordered"},{state:"رد شده",abbr:"rejected"},{state:"پرداخت شده",abbr:"approved"},{state:"تحویل شده",abbr:"delivered"}],m=a(""),d=a({state:"در انتظار پرداخت",abbr:"waiting"}),O=async()=>{try{const n=i.params.id,e={delivery_tracking_code:m.value,status:d.value.abbr};s.value=!0;const{data:p}=await h.put(x.orders+"/"+n,e);k(),s.value=!1}catch{s.value=!1}},k=async()=>{var n;try{s.value=!0;const{data:e}=await h.get(x.orders+"/"+i.params.id);g.value=e,r.value=e.order_attributes,$.value=e.order_attributes.length,b.value=(n=e==null?void 0:e.customer)==null?void 0:n.name,y.value=e==null?void 0:e.status,m.value=e==null?void 0:e.delivery_tracking_code,c.value=e.quantity,d.value=V.filter(p=>p.abbr===y.value)[0],j.value=e==null?void 0:e.product_prices}catch{}finally{s.value=!1}};return G(()=>{k()}),(n,e)=>{const p=R,E=L,S=I,T=A,F=q;return o(),f(v,null,[t(C,{class:"mb-5"},{default:l(()=>[t(w,null,{default:l(()=>[J("div",Y,[t(p,{modelValue:m.value,"onUpdate:modelValue":e[0]||(e[0]=u=>m.value=u),label:"کد پیگیری مرسوله پستی",density:"compact",loading:s.value,placeholder:"کد پیگیری مرسوله پستی",class:"w-100"},null,8,["modelValue","loading"]),t(E,{modelValue:d.value,"onUpdate:modelValue":e[1]||(e[1]=u=>d.value=u),items:V,"item-title":"state","item-value":"abbr","return-object":"","single-line":"","menu-props":{transition:"scroll-y-transition"},label:"وضعیت",placeholder:"وضعیت",class:"w-100"},null,8,["modelValue"]),t(K,{color:"primary",class:"align-self-start align-self-md-end w-100",style:{"flex-shrink":"2"},onClick:O},{default:l(()=>[P(" ذخیره ")]),_:1})])]),_:1})]),_:1}),t(C,null,{default:l(()=>[t(W,null,{default:l(()=>[t(S,{order:g.value,"has-action":!1},null,8,["order"])]),_:1}),t(w,null,{default:l(()=>{var u;return[s.value?(o(),_(X,{key:0})):(o(),f(v,{key:1},[(u=r.value)!=null&&u.length?(o(),_(T,{key:0,attrs:r.value,class:"mx-auto"},null,8,["attrs"])):(o(),_(F,{key:1,class:"mx-auto"}))],64))]}),_:1})]),_:1})],64)}}};export{Ce as default};
