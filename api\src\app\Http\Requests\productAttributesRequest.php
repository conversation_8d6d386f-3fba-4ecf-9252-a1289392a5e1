<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class productAttributesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if(auth()->user() == null) {
            return false;
        }
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type'=>'required|max:500',
            'key'=>'required|max:500',
//            'value'=>'required|max:500',
            'product_id'=>'required|exists:App\Models\Products,id',
            'order'=>'nullable|integer',

        ];
    }
    public function messages(): array
    {
        return [
            'type.required'=>'تایپ اجباری است',
            'type.max'=>'تایپ نمیتواند بیشتر 500 کاراکتر باشد',
            'key.required'=>'پیام اجباری است',
            'key.max'=>'پیام نمیتواند بیشتر از 500 کاراکتر باشد',
//            'value.required'=>'please enter value',
//            'value.max'=>'value can not more than 500 character',
            'product_id.required'=>'ارسال محصول اجباری است',
            'product_id.exists'=> 'محصول معتبر نیست',
            'product_id.id'=> 'محصول معتبر نیست'
        ];
    }
}
