<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;
use App\Models\order;
use App\Models\Products;
use Illuminate\Support\Facades\Http;

class DeleteWebhookStates implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    protected $state;
    protected $id;
    protected $access_token;
    protected $orderId;
    public $tries = 1;
    public function __construct($state,$id,$access_token,$orderId)
    {
        $this->state = $state;
        $this->id = $id;
        $this->access_token = $access_token;
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $data = Redis::get($this->state);
        \Log::info($data);
        \Log::info("json==============");
        $this->CheckOrder();
        if(gettype($data)!=='NULL'){
            $json = json_decode($data,true);
            if($json['order']!=null){
                if($json['order']==$this->orderId){
                    Redis::delete($this->state);
                }
            }
        }
    }
    private function CheckOrder(){
        $order = order::whereId($this->orderId)->first();
        $trigger = Products::whereId($order->product_id)->with(['deliveryMethods','paymentMethods'])->first();
        if(!$trigger){
            return;
        }
        $deliveryMethodsExist = $trigger->deliveryMethods()->count();
        $paymentMethodsExist = $trigger->paymentMethods()->count();
        if($order->status=='waiting'
            ||(($order->status=='ordered'&&$deliveryMethodsExist&&$order->delivery_method_id===null)
            ||($order->status=='ordered'&&$paymentMethodsExist&&$order->payment_method_id===null))
        ){

            $order->status = 'canceled';
            $order->save();
        }
    }
    private function sendMessageToUser($msg){
        $payload = ['recipient'=>['id'=>$this->id],'message'=>['text'=>$msg]];
        $facebookUrl='https://graph.facebook.com/v14.0/me/messages?access_token='.$this->access_token;
        $payload = [
            'url' => $facebookUrl,
            'payload' => json_encode($payload)
        ];
        $response = Http::asForm()->post('https://storyreply.manymessage.com/chatbot/api.php', $payload);
        return $response;
    }
}
