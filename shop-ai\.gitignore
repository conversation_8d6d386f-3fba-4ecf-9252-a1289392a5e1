# Node modules
node_modules/

# Build output
dist/
build/

# Environment variables
.env
.env.*.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
*.log

# OS-specific files
.DS_Store
Thumbs.db

# IDEs and editors
.vscode/
.idea/
*.sw?

# Optional: TypeScript cache
*.tsbuildinfo

# Optional: coverage reports
coverage/

# Optional: local development files
*.local

# Docker
docker-compose.override.yml

# Optional: NestJS CLI files
.nest-cli.json
