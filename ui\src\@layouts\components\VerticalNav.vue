<script setup>
import { layoutConfig } from "@layouts";
import {
  VerticalNavGroup,
  VerticalNavLink,
  VerticalNavSectionTitle,
} from "@layouts/components";
import { useLayoutConfigStore } from "@layouts/stores/config";
import { injectionKeyIsVerticalNavHovered } from "@layouts/symbols";
import { onMounted, ref } from "vue";
import { PerfectScrollbar } from "vue3-perfect-scrollbar";
import { VNodeRenderer } from "./VNodeRenderer";

const props = defineProps({
  tag: {
    type: [String, Object, Function],
    required: false,
    default: "aside",
  },
  navItems: {
    type: null,
    required: true,
  },
  isOverlayNavActive: {
    type: Boolean,
    required: true,
  },
  toggleIsOverlayNavActive: {
    type: Function,
    required: true,
  },
});

const refNav = ref();
const isHovered = useElementHover(refNav);

provide(injectionKeyIsVerticalNavHovered, isHovered);

const configStore = useLayoutConfigStore();

const resolveNavItemComponent = (item) => {
  if ("heading" in item) return VerticalNavSectionTitle;
  if ("children" in item) return VerticalNavGroup;

  return VerticalNavLink;
};

/*ℹ️ Close overlay side when route is changed
Close overlay vertical nav when link is clicked
*/
const route = useRoute();

watch(
  () => route.name,
  () => {
    props.toggleIsOverlayNavActive(false);
  },
);

const isVerticalNavScrolled = ref(false);
const updateIsVerticalNavScrolled = (val) =>
  (isVerticalNavScrolled.value = val);

const handleNavScroll = (evt) => {
  isVerticalNavScrolled.value = evt.target.scrollTop > 0;
};

const hideTitleAndIcon = configStore.isVerticalNavMini(isHovered);

// Add this method to generate flowers
const generateFlowers = () => {
  const navHeader = document.querySelector(".nav-header");
  for (let i = 0; i < 50; i++) {
    const flower = document.createElement("div");

    flower.classList.add("flower", `flower${Math.ceil(Math.random() * 3)}`);
    flower.style.left = `${Math.random() * 100}%`;
    flower.style.animationDuration = `${Math.random() * 5 + 5}s`; // Duration from 5 to 10 seconds
    flower.style.animationDelay = `-${Math.random() * 2}s`; // Starts at different times, including immediate start
    // navHeader.appendChild(flower)
  }
};

//TODO: ************* REMOVE THESE LINES FOR PRODUCTION ************

// const userPhoneNumber = useCookie("mobile");

// const allowedCommentAINumbers = [
//   "09335251871",
//   "09981382152",
//   "09366845520",
//   "09398187046",
//   "09337533195"
// ];

// const filteredNavItems = props.navItems.filter((item) => {
//   if (allowedCommentAINumbers.includes(userPhoneNumber.value)) {
//     return item;
//   } else {
//     return item.title !== "کامنت AI";
//   }
// });
//*********************************************************************

onMounted(() => {
  generateFlowers();
});
</script>

<template>
  <Component
    :is="props.tag"
    ref="refNav"
    class="layout-vertical-nav"
    :class="[
      {
        'overlay-nav': configStore.isLessThanOverlayNavBreakpoint,
        hovered: isHovered,
        visible: isOverlayNavActive,
        scrolled: isVerticalNavScrolled,
      },
    ]"
  >
    <!-- 👉 Header -->
    <div id="" class="nav-header">
      <slot name="nav-header">
        <RouterLink to="/" class="app-logo app-title-wrapper">
          <VNodeRenderer :nodes="layoutConfig.app.logo" />

          <Transition name="vertical-nav-app-title">
            <h1
              v-show="!hideTitleAndIcon"
              class="app-logo-title leading-normal"
            >
              {{ layoutConfig.app.title }}
            </h1>
          </Transition>
        </RouterLink>
        <!-- 👉 Vertical nav actions -->
        <!-- Show toggle collapsible in >md and close button in <md -->
        <Component
          :is="layoutConfig.app.iconRenderer || 'div'"
          v-show="configStore.isVerticalNavCollapsed"
          class="header-action d-none nav-unpin"
          :class="configStore.isVerticalNavCollapsed && 'd-lg-block'"
          v-bind="layoutConfig.icons.verticalNavUnPinned"
          @click="
            configStore.isVerticalNavCollapsed =
              !configStore.isVerticalNavCollapsed
          "
        />
        <Component
          :is="layoutConfig.app.iconRenderer || 'div'"
          v-show="!configStore.isVerticalNavCollapsed"
          class="header-action d-none nav-pin"
          :class="!configStore.isVerticalNavCollapsed && 'd-lg-block'"
          v-bind="layoutConfig.icons.verticalNavPinned"
          @click="
            configStore.isVerticalNavCollapsed =
              !configStore.isVerticalNavCollapsed
          "
        />
        <Component
          :is="layoutConfig.app.iconRenderer || 'div'"
          class="header-action d-lg-none"
          v-bind="layoutConfig.icons.close"
          @click="toggleIsOverlayNavActive(false)"
        />
      </slot>
    </div>

    <slot name="before-nav-items">
      <div class="vertical-nav-items-shadow" />
    </slot>

    <!--    <div v-show="!hideTitleAndIcon">-->
    <!--      <Transition name="vertical-nav-app-title">-->
    <!--        <div class="d-flex align-center gap-2 pa-4">-->
    <!--          <VImg-->
    <!--            v-if="userData.insta_pic"-->
    <!--            :src="userData.insta_pic"-->
    <!--            width="40"-->
    <!--            height="40"-->
    <!--            cover-->
    <!--            rounded-->
    <!--          />-->
    <!--          <div-->
    <!--            v-else-->
    <!--            class="profile-avatar"-->
    <!--          >-->
    <!--            <VIcon-->
    <!--              icon="tabler-user"-->
    <!--              color="rgb(var(&#45;&#45;v-theme-primary))"-->
    <!--            />-->
    <!--          </div>-->
    <!--          <div class="d-flex flex-column">-->
    <!--            <strong>{{ userData.name || "" }} {{ userData.last_name || "" }}</strong>-->
    <!--            <div class="d-flex gap-1">-->
    <!--              <VChip-->
    <!--                rounded="0"-->
    <!--                style="border-radius: 4px !important;font-size: 9px"-->
    <!--              >-->
    <!--                {{ userData.mobile || userData.phone }}-->
    <!--              </VChip>-->
    <!--            </div>-->
    <!--          </div>-->
    <!--        </div>-->
    <!--      </Transition>-->
    <!--    </div>-->

    <slot
      name="nav-items"
      :update-is-vertical-nav-scrolled="updateIsVerticalNavScrolled"
    >
      <PerfectScrollbar
        :key="configStore.isAppRTL"
        tag="ul"
        class="nav-items"
        :options="{ wheelPropagation: false }"
        @ps-scroll-y="handleNavScroll"
      >
        <!--        TODO: REPLACE filteredNavItems WITH navItems FOR PRODUCTION      -->
        <Component
          :is="resolveNavItemComponent(item)"
          v-for="(item, index) in navItems"
          :key="index"
          :item="item"
        />
      </PerfectScrollbar>
    </slot>
  </Component>
</template>

<style lang="scss" scoped>
.app-logo {
  display: flex;
  align-items: center;
  column-gap: 0.75rem;

  .app-logo-title {
    color: rgb(var(--v-theme-on-background));
    font-size: 1.375rem;
    font-weight: 700;
    line-height: 1.75rem;
    text-transform: capitalize;
  }
}
</style>

<style lang="scss">
@use "@configured-variables" as variables;
@use "@layouts/styles/mixins";

// flowers
@keyframes fall {
  0% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(calc(100% + 50px));
  } /* Adjust based on nav-header height, adding flower height to ensure it starts just off screen */
}

.nav-header {
  position: relative; /* This will contain the absolutely positioned flowers */
}

.flower {
  position: absolute;
  top: 0; /* Start from the top of the parent element */
  animation: fall 3s linear infinite; /* Adjust time as needed */
  z-index: 1000; /* Ensure it's above other elements but below fixed overlays */
  display: none;
}

/* Example flower styles, you can add more for variety */
.flower1 {
  width: 10px;
  height: 10px;
  background: url("https://uploadkon.ir/uploads/a02d21_24flower-svgrepo-com.svg")
    no-repeat center center;
  background-size: contain;
}

/* Add more flower styles as needed */

// 👉 Vertical Nav
.layout-vertical-nav {
  position: fixed;
  z-index: variables.$layout-vertical-nav-z-index;
  display: flex;
  flex-direction: column;
  block-size: 100%;
  inline-size: variables.$layout-vertical-nav-width;
  inset-block-start: 0;
  inset-inline-start: 0;
  transition:
    inline-size 0.25s ease-in-out,
    box-shadow 0.25s ease-in-out;
  will-change: transform, inline-size;

  .nav-header {
    display: flex;
    align-items: center;

    .header-action {
      cursor: pointer;

      @at-root {
        #{variables.$selector-vertical-nav-mini} .nav-header .header-action {
          &.nav-pin,
          &.nav-unpin {
            display: none !important;
          }
        }
      }
    }
  }

  .app-title-wrapper {
    margin-inline-end: auto;
  }

  .nav-items {
    block-size: 100%;

    // ℹ️ We no loner needs this overflow styles as perfect scrollbar applies it
    // overflow-x: hidden;

    // // ℹ️ We used `overflow-y` instead of `overflow` to mitigate overflow x. Revert back if any issue found.
    // overflow-y: auto;
  }

  .nav-item-title {
    overflow: hidden;
    margin-inline-end: auto;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 👉 Collapsed
  .layout-vertical-nav-collapsed & {
    &:not(.hovered) {
      inline-size: variables.$layout-vertical-nav-collapsed-width;
    }
  }
}

// Small screen vertical nav transition
@media (max-width: 1279px) {
  .layout-vertical-nav {
    &:not(.visible) {
      transform: translateX(-#{variables.$layout-vertical-nav-width});

      @include mixins.rtl {
        transform: translateX(variables.$layout-vertical-nav-width);
      }
    }

    transition: transform 0.25s ease-in-out;
  }
}
</style>
