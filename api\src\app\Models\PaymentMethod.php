<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentMethod extends Model
{
    use HasFactory,SoftDeletes;
    protected $fillable = [
        'page_id',
        'user_id',
        'token',
        'payment_method',
        'secret',
        'data'
    ];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Products::class);
    }
    public function page(): BelongsTo
    {
        return $this->belongsTo(Page::class);
    }
    public function orders(): Has<PERSON><PERSON>
    {
        return $this->hasMany(order::class);
    }
}
