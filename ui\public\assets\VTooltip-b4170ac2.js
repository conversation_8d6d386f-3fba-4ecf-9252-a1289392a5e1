import{ap as S,bg as h,bx as p,as as x,b6 as O,b7 as T,by as k,a8 as e,r as w,s as u,at as A,bz as d,b as C,bi as I}from"./index-169996dc.js";const R=S({id:String,text:String,...h(p({closeOnBack:!1,location:"end",locationStrategy:"connected",eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:"auto",scrim:!1,scrollStrategy:"reposition",transition:!1}),["absolute","persistent"])},"VTooltip"),$=x()({name:"VTooltip",props:R(),emits:{"update:modelValue":t=>!0},setup(t,v){let{slots:a}=v;const n=O(t,"modelValue"),{scopeId:g}=T(),f=k(),r=e(()=>t.id||`v-tooltip-${f}`),l=w(),m=e(()=>t.location.split(" ").length>1?t.location:t.location+" center"),b=e(()=>t.origin==="auto"||t.origin==="overlap"||t.origin.split(" ").length>1||t.location.split(" ").length>1?t.origin:t.origin+" center"),V=e(()=>t.transition?t.transition:n.value?"scale-transition":"fade-transition"),y=e(()=>u({"aria-describedby":r.value},t.activatorProps));return A(()=>{const P=d.filterProps(t);return C(d,u({ref:l,class:["v-tooltip",t.class],style:t.style,id:r.value},P,{modelValue:n.value,"onUpdate:modelValue":o=>n.value=o,transition:V.value,absolute:!0,location:m.value,origin:b.value,persistent:!0,role:"tooltip",activatorProps:y.value,_disableGlobalStack:!0},g),{activator:a.activator,default:function(){var c;for(var o=arguments.length,s=new Array(o),i=0;i<o;i++)s[i]=arguments[i];return((c=a.default)==null?void 0:c.call(a,...s))??t.text}})}),I({},l)}});export{$ as V};
