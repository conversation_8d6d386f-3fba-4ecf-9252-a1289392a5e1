<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->enum('status',['waiting','canceled','ordered','approved','rejected','delivered'])->default('waiting');
            $table->string('ref_num')->nullable();
            $table->string('payment_amount')->nullable();
            $table->text('token')->nullable();
            $table->string('card_number')->nullable();
            $table->string('tracking_code')->nullable();
            $table->string('delivery_tracking_code')->nullable();
            $table->string('redirectURL')->nullable();
            $table->unsignedBigInteger('quantity')->nullable();
            $table->text('tag')->nullable();
            $table->unsignedBigInteger('product_prices_id')->nullable();
            $table->foreign('product_prices_id')->references('id')->on('product_prices')->onDelete('set null');
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('set null');
            $table->unsignedBigInteger('product_id')->nullable();
            $table->foreign('product_id')->references('id')->on('products')->onDelete('set null');
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unsignedBigInteger('page_id');
            $table->foreign('page_id')->references('id')->on('pages')->onDelete('cascade');
            $table->unsignedBigInteger('delivery_method_id')->nullable();
            $table->foreign('delivery_method_id')->references('id')->on('delivery_methods')->onDelete('set null');
            $table->unsignedBigInteger('payment_method_id')->nullable();
            $table->foreign('payment_method_id')->references('id')->on('payment_methods')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
