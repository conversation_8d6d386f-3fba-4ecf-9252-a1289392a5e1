<?php

namespace App\Http\Controllers\Ai;

use App\Services\Ai\AiSettingService;
use Illuminate\Http\JsonResponse;

class AiSettingDisplayController extends AbstractAiSettingController
{
    public function __construct(AiSettingService $aiSettingService)
    {
        parent::__construct($aiSettingService);
    }

    public function show(): JsonResponse
    {
        $this->authUser = auth()->user();
        $aiSettings = $this->aiSettingService->getActiveUserAiSettings($this->authUser->id);

        if ($aiSettings->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'AI setting not found.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data'    => $aiSettings,
        ], 200);
    }
}
