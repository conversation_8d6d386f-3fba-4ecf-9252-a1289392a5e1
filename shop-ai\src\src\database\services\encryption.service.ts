import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

@Injectable()
export class EncryptionService {
  constructor(private configService: ConfigService) {}

  public decryptToken(encryptedAt: string): string {
    try {
      const normalizedInput = encryptedAt.replace(/\\\//g, '/');
      const encrypted = JSON.parse(normalizedInput);

      if (!Array.isArray(encrypted) || encrypted.length !== 3) {
        throw new Error('Invalid encrypted token format');
      }

      // Convert base64 strings to buffers
      const encryptedData = Buffer.from(encrypted[0], 'base64');
      const encryptedAesKey = Buffer.from(encrypted[1], 'base64');
      const iv = Buffer.from(encrypted[2], 'base64');

      return this.decryptData(encryptedData, encryptedAesKey, iv);
    } catch (error) {
      console.error('Decryption process error:', error);
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }

  private decryptData(
    encryptedData: Buffer,
    encryptedAesKey: Buffer,
    iv: Buffer,
  ): string {
    const privateKeyString = this.configService.get<string>('PRIVATE_KEY');
    const password = this.configService.get<string>('PRIVATE_KEY_PASSWORD');

    if (!privateKeyString || !password) {
      throw new Error('Missing decryption configuration');
    }

    try {
      const privateKey = crypto.createPrivateKey({
        key: privateKeyString,
        passphrase: password,
        format: 'pem',
        type: 'pkcs8',
      });

      const decryptedAesKey = crypto.privateDecrypt(
        {
          key: privateKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
          oaepHash: 'sha256',
        },
        encryptedAesKey,
      );

      const decipher = crypto.createDecipheriv(
        'aes-256-cbc',
        decryptedAesKey,
        iv,
      );

      let decrypted = decipher.update(encryptedData);
      decrypted = Buffer.concat([decrypted, decipher.final()]);

      return decrypted.toString('utf8');
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }
}
