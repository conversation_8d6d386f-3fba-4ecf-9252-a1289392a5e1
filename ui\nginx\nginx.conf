user			nginx;
pid			/run/nginx.pid;
worker_processes	auto;
worker_rlimit_nofile	65535;

# Load modules
include	/etc/nginx/modules-enabled/*.conf;

events {
	multi_accept		on;
	worker_connections	65535;
}

http {
	charset			utf-8;
	sendfile		on;
	tcp_nopush		on;
	tcp_nodelay		on;
	server_tokens		off;
	log_not_found		off;
	types_hash_max_size	2048;
	types_hash_bucket_size	64;
	client_max_body_size	16M;

	# MIME
	include		mime.types;
	default_type	application/octet-stream;

	# Log Format
	log_format	cloudflare '$remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent "$http_referer" "$http_user_agent" $http_cf_ray $http_cf_connecting_ip $http_x_forwarded_for $http_x_forwarded_proto $http_true_client_ip $http_cf_ipcountry $http_cf_visitor $http_cdn_loop';

	# Logging
	access_log	/var/log/nginx/access.log;
	error_log	/var/log/nginx/error.log warn;

	# SSL
	ssl_session_timeout	1d;
	ssl_session_cache	shared:SSL:10m;
	ssl_session_tickets	off;

	# Diffie-<PERSON>man parameter for DHE ciphersuites
	ssl_dhparam	/etc/nginx/dhparam.pem;

	# Mozilla Intermediate configuration
	ssl_protocols	TLSv1.2 TLSv1.3;
	ssl_ciphers	ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

	# OCSP Stapling
	ssl_stapling		on;
	ssl_stapling_verify	on;
	resolver		******* ******* ******* ******* valid=60s;
	resolver_timeout	2s;

	# Load configs
	include	/etc/nginx/conf.d/*.conf;
	include	/etc/nginx/sites-enabled/*;
}

