<?php

namespace App\Http\Controllers;

use App\Http\Requests\orderAttributesRequest;
use App\Models\order;
use App\Models\orderAttribute;
use App\Models\Products;
use Illuminate\Http\Request;

class OrderAttributesController extends Controller
{
    public function index(){
        $orders = orderAttribute::where('user_id',auth()->user()->id)->paginate(15);
        return $orders;
    }
    public function show($id){
        $order = orderAttribute::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        return $order;
    }
    public function create(orderAttributesRequest $request){
        $order = new orderAttribute();
        $order->order_id = $request->order_id;
        $order->product_id = $request->product_id;
        $order->product_attrs_id = $request->product_attrs_id;
        $order->value = $request->value;
        $order->user_id = auth()->user()->id;
        $order->save();
        return $order;
    }
    public function update(orderAttributesRequest $request, $id){
        $orderAttr = orderAttribute::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        $orderAttr->order_id = $request->order_id;
        $orderAttr->product_id = $request->product_id;
        $orderAttr->product_attrs_id = $request->product_attrs_id;
        $orderAttr->value = $request->value;
        $orderAttr->save();
        return $orderAttr;
    }
    public function destroy($id){
        $orderAttr = orderAttribute::whereId($id)->where('user_id',auth()->user()->id)->firstOrFail();
        $orderAttr->delete();
        return $orderAttr;
    }
}
