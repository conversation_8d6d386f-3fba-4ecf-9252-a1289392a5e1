<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\CrmApiService;
use Illuminate\Console\Command;

class SendDailyData extends Command
{
	private CrmApiService $crmApiService;

	public function __construct(CrmApiService $crmApiService)
	{
		parent::__construct();
		$this->crmApiService = $crmApiService;
	}

	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'app:send-daily-data';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Command description';

	/**
	 * Execute the console command.
	 */
	public function handle()
	{
		$this->sendExpiringPages(days: 0);
		$this->sendExpiringPages(days: 2);
		$this->sendExpiringPages(days: 7);
	}

	private function sendExpiringPages(int $days): void
	{
		$users = User::with([
			'pages' => fn($query) => $this->filterPages($query, $days),
			'pages.user',
			'pages.transaction',
		])
			->whereHas('pages', fn($query) => $this->filterPages($query, $days))
			->get();

		foreach ($users as $user) {
			foreach ($user->pages as $page) {
				// Send page data to the CRM
				$this->crmApiService->sendPageData($page, $days);
			}
		}
	}

	private function filterPages($query, $days): void
	{
		if ($days === 0)
			$query->whereDate('expired_at', '<', now()->toDateString());
		else
			$query->whereDate('expired_at', now()->addDays($days)->toDateString());
	}
}
