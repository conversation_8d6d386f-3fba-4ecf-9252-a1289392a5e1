<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StorePageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if(Auth::user() == null) {
            return false;
        }
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|max:255',
        ];
    }
    public function messages(): array
    {
        return [
            'title.required'=>'عنوان اجباری است',
            'title.max'=>'عنوان حداکثر 255 کاراکتر میباشد',
            'access_token.required' => 'اکسس توکن اجباری است',
            'access_token.max' => 'اکسس توکن حداکثر 500 کاراکتر میباشد',
            'access_token.unique' => 'اکسس توکن معتبر نیست',
            'page_user_id.unique' => 'پیج معتبر نیست',
            'page_user_id.required' => 'پیج اجباری است',
            'page_user_id.max' => 'پیج حداکثر 500 کاراکتر میباشد',
        ];
    }
}
