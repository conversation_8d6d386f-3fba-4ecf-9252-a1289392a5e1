import{V as N,aV as F,P as R,bD as I,r as w,ap as M,b6 as Y,a8 as f,aS as ye,bE as ze,a1 as p,by as Je,bF as Qe,au as x,aq as $,ar as E,as as G,at as K,b as d,bc as X,aG as We,K as me,L as Ye,bo as ge,bG as re,aY as be,bl as Se,bH as he,a_ as pe,aw as Ce,bI as Xe,ax as ke,bJ as we,bK as Ze,bL as et,w as tt,b1 as Ie,b2 as Ae,bM as at,bm as Ve,bN as Pe,b3 as Le,aA as Oe,bO as nt,bP as lt,bQ as it,F as ue,ac as oe,a4 as ce,aC as st,s as de,bR as V,bg as Be,bS as rt,az as ut,bb as ot,bT as ct}from"./index-169996dc.js";import{V as dt}from"./index-00c7d20d.js";import{u as vt}from"./ssrBoot-c101cd97.js";import{V as ft}from"./VDivider-12bfa926.js";const Z=Symbol.for("vuetify:list");function Me(){const e=N(Z,{hasPrepend:F(!1),updateHasPrepend:()=>null}),i={hasPrepend:F(!1),updateHasPrepend:t=>{t&&(i.hasPrepend.value=t)}};return R(Z,i),e}function xe(){return N(Z,null)}const te=e=>{const i={activate:t=>{let{id:a,value:l,activated:n}=t;return a=I(a),e&&!l&&n.size===1&&n.has(a)||(l?n.add(a):n.delete(a)),n},in:(t,a,l)=>{let n=new Set;for(const s of t||[])n=i.activate({id:s,value:!0,activated:new Set(n),children:a,parents:l});return n},out:t=>Array.from(t)};return i},_e=e=>{const i=te(e);return{activate:a=>{let{activated:l,id:n,...s}=a;n=I(n);const u=l.has(n)?new Set([n]):new Set;return i.activate({...s,id:n,activated:u})},in:(a,l,n)=>{let s=new Set;return a!=null&&a.length&&(s=i.in(a.slice(0,1),l,n)),s},out:(a,l,n)=>i.out(a,l,n)}},yt=e=>{const i=te(e);return{activate:a=>{let{id:l,activated:n,children:s,...u}=a;return l=I(l),s.has(l)?n:i.activate({id:l,activated:n,children:s,...u})},in:i.in,out:i.out}},mt=e=>{const i=_e(e);return{activate:a=>{let{id:l,activated:n,children:s,...u}=a;return l=I(l),s.has(l)?n:i.activate({id:l,activated:n,children:s,...u})},in:i.in,out:i.out}},gt={open:e=>{let{id:i,value:t,opened:a,parents:l}=e;if(t){const n=new Set;n.add(i);let s=l.get(i);for(;s!=null;)n.add(s),s=l.get(s);return n}else return a.delete(i),a},select:()=>null},Fe={open:e=>{let{id:i,value:t,opened:a,parents:l}=e;if(t){let n=l.get(i);for(a.add(i);n!=null&&n!==i;)a.add(n),n=l.get(n);return a}else a.delete(i);return a},select:()=>null},bt={open:Fe.open,select:e=>{let{id:i,value:t,opened:a,parents:l}=e;if(!t)return a;const n=[];let s=l.get(i);for(;s!=null;)n.push(s),s=l.get(s);return new Set(n)}},ae=e=>{const i={select:t=>{let{id:a,value:l,selected:n}=t;if(a=I(a),e&&!l){const s=Array.from(n.entries()).reduce((u,h)=>{let[y,m]=h;return m==="on"&&u.push(y),u},[]);if(s.length===1&&s[0]===a)return n}return n.set(a,l?"on":"off"),n},in:(t,a,l)=>{let n=new Map;for(const s of t||[])n=i.select({id:s,value:!0,selected:new Map(n),children:a,parents:l});return n},out:t=>{const a=[];for(const[l,n]of t.entries())n==="on"&&a.push(l);return a}};return i},Te=e=>{const i=ae(e);return{select:a=>{let{selected:l,id:n,...s}=a;n=I(n);const u=l.has(n)?new Map([[n,l.get(n)]]):new Map;return i.select({...s,id:n,selected:u})},in:(a,l,n)=>{let s=new Map;return a!=null&&a.length&&(s=i.in(a.slice(0,1),l,n)),s},out:(a,l,n)=>i.out(a,l,n)}},St=e=>{const i=ae(e);return{select:a=>{let{id:l,selected:n,children:s,...u}=a;return l=I(l),s.has(l)?n:i.select({id:l,selected:n,children:s,...u})},in:i.in,out:i.out}},ht=e=>{const i=Te(e);return{select:a=>{let{id:l,selected:n,children:s,...u}=a;return l=I(l),s.has(l)?n:i.select({id:l,selected:n,children:s,...u})},in:i.in,out:i.out}},pt=e=>{const i={select:t=>{let{id:a,value:l,selected:n,children:s,parents:u}=t;a=I(a);const h=new Map(n),y=[a];for(;y.length;){const b=y.shift();n.set(b,l?"on":"off"),s.has(b)&&y.push(...s.get(b))}let m=u.get(a);for(;m;){const b=s.get(m),S=b.every(o=>n.get(o)==="on"),r=b.every(o=>!n.has(o)||n.get(o)==="off");n.set(m,S?"on":r?"off":"indeterminate"),m=u.get(m)}return e&&!l&&Array.from(n.entries()).reduce((S,r)=>{let[o,c]=r;return c==="on"&&S.push(o),S},[]).length===0?h:n},in:(t,a,l)=>{let n=new Map;for(const s of t||[])n=i.select({id:s,value:!0,selected:new Map(n),children:a,parents:l});return n},out:(t,a)=>{const l=[];for(const[n,s]of t.entries())s==="on"&&!a.has(n)&&l.push(n);return l}};return i},T=Symbol.for("vuetify:nested"),Ge={id:F(),root:{register:()=>null,unregister:()=>null,parents:w(new Map),children:w(new Map),open:()=>null,openOnSelect:()=>null,activate:()=>null,select:()=>null,activatable:w(!1),selectable:w(!1),opened:w(new Set),activated:w(new Set),selected:w(new Map),selectedValues:w([])}},Ct=M({activatable:Boolean,selectable:Boolean,activeStrategy:[String,Function],selectStrategy:[String,Function],openStrategy:[String,Object],opened:Array,activated:Array,selected:Array,mandatory:Boolean},"nested"),kt=e=>{let i=!1;const t=w(new Map),a=w(new Map),l=Y(e,"opened",e.opened,r=>new Set(r),r=>[...r.values()]),n=f(()=>{if(typeof e.activeStrategy=="object")return e.activeStrategy;switch(e.activeStrategy){case"leaf":return yt(e.mandatory);case"single-leaf":return mt(e.mandatory);case"independent":return te(e.mandatory);case"single-independent":default:return _e(e.mandatory)}}),s=f(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;switch(e.selectStrategy){case"single-leaf":return ht(e.mandatory);case"leaf":return St(e.mandatory);case"independent":return ae(e.mandatory);case"single-independent":return Te(e.mandatory);case"classic":default:return pt(e.mandatory)}}),u=f(()=>{if(typeof e.openStrategy=="object")return e.openStrategy;switch(e.openStrategy){case"list":return bt;case"single":return gt;case"multiple":default:return Fe}}),h=Y(e,"activated",e.activated,r=>n.value.in(r,t.value,a.value),r=>n.value.out(r,t.value,a.value)),y=Y(e,"selected",e.selected,r=>s.value.in(r,t.value,a.value),r=>s.value.out(r,t.value,a.value));ye(()=>{i=!0});function m(r){const o=[];let c=r;for(;c!=null;)o.unshift(c),c=a.value.get(c);return o}const b=ze("nested"),S={id:F(),root:{opened:l,activatable:p(e,"activatable"),selectable:p(e,"selectable"),activated:h,selected:y,selectedValues:f(()=>{const r=[];for(const[o,c]of y.value.entries())c==="on"&&r.push(o);return r}),register:(r,o,c)=>{o&&r!==o&&a.value.set(r,o),c&&t.value.set(r,[]),o!=null&&t.value.set(o,[...t.value.get(o)||[],r])},unregister:r=>{if(i)return;t.value.delete(r);const o=a.value.get(r);if(o){const c=t.value.get(o)??[];t.value.set(o,c.filter(v=>v!==r))}a.value.delete(r),l.value.delete(r)},open:(r,o,c)=>{b.emit("click:open",{id:r,value:o,path:m(r),event:c});const v=u.value.open({id:r,value:o,opened:new Set(l.value),children:t.value,parents:a.value,event:c});v&&(l.value=v)},openOnSelect:(r,o,c)=>{const v=u.value.select({id:r,value:o,selected:new Map(y.value),opened:new Set(l.value),children:t.value,parents:a.value,event:c});v&&(l.value=v)},select:(r,o,c)=>{b.emit("click:select",{id:r,value:o,path:m(r),event:c});const v=s.value.select({id:r,value:o,selected:new Map(y.value),children:t.value,parents:a.value,event:c});v&&(y.value=v),S.root.openOnSelect(r,o,c)},activate:(r,o,c)=>{if(!e.activatable)return S.root.select(r,!0,c);b.emit("click:activate",{id:r,value:o,path:m(r),event:c});const v=n.value.activate({id:r,value:o,activated:new Set(h.value),children:t.value,parents:a.value,event:c});v&&(h.value=v)},children:t,parents:a}};return R(T,S),S.root},je=(e,i)=>{const t=N(T,Ge),a=Symbol(Je()),l=f(()=>e.value!==void 0?e.value:a),n={...t,id:l,open:(s,u)=>t.root.open(l.value,s,u),openOnSelect:(s,u)=>t.root.openOnSelect(l.value,s,u),isOpen:f(()=>t.root.opened.value.has(l.value)),parent:f(()=>t.root.parents.value.get(l.value)),activate:(s,u)=>t.root.activate(l.value,s,u),isActivated:f(()=>t.root.activated.value.has(I(l.value))),select:(s,u)=>t.root.select(l.value,s,u),isSelected:f(()=>t.root.selected.value.get(I(l.value))==="on"),isIndeterminate:f(()=>t.root.selected.value.get(l.value)==="indeterminate"),isLeaf:f(()=>!t.root.children.value.get(l.value)),isGroupActivator:t.isGroupActivator};return!t.isGroupActivator&&t.root.register(l.value,t.id.value,i),ye(()=>{!t.isGroupActivator&&t.root.unregister(l.value)}),i&&R(T,n),n},wt=()=>{const e=N(T,Ge);R(T,{...e,isGroupActivator:!0})},It=Qe({name:"VListGroupActivator",setup(e,i){let{slots:t}=i;return wt(),()=>{var a;return(a=t.default)==null?void 0:a.call(t)}}}),At=M({activeColor:String,baseColor:String,color:String,collapseIcon:{type:x,default:"$collapse"},expandIcon:{type:x,default:"$expand"},prependIcon:x,appendIcon:x,fluid:Boolean,subgroup:Boolean,title:String,value:null,...$(),...E()},"VListGroup"),ve=G()({name:"VListGroup",props:At(),setup(e,i){let{slots:t}=i;const{isOpen:a,open:l,id:n}=je(p(e,"value"),!0),s=f(()=>`v-list-group--id-${String(n.value)}`),u=xe(),{isBooted:h}=vt();function y(r){l(!a.value,r)}const m=f(()=>({onClick:y,class:"v-list-group__header",id:s.value})),b=f(()=>a.value?e.collapseIcon:e.expandIcon),S=f(()=>({VListItem:{active:a.value,activeColor:e.activeColor,baseColor:e.baseColor,color:e.color,prependIcon:e.prependIcon||e.subgroup&&b.value,appendIcon:e.appendIcon||!e.subgroup&&b.value,title:e.title,value:e.value}}));return K(()=>d(e.tag,{class:["v-list-group",{"v-list-group--prepend":u==null?void 0:u.hasPrepend.value,"v-list-group--fluid":e.fluid,"v-list-group--subgroup":e.subgroup,"v-list-group--open":a.value},e.class],style:e.style},{default:()=>[t.activator&&d(X,{defaults:S.value},{default:()=>[d(It,null,{default:()=>[t.activator({props:m.value,isOpen:a.value})]})]}),d(We,{transition:{component:dt},disabled:!h.value},{default:()=>{var r;return[me(d("div",{class:"v-list-group__items",role:"group","aria-labelledby":s.value},[(r=t.default)==null?void 0:r.call(t)]),[[Ye,a.value]])]}})]})),{isOpen:a}}});const Vt=ge("v-list-item-subtitle"),Pt=ge("v-list-item-title"),Lt=M({active:{type:Boolean,default:void 0},activeClass:String,activeColor:String,appendAvatar:String,appendIcon:x,baseColor:String,disabled:Boolean,lines:String,link:{type:Boolean,default:void 0},nav:Boolean,prependAvatar:String,prependIcon:x,ripple:{type:[Boolean,Object],default:!0},slim:Boolean,subtitle:[String,Number],title:[String,Number],value:null,onClick:re(),onClickOnce:re(),...be(),...$(),...Se(),...he(),...pe(),...Ce(),...Xe(),...E(),...ke(),...we({variant:"text"})},"VListItem"),fe=G()({name:"VListItem",directives:{Ripple:Ze},props:Lt(),emits:{click:e=>!0},setup(e,i){let{attrs:t,slots:a,emit:l}=i;const n=et(e,t),s=f(()=>e.value===void 0?n.href.value:e.value),{activate:u,isActivated:h,select:y,isSelected:m,isIndeterminate:b,isGroupActivator:S,root:r,parent:o,openOnSelect:c}=je(s,!1),v=xe(),P=f(()=>{var g;return e.active!==!1&&(e.active||((g=n.isActive)==null?void 0:g.value)||(r.activatable.value?h.value:m.value))}),_=f(()=>e.link!==!1&&n.isLink.value),A=f(()=>!e.disabled&&e.link!==!1&&(e.link||n.isClickable.value||!!v&&(r.selectable.value||r.activatable.value||e.value!=null))),O=f(()=>e.rounded||e.nav),B=f(()=>e.color??e.activeColor),H=f(()=>({color:P.value?B.value??e.baseColor:e.baseColor,variant:e.variant}));tt(()=>{var g;return(g=n.isActive)==null?void 0:g.value},g=>{g&&o.value!=null&&r.open(o.value,!0),g&&c(g)},{immediate:!0});const{themeClasses:U}=Ie(e),{borderClasses:q}=Ae(e),{colorClasses:z,colorStyles:J,variantClasses:L}=at(H),{densityClasses:C}=Ve(e),{dimensionStyles:j}=Pe(e),{elevationClasses:$e}=Le(e),{roundedClasses:Ee}=Oe(O),Ke=f(()=>e.lines?`v-list-item--${e.lines}-line`:void 0),Q=f(()=>({isActive:P.value,select:y,isSelected:m.value,isIndeterminate:b.value}));function ne(g){var D;l("click",g),!(S||!A.value)&&((D=n.navigate)==null||D.call(n,g),r.activatable.value?u(!h.value,g):(r.selectable.value||e.value!=null)&&y(!m.value,g))}function He(g){(g.key==="Enter"||g.key===" ")&&(g.preventDefault(),ne(g))}return K(()=>{const g=_.value?"a":e.tag,D=a.title||e.title!=null,Ue=a.subtitle||e.subtitle!=null,le=!!(e.appendAvatar||e.appendIcon),qe=!!(le||a.append),ie=!!(e.prependAvatar||e.prependIcon),W=!!(ie||a.prepend);return v==null||v.updateHasPrepend(W),e.activeColor&&nt("active-color",["color","base-color"]),me(d(g,{class:["v-list-item",{"v-list-item--active":P.value,"v-list-item--disabled":e.disabled,"v-list-item--link":A.value,"v-list-item--nav":e.nav,"v-list-item--prepend":!W&&(v==null?void 0:v.hasPrepend.value),"v-list-item--slim":e.slim,[`${e.activeClass}`]:e.activeClass&&P.value},U.value,q.value,z.value,C.value,$e.value,Ke.value,Ee.value,L.value,e.class],style:[J.value,j.value,e.style],href:n.href.value,tabindex:A.value?v?-2:0:void 0,onClick:ne,onKeydown:A.value&&!_.value&&He},{default:()=>{var se;return[it(A.value||P.value,"v-list-item"),W&&d("div",{key:"prepend",class:"v-list-item__prepend"},[a.prepend?d(X,{key:"prepend-defaults",disabled:!ie,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon},VListItemAction:{start:!0}}},{default:()=>{var k;return[(k=a.prepend)==null?void 0:k.call(a,Q.value)]}}):d(ue,null,[e.prependAvatar&&d(oe,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&d(ce,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)]),d("div",{class:"v-list-item__spacer"},null)]),d("div",{class:"v-list-item__content","data-no-activator":""},[D&&d(Pt,{key:"title"},{default:()=>{var k;return[((k=a.title)==null?void 0:k.call(a,{title:e.title}))??e.title]}}),Ue&&d(Vt,{key:"subtitle"},{default:()=>{var k;return[((k=a.subtitle)==null?void 0:k.call(a,{subtitle:e.subtitle}))??e.subtitle]}}),(se=a.default)==null?void 0:se.call(a,Q.value)]),qe&&d("div",{key:"append",class:"v-list-item__append"},[a.append?d(X,{key:"append-defaults",disabled:!le,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon},VListItemAction:{end:!0}}},{default:()=>{var k;return[(k=a.append)==null?void 0:k.call(a,Q.value)]}}):d(ue,null,[e.appendIcon&&d(ce,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&d(oe,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)]),d("div",{class:"v-list-item__spacer"},null)])]}}),[[lt("ripple"),A.value&&e.ripple]])}),{isGroupActivator:S,isSelected:m,list:v,select:y}}}),Ot=M({color:String,inset:Boolean,sticky:Boolean,title:String,...$(),...E()},"VListSubheader"),Bt=G()({name:"VListSubheader",props:Ot(),setup(e,i){let{slots:t}=i;const{textColorClasses:a,textColorStyles:l}=st(p(e,"color"));return K(()=>{const n=!!(t.default||e.title);return d(e.tag,{class:["v-list-subheader",{"v-list-subheader--inset":e.inset,"v-list-subheader--sticky":e.sticky},a.value,e.class],style:[{textColorStyles:l},e.style]},{default:()=>{var s;return[n&&d("div",{class:"v-list-subheader__text"},[((s=t.default)==null?void 0:s.call(t))??e.title])]}})}),{}}}),Mt=M({items:Array,returnObject:Boolean},"VListChildren"),De=G()({name:"VListChildren",props:Mt(),setup(e,i){let{slots:t}=i;return Me(),()=>{var a,l;return((a=t.default)==null?void 0:a.call(t))??((l=e.items)==null?void 0:l.map(n=>{var S,r;let{children:s,props:u,type:h,raw:y}=n;if(h==="divider")return((S=t.divider)==null?void 0:S.call(t,{props:u}))??d(ft,u,null);if(h==="subheader")return((r=t.subheader)==null?void 0:r.call(t,{props:u}))??d(Bt,u,null);const m={subtitle:t.subtitle?o=>{var c;return(c=t.subtitle)==null?void 0:c.call(t,{...o,item:y})}:void 0,prepend:t.prepend?o=>{var c;return(c=t.prepend)==null?void 0:c.call(t,{...o,item:y})}:void 0,append:t.append?o=>{var c;return(c=t.append)==null?void 0:c.call(t,{...o,item:y})}:void 0,title:t.title?o=>{var c;return(c=t.title)==null?void 0:c.call(t,{...o,item:y})}:void 0},b=ve.filterProps(u);return s?d(ve,de({value:u==null?void 0:u.value},b),{activator:o=>{let{props:c}=o;const v={...u,...c,value:e.returnObject?y:u.value};return t.header?t.header({props:v}):d(fe,v,m)},default:()=>d(De,{items:s},t)}):t.item?t.item({props:u}):d(fe,de(u,{value:e.returnObject?y:u.value}),m)}))}}}),xt=M({items:{type:Array,default:()=>[]},itemTitle:{type:[String,Array,Function],default:"title"},itemValue:{type:[String,Array,Function],default:"value"},itemChildren:{type:[Boolean,String,Array,Function],default:"children"},itemProps:{type:[Boolean,String,Array,Function],default:"props"},returnObject:Boolean,valueComparator:{type:Function,default:rt}},"list-items");function ee(e,i){const t=V(i,e.itemTitle,i),a=V(i,e.itemValue,t),l=V(i,e.itemChildren),n=e.itemProps===!0?typeof i=="object"&&i!=null&&!Array.isArray(i)?"children"in i?Be(i,["children"]):i:void 0:V(i,e.itemProps),s={title:t,value:a,...n};return{title:String(s.title??""),value:s.value,props:s,children:Array.isArray(l)?Ne(e,l):void 0,raw:i}}function Ne(e,i){const t=[];for(const a of i)t.push(ee(e,a));return t}function $t(e){const i=f(()=>Ne(e,e.items)),t=f(()=>i.value.some(n=>n.value===null));function a(n){return t.value||(n=n.filter(s=>s!==null)),n.map(s=>e.returnObject&&typeof s=="string"?ee(e,s):i.value.find(u=>e.valueComparator(s,u.value))||ee(e,s))}function l(n){return e.returnObject?n.map(s=>{let{raw:u}=s;return u}):n.map(s=>{let{value:u}=s;return u})}return{items:i,transformIn:a,transformOut:l}}function _t(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"}function Ft(e,i){const t=V(i,e.itemType,"item"),a=_t(i)?i:V(i,e.itemTitle),l=V(i,e.itemValue,void 0),n=V(i,e.itemChildren),s=e.itemProps===!0?Be(i,["children"]):V(i,e.itemProps),u={title:a,value:l,...s};return{type:t,title:u.title,value:u.value,props:u,children:t==="item"&&n?Re(e,n):void 0,raw:i}}function Re(e,i){const t=[];for(const a of i)t.push(Ft(e,a));return t}function Tt(e){return{items:f(()=>Re(e,e.items))}}const Gt=M({baseColor:String,activeColor:String,activeClass:String,bgColor:String,disabled:Boolean,expandIcon:String,collapseIcon:String,lines:{type:[Boolean,String],default:"one"},slim:Boolean,nav:Boolean,...Ct({selectStrategy:"single-leaf",openStrategy:"list"}),...be(),...$(),...Se(),...he(),...pe(),itemType:{type:String,default:"type"},...xt(),...Ce(),...E(),...ke(),...we({variant:"text"})},"VList"),Et=G()({name:"VList",props:Gt(),emits:{"update:selected":e=>!0,"update:activated":e=>!0,"update:opened":e=>!0,"click:open":e=>!0,"click:activate":e=>!0,"click:select":e=>!0},setup(e,i){let{slots:t}=i;const{items:a}=Tt(e),{themeClasses:l}=Ie(e),{backgroundColorClasses:n,backgroundColorStyles:s}=ut(p(e,"bgColor")),{borderClasses:u}=Ae(e),{densityClasses:h}=Ve(e),{dimensionStyles:y}=Pe(e),{elevationClasses:m}=Le(e),{roundedClasses:b}=Oe(e),{children:S,open:r,parents:o,select:c}=kt(e),v=f(()=>e.lines?`v-list--${e.lines}-line`:void 0),P=p(e,"activeColor"),_=p(e,"baseColor"),A=p(e,"color");Me(),ot({VListGroup:{activeColor:P,baseColor:_,color:A,expandIcon:p(e,"expandIcon"),collapseIcon:p(e,"collapseIcon")},VListItem:{activeClass:p(e,"activeClass"),activeColor:P,baseColor:_,color:A,density:p(e,"density"),disabled:p(e,"disabled"),lines:p(e,"lines"),nav:p(e,"nav"),slim:p(e,"slim"),variant:p(e,"variant")}});const O=F(!1),B=w();function H(C){O.value=!0}function U(C){O.value=!1}function q(C){var j;!O.value&&!(C.relatedTarget&&((j=B.value)!=null&&j.contains(C.relatedTarget)))&&L()}function z(C){if(B.value){if(C.key==="ArrowDown")L("next");else if(C.key==="ArrowUp")L("prev");else if(C.key==="Home")L("first");else if(C.key==="End")L("last");else return;C.preventDefault()}}function J(C){O.value=!0}function L(C){if(B.value)return ct(B.value,C)}return K(()=>d(e.tag,{ref:B,class:["v-list",{"v-list--disabled":e.disabled,"v-list--nav":e.nav,"v-list--slim":e.slim},l.value,n.value,u.value,h.value,m.value,v.value,b.value,e.class],style:[s.value,y.value,e.style],tabindex:e.disabled||O.value?-1:0,role:"listbox","aria-activedescendant":void 0,onFocusin:H,onFocusout:U,onFocus:q,onKeydown:z,onMousedown:J},{default:()=>[d(De,{items:a.value,returnObject:e.returnObject},t)]})),{open:r,select:c,focus:L,children:S,parents:o}}});export{Et as V,fe as a,Pt as b,Vt as c,Bt as d,xt as m,ee as t,$t as u};
