import"./VRow-6c1d54f3.js";import{ap as n,aq as o,ar as r,as as l,b0 as i,at as c,b as m}from"./index-169996dc.js";const p=n({fluid:{type:Boolean,default:!1},...o(),...r()},"VContainer"),d=l()({name:"VContainer",props:p(),setup(a,e){let{slots:s}=e;const{rtlClasses:t}=i();return c(()=>m(a.tag,{class:["v-container",{"v-container--fluid":a.fluid},t.value,a.class],style:a.style},s)),{}}});export{d as V};
