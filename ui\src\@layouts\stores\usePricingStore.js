export const usePricingStore = defineStore('pricing', {
  // arrow function recommended for full type inference
  state: () => ({
    showMobileDialog: false,
    packageId: -1,
  }),
  getters: {
    getShowMobileDialog: state => state.showMobileDialog,
  },
  actions: {
    setMobileDialog(status) {
      this.showMobileDialog = status
    },
    setPackageId(id) {
      this.packageId = id
    },
  },
})
