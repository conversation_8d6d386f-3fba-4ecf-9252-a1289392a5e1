<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DeliveryMethod;
use App\Models\Page;

class DeliveryMethodController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $shippings = DeliveryMethod::where('user_id',auth()->user()->id)
            ->orderBy('created_at','desc')
            ->paginate(10);
        return $shippings;
    }
    public function indexPage($id=null)
    {
        $pageId = null;
        if($id){
            $page = Page::where('id',$id)->where('user_id',auth()->user()->id)->first();
            if(!$page){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $page->id;
        }
        if(!$pageId){
            $page = Page::where('user_id',auth()->user()->id)->first();
            $pageId = $page->id;
        }
        $shippings = DeliveryMethod::where('user_id',auth()->user()->id)
            ->where('page_id',$pageId)
            ->orderBy('created_at','desc')
            ->paginate(10);
        return $shippings;
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $pageId = null;
        if($request->page){
            $page = Page::where('id',$request->page)->where('user_id',auth()->user()->id)->first();
            if(!$page){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $page->id;
        }
        if(!$pageId){
            $page = Page::where('user_id',auth()->user()->id)->first();
            $pageId = $page->id;
        }
        $deliveryCounts = DeliveryMethod::where('user_id',auth()->user()->id)->where('page_id',$pageId)->count();
        if($deliveryCounts>=5){
            return response()->json([
                'success'=>false,
                'message'=>'شما حداکثر 5 روش ارسال میتوانید بسازید'
            ]);
        }
        $shipping = new DeliveryMethod();
        $shipping->name = $request->name;
        $shipping->description = $request->description;
        $shipping->price = $request->price;
        $shipping->user_id = auth()->user()->id;
        $shipping->page_id = $pageId;
        $shipping->save();
        return $shipping;
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $shipping = DeliveryMethod::whereId($id)
            ->where('user_id',auth()->user()->id)
            ->firstOrFail();
        return $shipping;
    }

    /**
     * Show the form for editing the specified resource.
     */
    

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        
        $shipping = DeliveryMethod::whereId($id)
            ->where('user_id',auth()->user()->id)
            ->firstOrFail();
        $shipping->name = $request->name;
        $shipping->description = $request->description;
        $shipping->price = $request->price;
        $shipping->save();
        return $shipping;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $shipping = DeliveryMethod::whereId($id)
            ->where('user_id',auth()->user()->id)
            ->firstOrFail();
        $shipping->delete();
        return $shipping;
    }
}
