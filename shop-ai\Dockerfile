# Use a Node.js base image
FROM node:22-alpine AS builder

ENV NODE_ENV=build

# add OpenSSL
RUN set -ex; \
    apk update; \
    apk add --no-cache \
    openssl

# Set the working directory inside the container
RUN chown node:node /home/<USER>
WORKDIR /home/<USER>
USER node

# Install NestJS CLI globally
USER root
RUN npm cache clean --force && npm install -g @nestjs/cli --unsafe-perm

RUN chown -R node:node /home/<USER>

# Switch back to node user
USER node

# Copy package.json and package-lock.json to the container
COPY src/package*.json ./
RUN npm i

COPY --chown=node:node ./src .
RUN npm run build \
    && npm prune --omit=dev

# ---

# Use a Node.js base image
FROM node:22-alpine

ENV NODE_ENV=production

# add OpenSSL
RUN set -ex; \
    apk update; \
    apk add --no-cache \
    openssl

# Set the working directory inside the container
USER node
WORKDIR /home/<USER>

# Install NestJS CLI globally in the final stage
USER root
RUN npm install -g @nestjs/cli --unsafe-perm

# Switch back to node user
USER node

COPY --from=builder --chown=node:node /home/<USER>/package*.json ./
COPY --from=builder --chown=node:node /home/<USER>/node_modules/ ./node_modules/
COPY --from=builder --chown=node:node /home/<USER>/dist/ ./dist/
COPY --from=builder --chown=node:node /home/<USER>/tsconfig*.json ./
# Expose the port the app runs on
EXPOSE 3000

# Start the application
CMD ["npm", "start"]