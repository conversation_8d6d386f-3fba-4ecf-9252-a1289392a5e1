import{a as d}from"./VInput-c4d3942a.js";import{V as u}from"./VSelect-d6fde9f4.js";import{a8 as r,cd as o,o as c,c as f,n as t,f as _,z as b,b as h,ao as $,i as S,e as g,A as k,ce as i,cf as p,y as v}from"./index-169996dc.js";const z=Object.assign({name:"AppSelect",inheritAttrs:!1},{__name:"AppSelect",setup(A){const a=r(()=>{const e=o(),s=e.id||e.label;return s?`app-select-${s}-${Math.random().toString(36).slice(2,7)}`:void 0}),l=r(()=>o().label);return(e,s)=>(c(),f("div",{class:v(["app-select flex-grow-1",e.$attrs.class])},[t(l)?(c(),_(d,{key:0,for:t(a),class:"mb-1 text-body-2 text-high-emphasis",text:t(l)},null,8,["for","text"])):b("",!0),h(u,i(p({...e.$attrs,class:null,label:void 0,variant:"outlined",id:t(a),menuProps:{contentClass:["app-inner-list","app-select__content","v-select__content",e.$attrs.multiple!==void 0?"v-list-select-multiple":""]}})),$({_:2},[S(e.$slots,(V,n)=>({name:n,fn:g(m=>[k(e.$slots,n,i(p(m||{})))])}))]),1040)],2))}});export{z as _};
