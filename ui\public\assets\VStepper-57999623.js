import{ap as y,as as k,aB as F,at as h,b as l,bc as T,aj as W,bo as G,cr as j,bK as H,ct as N,a8 as d,K as z,bP as K,ac as q,a4 as M,bg as O,V as U,b6 as Y,s as I,aY as J,aq as Q,bH as X,a_ as Z,av as ee,cg as te,aw as ae,ar as le,ax as ne,b1 as se,az as oe,a1 as re,b2 as ie,bN as ue,b3 as ce,aE as de,ch as ve,aA as me,cn as pe,cI as be,co as Se,aT as fe,bR as A,bb as Ve,F as ye}from"./index-169996dc.js";import{m as ke,V as _,b as he,a as $}from"./VWindowItem-f84b956d.js";import{V as Pe}from"./VDivider-12bfa926.js";const E=y({color:String,disabled:{type:[Boolean,String],default:!1},prevText:{type:String,default:"$vuetify.stepper.prev"},nextText:{type:String,default:"$vuetify.stepper.next"}},"VStepperActions"),ge=k()({name:"VStepperActions",props:E(),emits:{"click:prev":()=>!0,"click:next":()=>!0},setup(e,u){let{emit:t,slots:a}=u;const{t:n}=F();function r(){t("click:prev")}function s(){t("click:next")}return h(()=>{const o={onClick:r},c={onClick:s};return l("div",{class:"v-stepper-actions"},[l(T,{defaults:{VBtn:{disabled:["prev",!0].includes(e.disabled),text:n(e.prevText),variant:"text"}}},{default:()=>{var i;return[((i=a.prev)==null?void 0:i.call(a,{props:o}))??l(W,o,null)]}}),l(T,{defaults:{VBtn:{color:e.color,disabled:["next",!0].includes(e.disabled),text:n(e.nextText),variant:"tonal"}}},{default:()=>{var i;return[((i=a.next)==null?void 0:i.call(a,{props:c}))??l(W,c,null)]}})])}),{}}}),xe=G("v-stepper-header");const we=y({color:String,title:String,subtitle:String,complete:Boolean,completeIcon:{type:String,default:"$complete"},editable:Boolean,editIcon:{type:String,default:"$edit"},error:Boolean,errorIcon:{type:String,default:"$error"},icon:String,ripple:{type:[Boolean,Object],default:!0},rules:{type:Array,default:()=>[]},...j()},"VStepperItem"),Ie=k()({name:"VStepperItem",directives:{Ripple:H},props:we(),emits:{"group:selected":e=>!0},setup(e,u){let{slots:t}=u;const a=N(e,R,!0),n=d(()=>(a==null?void 0:a.value.value)??e.value),r=d(()=>e.rules.every(p=>p()===!0)),s=d(()=>!e.disabled&&e.editable),o=d(()=>e.error||!r.value),c=d(()=>e.complete||e.rules.length>0&&r.value),i=d(()=>o.value?e.errorIcon:c.value?e.completeIcon:e.editable?e.editIcon:e.icon),m=d(()=>({canEdit:s.value,hasError:o.value,hasCompleted:c.value,title:e.title,subtitle:e.subtitle,step:n.value,value:e.value}));return h(()=>{var f,b,V;const p=(!a||a.isSelected.value||c.value||s.value)&&!o.value&&!e.disabled,g=!!(e.title!=null||t.title),w=!!(e.subtitle!=null||t.subtitle);function v(){a==null||a.toggle()}return z(l("button",{class:["v-stepper-item",{"v-stepper-item--complete":c.value,"v-stepper-item--disabled":e.disabled,"v-stepper-item--error":o.value},a==null?void 0:a.selectedClass.value],disabled:!e.editable,onClick:v},[l(q,{key:"stepper-avatar",class:"v-stepper-item__avatar",color:p?e.color:void 0,size:24},{default:()=>{var P;return[((P=t.icon)==null?void 0:P.call(t,m.value))??(i.value?l(M,{icon:i.value},null):n.value)]}}),l("div",{class:"v-stepper-item__content"},[g&&l("div",{key:"title",class:"v-stepper-item__title"},[((f=t.title)==null?void 0:f.call(t,m.value))??e.title]),w&&l("div",{key:"subtitle",class:"v-stepper-item__subtitle"},[((b=t.subtitle)==null?void 0:b.call(t,m.value))??e.subtitle]),(V=t.default)==null?void 0:V.call(t,m.value)])]),[[K("ripple"),e.ripple&&e.editable,null]])}),{}}}),Ce=Symbol.for("vuetify:v-stepper"),Be=y({...O(ke(),["continuous","nextIcon","prevIcon","showArrows","touch","mandatory"])},"VStepperWindow"),Te=k()({name:"VStepperWindow",props:Be(),emits:{"update:modelValue":e=>!0},setup(e,u){let{slots:t}=u;const a=U(Ce,null),n=Y(e,"modelValue"),r=d({get(){var s;return n.value!=null||!a?n.value:(s=a.items.value.find(o=>a.selected.value.includes(o.id)))==null?void 0:s.value},set(s){n.value=s}});return h(()=>{const s=_.filterProps(e);return l(_,I({_as:"VStepperWindow"},s,{modelValue:r.value,"onUpdate:modelValue":o=>r.value=o,class:"v-stepper-window",mandatory:!1,touch:!1}),t)}),{}}}),We=y({...he()},"VStepperWindowItem"),Ae=k()({name:"VStepperWindowItem",props:We(),setup(e,u){let{slots:t}=u;return h(()=>{const a=$.filterProps(e);return l($,I({_as:"VStepperWindowItem"},a,{class:"v-stepper-window-item"}),t)}),{}}});const L=y({color:String,...J(),...Q(),...X(),...Z(),...ee(),...te(),...ae(),...le(),...ne()},"VSheet"),D=k()({name:"VSheet",props:L(),setup(e,u){let{slots:t}=u;const{themeClasses:a}=se(e),{backgroundColorClasses:n,backgroundColorStyles:r}=oe(re(e,"color")),{borderClasses:s}=ie(e),{dimensionStyles:o}=ue(e),{elevationClasses:c}=ce(e),{locationStyles:i}=de(e),{positionClasses:m}=ve(e),{roundedClasses:p}=me(e);return h(()=>l(e.tag,{class:["v-sheet",a.value,n.value,s.value,c.value,m.value,p.value,e.class],style:[r.value,o.value,i.value,e.style]},t)),{}}}),R=Symbol.for("vuetify:v-stepper"),_e=y({altLabels:Boolean,bgColor:String,editable:Boolean,hideActions:Boolean,items:{type:Array,default:()=>[]},itemTitle:{type:String,default:"title"},itemValue:{type:String,default:"value"},mobile:Boolean,nonLinear:Boolean,flat:Boolean,...pe({mandatory:"force",selectedClass:"v-stepper-item--selected"}),...L(),...be(E(),["prevText","nextText"])},"VStepper"),Le=k()({name:"VStepper",props:_e(),emits:{"update:modelValue":e=>!0},setup(e,u){let{slots:t}=u;const{items:a,next:n,prev:r,selected:s}=Se(e,R),{color:o,editable:c,prevText:i,nextText:m}=fe(e),p=d(()=>e.items.map((v,f)=>{const b=A(v,e.itemTitle,v),V=A(v,e.itemValue,f+1);return{title:b,value:V,raw:v}})),g=d(()=>a.value.findIndex(v=>s.value.includes(v.id))),w=d(()=>e.disabled?e.disabled:g.value===0?"prev":g.value===a.value.length-1?"next":!1);return Ve({VStepperItem:{editable:c,prevText:i,nextText:m},VStepperActions:{color:o,disabled:w,prevText:i,nextText:m}}),h(()=>{const v=D.filterProps(e),f=!!(t.header||e.items.length),b=e.items.length>0,V=!e.hideActions&&!!(b||t.actions);return l(D,I(v,{color:e.bgColor,class:["v-stepper",{"v-stepper--alt-labels":e.altLabels,"v-stepper--flat":e.flat,"v-stepper--non-linear":e.nonLinear,"v-stepper--mobile":e.mobile},e.class],style:e.style}),{default:()=>{var P,C;return[f&&l(xe,{key:"stepper-header"},{default:()=>[p.value.map((S,x)=>l(ye,null,[!!x&&l(Pe,null,null),l(Ie,S,{default:t[`header-item.${S.value}`]??t.header,icon:t.icon,title:t.title,subtitle:t.subtitle})]))]}),b&&l(Te,{key:"stepper-window"},{default:()=>[p.value.map(S=>l(Ae,{value:S.value},{default:()=>{var x,B;return((x=t[`item.${S.value}`])==null?void 0:x.call(t,S))??((B=t.item)==null?void 0:B.call(t,S))}}))]}),(P=t.default)==null?void 0:P.call(t,{prev:r,next:n}),V&&(((C=t.actions)==null?void 0:C.call(t,{next:n,prev:r}))??l(ge,{key:"stepper-actions","onClick:prev":r,"onClick:next":n},t))]}})}),{prev:r,next:n}}});export{Le as V};
