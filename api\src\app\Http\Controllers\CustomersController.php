<?php

namespace App\Http\Controllers;

use App\Models\Customers;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;
use App\Models\order;
use App\Models\Page;

class CustomersController extends Controller
{
    public function index(Request $request){
        $search = $request->search;
        if(!$search){
            $customers = Customers::where('user_id',auth()->user()->id)->orderBy('created_at','desc')->paginate(10);
            return $customers;
        }
        $customer = Customers::search($search)
            ->query(function (Builder $query) {
                $query->where('user_id',auth()->user()->id);
            })
            ->orderBy('created_at','desc')
            ->paginate(10);
        return $customer;
    }
    public function indexPage(Request $request,$id=null){
        $search = $request->search;
        $pageId = null;
        if($id){
            $page = Page::where('id',$id)->where('user_id',auth()->user()->id)->first();
            if(!$page){
                return response()->json(['success'=>false,'message'=>'page not allowed'],404);
            }
            $pageId = $page->id;
        }
        if(!$search){
            $customers = Customers::where('page_id',$pageId)->where('user_id',auth()->user()->id)->orderBy('created_at','desc')->paginate(10);
            return $customers;
        }
        $customer = Customers::search($search)
            ->query(function (Builder $query) use ($pageId) {
                $query->where('user_id',auth()->user()->id);
                $query->where('page_id',$pageId);
            })
            ->orderBy('created_at','desc')
            ->paginate(10);
        return $customer;
    }
    public function show(Request $request,$id){
        $customer = Customers::where('user_id',auth()->user()->id)
            ->where('id',$id)
            ->first();
        $search = $request->search;
        $status = $request->status;
        if($search&&$status){
            $orders = order::search($search)
                ->query(function (Builder $query) use ($id, $status, $search) {
                    $query->where('status',$status);
                    $query->where('customer_id',$id);
                    $query->with(['paymentMethod','deliveryMethod','product','product.productGallery','orderAttributes','orderAttributes.productAttributes']);
                    $query->where('user_id',auth()->user()->id)
                        ->where(function ($query) use ($search) {

                            $query->orWhere('tracking_code', 'like', "%{$search}%");
                            $query->orWhere('payment_amount', 'like', "%{$search}%");
                            $query->orWhere('ref_num', 'like', "%{$search}%");
                        });
                })
                ->orderBy('created_at','desc')
                ->paginate(10);
        }else
        if($search&&!$status){
            $orders = order::search($search)
                ->query(function (Builder $query) use ($id, $search) {
                    $query->where('customer_id',$id);
                    $query->with(['paymentMethod','deliveryMethod','product','product.productGallery','orderAttributes','orderAttributes.productAttributes']);
                    $query->where('user_id',auth()->user()->id)
                        ->where(function ($query) use ($search) {

                            $query->orWhere('tracking_code', 'like', "%{$search}%");
                            $query->orWhere('ref_num', 'like', "%{$search}%");
                            $query->orWhere('payment_amount', 'like', "%{$search}%");
                        });
                })
                ->orderBy('created_at','desc')
                ->paginate(10);
        }else
        if(!$search&&$status){
            $orders = $customer->orders()
                ->where('status',$status)
                ->with(['paymentMethod','deliveryMethod','product','product.productGallery','orderAttributes','orderAttributes.productAttributes'])
                ->orderBy('created_at', 'DESC')
                ->paginate(10);
        }
        else{
            $orders = $customer->orders()
                ->with(['paymentMethod','deliveryMethod','product','product.productGallery','orderAttributes','orderAttributes.productAttributes'])
                ->orderBy('created_at', 'DESC')
                ->paginate(10);
        }
        $ordersForTotal = $customer->orders()->select(['status','payment_amount'])->get();
        $totalPurchase = 0;
        foreach ($ordersForTotal as $order) {
            if($order->status==='approved'){
                $totalPurchase += (int)$order->payment_amount;
            }
        }
        return ['customer'=>$customer,'orders'=>$orders,'totalPurchase'=>$totalPurchase];
    }
}
