<?php

namespace App\Repositories\Interfaces;

use App\Models\Ai\AiSetting;
use Illuminate\Database\Eloquent\Collection;

/**
 * Interface AiSettingRepositoryInterface
 *
 * This interface defines the methods for interacting with the AiSetting model.
 * It provides functions to create, update, retrieve, and manage AI settings,
 * including retrieving settings by post ID or user ID, and decrementing editable post limits.
 */
interface AiSettingRepositoryInterface
{
    /**
     * Include soft-deleted AI settings in the next query.
     *
     * This method is used to modify the repository’s query behavior so that
     * subsequent retrieval methods (like `getByUserId` or `getByPostId`) will also
     * include soft-deleted (trashed) records. This is useful when you need to audit,
     * restore, or view deleted entries.
     *
     * @return self Returns the repository instance to allow method chaining.
     *
     * @example
     * ```php
     * // Get all AI settings for a user including soft-deleted ones
     * $settings = $aiSettingRepository->withTrashed()->getByUserId(1);
     * ```
     */
    public function withTrashed(): self;

    /**
     * Retrieve an AI setting by its ID.
     *
     * This method retrieves the `AiSetting` instance associated with the provided `id`.
     * If no setting is found for the given `id`, it returns `null`. This method is useful
     * when you need to retrieve a specific AI setting based on its unique identifier.
     *
     * @param int $id The ID of the AI setting to retrieve.
     * @return AiSetting|null The `AiSetting` instance if found, otherwise `null`.
     *
     * @example
     * ```php
     * // Retrieve an AI setting by its ID
     * $aiSetting = $aiSettingRepository->find(1);
     *
     * if ($aiSetting) {
     *     echo $aiSetting->ai_driver; // Outputs the AI driver associated with this setting
     * } else {
     *     echo 'AI setting not found.';
     * }
     * ```
     */
    public function find(int $id): ?AiSetting;

    /**
     * Creates a new AI setting.
     *
     * This method creates a new AI setting entry in the database using the provided
     * data and returns the created `AiSetting` instance.
     *
     * @param array $data The data required to create the AI setting.
     * @return AiSetting The created `AiSetting` instance.
     *
     * @example
     * ```php
     * $aiSetting = $aiSettingRepository->create([
     *     'user_id' => 1,
     *     'friendly_tone' => 'casual',
     *     'bot_character' => 'helpful',
     *     'post_id' => '12345',
     *     'custom_prompt' => 'Please provide a response to the user's query.',
     *     'post_description' => 'A description of the post.',
     *     'ai_driver' => 'openai',
     *     'remaining_comments' => 10,
     * ]);
     * echo $aiSetting->id; // Outputs the ID of the newly created AI setting
     * ```
     */
    public function create(array $data): AiSetting;

    /**
     * Update an existing AI setting.
     *
     * This method updates the `AiSetting` with the given ID using the provided
     * data and returns the updated `AiSetting` instance.
     *
     * @param int $id The ID of the AI setting to update.
     * @param array $data The data to update the AI setting with.
     * @return AiSetting The updated `AiSetting` instance.
     *
     * @example
     * ```php
     * $updatedAiSetting = $aiSettingRepository->update(1, [
     *     'ai_driver' => 'updated_driver',
     * ]);
     * echo $updatedAiSetting->ai_driver;
     * ```
     */
    public function update(int $id, array $data): AiSetting;

    /**
     * Retrieve an AI setting by its post ID.
     *
     * This method fetches the `AiSetting` associated with the given post ID.
     * If no setting is found, it returns `null`.
     *
     * @param string $postId The post ID to retrieve the AI setting for.
     * @return AiSetting|null The `AiSetting` instance if found, otherwise `null`.
     *
     * @example
     * ```php
     * $aiSetting = $aiSettingRepository->getByPostId('12345');
     * if ($aiSetting) {
     *     echo $aiSetting->id;
     * }
     * ```
     */
    public function getByPostId(string $postId): ?AiSetting;

    /**
     * Retrieve all AI settings for a specific user.
     *
     * This method fetches all `AiSetting` instances associated with the provided
     * `user_id`. It returns a collection of `AiSetting` models.
     *
     * @param int $userId The user ID to retrieve the AI settings for.
     * @return Collection A collection of `AiSetting` instances.
     *
     * @example
     * ```php
     * $aiSettings = $aiSettingRepository->getByUserId(1);
     * foreach ($aiSettings as $aiSetting) {
     *     echo $aiSetting->post_id;
     * }
     * ```
     */
    public function getByUserId(int $userId): Collection;

    /**
     * Decrement the remaining editable posts for a given post ID.
     *
     * This method decrements the `remaining_comments` attribute for the `AiSetting`
     * associated with the provided `post_id`. It reduces the number of times the
     * AI setting can be edited or interacted with, based on the business logic of
     * limiting the number of remaining editable actions.
     *
     * After calling this method, the `remaining_comments` value will be decreased by 1.
     * If the `remaining_comments` value is 0 or negative, no further decrements will be possible.
     *
     * @param string $postId The post ID whose remaining editable posts will be decremented.
     * @return void
     *
     * @example
     * ```php
     * // Decrement the remaining editable posts for the specified post
     * $aiSettingRepository->decrementRemainingEditPosts('12345');
     *
     * // After this method call, the `remaining_comments` attribute of the AI setting
     * // associated with the post ID '12345' will be decreased by 1. If the original
     * // `remaining_comments` was 10, it will now be 9. You can check it as follows:
     *
     * $aiSetting = $aiSettingRepository->getByPostId('12345');
     * echo $aiSetting->remaining_comments; // Outputs 9 (if it was 10 before)
     * ```
     */
    public function decrementRemainingEditPosts(string $postId): void;

    /**
     * Soft delete an AI setting by its ID.
     *
     * This method marks the `AiSetting` with the given ID as deleted without permanently
     * removing it from the database. It utilizes Laravel’s soft delete functionality,
     * allowing the record to be restored later if needed. If the record is not found,
     * it will silently do nothing or throw an exception depending on the implementation.
     *
     * @param int $id The ID of the AI setting to soft delete.
     * @return void
     *
     * @example
     * ```php
     * // Soft delete the AI setting with ID 5
     * $aiSetting = $aiSettingRepository->find(5);
     * if ($aiSetting) {
     *     $aiSettingRepository->remove(5);
     * }
     * ```
     */
    public function remove(int $id): void;
}
