import{r as P}from"./validations-4c0aab88.js";import{r as x,a8 as C,w as M,aR as A,cF as L,_ as w,M as G,cG as _,o as T,f as k,n as z}from"./index-169996dc.js";import{V as W}from"./VTextField-a8984053.js";/**
 * Vue Currency Input 3.1.0
 * (c) 2018-2024 <PERSON>
 * @license MIT
 */var y;(function(n){n.symbol="symbol",n.narrowSymbol="narrowSymbol",n.code="code",n.name="name",n.hidden="hidden"})(y||(y={}));var p;(function(n){n.precision="precision",n.thousands="thousands",n.millions="millions",n.billions="billions"})(p||(p={}));const D=n=>n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),N=n=>n.replace(/^0+(0$|[^0])/,"$1"),E=(n,i)=>(n.match(new RegExp(D(i),"g"))||[]).length,j=(n,i)=>n.substring(0,n.indexOf(i)),O=[",",".","٫","。"],$="(0|[1-9]\\d*)";class X{constructor(i){var t,e,s,c,o,u;const{currency:m,currencyDisplay:g,locale:r,precision:a,accountingSign:h,useGrouping:f}=i;this.locale=r,this.options={currency:m,useGrouping:f,style:"currency",currencySign:h?"accounting":void 0,currencyDisplay:g!==y.hidden?g:void 0};const d=new Intl.NumberFormat(r,this.options),F=d.formatToParts(123456);this.currency=(t=F.find(({type:l})=>l==="currency"))===null||t===void 0?void 0:t.value,this.digits=[0,1,2,3,4,5,6,7,8,9].map(l=>l.toLocaleString(r)),this.decimalSymbol=(e=F.find(({type:l})=>l==="decimal"))===null||e===void 0?void 0:e.value,this.groupingSymbol=(s=F.find(({type:l})=>l==="group"))===null||s===void 0?void 0:s.value,this.minusSign=(c=d.formatToParts(-1).find(({type:l})=>l==="minusSign"))===null||c===void 0?void 0:c.value,this.decimalSymbol===void 0?this.minimumFractionDigits=this.maximumFractionDigits=0:typeof a=="number"?this.minimumFractionDigits=this.maximumFractionDigits=a:(this.minimumFractionDigits=(o=a==null?void 0:a.min)!==null&&o!==void 0?o:d.resolvedOptions().minimumFractionDigits,this.maximumFractionDigits=(u=a==null?void 0:a.max)!==null&&u!==void 0?u:d.resolvedOptions().maximumFractionDigits);const b=l=>j(l,this.digits[1]),S=l=>l.substring(l.lastIndexOf(this.decimalSymbol?this.digits[0]:this.digits[1])+1);this.prefix=b(d.format(1)),this.suffix=S(d.format(1)),this.negativePrefix=b(d.format(-1)),this.negativeSuffix=S(d.format(-1))}parse(i){if(i){const t=this.isNegative(i);i=this.normalizeDigits(i),i=this.stripCurrency(i,t),i=this.stripSignLiterals(i);const e=this.decimalSymbol?`(?:${D(this.decimalSymbol)}(\\d*))?`:"",s=this.stripGroupingSeparator(i).match(new RegExp(`^${$}${e}$`));if(s&&this.isValidIntegerFormat(this.decimalSymbol?i.split(this.decimalSymbol)[0]:i,Number(s[1])))return+`${t?"-":""}${this.onlyDigits(s[1])}.${this.onlyDigits(s[2]||"")}`}return null}isValidIntegerFormat(i,t){const e={...this.options,minimumFractionDigits:0};return[this.stripCurrency(this.normalizeDigits(t.toLocaleString(this.locale,{...e,useGrouping:!0})),!1),this.stripCurrency(this.normalizeDigits(t.toLocaleString(this.locale,{...e,useGrouping:!1})),!1)].includes(i)}format(i,t={minimumFractionDigits:this.minimumFractionDigits,maximumFractionDigits:this.maximumFractionDigits}){return i!=null?i.toLocaleString(this.locale,{...this.options,...t}):""}toFraction(i){return`${this.digits[0]}${this.decimalSymbol}${this.onlyLocaleDigits(i.substring(1)).substring(0,this.maximumFractionDigits)}`}isFractionIncomplete(i){return!!this.normalizeDigits(this.stripGroupingSeparator(i)).match(new RegExp(`^${$}${D(this.decimalSymbol)}$`))}isNegative(i){return i.startsWith(this.negativePrefix)||this.minusSign===void 0&&(i.startsWith("(")||i.startsWith("-"))||this.minusSign!==void 0&&i.replace("-",this.minusSign).startsWith(this.minusSign)}insertCurrency(i,t){return`${t?this.negativePrefix:this.prefix}${i}${t?this.negativeSuffix:this.suffix}`}stripGroupingSeparator(i){return this.groupingSymbol!==void 0?i.replace(new RegExp(D(this.groupingSymbol),"g"),""):i}stripSignLiterals(i){return this.minusSign!==void 0?i.replace("-",this.minusSign).replace(this.minusSign,""):i.replace(/[-()]/g,"")}stripCurrency(i,t){return i.replace(t?this.negativePrefix:this.prefix,"").replace(t?this.negativeSuffix:this.suffix,"")}normalizeDecimalSeparator(i,t){return O.forEach(e=>{i=i.substring(0,t)+i.substring(t).replace(e,this.decimalSymbol)}),i}normalizeDigits(i){return this.digits[0]!=="0"&&this.digits.forEach((t,e)=>{i=i.replace(new RegExp(t,"g"),String(e))}),i}onlyDigits(i){return this.normalizeDigits(i).replace(/\D+/g,"")}onlyLocaleDigits(i){return i.replace(new RegExp(`[^${this.digits.join("")}]*`,"g"),"")}}class R{constructor(i){this.currencyFormat=i}}class q extends R{conformToMask(i,t=""){const e=this.currencyFormat.isNegative(i),s=d=>d===""&&e&&!(this.currencyFormat.minusSign===void 0?t===this.currencyFormat.negativePrefix+this.currencyFormat.negativeSuffix:t===this.currencyFormat.negativePrefix),c=d=>{if(s(d))return"";if(this.currencyFormat.maximumFractionDigits>0){if(this.currencyFormat.isFractionIncomplete(d))return d;if(d.startsWith(this.currencyFormat.decimalSymbol))return this.currencyFormat.toFraction(d)}return null};let o=i;o=this.currencyFormat.stripCurrency(o,e),o=this.currencyFormat.stripSignLiterals(o);const u=c(o);if(u!=null)return this.currencyFormat.insertCurrency(u,e);const[m,...g]=o.split(this.currencyFormat.decimalSymbol),r=N(this.currencyFormat.onlyDigits(m)),a=this.currencyFormat.onlyDigits(g.join("")).substring(0,this.currencyFormat.maximumFractionDigits),h=g.length>0&&a.length===0,f=r===""&&e&&(this.currencyFormat.minusSign===void 0?t===i.slice(0,-2)+this.currencyFormat.negativeSuffix:t===i.slice(0,-1));return h||f||s(r)?t:r.match(/\d+/)?{numberValue:+`${e?"-":""}${r}.${a}`,fractionDigits:a}:""}}class B extends R{conformToMask(i,t=""){if(i===""||this.currencyFormat.parse(t)===0&&this.currencyFormat.stripCurrency(t,!0).slice(0,-1)===this.currencyFormat.stripCurrency(i,!0))return"";const e=this.currencyFormat.isNegative(i),s=this.currencyFormat.stripSignLiterals(i)===""?-0:+`${e?"-":""}${N(this.currencyFormat.onlyDigits(i))}`/Math.pow(10,this.currencyFormat.maximumFractionDigits);return{numberValue:s,fractionDigits:s.toFixed(this.currencyFormat.maximumFractionDigits).slice(-this.currencyFormat.maximumFractionDigits)}}}const U={locale:void 0,currency:void 0,currencyDisplay:void 0,hideGroupingSeparatorOnFocus:!0,hideCurrencySymbolOnFocus:!0,hideNegligibleDecimalDigitsOnFocus:!0,precision:void 0,autoDecimalDigits:!1,valueRange:void 0,useGrouping:void 0,valueScaling:void 0};class Z{constructor(i){this.el=i.el,this.onInput=i.onInput,this.onChange=i.onChange,this.addEventListener(),this.init(i.options)}setOptions(i){this.init(i),this.format(this.currencyFormat.format(this.validateValueRange(this.numberValue))),this.onChange(this.getValue())}getValue(){return{number:this.valueScaling&&this.numberValue!=null?this.toInteger(this.numberValue,this.valueScaling):this.numberValue,formatted:this.formattedValue}}setValue(i){const t=this.valueScaling!==void 0&&i!=null?this.toFloat(i,this.valueScaling):i;t!==this.numberValue&&(this.format(this.currencyFormat.format(this.validateValueRange(t))),this.onChange(this.getValue()))}init(i){this.options={...U,...i},this.options.autoDecimalDigits&&(this.options.hideNegligibleDecimalDigitsOnFocus=!1),this.el.getAttribute("inputmode")||this.el.setAttribute("inputmode",this.options.autoDecimalDigits?"numeric":"decimal"),this.currencyFormat=new X(this.options),this.numberMask=this.options.autoDecimalDigits?new B(this.currencyFormat):new q(this.currencyFormat);const t={[p.precision]:this.currencyFormat.maximumFractionDigits,[p.thousands]:3,[p.millions]:6,[p.billions]:9};this.valueScaling=this.options.valueScaling?t[this.options.valueScaling]:void 0,this.valueScalingFractionDigits=this.valueScaling!==void 0&&this.options.valueScaling!==p.precision?this.valueScaling+this.currencyFormat.maximumFractionDigits:this.currencyFormat.maximumFractionDigits,this.minValue=this.getMinValue(),this.maxValue=this.getMaxValue()}getMinValue(){var i,t;let e=this.toFloat(-Number.MAX_SAFE_INTEGER);return((i=this.options.valueRange)===null||i===void 0?void 0:i.min)!==void 0&&(e=Math.max((t=this.options.valueRange)===null||t===void 0?void 0:t.min,this.toFloat(-Number.MAX_SAFE_INTEGER))),e}getMaxValue(){var i,t;let e=this.toFloat(Number.MAX_SAFE_INTEGER);return((i=this.options.valueRange)===null||i===void 0?void 0:i.max)!==void 0&&(e=Math.min((t=this.options.valueRange)===null||t===void 0?void 0:t.max,this.toFloat(Number.MAX_SAFE_INTEGER))),e}toFloat(i,t){return i/Math.pow(10,t??this.valueScalingFractionDigits)}toInteger(i,t){return Number(i.toFixed(t??this.valueScalingFractionDigits).split(".").join(""))}validateValueRange(i){return i!=null?Math.min(Math.max(i,this.minValue),this.maxValue):i}format(i,t=!1){if(i!=null){this.decimalSymbolInsertedAt!==void 0&&(i=this.currencyFormat.normalizeDecimalSeparator(i,this.decimalSymbolInsertedAt),this.decimalSymbolInsertedAt=void 0);const e=this.numberMask.conformToMask(i,this.formattedValue);let s;if(typeof e=="object"){const{numberValue:c,fractionDigits:o}=e;let{maximumFractionDigits:u,minimumFractionDigits:m}=this.currencyFormat;this.focus?m=t?o.replace(/0+$/,"").length:Math.min(u,o.length):Number.isInteger(c)&&!this.options.autoDecimalDigits&&(this.options.precision===void 0||m===0)&&(m=u=0),s=this.toInteger(Math.abs(c))>Number.MAX_SAFE_INTEGER?this.formattedValue:this.currencyFormat.format(c,{useGrouping:this.options.useGrouping!==!1&&!(this.focus&&this.options.hideGroupingSeparatorOnFocus),minimumFractionDigits:m,maximumFractionDigits:u})}else s=e;this.maxValue<=0&&!this.currencyFormat.isNegative(s)&&this.currencyFormat.parse(s)!==0&&(s=s.replace(this.currencyFormat.prefix,this.currencyFormat.negativePrefix)),this.minValue>=0&&(s=s.replace(this.currencyFormat.negativePrefix,this.currencyFormat.prefix)),(this.options.currencyDisplay===y.hidden||this.focus&&this.options.hideCurrencySymbolOnFocus)&&(s=s.replace(this.currencyFormat.negativePrefix,this.currencyFormat.minusSign!==void 0?this.currencyFormat.minusSign:"(").replace(this.currencyFormat.negativeSuffix,this.currencyFormat.minusSign!==void 0?"":")").replace(this.currencyFormat.prefix,"").replace(this.currencyFormat.suffix,"")),this.el.value=s,this.numberValue=this.currencyFormat.parse(s)}else this.el.value="",this.numberValue=null;this.formattedValue=this.el.value,this.onInput(this.getValue())}addEventListener(){this.el.addEventListener("input",i=>{const{value:t,selectionStart:e}=this.el,s=i;if(e&&s.data&&O.includes(s.data)&&(this.decimalSymbolInsertedAt=e-1),this.format(t),this.focus&&e!=null){const c=()=>{const{prefix:o,suffix:u,decimalSymbol:m,maximumFractionDigits:g,groupingSymbol:r}=this.currencyFormat;let a=t.length-e;const h=this.formattedValue.length;if(this.currencyFormat.minusSign===void 0&&(t.startsWith("(")||t.startsWith("-"))&&!t.endsWith(")"))return h-this.currencyFormat.negativeSuffix.length>1?this.formattedValue.substring(e).length:1;if(this.formattedValue.substring(e,1)===r&&E(this.formattedValue,r)===E(t,r)+1)return h-a-1;if(h<a)return e;if(m!==void 0&&t.indexOf(m)!==-1){const f=t.indexOf(m)+1;if(Math.abs(h-t.length)>1&&e<=f)return this.formattedValue.indexOf(m)+1;!this.options.autoDecimalDigits&&e>f&&this.currencyFormat.onlyDigits(t.substring(f)).length-1===g&&(a-=1)}return this.options.hideCurrencySymbolOnFocus||this.options.currencyDisplay===y.hidden?h-a:Math.max(h-Math.max(a,u.length),o.length)};this.setCaretPosition(c())}}),this.el.addEventListener("focus",()=>{this.focus=!0,this.numberValueOnFocus=this.numberValue,setTimeout(()=>{const{value:i,selectionStart:t,selectionEnd:e}=this.el;if(this.format(i,this.options.hideNegligibleDecimalDigitsOnFocus),t!=null&&e!=null&&Math.abs(t-e)>0)this.setCaretPosition(0,this.el.value.length);else if(t!=null){const s=this.getCaretPositionOnFocus(i,t);this.setCaretPosition(s)}})}),this.el.addEventListener("blur",()=>{this.focus=!1,this.format(this.currencyFormat.format(this.validateValueRange(this.numberValue))),this.numberValueOnFocus!==this.numberValue&&this.onChange(this.getValue())})}getCaretPositionOnFocus(i,t){if(this.numberValue==null)return t;const{prefix:e,negativePrefix:s,suffix:c,negativeSuffix:o,groupingSymbol:u,currency:m}=this.currencyFormat,g=this.numberValue<0,r=g?s:e,a=r.length;if(this.options.hideCurrencySymbolOnFocus||this.options.currencyDisplay===y.hidden){if(g){if(t<=1)return 1;if(i.endsWith(")")&&t>i.indexOf(")"))return this.formattedValue.length-1}}else{const f=g?o.length:c.length;if(t>=i.length-f)return this.formattedValue.length-f;if(t<a)return a}let h=t;return this.options.hideCurrencySymbolOnFocus&&this.options.currencyDisplay!==y.hidden&&t>=a&&m!==void 0&&r.includes(m)&&(h-=a,g&&(h+=1)),this.options.hideGroupingSeparatorOnFocus&&u!==void 0&&(h-=E(i.substring(0,t),u)),h}setCaretPosition(i,t=i){this.el.setSelectionRange(i,t)}}const H=n=>n!=null&&n.matches("input")?n:n==null?void 0:n.querySelector("input");function J(n,i){var t,e,s,c;let o;const u=x(null),m=x(null),g=x(null),r=A(),a=(r==null?void 0:r.emit)||((e=(t=r==null?void 0:r.proxy)===null||t===void 0?void 0:t.$emit)===null||e===void 0?void 0:e.bind(r==null?void 0:r.proxy)),h=(r==null?void 0:r.props)||((s=r==null?void 0:r.proxy)===null||s===void 0?void 0:s.$props),f=L.startsWith("3"),d=f&&((c=r==null?void 0:r.attrs.modelModifiers)===null||c===void 0?void 0:c.lazy),F=C(()=>h==null?void 0:h[f?"modelValue":"value"]),b=f?"update:modelValue":"input",S=d?"update:modelValue":"change";return M(u,l=>{var V;if(l){const I=H((V=l==null?void 0:l.$el)!==null&&V!==void 0?V:l);I?(o=new Z({el:I,options:n,onInput:v=>{!d&&i!==!1&&F.value!==v.number&&(a==null||a(b,v.number)),g.value=v.number,m.value=v.formatted},onChange:v=>{i!==!1&&(a==null||a(S,v.number))}}),o.setValue(F.value)):console.error('No input element found. Please make sure that the "inputRef" template ref is properly assigned.')}else o=null}),{inputRef:u,numberValue:g,formattedValue:m,setValue:l=>o==null?void 0:o.setValue(l),setOptions:l=>o==null?void 0:o.setOptions(l)}}const K=G({name:"CurrencyInput",props:{modelValue:{type:Number,default:0},options:{type:Object,default:()=>({})}},setup(n,{emit:i}){const{inputRef:t,numberValue:e,setValue:s,formattedValue:c}=J(n.options,!1),o=x("");return _(e,u=>{i("update:modelValue",u)},{debounce:100}),_(c,u=>{o.value=u},{debounce:100}),M(()=>n.modelValue,u=>{s(u)}),{inputRef:t,price:o}}});function Q(n,i,t,e,s,c){return T(),k(W,{rules:("requiredField"in n?n.requiredField:z(P))("قیمت"),label:"قیمت",suffix:"ریال",modelValue:n.price,"onUpdate:modelValue":i[0]||(i[0]=o=>n.price=o),ref:"inputRef",placeholder:"قیمت",type:"text",dir:"rtl"},null,8,["rules","modelValue"])}const ei=w(K,[["render",Q]]);export{ei as _};
