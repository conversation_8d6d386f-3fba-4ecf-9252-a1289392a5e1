import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('pages')
@Index('pages_access_token_unique', ['access_token'], { unique: true })
@Index('pages_page_user_id_unique', ['page_user_id'], { unique: true })
export class Page {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column('text', { nullable: true })
  access_token: string;

  @Column('text')
  page_user_id: string;

  @Column('text', { nullable: true })
  fb_user_id: string;

  @Column('text', { nullable: true })
  fb_access_token: string;

  @Column('text', { nullable: true })
  profile: string;

  @Column('timestamp', { nullable: true })
  expired_at: Date;

  @Column('bigint')
  user_id: number;

  @Column('bigint', { nullable: true })
  plan_id: number;

  @Column('timestamp', { nullable: true })
  created_at: Date;

  @Column('timestamp', { nullable: true })
  updated_at: Date;
}
