import{bp as d,a2 as C,bq as g}from"./index-169996dc.js";const M=d({baseUrl:"https://prest.manymessage.com/api",fetchOptions:{headers:{Accept:"application/json"}},options:{refetch:!0,async beforeFetch({options:e}){const t=C("accessToken").value;return t&&(e.headers={...e.headers,Authorization:`Bearer ${t}`}),{options:e}},afterFetch(e){const{data:t,response:o}=e;let r=null;try{r=g(t)}catch(c){console.error(c)}return{data:r,response:o}}}}),b=/#/g,O=/&/g,P=/\//g,L=/=/g,h=/\+/g,Q=/%5e/gi,T=/%60/gi,w=/%7c/gi,I=/%20/gi;function U(e){return encodeURI(""+e).replace(w,"|")}function a(e){return U(typeof e=="string"?e:JSON.stringify(e)).replace(h,"%2B").replace(I,"+").replace(b,"%23").replace(O,"%26").replace(T,"`").replace(Q,"^").replace(P,"%2F")}function s(e){return a(e).replace(L,"%3D")}function p(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function j(e){return p(e.replace(h," "))}function N(e){return p(e.replace(h," "))}function S(e=""){const t={};e[0]==="?"&&(e=e.slice(1));for(const o of e.split("&")){const r=o.match(/([^=]+)=?(.*)/)||[];if(r.length<2)continue;const c=j(r[1]);if(c==="__proto__"||c==="constructor")continue;const n=N(r[2]||"");t[c]===void 0?t[c]=n:Array.isArray(t[c])?t[c].push(n):t[c]=[t[c],n]}return t}function $(e,t){return(typeof t=="number"||typeof t=="boolean")&&(t=String(t)),t?Array.isArray(t)?t.map(o=>`${s(e)}=${a(o)}`).join("&"):`${s(e)}=${a(t)}`:s(e)}function v(e){return Object.keys(e).filter(t=>e[t]!==void 0).map(t=>$(t,e[t])).filter(Boolean).join("&")}const B=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,F=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,K=/^([/\\]\s*){2,}[^/\\]/;function D(e,t={}){return typeof t=="boolean"&&(t={acceptRelative:t}),t.strict?B.test(e):F.test(e)||(t.acceptRelative?K.test(e):!1)}function V(e,t){const o=E(e),r={...S(o.search),...t};return o.search=v(r),G(o)}const R=Symbol.for("ufo:protocolRelative");function E(e="",t){const o=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(o){const[,u,f=""]=o;return{protocol:u.toLowerCase(),pathname:f,href:u+f,auth:"",host:"",search:"",hash:""}}if(!D(e,{acceptRelative:!0}))return t?E(t+e):l(e);const[,r="",c,n=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[],[,i="",y=""]=n.match(/([^#/?]*)(.*)?/)||[],{pathname:_,search:m,hash:A}=l(y.replace(/\/(?=[A-Za-z]:)/,""));return{protocol:r.toLowerCase(),auth:c?c.slice(0,Math.max(0,c.length-1)):"",host:i,pathname:_,search:m,hash:A,[R]:!r}}function l(e=""){const[t="",o="",r=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:o,hash:r}}function G(e){const t=e.pathname||"",o=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",r=e.hash||"",c=e.auth?e.auth+"@":"",n=e.host||"";return(e.protocol||e[R]?(e.protocol||"")+"//":"")+c+n+t+o+r}export{v as s,M as u,V as w};
